/*
 * switch组件 用于将0 1 转换成布尔值，便于组件接受
 * @param
 * */
import React from 'react';
import { Switch } from 'antd';

const ProSwitch = ({ value, onChange, defaultValue }) => {
  const [options, setOptions] = React.useState([]);
  const [defaultChecked, setDefaultChecked] = React.useState(true); //默认值
  const transitionString = (val) => {
    let res = '';
    switch (val) {
      case '1':
        res = true;
        break;
      case '0':
        res = false;
        break;
      case true:
        res = '1';
        break;
      case false:
        res = '0';
        break;
    }
    console.log('switch');
    // console.log(res);
    return res;
  };
  React.useEffect(() => {
    //后端传 0 或 1，转换成布尔值给组件
    if (value === '1') {
      setDefaultChecked(true);
    } else if (value === '0') {
      setDefaultChecked(false);
    }
    console.log('switch:' + value);
    return () => {
      // 在组件卸载前执行
      // 在此做一些收尾工作, 比如清除定时器/取消订阅等
    };
  }, [value]); // 如果指定的是[], 回调函数只会在第一次render()后执行

  React.useEffect(() => {
    //初始化
    console.log('defaultValue');
    console.log(defaultValue);
    console.log(value);
    if (value !== '1' && value !== '0') {
      //没有赋值
      setDefaultChecked(transitionString(defaultValue));
      onChange(defaultValue);
    } else {
      //有赋值
      setDefaultChecked(transitionString(value));
      onChange(value);
    }
    return () => {
      // 在组件卸载前执行
      // 在此做一些收尾工作, 比如清除定时器/取消订阅等
    };
  }, []);

  //给父级传数据
  const handleOnChange = (value) => {
    console.log('给父级传数据:' + value);
    if (value) {
      onChange('1');
    } else {
      onChange('0');
    }
  };

  return (
    <Switch
      checkedChildren="开启"
      unCheckedChildren="关闭"
      defaultChecked={defaultChecked}
      onChange={handleOnChange}
    />
  );
};

export default ProSwitch;
