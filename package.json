{"name": "scenic", "version": "2.0.0", "private": true, "description": "慧景云", "scripts": {"analyze": "cross-env ANALYZE=1 max build", "dev": "npm run start:dev", "start:dev": "cross-env PROXY_ENV=dev max dev", "start:test": "cross-env PROXY_ENV=test max dev", "start:canary": "cross-env PROXY_ENV=canary max dev", "start:prod": "cross-env PROXY_ENV=prod max dev", "build": "max build && node config/createIndexHTML.js", "build:dev": "max build && node config/createIndexHTML.js", "build:test": "max build && node config/createIndexHTML.js", "build:canary": "max build && node config/createIndexHTML.js", "build:master": "max build && node config/createIndexHTML.js", "postinstall": "max setup", "lint": "npm run lint:js && npm run lint:prettier && npm run tsc", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src ", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\" --end-of-line auto", "prepare": "husky install", "prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\"", "preview": "npm run build && max preview --port 8000", "record": "cross-env NODE_ENV=development PROXY_ENV=test max record --scene=login", "serve": "umi-serve", "tsc": "tsc --noEmit"}, "lint-staged": {"**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ant-design/icons": "^4.8.3", "@ant-design/pro-card": "^2.9.9", "@ant-design/pro-components": "^2.7.9", "@ant-design/pro-descriptions": "^2.6.9", "@ant-design/pro-form": "^2.31.9", "@ant-design/pro-layout": "^7.22.6", "@ant-design/pro-table": "^3.20.1", "@bytemd/plugin-gfm": "^1.17.2", "@bytemd/react": "^1.17.2", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@microsoft/fetch-event-source": "^2.0.1", "@umijs/route-utils": "^2.2.2", "ahooks": "^3.9.0", "antd": "^5.18.0", "antd-img-crop": "^4.2.3", "antd-style": "^3.6.2", "bytemd": "1.17.2", "classnames": "^2.5.1", "copy-to-clipboard": "^3.3.3", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "^5.4.3", "github-markdown-css": "^5.1.0", "html2canvas": "^1.4.1", "immer": "^9.0.15", "lodash": "^4.17.4", "markdown-it": "^14.1.0", "omit.js": "^2.0.2", "qrcode.react": "^3.1.0", "qs": "^6.14.0", "querystring": "^0.2.1", "rc-menu": "^9.14.0", "rc-util": "^5.41.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet-async": "^1.3.0", "swagger-ui-dist": "^5.26.2", "use-immer": "^0.7.0"}, "devDependencies": {"@ant-design/pro-cli": "^3.3.0", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@testing-library/react": "^13.4.0", "@types/classnames": "^2.3.1", "@types/history": "^4.7.11", "@types/lodash": "^4.17.4", "@types/qs": "^6.9.16", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-helmet": "^6.1.11", "@umijs/fabric": "^4.0.1", "@umijs/lint": "^4.2.9", "@umijs/max": "^4.2.9", "commitlint": "^19.8.1", "cross-env": "^7.0.3", "eslint": "^8.57.0", "husky": "^7.0.4", "lint-staged": "^10.5.4", "prettier": "^2.8.8", "react-dev-inspector": "^1.9.0", "ts-node": "^10.9.2", "typescript": "^5.4.5", "umi-presets-pro": "^2.0.3", "umi-serve": "^1.9.11"}, "engines": {"node": ">=12.0.0"}}