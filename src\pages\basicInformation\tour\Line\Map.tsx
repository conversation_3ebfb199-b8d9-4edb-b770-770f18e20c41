import { pointType } from "@/common/utils/enum";
import { listPoint } from "@/services/api/tour";
import { DownOutlined } from "@ant-design/icons";
import { Dropdown, Transfer } from "antd";
import type { TransferDirection } from "antd/es/transfer";
import { cloneDeep } from "lodash";
import type { ReactNode } from "react";
import { useEffect, useState } from "react";
import { useModel } from "@umijs/max";
import styles from "./Map.less";

let pointIndex: number = 0;
let pointObj: any = {};
let initialValue = true;

export default (props: any) => {
  const { initialState } = useModel("@@initialState");
  const {
    scenicId,
    scenicAddress: { latitude, longitude }
  }: any = initialState?.scenicInfo;
  const [TMap] = useState(window.TMap);
  const [map, setMap] = useState<any>(null);
  const [marker, setMarker] = useState<any>(null);
  const [polylineLayer, setPolylineLayer] = useState<any>(null);
  const [type, setType] = useState<string>();
  const [typeList, setTypeList] = useState<any[]>([]);
  const [dataSource, setDataSource] = useState<Record<string, any>[]>([]);
  const [targetKeys, setTargetKeys] = useState<string[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);

  // 点位列表
  const getPointList = () => {
    listPoint({ scenicId, sort: 0 }).then(({ data }) => {
      const obj = {};
      setDataSource(
        data.map((item: any) => {
          if (!obj[item.pointType]) {
            obj[item.pointType] = pointType[item.pointType];
          }
          item.key = item.id;
          pointObj[item.id] = item;
          return item;
        })
      );
      setTypeList(Object.entries(obj).map((item: any) => ({ key: item[0], label: item[1] })));
      setType(Object.entries(obj)?.[0]?.[0]);
    });
  };
  // 点位类型下拉
  const DropDown: ReactNode = (
    <Dropdown
      menu={{
        items: typeList,
        onClick: ({ key }) => {
          setType(key);
        }
      }}
    >
      <a>
        {pointType[type]} <DownOutlined />
      </a>
    </Dropdown>
  );
  // 穿梭项
  const itemDom = (item: any) => {
    return (
      <span
        className={item.pointType == type ? "" : "not"}
        draggable={targetKeys.some((k: any) => item.key == k)}
        onDragStart={e => {
          e.dataTransfer.setData("Key", item.key);
        }}
        onDragOver={(e: any) => e.preventDefault()}
        onDrop={e => {
          const befourKey = e.dataTransfer.getData("Key");
          const afterKey = item.key;
          setTargetKeys((v: any) => {
            const value = cloneDeep(v);
            value.splice(value.indexOf(befourKey), 1);
            value.splice(value.indexOf(afterKey) + 1, 0, befourKey);
            return value;
          });
        }}
      >
        {item.pointName}
      </span>
    );
  };
  // 穿梭动作
  const onChange = (newTargetKeys: string[], direction: TransferDirection, moveKeys: string[]) => {
    if (direction == "left") {
      // 移除地图点位
      marker.remove(moveKeys);
      // 移除途经点数据
      if (moveKeys[0].includes("point")) {
        setDataSource((value: any) => value.filter((item: any) => item.key != moveKeys[0]));
      }
    } else {
      // 新增点位后置
      newTargetKeys.push(...newTargetKeys.splice(0, moveKeys.length));
      // 添加地图点位
      moveKeys.map((key: any) => {
        const obj: any = dataSource.find((item: any) => item.id == key);
        marker.add([
          {
            id: key,
            position: new TMap.LatLng(obj.latitude, obj.longitude),
            content: obj.pointName
          }
        ]);
      });
    }
    setTargetKeys(newTargetKeys);
  };
  // 路线渲染
  const lineValue = () => {
    return JSON.stringify(
      targetKeys
        .filter(i => pointObj[i])
        .map((item: any) =>
          pointObj[item].key
            ? {
                pointId: pointObj[item].id
              }
            : {
                id: pointObj[item].id,
                pointName: pointObj[item].content,
                latitude: pointObj[item].position.lat,
                longitude: pointObj[item].position.lng
              }
        )
    );
  };

  useEffect(() => {
    pointIndex = 0;
    pointObj = {};
    initialValue = true;
    // 获取点位列表
    getPointList();
    // 加载地图
    const map = new TMap.Map(document.getElementById("container"), {
      center: new TMap.LatLng(latitude || 22.530935, longitude || 113.951875), // 中心点坐标
      zoom: 16, // 缩放级别 [3, 20]
      viewMode: "2D", // 视图模式
      baseMap: {
        type: "vector",
        features: ["base", "building2d", "point"] // 仅渲染：道路及底面(base) + 2d建筑物(building2d) + poi文字
      }
    });
    const marker = new TMap.MultiMarker({
      map,
      styles: {
        default: new TMap.MarkerStyle({
          offset: { x: 0, y: 40 },
          strokeColor: "#fff",
          strokeWidth: 2
        })
      }
    });
    const polylineLayer = new TMap.MultiPolyline({
      id: "pl",
      map,
      styles: {
        default: new TMap.PolylineStyle({
          width: 8,
          borderWidth: 2,
          color: "#21b977",
          borderColor: "#178153",
          showArrow: true,
          lineCap: "round",
          arrowOptions: {
            height: 6
          }
        })
      }
    });
    map.removeControl(TMap.constants.DEFAULT_CONTROL_ID.ROTATION);
    if (props.onChange) {
      map.on("click", (v: any) => {
        pointIndex++;
        const key = `point${pointIndex}`;
        const pointName = `途径点${pointIndex}`;
        const item = { id: key, position: v.latLng, content: pointName };
        marker.add([item]);
        setDataSource((value: any) => [...value, { key, pointName }]);
        setTargetKeys((value: any) => [...value, key]);
        pointObj[key] = item;
        // props.formRef.setFieldsValue({ longitude: v.latLng.lng, latitude: v.latLng.lat });
      });
    }
    setMarker(marker);
    setPolylineLayer(polylineLayer);
    setMap(map);
    return () => map && map.destroy();
  }, []);

  useEffect(() => {
    const paths = targetKeys
      .filter(i => pointObj[i])
      .map(
        (item: any) =>
          new TMap.LatLng(
            pointObj[item].latitude || pointObj[item].position.lat,
            pointObj[item].longitude || pointObj[item].position.lng
          )
      );
    if (paths.length > 1) {
      polylineLayer?.updateGeometries({ id: "pl", paths });
    } else {
      polylineLayer?.remove(["pl"]);
    }
    if (props.onChange) {
      props.onChange(lineValue());
    }
  }, [targetKeys]);

  useEffect(() => {
    // 回显数据
    if (
      (props.type == "edit" || props.type == "info") &&
      initialValue &&
      dataSource?.length &&
      props.value &&
      props.value != "[]"
    ) {
      initialValue = false;
      const data = cloneDeep(dataSource);
      const key: any = [];
      const value = JSON.parse(props.value);
      value.map((item: any) => {
        const pointItem = {
          id: item.id || item.pointId,
          position: new TMap.LatLng(item.latitude || 0, item.longitude || 0),
          content: item.pointName
        };
        marker.add([pointItem]);
        key.push(item.id || item.pointId);
        if (item.id) {
          // 途径点
          pointIndex++;
          data.push({ key: item.id, pointName: item.pointName });
          pointObj[item.id] = pointItem;
        }
      });
      setDataSource(data);
      setTargetKeys(key);
      // 设置范围
      if (map && marker) {
        const bounds = new TMap.LatLngBounds();
        // 扩大 bounds 范围
        marker.geometries.forEach((item: any) => {
          bounds.extend(item.position);
        });
        // 设置地图可视范围
        map.fitBounds(bounds, { padding: { top: 100, bottom: 200, left: 100, right: 100 } });
      }
    }
  }, [dataSource, props.value]);

  return (
    <div
      style={{
        width: "100%",
        height: 675,
        // paddingBottom: '56.25%',
        position: "relative",
        zIndex: 0
      }}
    >
      <div
        id="container"
        style={{
          width: "100%",
          height: "100%",
          position: "absolute",
          zIndex: 0,
          border: "1px solid #d9d9d9",
          borderRadius: "2px",
          overflow: "hidden"
        }}
      />
      {props.type != "info" && (
        <div className={styles.control}>
          <Transfer
            key={type}
            oneWay
            showSelectAll={false}
            listStyle={{ background: "#fff" }}
            titles={[DropDown, "线路点位"]}
            dataSource={dataSource}
            targetKeys={targetKeys}
            selectedKeys={selectedKeys}
            onChange={onChange}
            onSelectChange={setSelectedKeys}
            render={itemDom}
          />
        </div>
      )}
    </div>
  );
};
