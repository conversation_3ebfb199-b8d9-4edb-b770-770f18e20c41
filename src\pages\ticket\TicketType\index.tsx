import {
  apiCoInfoOption,
  apiSimpleGoods,
  apiSimpleGoodsDel,
  apiSimpleGoodsStatus,
  apiSimpleTicket,
  apiSimpleTicketAdd,
  apiSimpleTicketDel,
  apiSimpleTicketEdit,
  apiSimpleTicketInfo,
  apiSimpleTicketStatus,
  getCoInfo
} from "@/services/api/ticket";
import { InfoCircleTwoTone, MinusCircleOutlined, PlusCircleOutlined, PlusOutlined } from "@ant-design/icons";
import type { ProFormColumnsType } from "@ant-design/pro-form";
import ProForm from "@ant-design/pro-form";
import type { ActionType, ProColumns } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import { Button, message, Modal, Space, Switch, Tabs, Tag, TimePicker } from "antd";
import React, { useRef, useState } from "react";

import DetailsPop from "@/common/components/DetailsPop";
import EditPop from "@/common/components/EditPop";
import { tableConfig } from "@/common/utils/config";
import {
  baseProductTypeEnum,
  enableEnum,
  isChainEnum,
  productTypeEnum,
  RightsTicketStatus,
  ticketTypeEnum,
  weekEnum
} from "@/common/utils/enum";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import { getUniqueId, jumpPage, toValueEnum } from "@/common/utils/tool";
import ImageUpload from "@/components/ImageUpload";
import MDEditor from "@/components/MDEditor";
import { round, trim } from "lodash";
import dayjs from "dayjs";
import { parse } from "querystring";
import { Access, useAccess, useModel, useRequest } from "@umijs/max";
import Detail from "./components/CommodityDetails";
import Commoditys from "./components/Commoditys";
import GoodsForm from "./components/GoodsForm";
import ProductForm from "./components/ProductForm";

// 【全局】静态变量/格式化
// 是否
const booleanFormat = (e: any) => ["否", "是"][e];
// 禁启用
export const enableFormat = (e: any) => <Tag color={["red", "blue"][e]}>{["禁用", "启用"][e]}</Tag>;
// 星期
const weekOptions = [
  { value: 0, label: "星期日" },
  { value: 1, label: "星期一" },
  { value: 2, label: "星期二" },
  { value: 3, label: "星期三" },
  { value: 4, label: "星期四" },
  { value: 5, label: "星期五" },
  { value: 6, label: "星期六" }
];
// 时区
const timeShareOptions = Array(25)
  .fill("")
  .map((_, i) => (i < 10 ? "0" + i : i) + ":00");

let timeList: any = [];
const defaultTime = {
  id: "",
  beginTime: "",
  endTime: "",
  unique: getUniqueId()
};

//埋点格式数据
const operationData = [
  {
    name: "景区名称",
    key: "scenicName"
  },
  {
    name: "产品类型",
    key: "proType"
  },
  {
    name: "所属服务商",
    key: "operatorId"
  },
  {
    name: "产品名称",
    key: "name"
  },
  {
    name: "C 端显示名称",
    key: "pcName"
  },
  {
    name: "是否按星期控制（设置该星期下，不可售该票）",
    key: "restrictWeekList"
  },
  {
    name: "分时预约（入园开始时间（含） 至 入园截止时间（不含））",
    key: "timeRestrict"
  },
  {
    name: "参与入园统计",
    key: "parkStatistic"
  },
  {
    name: "入园须知",
    key: "notice"
  },
  {
    name: "备注",
    key: "remark"
  }
];
const TableList: React.FC = props => {
  // 【景区】信息
  const { initialState } = useModel("@@initialState");
  const { scenicId = "", scenicName = "", isBlockChain } = initialState?.scenicInfo || {};
  const { currentCompanyInfo }: any = initialState || {};
  const { type, id, tag } = parse(window.location.hash.split("?")[1]) || {};
  console.log(type);
  const format = "HH:mm";
  const access = useAccess();

  const { data: coInfoMap } = useRequest(
    () =>
      getCoInfo({
        scenicId
      }),
    {
      formatResult(res) {
        const coInfoMap = new Map();
        for (const item of res.data ?? []) {
          coInfoMap.set(item.coId, item.coName);
        }
        return coInfoMap;
      }
    }
  );

  // 【产品表格】数据绑定
  const actionRef = useRef<ActionType>();
  const [columnsKey, setColumnsKey] = useState<any>("commoditys");
  const product: ProColumns[] = [
    {
      title: "产品名称",
      dataIndex: "name",
      fixed: "left",
      search: {
        transform: val => trim(val)
      }
    },

    {
      title: "产品类型",
      dataIndex: "proType",
      valueEnum: productTypeEnum
    },
    {
      title: "市场标准价",
      dataIndex: "marketPrice",
      hideInSearch: true,
      render: (dom, record) => {
        return (record.marketPrice * 1).toFixed(2);
      }
    },
    {
      title: "有效时长天数（天）",
      search: false,

      dataIndex: "validityDay",
      hideInSearch: true
    },
    {
      title: "是否首日激活",
      dataIndex: "isActivate",
      valueEnum: {
        0: "否",
        1: "是"
      }
    },
    {
      title: "可入园天数",
      search: false,
      dataIndex: "availableDays",
      renderText: text => `${text} 天`
    },
    {
      title: "使用次数",
      search: false,
      dataIndex: "useCount",
      renderText: (_, { useType = 0, useCount = 0 }) => {
        const useTypeArr = ["每天", "一共"];
        return `${useTypeArr[useType]}${useCount || "无限"}次`;
      }
    },
    {
      title: "参与入园统计",
      dataIndex: "parkStatistic",
      valueEnum: {
        0: "否",
        1: "是"
      }
    },
    // {
    //   title: '分时预约',
    //   dataIndex: 'timeRestrict',
    //   valueEnum: {
    //     0: '否',
    //     1: '是',
    //   },
    // },
    // {
    //   title: '分时预约时间',
    //   hideInSearch: true,
    //   render: (_, entity: any) =>
    //     entity.beginTime && entity.endTime ? entity.beginTime + ' - ' + entity.endTime : '-',
    // },
    {
      title: "启用状态",
      dataIndex: "isEnable",
      fixed: "right",
      valueEnum: enableEnum,
      renderText: (dom: any, entity: any) => (
        <Switch
          disabled={!access.canTicketSet_openClose}
          checkedChildren="启用"
          unCheckedChildren="禁用"
          checked={!!dom}
          onChange={() => {
            apiSimpleTicketStatus({
              id: entity.id,
              isEnable: 1 - entity.isEnable,
              scenicId
            })
              .then(() => {
                message.success(dom ? "已禁用" : "已启用");
                addOperationLogRequest({
                  action: "disable",
                  content: `${dom ? "禁用" : "启用"}【${entity.name}】产品`
                });
                actionRef.current?.reload();
              })
              .catch(() => {});
          }}
        />
      )
    },
    {
      width: 250,
      title: "操作",
      valueType: "option",
      fixed: "right",
      render: (_: any, record: any) => (
        <Space size="large">
          <a
            onClick={async () => {
              setLoading(true);
              setDetailsVisible(true);
              try {
                const data = await apiSimpleTicketInfo(record.id);
                data.proType += "";
                setDataSource(data);
                timeList = data.timeShare;
                setTimeData(!timeData);
                setLoading(false);
                addOperationLogRequest({
                  action: "info",
                  content: `查看【${record.name}】产品详情`
                });
              } catch (error) {
                setDetailsVisible(false);
              }
            }}
          >
            查看
          </a>
          <Access accessible={access.canTicketSet_edit && record?.isDigit != "1"}>
            <a
              onClick={async () => {
                jumpPage.push(`/ticket/ticket-type/edit?type=edit&tag=product&id=${record.id}`);
              }}
            >
              编辑
            </a>
          </Access>
          <Access accessible={access.canTicketSet_delete && record?.isDigit != "1"}>
            <a
              style={{ color: "red" }}
              onClick={async () => {
                if (record.isEnable) {
                  Modal.warning({
                    title: "不可删除",
                    content: "请先禁用后删除"
                  });
                  return;
                }
                Modal.confirm({
                  title: "确认删除吗？",
                  icon: <InfoCircleTwoTone />,
                  content: "删除后不可恢复",
                  okText: "确认",
                  cancelText: "取消",
                  onOk: () => {
                    apiSimpleTicketDel(record.id)
                      .then(() => {
                        message.success("删除成功");

                        addOperationLogRequest({
                          action: "del",
                          content: `删除【${record.name}】产品`
                        });

                        actionRef.current?.reload();
                      })
                      .catch(() => {});
                  }
                });
              }}
            >
              删除
            </a>
          </Access>
          <Access accessible={access.canTicketSet_goodsSelect}>
            <a
              onClick={() => {
                setTicketId(record.id);
                setTimeId(record.timeId);
                setTimeShare(record.beginTime && record.endTime ? record.beginTime + " - " + record.endTime : "-");
                setTicketName(record.name);
                setType(record.proType);
                setMarketPriceValue(record.marketPrice * 1);
                setCommodityVisible(true);

                addOperationLogRequest({
                  action: "info",
                  content: `查看【${record.name}】对应商品列表`
                });
              }}
            >
              查看商品
            </a>
          </Access>
        </Space>
      )
    }
  ];
  const commoditys: ProColumns[] = [
    {
      title: "产品名称",
      dataIndex: "name",
      fixed: "left",
      search: {
        transform: val => trim(val)
      }
    },
    {
      title: "商品名称",
      dataIndex: "goodsName",
      fixed: "left",
      search: {
        transform: val => trim(val)
      }
    },
    {
      title: "票种",
      dataIndex: "type",
      valueEnum: ticketTypeEnum
    },
    {
      title: "数字资产",
      dataIndex: "isDigit",
      valueEnum: isChainEnum,
      search: false,
      hideInTable: isBlockChain != 1
    },
    {
      title: "特殊折扣率",
      dataIndex: "overallDiscount",
      search: false,
      renderText: (_, { marketPrice = 0, overallDiscount }) => {
        return `${overallDiscount}% (${round(marketPrice * (overallDiscount / 100), 2)}元)`;
      }
    },
    {
      title: "分销折扣区间",
      dataIndex: "beginDiscount",
      search: false,
      renderText: (_, { beginDiscount, endDiscount, marketPrice = 0, overallDiscount }) => {
        return `${beginDiscount}% - ${endDiscount}% (${round(
          (beginDiscount * marketPrice * overallDiscount) / 10000,
          2
        )}元
        - ${round((endDiscount * marketPrice * overallDiscount) / 10000, 2)}元)`;
      }
    },

    {
      title: "权益票审核",
      dataIndex: "rightsStatus",
      valueEnum: toValueEnum(RightsTicketStatus)
    },
    {
      title: "分时预约",
      dataIndex: "timeRestrict",
      valueEnum: {
        0: "否",
        1: "是"
      }
    },
    {
      title: "分时时段",
      hideInSearch: true,
      dataIndex: "timeShareVoList",
      render: (_, { timeShareVoList }: any) => {
        return timeShareVoList?.length
          ? timeShareVoList.map((item: any) => <Tag key={item.id}>{[item.beginTime, item.endTime].join("-")}</Tag>)
          : "-";
      }
    },
    {
      title: "启用状态",
      dataIndex: "isEnable",
      fixed: "right",
      valueEnum: enableEnum,
      editable: false,
      renderText: (dom: any, entity: any) => (
        <Switch
          disabled={entity.rightsStatus == 1}
          checkedChildren="启用"
          unCheckedChildren="禁用"
          checked={dom == 1}
          onChange={() => {
            apiSimpleGoodsStatus({
              goodsId: entity.id,
              isEnable: 1 - entity.isEnable,
              scenicId,
              productId: entity.productId
            })
              .then(() => {
                message.success(dom == 1 ? "已禁用" : "已启用");
                addOperationLogRequest({
                  action: "disable",
                  content: `${dom == 1 ? "禁用" : "启用"}【${entity.goodsName}】商品`
                });

                actionRef.current?.reload();
              })
              .catch(() => {});
          }}
        />
      )
    },
    {
      width: 200,
      title: "操作",
      valueType: "option",
      fixed: "right",
      render: (_: any, record: any) => (
        <Space size="large">
          <a
            onClick={async () => {
              Detail.show(record.id);
              addOperationLogRequest({
                action: "info",
                content: `查看【${record.goodsName}】商品详情`
              });
            }}
          >
            查看
          </a>
          {record.rightsStatus !== 1 && record?.isDigit != "1" && (
            <a
              onClick={() => {
                jumpPage.push(`/ticket/ticket-type/edit?type=edit&tag=goods&id=${record.id}`);
              }}
            >
              编辑
            </a>
          )}
          {record?.isDigit != "1" && (
            <a
              style={{ color: "red" }}
              onClick={async () => {
                if (record.isEnable == 1) {
                  Modal.warning({
                    title: "不可删除",
                    content: "请先禁用后删除"
                  });
                  return;
                }
                Modal.confirm({
                  title: "确认删除吗？",
                  icon: <InfoCircleTwoTone />,
                  content: "删除后不可恢复",
                  okText: "确认",
                  cancelText: "取消",
                  onOk: () => {
                    apiSimpleGoodsDel(record.id)
                      .then(() => {
                        message.success("删除成功");
                        addOperationLogRequest({
                          action: "del",
                          content: `删除【${record.goodsName}】商品`
                        });
                        actionRef.current?.reload();
                      })
                      .catch(() => {});
                  }
                });
              }}
            >
              删除
            </a>
          )}
        </Space>
      )
    }
  ];
  const tabColumns = { product, commoditys };

  // 【产品表单】数据绑定
  const [dataSource, setDataSource] = useState<any>({ id: "", isEnable: 0, notice: "无" });
  const [editVisible, setEditVisible] = useState<boolean>(false);
  // 是否按星期控制
  const [isRestrict, setIsRestrict] = useState<boolean>(false);
  // 分时预约
  const [isTimeRestrict, setIsTimeRestrict] = useState<boolean>(false);

  const [timeData, setTimeData] = useState(false);
  const changeValue = (index: any, type: any, e: any) => {
    timeList[index][type] = e;
    setTimeData(!timeData);
  };

  const TimeList: any = () => {
    return timeList?.map((item: any, index: any) => (
      <div
        key={item.unique ? item.unique : item.id}
        style={{ width: "688px", display: "flex", marginBottom: "12px", alignItems: "center" }}
      >
        {/* <TimePicker
          style={{ width: '300px' }}
          format={format}
          onChange={(time, timeString) => {
            changeValue(index, 'beginTime', timeString[0]);
          }}
        /> */}
        <TimePicker.RangePicker
          style={{ width: "624px" }}
          defaultValue={item.beginTime ? [dayjs(item.beginTime, format), dayjs(item.endTime, format)] : [null, null]}
          disabled={dataSource.id}
          format={format}
          onChange={(_, e) => {
            changeValue(index, "beginTime", e[0]);
            changeValue(index, "endTime", e[1]);
          }}
        />

        <div
          style={{
            flex: 1,
            display: "flex",
            justifyContent: "flex-end",
            alignItems: "center"
          }}
        >
          <PlusCircleOutlined
            style={{
              marginLeft: "16px",
              cursor: !dataSource.id ? "pointer" : "not-allowed",
              fontSize: "16px"
            }}
            onClick={() => {
              if (!dataSource.id) {
                defaultTime.unique = getUniqueId();
                timeList.splice(index + 1, 0, { ...defaultTime });
                setTimeData(!timeData);
              }
            }}
          />
          <MinusCircleOutlined
            style={{
              marginLeft: "16px",
              cursor: !dataSource.id && timeList.length > 1 ? "pointer" : "not-allowed",
              fontSize: "16px"
            }}
            onClick={() => {
              if (!dataSource.id && timeList.length > 1) {
                timeList.splice(index, 1);
                setTimeData(!timeData);
              }
            }}
          />
        </div>
      </div>
    ));
  };

  const editColumns: ProFormColumnsType<any>[] = [
    {
      title: "基础信息",
      columns: [
        {
          title: "景区名称",
          dataIndex: "scenicId",
          valueType: "select",
          valueEnum: { [scenicId]: scenicName },
          initialValue: scenicId,
          fieldProps: { disabled: true }
        },
        {
          title: "产品类型",
          dataIndex: "proType",
          valueType: "select",
          valueEnum: baseProductTypeEnum,
          fieldProps: {
            allowClear: false,
            getPopupContainer: (node: any) => node.parentNode
          },
          initialValue: "0",
          formItemProps: { rules: [{ required: true }] }
        },
        {
          title: "所属服务商",
          dataIndex: "operatorId",
          valueType: "select",
          initialValue: currentCompanyInfo?.coId,
          valueEnum: coInfoMap,
          // params: { scenicId },
          // request: apiCoInfoOption,
          fieldProps: {
            allowClear: false,
            disabled: true
          }
          // formItemProps: { rules: [{ required: true }] },
        },
        {
          title: "产品名称",
          dataIndex: "name",
          formItemProps: { rules: [{ required: true }] }
        },
        {
          title: "C 端显示名称",
          dataIndex: "pcName",
          formItemProps: { rules: [{ required: true }] }
        },
        {
          title: "市场标准价",
          dataIndex: "marketPrice",
          valueType: "digit",
          fieldProps: { min: 0 },
          // render:(dom)=>dom.toFixed(2),
          formItemProps: { rules: [{ required: true }] }
        }
      ]
    },
    {
      title: "票务属性信息",
      valueType: "group",
      columns: [
        {
          title: "是否按星期控制（设置该星期下，不可售该票）",
          dataIndex: "restrictType",
          valueType: "switch",
          fieldProps: {
            onChange: (e: any) => {
              setIsRestrict(e);
            },
            disabled: dataSource?.id
          }
        },
        {
          width: 688,
          // hideInForm: !isRestrict,
          title: "",
          dataIndex: "restrictWeekList",
          valueType: "select",
          fieldProps: {
            showSearch: false,
            mode: "multiple",
            options: weekOptions,
            style: { display: isRestrict ? "block" : "none" },
            disabled: dataSource?.id
          },
          formItemProps: { rules: [{ required: isRestrict }] }
        },
        {
          title: "分时预约（入园开始时间（含）至 入园截止时间（不含））",
          dataIndex: "timeRestrict",
          valueType: "switch",
          fieldProps: {
            onChange: (e: any) => {
              setIsTimeRestrict(e);
            },
            disabled: dataSource?.id
          }
        },
        {
          title: "",
          width: 688,
          hideInForm: !isTimeRestrict,
          renderFormItem: timeData ? TimeList : TimeList
        },
        {
          title: "参与入园统计",
          dataIndex: "parkStatistic",
          valueType: "switch",
          initialValue: true
        }
      ]
    },
    {
      title: "说明信息",
      columns: [
        {
          width: 688,
          title: "上传产品图片",
          tooltip: (
            <>
              <span>最多可上传 9 张图片，每张最</span>
              <div>大为 100M，支持 jpg、png 格式</div>
            </>
          ),
          dataIndex: "pictureUrl",
          renderFormItem: (_, { record }) => {
            return (
              <ProForm.Item name="pictureUrl">
                <ImageUpload defaultValue={dataSource?.pictureUrl?.trim()} maxCount={9} />
              </ProForm.Item>
            );
          }
        },
        {
          width: 688,
          dataIndex: "notice",
          renderFormItem: () => (
            <ProForm.Item
              name="notice"
              label="入园须知"
              rules={[
                {
                  type: "string",
                  max: 2000
                }
              ]}
            >
              <MDEditor />
            </ProForm.Item>
          )
        },
        {
          width: 688,
          title: "备注",
          dataIndex: "remark",
          valueType: "textarea",
          fieldProps: {
            showCount: true,
            maxLength: 1000
          }
        }
      ]
    }
  ];

  // 【产品列表】数据绑定
  const [detailsVisible, setDetailsVisible] = useState<boolean>(false);
  const [isLoading, setLoading] = useState<boolean>(true);
  const columnsInitial = [
    {
      title: "基础信息",
      columns: [
        {
          title: "所属景区",
          dataIndex: "scenicName",
          render: () => scenicName
        },
        {
          title: "产品类型",
          dataIndex: "proType",
          valueEnum: productTypeEnum
        },
        {
          title: "所属服务商",
          dataIndex: "operatorId",
          // renderText: (text) => (coInfoMap ? coInfoMap[text] : '-'),
          // valueType: 'select',
          // valueEnum: coInfoMap,
          params: { scenicId },
          request: apiCoInfoOption
        },
        {
          title: "产品名称",
          dataIndex: "name"
        },
        {
          title: "C 端显示名称",
          dataIndex: "pcName"
        },
        {
          title: "市场标准价",
          dataIndex: "marketPrice",
          render: (dom, record) => {
            return (record.marketPrice * 1).toFixed(2);
          }
        },
        {
          title: "有效时长天数（天）",
          dataIndex: "validityDay",
          renderText: text => `${text} 天`
        },
        {
          title: "是否首日激活",
          dataIndex: "isActivate",
          valueEnum: {
            0: "否",
            1: "是"
          }
        },
        {
          title: "可入园天数",
          dataIndex: "availableDays",
          renderText: text => `${text} 天`
        },
        {
          title: "使用次数",
          dataIndex: "useCount",
          renderText: (_, { useType = 0, useCount = 0 }) => {
            const useTypeArr = ["每天", "一共"];
            return `${useTypeArr[useType]}${useCount || "无限"}次`;
          }
        }
      ]
    },
    {
      title: "票务属性信息",
      columns: [
        {
          span: 2,
          title: "是否按星期控制",
          dataIndex: "restrictType",
          render: (dom: any) => booleanFormat(dom)
        },
        {
          span: 2,
          dataIndex: "restrictWeekList",
          style: {
            paddingBottom: 0
          },
          render: (dom: any, entity: any) =>
            entity.restrictType ? (
              <div style={{ paddingBottom: "16px" }}>
                <span className="ant-descriptions-item-label" style={{ fontWeight: "bold" }}>
                  该星期下，不可售该票
                </span>
                <span>
                  {dom
                    ? dom.map((item: any, index: any) => (
                        <Tag style={{ marginBottom: "8px" }} key={index}>
                          {weekEnum[item]}
                        </Tag>
                      ))
                    : "-"}
                </span>
              </div>
            ) : (
              ""
            )
        }
        // {
        //   span: 2,
        //   title: '分时预约',
        //   dataIndex: 'timeRestrict',
        //   render: (dom: any) => booleanFormat(dom),
        // },
        // {
        //   span: 2,
        //   dataIndex: 'timeShare',
        //   style: {
        //     paddingBottom: 0,
        //   },
        //   render: (dom: any, entity: any) =>
        //     entity.timeRestrict ? (
        //       <div style={{ paddingBottom: '16px' }}>
        //         <span className="ant-descriptions-item-label" style={{ fontWeight: 'bold' }}>
        //           入园开始时间（含）至截止时间（不含）
        //         </span>
        //         <span>
        //           {dom.map((item: any) => (
        //             <Tag key={key()} style={{ marginBottom: '8px' }}>
        //               <span>{item.beginTime}</span>
        //               <span> - </span>
        //               <span>{item.endTime} </span>
        //             </Tag>
        //           ))}
        //         </span>
        //       </div>
        //     ) : (
        //       ''
        //     ),
        // },
      ]
    },
    {
      title: "其他信息",
      columns: [
        {
          title: "参与入园统计",
          dataIndex: "parkStatistic",
          render: (dom: any) => booleanFormat(dom)
        }
      ]
    },
    {
      title: "说明信息",

      columns: [
        {
          title: "入园须知",
          span: 2,
          dataIndex: "notice",
          render: (value: string) => (value ? <MDEditor value={value} readonly /> : "-")
        },
        {
          title: "备注",
          dataIndex: "remark"
        }
      ]
    }
  ];

  // 【商品弹窗】数据绑定
  const [ticketId, setTicketId] = useState("");
  const [timeId, setTimeId] = useState("");
  const [timeShare, setTimeShare] = useState("");
  const [ticketName, setTicketName] = useState("");
  const [proType, setType] = useState("");
  const [commodityVisible, setCommodityVisible] = useState(false);
  const [marketPriceValue, setMarketPriceValue] = useState(0);
  return (
    <>
      <div style={{ display: type ? "none" : "block" }}>
        <Tabs
          style={{ background: "#fff" }}
          tabBarStyle={{ padding: "0 24px", margin: "0" }}
          onChange={activeKey => {
            setColumnsKey(activeKey);
            actionRef?.current?.reload();
          }}
          items={[
            {
              label: "商品列表",
              key: "commoditys"
            },
            {
              label: "产品列表",
              key: "product"
            }
          ]}
        />
        {/* 表格 */}
        <ProTable<API.RuleListItem, API.PageParams>
          {...tableConfig}
          rowClassName={(row: any) => (row.isEnable == 1 ? "" : "disableRow")}
          actionRef={actionRef}
          toolbar={{
            actions: [
              <Access key={getUniqueId()} accessible={access.canTicketSet_insert}>
                <Button
                  type="primary"
                  key="primary"
                  onClick={() => {
                    jumpPage.push(
                      `/ticket/ticket-type/edit?type=add&tag=${columnsKey == "product" ? "product" : "goods"}`
                    );
                  }}
                >
                  <PlusOutlined /> 新增
                </Button>
              </Access>
            ]
          }}
          params={{
            scenicId,
            operatorId: currentCompanyInfo.coId
          }}
          request={columnsKey == "product" ? apiSimpleTicket : apiSimpleGoods}
          columns={tabColumns[columnsKey]}
        />

        {/* 产品表单 */}
        <EditPop
          title="产品"
          visible={editVisible}
          setVisible={setEditVisible}
          columns={editColumns}
          dataSource={dataSource}
          onValuesChange={(formRef: any, e: any) => {
            if (e.name) {
              formRef?.current?.setFieldsValue({ pcName: e.name });
            }
            if (e.hasOwnProperty("restrictType") && !e.restrictType) {
              formRef?.current?.validateFields(["restrictWeekList"]);
            }
          }}
          // 新增/编辑
          onFinish={async (val: any) => {
            val.timeShare = timeList;
            try {
              if (val.timeRestrict) {
                // 判断两个去交是否有交集
                function isIntersect(arr1: any, arr2: any) {
                  const start = [Math.min(...arr1), Math.min(...arr2)]; //区间的两个最小值
                  const end = [Math.max(...arr1), Math.max(...arr2)]; //区间的两个最大值
                  return Math.max(...start) <= Math.min(...end); //最大值里的最小值 是否 小于等于 最大值的最小值
                }

                const arr: any = [];
                val.timeShare?.map((item: any) => {
                  if (!item.beginTime || !item.endTime) {
                    message.info("时间区间不能为空");
                    throw new Error("");
                  }
                  // 向区间集合新增新的区间时，判断区间集合中是否存在与新区间有交集的，如果存在，去除区间集合中有交集的区间，再加入新区间
                  const arr2 = [item.beginTime.slice(0, 2), item.endTime.slice(0, 2) - 1];
                  for (let i = arr.length - 1; i >= 0; i--) {
                    if (isIntersect(arr[i], arr2)) {
                      // arr.splice(i, 1);
                      message.info("时间区间存在交集");
                      throw new Error("");
                    }
                  }
                  arr.push(arr2);
                });
              }

              // 格式化数据
              if (dataSource.id) {
                val.id = dataSource.id;
              }
              val.scenicName = scenicName;
              val.restrictType = val.restrictType ? 1 : 0;
              val.timeRestrict = val.timeRestrict ? 1 : 0;
              val.parkStatistic = val.parkStatistic ? 1 : 0;
              val.pictureUrl = val.pictureUrl || " ";
              const msgType = val.id ? "编辑" : "新增";
              const hide = message.loading("正在" + msgType);

              try {
                await (dataSource.id
                  ? apiSimpleTicketEdit({ ...val, remark: val.remark || " " })
                  : apiSimpleTicketAdd(val));

                if (dataSource.id) {
                  addOperationLogRequest({
                    action: "edit",
                    content: `编辑【${val.pcName}】产品信息`
                  });
                } else {
                  addOperationLogRequest({
                    action: "add",
                    content: `新增【${val.pcName}】产品信息`
                  });
                }

                message.success(msgType + "成功");

                // 埋点
                const isAdd = !dataSource.id;
                // 需要处理的数据
                const getOther = (obj: any) => ({
                  scenicName,
                  proType: baseProductTypeEnum[obj.proType],
                  operatorId: coInfoMap?.get(obj.operatorId)
                });

                // 关闭弹窗并刷新列表
                setEditVisible(false);
                actionRef?.current?.reload();
              } catch (error) {}
              hide();
            } catch (error) {}
          }}
        />

        {/* 产品详情 */}
        <DetailsPop
          title="产品详情"
          visible={detailsVisible}
          isLoading={isLoading}
          setVisible={setDetailsVisible}
          columnsInitial={columnsInitial}
          dataSource={dataSource}
        />

        {/* 商品弹窗 */}
        <Commoditys
          ticketId={ticketId}
          timeShare={timeShare}
          timeShareId={timeId}
          ticketName={ticketName}
          proType={proType}
          commodityVisible={commodityVisible}
          setCommodityVisible={setCommodityVisible}
          marketPrice={marketPriceValue}
          productionRef={actionRef}
        />
      </div>
      {tag === "product" && type && <ProductForm type={type} id={id} actionRef={actionRef} />}
      {tag === "goods" && type && <GoodsForm type={type} id={id} actionRef={actionRef} />}
    </>
  );
};

export default TableList;
