import { useState } from "react";
import { message, Tag } from "antd";
import { groupBy } from "lodash";
import dayjs from "dayjs";
import { useModel } from "@umijs/max";

import NoticeIcon from "./NoticeIcon";
import styles from "./index.less";
import { apiMessageList, apiProcessingFlowBizList } from "@/services/api/erp";
import { history } from "@@/core/history";

export type GlobalHeaderRightProps = {
  fetchingNotices?: boolean;
  onNoticeVisibleChange?: (visible: boolean) => void;
  onNoticeClear?: (tabName?: string) => void;
};

const getNoticeData = (notices: API.NoticeIconItem[]): Record<string, API.NoticeIconItem[]> => {
  if (!notices || notices.length === 0 || !Array.isArray(notices)) {
    return {};
  }

  const newNotices = notices.map(notice => {
    const newNotice = { ...notice };

    if (newNotice.datetime) {
      newNotice.datetime = dayjs(notice.datetime as string).fromNow();
    }

    if (newNotice.id) {
      newNotice.key = newNotice.id;
    }

    if (newNotice.extra && newNotice.status) {
      const color = {
        todo: "",
        processing: "blue",
        urgent: "red",
        doing: "gold"
      }[newNotice.status];
      newNotice.extra = (
        <Tag
          color={color}
          style={{
            marginRight: 0
          }}
        >
          {newNotice.extra}
        </Tag>
      ) as any;
    }

    return newNotice;
  });
  return groupBy(newNotices, "type");
};

const getUnreadData = (noticeData: Record<string, API.NoticeIconItem[]>) => {
  const unreadMsg: Record<string, number> = {};
  Object.keys(noticeData).forEach(key => {
    const value = noticeData[key];

    if (!unreadMsg[key]) {
      unreadMsg[key] = 0;
    }

    if (Array.isArray(value)) {
      unreadMsg[key] = value.filter(item => !item.read).length;
    }
  });
  return unreadMsg;
};

const NoticeIconView = () => {
  const { initialState } = useModel("@@initialState");
  const { currentUser } = initialState || {};
  const [notices, setNotices] = useState<API.NoticeIconItem[]>([]);
  // const { data } = useRequest(getNotices);
  const [messageList, setMessageList] = useState([]);
  const [backlog, setBacklog] = useState([]);
  const { messageCount, backlogCount } = useModel("messageCount");

  // useEffect(() => {
  //   setNotices(data || []);
  // }, [data]);

  // useEffect(() => {
  //   getMessageList();
  // }, [messageCount]);
  // useEffect(() => {
  //   getProcessingFlowBizList();
  // }, [backlogCount]);

  // api 消息列表
  const getMessageList = async () => {
    const res = await apiMessageList();
    if (res.code === 20000) {
      setMessageList(res?.data?.records?.slice(0, 5));
    } else {
      message.error(res.msg);
    }
  };
  // api 待办列表
  const getProcessingFlowBizList = async () => {
    const res = await apiProcessingFlowBizList();
    if (res.code === 20000) {
      setBacklog(res?.data?.records?.slice(0, 5));
    } else {
      message.error(res.msg);
    }
  };

  const noticeData = getNoticeData(notices);
  const unreadMsg = getUnreadData(noticeData || {});

  // const changeReadState = (id: string) => {
  //   setNotices(
  //     notices.map((item) => {
  //       const notice = { ...item };
  //       if (notice.id === id) {
  //         notice.read = true;
  //       }
  //       return notice;
  //     }),
  //   );
  // };

  const clearReadState = (title: string, key: string) => {
    setNotices(
      notices.map(item => {
        const notice = { ...item };
        if (notice.type === key) {
          notice.read = true;
        }
        return notice;
      })
    );
    message.success(`${"清空了"} ${title}`);
  };

  return (
    <NoticeIcon
      className={styles.action}
      count={currentUser && currentUser.unreadCount}
      // onItemClick={(item) => {
      //   changeReadState(item.id!);
      // }}
      onClear={(title: string, key: string) => clearReadState(title, key)}
      loading={false}
      clearText="清空"
      viewMoreText="查看更多"
      onViewMore={() => history.push("/messageCenter")}
      clearClose
    >
      <NoticeIcon.Tab
        tabKey="message"
        count={messageCount}
        list={messageList}
        title="消息"
        emptyText="您已读完所有消息"
        showViewMore
      />
      <NoticeIcon.Tab
        tabKey="event"
        title="待办"
        emptyText="你已完成所有待办"
        count={backlogCount}
        list={backlog}
        showViewMore
      />
    </NoticeIcon>
  );
};

export default NoticeIconView;
