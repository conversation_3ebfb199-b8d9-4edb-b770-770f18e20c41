/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-08-21 10:31:14
 * @LastEditTime: 2023-08-29 10:41:52
 * @LastEditors: zhangfengfei
 */
import PrefixTitle from "@/common/components/PrefixTitle";
import { ScenicServiceType, ServiceGrade } from "@/common/utils/enum";
import { getHashParams, markdownToHtml } from "@/common/utils/tool";
import { getEnv } from "@/common/utils/getEnv";
import useModal from "@/hooks/useModal";
import { getScenicInfoById } from "@/services/api/erp";
import type { ProDescriptionsProps } from "@ant-design/pro-descriptions";
import ProDescriptions from "@ant-design/pro-descriptions";
import { Button, Card, Divider, Image, Space } from "antd";
import type { FC } from "react";
import { useEffect } from "react";
import { useModel, useRequest } from "@umijs/max";
import Map from "./Map";
import ScenicInfoEditModal from "./ScenicInfoEditModal";

interface InformationProps {}

const Information: FC<InformationProps> = props => {
  const { initialState } = useModel("@@initialState");
  const { scenicId = "" } = initialState?.scenicInfo || {};
  const queryParams = getHashParams();
  const { IMG_HOST } = getEnv();
  const onClickEdit = () => {
    editModalState.setVisible(true);
  };

  const getScenicInfoReq = useRequest(getScenicInfoById, {
    manual: true,
    initialData: {},
    onSuccess: () => {
      if (queryParams?.type == "edit") {
        onClickEdit();
      }
    }
  });
  console.log(props);
  const { scenic, scenicBusiness, scenicAddress, scenicAttribute } = getScenicInfoReq.data || {};

  const editModalState = useModal();

  const cardData: ProDescriptionsProps[] = [
    {
      title: "基础信息",
      column: { xs: 1, sm: 2, xl: 3, xxl: 4 },
      dataSource: { ...scenic, ...scenicBusiness },
      columns: [
        {
          title: "景区名称",
          dataIndex: "name"
        },
        {
          title: "景区别名",
          dataIndex: "alias"
        },
        {
          title: "服务类型",
          dataIndex: "type",
          valueEnum: ScenicServiceType
        },
        {
          title: "景区评级",
          dataIndex: "grade",
          valueEnum: ServiceGrade
        },
        {
          title: "营业时间",
          dataIndex: "businessStartTime",
          renderText: (_, { businessStartTime, businessEndTime }) => {
            if (businessStartTime || businessEndTime) {
              return `${businessStartTime}-
            ${businessEndTime}`;
            }
            return "-";
          }
        },
        {
          title: "联系人",
          dataIndex: "contracts"
        },
        {
          title: "联系方式",
          dataIndex: "contractsPhone"
        },
        {
          title: "邮箱",
          dataIndex: "email"
        },
        {
          title: "全景链接",
          dataIndex: "panoramaUrl"
        },

        {
          title: "景区 logo",
          dataIndex: "scenicLogo",
          renderText: text => {
            if (text) {
              return <Image style={{ objectFit: "contain" }} src={IMG_HOST + text} alt="logo" width={80} height={80} />;
            }
            return "-";
          }
        },
        {
          span: 3,
          title: "景区图片",
          dataIndex: "picture",
          renderText: (text: string) => {
            if (text) {
              const imgList = (text || "").split(",");
              return (
                <Space style={{ flexWrap: "wrap" }}>
                  {imgList.map(item => (
                    <Image
                      style={{
                        border: "1px solid rgba(0,0,0,0.15)",
                        borderRadius: "2px",
                        padding: "5px",
                        objectFit: "cover"
                      }}
                      key={item}
                      src={IMG_HOST + item}
                      alt="景区图片"
                      width={80}
                      height={80}
                    />
                  ))}
                </Space>
              );
            }
            return "-";
          }
        },
        {
          span: 4,
          title: "景区信息",
          dataIndex: "remark",
          contentStyle: {
            maxWidth: "calc(100% - 75px)"
          },
          renderText(text, record, index, action) {
            if (!text) {
              return "-";
            }

            return (
              <div
                style={{
                  maxHeight: "200px",
                  overflow: "auto"
                }}
                dangerouslySetInnerHTML={{
                  __html: markdownToHtml(text)
                }}
              />
            );
          }
        }
      ]
    },
    {
      title: "地理位置",
      dataSource: scenicAddress,
      column: { xs: 1, sm: 2, xl: 3, xxl: 4 },
      columns: [
        {
          title: "省市区",
          dataIndex: "address",
          renderText: (_, { provinceName, cityName, areaName }) => {
            return provinceName + cityName + areaName || "-";
          }
        },
        {
          title: "详细地址",
          dataIndex: "address"
        },
        {
          title: "纬度",
          dataIndex: "latitude"
        },
        {
          title: "经度",
          dataIndex: "longitude"
        },
        {
          span: 4,
          title: "景区范围",
          dataIndex: "scenicRange",
          renderText: (text: any, record: any) => text && <Map {...record} />
        }
      ]
    },
    {
      title: "景区属性",
      column: { xs: 1, sm: 2, xl: 3, xxl: 4 },
      dataSource: scenicAttribute,
      columns: [
        {
          title: "唯一标志符",
          dataIndex: "uniqueIdentity"
        },
        {
          title: "是否启用区块链",
          dataIndex: "isBlockChain",
          valueEnum: {
            0: "禁用",
            1: "启用"
          }
        }
      ]
    }
  ];

  useEffect(() => {
    getScenicInfoReq.run({
      id: scenicId
    });
  }, []);

  return (
    <>
      {!editModalState.visible ? (
        <div className="relative">
          <Card>
            {cardData.map(({ title, ...rest }, index) => {
              return (
                <>
                  <ProDescriptions key={index} title={title && <PrefixTitle>{title}</PrefixTitle>} {...rest} />
                  {index < cardData.length - 1 && <Divider />}
                </>
              );
            })}
          </Card>
          <div
            className="flex w-100 justify-content-center align-items-center"
            style={{
              position: "sticky",
              height: 72,
              backgroundColor: "white",
              bottom: 0,
              zIndex: 2,
              boxShadow: "0px -2px 9px -1px rgba(208,208,208,0.5)"
            }}
          >
            <Button type="primary" onClick={() => onClickEdit()} key="2">
              编辑
            </Button>
          </div>
        </div>
      ) : (
        // {/* 编辑弹窗 */}
        <ScenicInfoEditModal
          modalState={editModalState}
          scenicData={getScenicInfoReq.data || {}}
          onSuccess={() => {
            location.reload();
            // getScenicInfoReq.refresh()
            // refresh()
          }}
        />
      )}
    </>
  );
};

export default Information;
