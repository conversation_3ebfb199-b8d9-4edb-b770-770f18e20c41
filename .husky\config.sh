#!/bin/bash

eval "$(curl -sSL -H "PRIVATE-TOKEN:jaPspoXXZHseuan3KoqS" \
  "https://git.shukeyun.com/api/v4/projects/650/repository/files/common-tools%2F.husky%2Fconfig.sh/raw?ref=frontend-common")"

# 工时填写Gitlab项目路径（按顺序查找,找到一个就算校验通过）
PROJECT_PATHS=(
  "research-and-development/scenic/3-yilvtong"
  "research-and-development/scenic/2-new-paradigm"
)

export PROJECT_PATHS_STR=$(printf "%s\n" "${PROJECT_PATHS[@]}")
