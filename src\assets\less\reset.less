/* 样式重置及公共样式 */

.clearfix::after {
  display: block;
  clear: both;
  height: 0;
  overflow: hidden;
  visibility: hidden;
  content: '.';
}

li {
  list-style: none;
}

body,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
p,
blockquote,
dl,
dt,
dd,
ul,
ol,
li,
pre,
form,
fieldset,
legend,
button,
input,
textarea,
th,
td {
  margin: 0;
  padding: 0;
}

body,
button,
input,
select,
textarea {
  -ms-overflow-style: scrollbar;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: 100%;
}

ul,
ol {
  list-style: none;
}

a {
  text-decoration: none;
  cursor: pointer;
}

img {
  border: 0;
}

button,
input,
select,
textarea {
  font-size: 100%;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
