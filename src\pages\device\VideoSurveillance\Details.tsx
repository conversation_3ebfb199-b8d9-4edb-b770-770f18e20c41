import ProDescriptions from "@ant-design/pro-descriptions";
import ProForm from "@ant-design/pro-form";
import { Image } from "antd";
import { useEffect, useState } from "react";
import { useModel } from "@umijs/max";
import { getEnv } from "@/common/utils/getEnv";

export default function Details(props: any) {
  const [formObj] = ProForm.useForm();
  const { detailData } = props;
  const [imgs, setImgs] = useState([]);
  // 获取景区 ID
  const { initialState }: any = useModel("@@initialState");
  const scenicId = initialState?.scenicInfo?.scenicId;
  console.log(scenicId, initialState);
  //获取景区名称
  const scenicName = initialState?.scenicInfo?.scenicName;
  console.log("景区名称", scenicName);
  const columns = [
    {
      title: "所属景区",
      dataIndex: "scenicName",
      name: "scenicName",
      initialValue: `${scenicName}`,
      key: "scenicName",
      render: () => {
        return scenicName;
      },
      // valueEnum: {
      //   '0': '公告',
      // },
      formItemProps: {
        // rules: [{ required: true }],
        // disable: false
      },
      fieldProps: {
        disabled: true
      }
    },
    {
      dataIndex: "videoName",
      title: "监控点名称",
      name: "videoName",
      key: "videoName",
      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      dataIndex: "loginName",
      title: "登录账号",
      name: "loginName",
      key: "loginName",
      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      dataIndex: "loginPassword",
      title: "登录密码",
      name: "loginPassword",
      key: "loginPassword",
      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      dataIndex: "longitude",
      title: "经度",
      name: "longitude",
      key: "longitude",
      render: (dom, record) => {
        const str = record.longitude.charAt(0);
        if (str == "+" || str == "-") {
          if (str == "+") {
            return <span>{record.longitude.slice(1)}° E </span>;
          } else if (str == "-") {
            return <span>{record.longitude.slice(1)}° W</span>;
          }
        } else {
          return <span>{record.longitude}° E</span>;
        }
      },
      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      dataIndex: "latitude",
      title: "纬度",
      name: "latitude",
      key: "latitude",
      render: (dom, record) => {
        const str = record.latitude.charAt(0);
        if (str == "+" || str == "-") {
          if (str == "+") {
            return <span>{record.latitude.slice(1)}° N</span>;
          } else if (str == "-") {
            return <span>{record.latitude.slice(1)}° S</span>;
          }
        } else {
          return <span>{record.latitude}° N</span>;
        }
      },
      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      title: "品牌",
      dataIndex: "brand",
      name: "brand",
      valueEnum: {
        "0": "雄迈",
        "1": "星望",
        "2": "宇视",
        "3": "天地伟业",
        "4": "景阳",
        "5": "联通",
        "6": "捷高",
        "7": "华迈",
        "8": "海康云平台",
        "9": "海康",
        "10": "电信全球眼副本",
        "11": "flash 格式",
        "12": "烽火",
        "13": "电信监控",
        "14": "大华云平台",
        "15": "大华",
        "16": "奥利克斯"
      },
      key: "brand",
      formItemProps: {
        rules: [{ required: true }]
      },
      valueType: "select",
      fieldProps: {
        // mode: 'multiple',
        // options: positionValue2 == 1 ? scenicData2 : companyData2,
        // onChange: (value, option) => {
        //   console.log(value);
        //   setAcceptorData(value);
        // },
        // showArrow: true,
        // disabled: flag ? false : show ? false : true,
      }
    },
    {
      dataIndex: "linkAddress",
      title: "链接地址",
      name: "linkAddress",
      key: "linkAddress",
      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      dataIndex: "largeM3u8Address",
      title: "大码率 M3U8 播放地址",
      name: "largeM3u8Address",
      key: "largeM3u8Address",
      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },

    {
      dataIndex: "littleM3u8Address",
      title: "小码率 M3U8 播放地址",
      name: "littleM3u8Address",
      key: "littleM3u8Address",
      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      dataIndex: "videoPoint",
      title: "管理端口",
      name: "videoPoint",
      key: "videoPoint",
      fieldProps: {}
    },
    {
      dataIndex: "channel",
      title: "通道号",
      name: "channel",
      key: "channel",
      fieldProps: {}
      // formItemProps: {
      //   rules: [{ required: true }],
      // },
    },
    {
      dataIndex: "videoAddress",
      title: "视频服务地址",
      name: "videoAddress",
      key: "videoAddress",
      fieldProps: {}
      // formItemProps: {
      //   rules: [{ required: true }],
      // },
    },
    {
      dataIndex: "serial",
      title: "序列号",
      name: "serial",
      key: "serial",
      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      dataIndex: "videoPicture2",
      title: "监控图片列表",
      render: () => {
        return (
          <>
            {detailData.videoPicture !== "" && detailData.videoPicture !== "null" ? (
              <>
                {detailData.videoPicture.split(",").map((item: any, index: any) => {
                  return <Image width={50} src={getEnv().UPLOAD_HOST + item} key={index} />;
                })}
              </>
            ) : (
              "-"
            )}
          </>
        );
      }
    }
  ];
  const data: any = [];
  useEffect(() => {}, []);
  return (
    <ProDescriptions title="基础信息" dataSource={detailData} columns={columns} column={2}>
      {/* <ProDescriptions.Item label='大图' >
                <Upload
                    action="https://www.mocky.io/v2/5cc8019d300000980a055e76"
                    listType="picture-card"
                // fileList={fileList}
                ></Upload>
            </ProDescriptions.Item> */}
    </ProDescriptions>
  );
}
