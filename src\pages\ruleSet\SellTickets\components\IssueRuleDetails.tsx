import DetailsPop from "@/common/components/DetailsPop";
import { whetherEnum } from "@/common/utils/enum";
import { apiApproveConfInfo, downListApi, ticketLssueXq } from "@/services/api/ticket";
import { useEffect, useState } from "react";
import { createRoot } from "react-dom/client";
import { useRequest } from "@umijs/max";

const IssueRuleDetails = ({ id }: any) => {
  const [isLoading, setLoading] = useState<boolean>(true);
  const [dataSource, setDataSource] = useState<any>({ id: "", isEnable: 0 });
  const [detailsVisible, setDetailsVisible] = useState<boolean>(false);
  const downListReq = useRequest(downListApi, {
    manual: true,
    formatResult: res => res
  });

  const columnsInitial = [
    {
      title: "基础信息",
      columns: [
        {
          title: "旅游服务名称",
          dataIndex: "scenicName"
          // render: () => scenicName,
        },
        {
          title: "出票规则名称",
          dataIndex: "name"
        },
        {
          title: "出票类型",
          dataIndex: "type",
          render: (dom: number) => ["一票一人", "一票多人"][dom]
        }
      ]
    },
    {
      title: "时间信息",
      columns: [
        {
          title: "使用时间",
          dataIndex: "useTime",
          render: (dom: any) => `购买 ${dom} 小时后`
        },
        {
          title: "当天购票起止时间",
          dataIndex: "beginTime",
          render: (dom: any, entity: any) => entity.beginTime + " - " + entity.endTime
        }
      ]
    },
    {
      title: "实名信息",
      columns: [
        {
          title: "是否需校验实名制",
          dataIndex: "realName",
          valueEnum: whetherEnum
        },
        {
          title: "是否仅限本人购买",
          dataIndex: "buyOwner",
          valueEnum: whetherEnum
        },
        {
          title: "实名方式",
          dataIndex: "isRealName",
          render: (dom: any) => ["非实名", "身份证"][dom]
        },
        {
          title: "是否需校验权益",
          dataIndex: "isRights",
          valueEnum: whetherEnum
        },
        {
          title: "已选权益",
          dataIndex: "rightsId",
          renderText: (text: any, record: any) => {
            if (record.isRights == 0) {
              return "-";
            }
            return (downListReq.data ?? []).find((item: any) => item.value === text)?.label ?? "-";
          }
        },
        {
          title: "是否出票即核销",
          dataIndex: "isCheck",
          valueEnum: whetherEnum
        },
        {
          title: "仅窗口售票",
          dataIndex: "isWindow",
          valueEnum: {
            0: "-",
            1: "是",
            2: "否"
          }
          // hideInDescriptions: dataSource.type === 0,
        },
        {
          title: "购买是否需审批",
          dataIndex: "isApprove",
          valueEnum: whetherEnum
        },
        {
          title: "上传审批内容规范说明",
          dataIndex: "approveContent"
        }
      ]
    },
    {
      title: "说明信息",
      columns: [
        {
          title: "备注",
          dataIndex: "remark"
        }
      ]
    }
  ];
  const init = async (e: string) => {
    setLoading(true);
    setDetailsVisible(true);
    try {
      const { data } = await ticketLssueXq({ id: e });
      data.way *= 1;
      data.type *= 1;
      data.isRealName *= 1;
      let approveData: any = {};
      if (data.approveId && data.approveId != 0) {
        const { data: approveData2 } = await apiApproveConfInfo({
          approveId: data.approveId
        });
        approveData = approveData2;
        approveData.approveType += "";
      }
      setDataSource({ ...data, ...approveData });
      setLoading(false);
    } catch (error) {}
  };
  useEffect(() => {
    init(id);
  }, [id]);

  useEffect(() => {
    downListReq.run();
  }, []);
  return (
    <DetailsPop
      title="出票规则详情"
      visible={detailsVisible}
      isLoading={isLoading}
      setVisible={setDetailsVisible}
      columnsInitial={columnsInitial}
      dataSource={dataSource}
    />
  );
};

IssueRuleDetails.show = (id: string) => {
  const detailBox = document.createElement("div");
  document.body.appendChild(detailBox);
  createRoot(detailBox).render(<IssueRuleDetails id={id} />);
};

export default IssueRuleDetails;
