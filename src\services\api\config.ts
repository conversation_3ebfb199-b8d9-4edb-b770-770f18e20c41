/**
 * 景区配置接口
 */
import { documentHost, scenicHost } from "@/services/api";
import { request } from "@umijs/max";
// 获取列表头部信息
export function apiExportHeaderQuery(params: any) {
  return request(`${scenicHost}/export/header/query`, { method: "GET", params });
}
// 设置列表头部信息
export function apiExportHeaderSet(data: any) {
  return request(`${scenicHost}/export/header/set`, { method: "POST", data });
}

// 报表下载列表
export async function apiCustomDownloadPage(data: any) {
  const res = await request(`${documentHost}/file/custom/download/page`, { method: "POST", data });
  return res.data;
}
// 报表导出
export function apiCustomDownload(data: any) {
  return request(`${documentHost}/file/custom/download`, { method: "POST", data });
}
// 表头设置
export function apiCustomHeaderSet(data: any) {
  return request(`${documentHost}/file/custom/export/header/set`, { method: "POST", data });
}
// 获取表头
export function apiCustomHeaderGet(params: any) {
  return request(`${documentHost}/file/custom/export/header/query`, { method: "GET", params });
}
