import ProModal from "@/common/components/ProModal";
import type { ProModalState } from "@/common/components/ProModal/useModal";
import { GuideStepStatus } from "@/common/utils/enum";
import { modelWidth } from "@/common/utils/gConfig";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import { markdownToHtml, removeStateFromUrl } from "@/common/utils/tool";
import MDEditor from "@/components/MDEditor";
import { useGuide } from "@/hooks/useGuide";
import useModalState from "@/hooks/useModal";
import { getRightsDownList } from "@/services/api/rightsManage";
import { apiCoInfoOption } from "@/services/api/ticket";
import { addRights, addRightsCard, editRightsCard, getRightsCardInfo } from "@/services/api/travelCard";
import type { ProFormColumnsType } from "@ant-design/pro-components";
import {
  ModalForm,
  ProForm,
  ProFormDependency,
  ProFormDigit,
  ProFormFieldSet,
  ProFormRadio,
  ProFormSelect,
  ProFormText
} from "@ant-design/pro-components";
import type { ActionType } from "@ant-design/pro-table";
import { useModel } from "@umijs/max";
import { InputNumber, Space } from "antd";
import { useForm } from "antd/lib/form/Form";
import { useState, type FC } from "react";
import RuleSelectModal from "../../../../ticket/TicketType/components/RuleSelectModal";
import styles from "./index.module.less";

type TravelCardModalProps = {
  travelCardItem?: API.TravelCardListItem;
  actionRef?: React.MutableRefObject<ActionType | undefined>;
  modalState: ProModalState;
};

const TravelCardModal: FC<TravelCardModalProps> = ({ modalState, actionRef, travelCardItem }) => {
  const { id } = travelCardItem || {};
  const { initialState } = useModel("@@initialState");
  const { updateGuideInfo } = useGuide();
  const selectModalState = useModalState();

  const [form] = useForm();

  const { scenicId = "", scenicName, scenicType = 0 } = initialState?.scenicInfo || {};
  const { coId = "" } = initialState?.currentCompanyInfo || {};

  const [visible, setVisible] = useState(false);

  const [issueRuleItem, setIssueRuleItem] = useState<Record<string, any>>();

  const [options, setOptions] = useState([]);

  const [rightsId, setRightsId] = useState("");

  const getRightsDownListReq = async () => {
    const { data = [] } = await getRightsDownList();
    const result = data.map(i => ({
      label: i.rightsName,
      value: i.rightsId
    }));

    setOptions(result);
    return result;
  };

  const columns: ProFormColumnsType<API.TravelCardListItem>[] = [
    {
      title: "基本信息",
      valueType: "group",
      columns: [
        {
          title: "发卡方",
          dataIndex: "scenicId",
          valueType: "select",
          valueEnum: { [scenicId]: scenicName },
          initialValue: scenicId,
          fieldProps: { disabled: true }
        },
        {
          title: "所属服务商",
          dataIndex: "operatorId",
          valueType: "select",
          initialValue: coId,
          params: { scenicId },
          request: apiCoInfoOption,
          fieldProps: {
            allowClear: false,
            disabled: true
          },
          formItemProps: { rules: [{ required: true }] }
        },

        {
          title: "权益卡名称",
          dataIndex: "travelCardName",
          fieldProps: form => {
            return {
              onChange: (e: any) => {
                // C 端显示名称默认为产品名称
                form.setFieldsValue({ htmlName: e.target.value });
              }
            };
          },
          formItemProps: { rules: [{ required: true, max: 30 }] }
        },
        {
          title: "C 端显示名称",
          dataIndex: "htmlName",
          formItemProps: { rules: [{ required: true, max: 30 }] }
        },
        {
          title: <span className={modalState.type === "info" ? "" : "required"}>权益卡有效期</span>,
          key: "eff",
          renderFormItem: (schema, config, form, action) => {
            return (
              <div className="flex">
                <ProFormDigit
                  colProps={{ span: 16 }}
                  name="effectiveTime"
                  fieldProps={{
                    min: 1,
                    precision: 0
                  }}
                  rules={[{ required: true, message: "请输入" }]}
                />
                <ProFormSelect
                  colProps={{ span: 6 }}
                  name="effectiveTimeUnit"
                  allowClear={false}
                  initialValue={"年"}
                  options={["年", "月", "日"]}
                  rules={[{ required: true, message: "请输入" }]}
                />
              </div>
            );
          },
          renderText(text, { effectiveTime, effectiveTimeUnit }, index, action) {
            return effectiveTime + " " + effectiveTimeUnit;
          }
        },
        {
          key: "renew",
          title: <span className={modalState.type === "info" ? "" : "required"}>续卡期限</span>,
          renderFormItem: () => {
            return (
              <Space className="flex align-items-center">
                <span style={{ width: 50 }}>失效后</span>
                <ProFormDigit
                  name="renewEndTime"
                  fieldProps={{
                    min: 1,
                    precision: 0
                  }}
                  rules={[{ required: true, message: "请输入" }]}
                  noStyle
                />
                <ProFormSelect
                  name="renewEndTimeUnit"
                  allowClear={false}
                  initialValue={"年"}
                  options={["年", "月", "日"]}
                  rules={[{ required: true, message: "请输入" }]}
                  noStyle
                />
                <span style={{ width: 50 }}>内可续卡</span>
              </Space>
            );
          },
          renderText(text, { renewEndTime, renewEndTimeUnit }, index, action) {
            return `失效后 ${renewEndTime} ${renewEndTimeUnit}内可续卡`;
          }
        },
        {
          dataIndex: "useFrequent",
          title: <span className={modalState.type === "info" ? "" : "required"}>使用次数</span>,
          renderFormItem(schema, config, form, action) {
            return (
              <ProFormFieldSet>
                <ProFormRadio.Group
                  colProps={{ span: 12 }}
                  name={"useFrequencyType"}
                  options={[
                    {
                      label: "不限",
                      value: 1
                    },
                    {
                      label: "限制次数",
                      value: 2
                    }
                  ]}
                  rules={[{ required: true, message: "请输入" }]}
                />

                <ProFormDependency name={["useFrequencyType"]}>
                  {({ useFrequencyType }) => {
                    if (useFrequencyType == "2") {
                      return (
                        <ProFormDigit
                          name={"useFrequency"}
                          colProps={{ span: 12 }}
                          fieldProps={{
                            min: 0,
                            max: 100,
                            precision: 0
                          }}
                          addonAfter="次"
                          rules={[{ required: true, message: "请输入" }]}
                        />
                      );
                    }
                    return null;
                  }}
                </ProFormDependency>
              </ProFormFieldSet>
            );
          },
          renderText(text, { useFrequencyType, useFrequency }, index, action) {
            if (useFrequencyType == 1) {
              return "不限";
            }

            return `${useFrequency} 次`;
          }
        },
        {
          title: "续卡提示",
          key: "renewTip",

          renderFormItem: () => {
            return (
              <Space className="flex">
                <span>失效前</span>
                <ProFormDigit
                  name="renewTipFrontTime"
                  fieldProps={{
                    min: 0,
                    max: 100,
                    precision: 0,
                    style: {
                      width: 100
                    }
                  }}
                  noStyle
                />
                <span>天，失效后</span>
                <ProFormDigit
                  name="renewTipAfterTime"
                  allowClear={false}
                  addonAfter="天"
                  fieldProps={{
                    style: {
                      width: 100
                    }
                  }}
                  noStyle
                />
                <span>天</span>
              </Space>
            );
          },
          renderText(_, { renewTipFrontTime, renewTipAfterTime }) {
            return `失效前 ${renewTipFrontTime} 天，失效后 ${renewTipAfterTime} 天`;
          }
        }
      ]
    },

    {
      title: "价格信息",
      valueType: "group",
      columns: [
        {
          title: "市场标准价",
          dataIndex: "marketPrice",
          valueType: "digit",
          fieldProps: {
            min: 0
          },
          formItemProps: { rules: [{ required: true }] }
        },
        {
          valueType: "dependency",
          name: ["overallDiscount", "marketPrice"],
          columns: ({ overallDiscount = 0, marketPrice = 0 }) => {
            return [
              {
                title: (
                  <div className="flex justify-content-between w-100 ">
                    <span>商品折扣率（%）</span>
                    {modalState.type !== "info" && (
                      <span className="text-red">
                        {marketPrice.toFixed(2)}元 * {overallDiscount}% ={" "}
                        {(marketPrice * (overallDiscount / 100)).toFixed(2)}元
                      </span>
                    )}
                  </div>
                ),
                dataIndex: "overallDiscount",
                valueType: "digit",
                width: "100%",
                fieldProps: {
                  min: 0,
                  max: 100,
                  precision: 0
                },
                colProps: { xs: 24, sm: 12, xl: 8, xxl: 6 },
                formItemProps: {
                  rules: [
                    {
                      required: true,
                      message: "请输入"
                    }
                  ]
                }
              }
            ];
          },
          renderText(text = 0, { marketPrice = 0 }, index, action) {
            return (
              <div>
                <span>{text}</span>
                <span>
                  （{marketPrice.toFixed(2)}元 * {text}% = {(marketPrice * (text / 100)).toFixed(2)}
                  元）
                </span>
              </div>
            );
          }
        },
        {
          valueType: "dependency",
          name: ["marketPrice", "overallDiscount", "beginDiscount", "endDiscount"],
          columns: ({ marketPrice = 0, overallDiscount = 0, beginDiscount = 0, endDiscount = 0 }) => {
            return [
              {
                title: (
                  <div className="flex justify-content-between w-100 ">
                    <span className={modalState.type === "info" ? "" : "required"}>分销折扣区间（%）</span>
                    {modalState.type !== "info" && (
                      <span className="text-red">
                        {(marketPrice * (overallDiscount / 100) * (beginDiscount / 100)).toFixed(2)}元 ~{" "}
                        {(marketPrice * (overallDiscount / 100) * (endDiscount / 100)).toFixed(2)}元
                      </span>
                    )}
                  </div>
                ),
                colProps: { xs: 24, sm: 12, xl: 8, xxl: 6 },
                renderFormItem: () => (
                  <div style={{ display: "flex" }}>
                    <div style={{ width: "calc(50% - 5px)" }}>
                      <ProForm.Item
                        name="beginDiscount"
                        rules={[{ required: true, message: "请输入" }]}
                        initialValue={100}
                      >
                        <InputNumber min={0} style={{ width: "100%" }} max={100} />
                      </ProForm.Item>
                    </div>
                    <span style={{ height: "32px", lineHeight: "32px", padding: "0 8px" }}>~</span>
                    <div style={{ width: "calc(50% - 5px)" }}>
                      <ProForm.Item
                        name="endDiscount"
                        rules={[{ required: true, message: "请输入" }]}
                        initialValue={100}
                      >
                        <InputNumber min={0} max={100} style={{ width: "100%" }} />
                      </ProForm.Item>
                    </div>
                  </div>
                ),
                renderText(text = 0, { marketPrice = 0 }, index, action) {
                  return (
                    <div>
                      <span>{text}</span>
                      <span>
                        （{marketPrice.toFixed(2)}元 * {text}% = {(marketPrice * (text / 100)).toFixed(2)}
                        元）
                      </span>
                    </div>
                  );
                }
              }
            ];
          }
        }
      ]
    },

    {
      title: "规则信息",
      columns: [
        {
          title: (
            <div className="flex w-100 justify-content-between">
              <span> 选择权益</span>
              {modalState.type !== "info" && <a onClick={() => setVisible(true)}>新增</a>}
            </div>
          ),

          valueType: "select",
          dataIndex: "rightsId",
          fieldProps: {
            showSearch: true
          },
          params: { rightsId },
          request: getRightsDownListReq,
          formItemProps: {
            rules: [
              {
                required: true,
                message: "请选择权益"
              }
            ]
          }
        },
        {
          title: "出卡规则",
          valueType: "select",
          dataIndex: "issueId",
          fieldProps: {
            open: false,
            onClick: () => {
              selectModalState.setTypeWithVisible("add");
            },
            options: [{ label: issueRuleItem?.name, value: issueRuleItem?.id }]
          },
          formItemProps: {
            rules: [
              {
                required: true
              }
            ]
          }
        }
      ]
    },

    {
      // valueType: "group",
      // title: "说明信息",
      // columns: [
      //   {
      //     colProps: { span: 24 },
      //     title: "购卡须知",
      //     dataIndex: "notices",
      //     valueType: "textarea",
      //     fieldProps: {
      //       showCount: true,
      //       maxLength: 1000,
      //       style: {
      //         height: 100
      //       }
      //     }
      //   }
      // ]
      title: "说明信息",
      valueType: "group",
      columns: [
        {
          colProps: { span: 24 },
          dataIndex: "notices",
          renderFormItem: () => (
            <ProForm.Item
              name="notices"
              label="购卡须知"
              rules={[
                {
                  type: "string",
                  max: 2000
                }
              ]}
            >
              <MDEditor />
            </ProForm.Item>
          ),
          renderText(text, record, index, action) {
            if (!text) {
              return "-";
            }

            return (
              <div
                style={{
                  maxHeight: "200px",
                  width: "100%",
                  overflow: "auto"
                }}
                dangerouslySetInnerHTML={{
                  __html: markdownToHtml(text)
                }}
              />
            );
          }
        }
      ]
    }
  ];

  const [infoData, setInfoData] = useState<Record<string, any>>();

  const logList = [
    {
      title: "权益卡名称",
      dataIndex: "travelCardName"
    },
    {
      title: "C 端显示名称",
      dataIndex: "htmlName"
    },
    {
      title: "权益卡有效期",
      dataIndex: "eff",
      renderText(text, { effectiveTime, effectiveTimeUnit }) {
        return effectiveTime + " " + effectiveTimeUnit;
      }
    },
    {
      title: "续卡期限",
      dataIndex: "renew",
      renderText(text, { renewEndTime, renewEndTimeUnit }) {
        return `失效后 ${renewEndTime} ${renewEndTimeUnit}内可续卡`;
      }
    },
    {
      title: "使用次数",
      dataIndex: "useFrequent",
      renderText(text, { useFrequencyType, useFrequency }) {
        if (useFrequencyType == 1) {
          return "不限";
        }

        return `${useFrequency} 次`;
      }
    },
    {
      title: "续卡提示",
      dataIndex: "renewTip",
      renderText(_, { renewTipFrontTime, renewTipAfterTime }) {
        return `失效前 ${renewTipFrontTime} 天，失效后 ${renewTipAfterTime} 天`;
      }
    },
    {
      title: "市场标准价",
      dataIndex: "marketPrice"
    },
    {
      title: "商品折扣率（%）",
      dataIndex: "overallDiscount"
    },
    {
      title: "分销折扣区间（%）",
      dataIndex: "discount",
      renderText(_, { beginDiscount, endDiscount }) {
        return `${beginDiscount} ~ ${endDiscount}`;
      }
    },

    {
      title: "选择权益",
      dataIndex: "rightsName"
    },
    {
      title: "出卡规则",
      dataIndex: "issueName"
    }
  ];

  // 详情
  const getTravelCardInfo = async () => {
    if (id) {
      const { data } = await getRightsCardInfo({
        id
      });
      const { travelGoodsVO = {}, travelCardVO } = data;
      addOperationLogRequest({
        action: "info",
        content: `查看【${travelGoodsVO.goodsName}】权益卡详情`
      });
      setIssueRuleItem({
        id: travelGoodsVO.issueId,
        name: travelGoodsVO.issueName
      });
      const result = {
        ...travelCardVO,
        ...travelGoodsVO
      };

      setInfoData(result);

      return {
        data: result
      };
    }
    return { data: {} };
  };

  // 编辑
  const editTravelCard = async (val: Record<string, any>) => {
    const { htmlName, marketPrice, travelCardName, ...rest } = val;

    const params = {
      travelCardEditVO: {
        marketPrice,
        travelCardName,
        htmlName
      },
      travelGoodsEditVO: rest
    };
    const res = await editRightsCard(params);

    addOperationLogRequest({
      action: "edit",
      changeConfig: {
        list: logList,
        beforeData: {
          ...infoData,
          rightsName: options.find(i => i.value === infoData?.rightsId)?.label
        },
        afterData: {
          ...val,
          rightsName: options.find(i => i.value === val?.rightsId)?.label,
          issueName: issueRuleItem?.name
        }
      },
      content: `编辑【${val.travelCardName}】权益卡`
    });

    return res;
  };

  const onRuleSelect = (val: Record<string, any>) => {
    form?.setFieldsValue({
      issueId: val.id,
      issueName: val.name
    });
    setIssueRuleItem(val);
  };

  return (
    <>
      <div className={styles.form}>
        <ProModal
          page
          {...modalState}
          title="权益卡"
          actionRef={actionRef}
          columns={columns}
          params={{ id, scenicId }}
          formInstance={form}
          infoRequest={getTravelCardInfo}
          onCancel={() => {
            history.pushState(null, null, removeStateFromUrl("type"));
          }}
          addRequest={async (params: any) => {
            const data = await addRightsCard(params);
            // 更新引导
            updateGuideInfo({ tabIndex: 1, status: GuideStepStatus.step1_1 });
            history.pushState(null, null, removeStateFromUrl("type"));
            addOperationLogRequest({
              action: "add",
              content: `新增【${params.travelCardName}】权益卡`
            });

            return data;
          }}
          editRequest={editTravelCard}
          // onFinish={(val) => {
          //   console.log(val);
          //   return false;
          // }}
        />
      </div>
      <ModalForm
        title="新增权益"
        open={visible}
        width={modelWidth.md}
        layout="vertical"
        onOpenChange={setVisible}
        onFinish={async values => {
          const {
            data: { id }
          } = await addRights({
            rightsName: values.rightsName,
            scenicId,
            scenicType
          });

          form?.setFieldsValue({
            rightsId: id
          });
          setRightsId(id);

          return true;
        }}
        modalProps={{
          destroyOnClose: true,
          onCancel(e) {
            setVisible(false);
          }
        }}
      >
        <ProFormText
          label="权益名称"
          name={"rightsName"}
          width={"md"}
          formItemProps={{
            rules: [
              {
                required: true
              }
            ]
          }}
        />
      </ModalForm>

      <RuleSelectModal modalState={selectModalState} onSelect={onRuleSelect} isTCard />
    </>
  );
};

export default TravelCardModal;
