import { Switch, message } from 'antd';
import { useState } from 'react';

export default (
  {
    access,
    value,
    params,
    request,
  }: {
    access: boolean;
    value: boolean;
    params: object;
    request: Function;
  } = {
    access: true,
    value: false,
    params: {},
    request: () => {},
  },
) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [checked, setChecked] = useState<boolean>(value);
  return (
    <Switch
      disabled={!access}
      checkedChildren="启用"
      unCheckedChildren="禁用"
      checked={checked}
      loading={loading}
      onChange={() => {
        setLoading(true);
        request(params)
          .then(() => {
            message.success(checked ? '已禁用' : '已启用');
            setLoading(false);
            setChecked(!checked);
          })
          .catch(() => {
            setLoading(false);
          });
      }}
    />
  );
};
