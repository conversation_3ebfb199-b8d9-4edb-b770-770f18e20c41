/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-04-27 14:55:32
 * @LastEditTime: 2022-08-24 15:07:05
 * @LastEditors: zhang<PERSON><PERSON>i
 */
import { request } from "@umijs/max";
import { scenicHost } from ".";
// 权益下拉列表
export async function getRightsDownList() {
  return request<ResponseData<API.RightsDownListItem[]>>(`${scenicHost}/rightsService/rights/downList`);
}

/** 查询权益列表 */
export async function getRightsList(params: API.RightsListParams, options: Record<string, any> = {}) {
  return request<ResponseListData<API.RightsListItem[]>>(`${scenicHost}/rightsService/rights/pageList`, {
    method: "GET",
    params,
    ...options
  });
}

/** 禁用/启用权益 */
export async function switchRights(
  params: Pick<API.RightsListItem, "id" | "isEnable">,
  options: Record<string, any> = {}
) {
  return request<ResponseData<null>>(`${scenicHost}/rightsService/privilege/disable`, {
    method: "PUT",
    data: params,
    ...options
  });
}
/** 特权用户列表 */
export async function getPrivilegeUserList(params: API.PrivilegeUserListParams, options: Record<string, any> = {}) {
  return request<ResponseListData<API.PrivilegeUserListItem[]>>(`${scenicHost}/rightsService/privilege/user/pageList`, {
    method: "GET",
    params,
    ...options
  });
}
/** 特权商品列表 */
export async function getPrivilegeGoodsList(params: API.PrivilegeGoodsListParams, options: Record<string, any> = {}) {
  return request<ResponseListData<API.PrivilegeGoodsListItem[]>>(
    `${scenicHost}/rightsService/privilege/goods/pageList`,
    {
      method: "GET",
      params,
      ...options
    }
  );
}
/** 续费日志列表 */
export async function getRenewalLogList(params: API.RenewalLogListParams, options: Record<string, any> = {}) {
  return request<ResponseData<API.RenewalLogListItem[]>>(`${scenicHost}/rightsService/renewal/pageList/${params.id}`, {
    method: "GET",
    params,
    ...options
  });
}
