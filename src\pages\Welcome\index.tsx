/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-08-15 10:43:48
 * @LastEditTime: 2023-08-24 14:49:23
 * @LastEditors: zhangfeng<PERSON>i
 */
import { addOperationLogRequest } from "@/common/utils/operationLog";
import { getCompanyInfoStore } from "@/common/utils/storage";
import { getUniqueId, jumpPage } from "@/common/utils/tool";
import { getEnv } from "@/common/utils/getEnv";
import ConsumeIcon from "@/components/ConsumeIcon";
import {
  apiGenerateScenicScreen,
  apiMyCoInfoList,
  apiScenicListByRegistration,
  apiUploadEvidence,
  getNoticeList
} from "@/services/api/erp";
import { uploadFile } from "@/services/api/file";
import { getStockWarningTotal } from "@/services/api/ticket";
import { getWorkOrderList } from "@/services/api/workOrder";
import { QuestionCircleOutlined, RightOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Col, Divider, Modal, Row, Space, Tooltip, Typography, message } from "antd";
import html2canvas from "html2canvas";
import dayjs from "dayjs";
import qs from "qs";
import type { FC } from "react";
import { useEffect, useState } from "react";
import { useModel, useRequest } from "@umijs/max";
import { ApprovalStatusEnum } from "../workOrder/common/data";
import Charts from "./components/Charts";
import styles from "./index.less";

const { Title, Paragraph } = Typography;

const responsiveStyle = {
  xs: 24,
  md: 12,
  xxl: 6
};

const queryParams = {
  stockAmount: 100,
  enterEndTime: dayjs().format("YYYY-MM-DD")
};

const Welcome: FC = () => {
  const { initialState } = useModel("@@initialState") || {};
  const { scenicId = "", scenicName, expiredDate } = initialState?.scenicInfo || {};
  const { coId } = initialState?.currentCompanyInfo || {};
  const { userId } = initialState?.userInfo || {};
  const [screenId, setScreenId] = useState(initialState?.scenicInfo?.screenId);
  const [scenicList, setScenicList] = useState([]);
  const [scenicVisible, setScenicVisible] = useState(false);
  useEffect(() => {
    if (!screenId) {
      apiGenerateScenicScreen(scenicId).then(res => {
        setScreenId(res.data.screenId);
      });
    }
  }, []);

  // 公告
  const noticeListReq = useRequest(getNoticeList, {
    manual: true,
    initialData: [],
    formatResult(res) {
      const nowTime = new Date();
      const result = (res.data?.records || []).filter(
        ({ noticeDisplayBeginTime, noticeDisplayEndTime }) =>
          nowTime > new Date(noticeDisplayBeginTime) && nowTime < new Date(noticeDisplayEndTime)
      );
      return result;
    }
  });

  // 工单
  const workOrderReq = useRequest(getWorkOrderList, {
    manual: true
  });

  // 库存预警
  const ticketStockReq = useRequest(getStockWarningTotal, {
    manual: true
  });

  // 当前绑定的企业列表
  const checkSettlementStatus = useRequest(apiMyCoInfoList, {
    defaultParams: [
      {
        userId: initialState?.userInfo?.userId ?? ""
      }
    ]
  });

  // 景区
  const getScenicListApi = useRequest(apiScenicListByRegistration, {
    manual: true,
    onSuccess: (res: any) => {
      const _scenicList = (res || [])?.map(e => ({
        ...e,
        title: e?.legalName,
        url: `${getEnv().ORGANIZATION_HOST}/${e?.exchangeId}/welcome`
      }));
      setScenicList(_scenicList);
    }
  });

  const cardList = [
    {
      title: "待审批工单",
      icon: "approve",
      tooltip: "待审批工单的数量",
      color: "#1890FF",
      value: workOrderReq.data?.total ?? 0,
      onClick: () => {
        jumpPage.push("/work-order/my");
      }
    },
    {
      title: "库存预警",
      tooltip: "当商品库存数少于 100 时预警（仅限启用状态的库存）",
      icon: "warning",
      color: "#FF4D4F",
      value: ticketStockReq.data ?? 0,
      onClick: () => {
        const params = qs.stringify(queryParams);
        jumpPage.push(`/ticket/stock?${params}`);
      }
    }
  ];

  const linkList = [
    {
      key: "exchange",
      title: {
        text: "电商系统",
        color: "#0050C9"
      },
      subTitle: {
        text: "店铺管理、订单信息、分销管理",
        color: "#0047B2"
      },
      icon: "exchange",
      button: {
        textColor: "#1890FF",
        bgColor: "rgba(24,144,255,0.11)",
        link: getEnv().EXCHANGE_URL,
        onClick: () => {
          addOperationLogRequest({
            action: "link",
            content: "跳转易旅通"
          });
        }
      }
    },
    {
      key: "data",
      title: {
        text: "景区数据大屏",
        color: "#5A18BA"
      },
      subTitle: {
        text: "查看景区实时综合分析数据",
        color: "#6700B2"
      },
      icon: "data",
      button: {
        textColor: "#AA2ED3",
        bgColor: "rgba(104,33,207,0.18)",
        link: getEnv().MAIN_HOST + `data/dashboard/#/home?id=${screenId}`,
        onClick: () => {
          addOperationLogRequest({
            action: "link",
            content: "跳转景区大屏"
          });
        }
      }
    },

    {
      key: "trading",
      title: {
        text: "交易所系统",
        color: "#0077a5"
      },
      subTitle: {
        text: "交易所认证、数字资产发布审核",
        color: "#1e7ba0"
      },
      icon: "trading",
      button: {
        textColor: "#117198",
        bgColor: "rgba(96, 186, 222, 0.17)",
        onClick: () => {
          if (scenicList?.length === 1) {
            addOperationLogRequest({
              action: "link",
              content: "跳转机构后台"
            });
            window.open(scenicList[0].url);
          } else if (scenicList?.length > 1) {
            setScenicVisible(true);
          } else {
            message.info("暂无已入驻交易所！");
          }
        }
      }
    },
    {
      key: "settle",
      title: {
        text: "结算系统",
        color: "#FF721B"
      },
      subTitle: {
        text: "结算交易记录",
        color: "#B25B00"
      },
      icon: "settle",
      button: {
        textColor: "#DE7D22",
        bgColor: "rgba(255,114,27,0.17)",
        link: getEnv().MERCHANTS_URL,
        onClick: () => {
          addOperationLogRequest({
            action: "link",
            content: "跳转结算系统"
          });
        }
      }
    }
  ];
  if (initialState?.scenicInfo?.isBlockChain != 1) {
    //非区块链直接移除掉交易所按钮
    linkList.splice(2, 1);
  }

  // 临期截图存证
  const onSnippingImg = async () => {
    const snippingDate = localStorage.getItem("snippingDate");
    if (expiredDate != -1 && snippingDate !== dayjs().format("YYYY-MM-DD")) {
      const canvas = await html2canvas(document.body);
      canvas.toBlob(async bolb => {
        const file = new File([bolb], "snipping.png", { type: "image/png" });
        const params = [file];
        const url = await uploadFile(params);
        const scenicExpireImg = url[0].path;
        // 上传成功后，存储当前日期
        apiUploadEvidence({
          scenicName,
          scenicId,
          scenicExpireImg
        });
        localStorage.setItem("snippingDate", dayjs().format("YYYY-MM-DD"));
      });
    }
  };

  useEffect(() => {
    workOrderReq.run({
      approvalPlatformId: `${scenicId}/${coId}`,
      approvalStatus: ApprovalStatusEnum.待审批,
      approvalGroup: "backend",
      approvalUserId: userId
    });

    ticketStockReq.run({
      operatorId: [coId],
      scenicId: scenicId,
      selectType: 0,
      ...queryParams
    });

    noticeListReq.run({
      isEnable: 1,
      noticePosition: 1,
      receiveList: scenicId
    });

    if (getCompanyInfoStore()?.coCode) {
      getScenicListApi.run({
        registrationNumber: getCompanyInfoStore()?.coCode
      });
    }

    onSnippingImg();
  }, []);

  return (
    <div className={styles.main}>
      <Card bordered={false}>
        {/* 临期提示 */}
        {expiredDate != -1 && (
          <Alert
            style={{ marginBottom: 20 }}
            message={`您的产品服务将在 ${expiredDate} 天后到期，到期后不续费将对数据进行删除，请及时续费。商务联系方式：40088-11138 或 (+86)755-88328999`}
            type="warning"
            showIcon
            closable
          />
        )}

        {/* 公告 */}
        {noticeListReq.data?.map(({ isClose, noticeId, noticeContent, noticeUrl }) => (
          <Alert
            key={noticeId}
            message="公告"
            description={
              <>
                <div>{noticeContent}</div>
                {noticeUrl && (
                  <div style={{ marginTop: 8 }}>
                    <a
                      onClick={() => {
                        const url = noticeUrl.includes("http") ? noticeUrl : `http://${noticeUrl}`;
                        window.open(url);
                      }}
                    >
                      查看详情
                    </a>
                  </div>
                )}
              </>
            }
            type="info"
            closable={isClose === 1}
            style={{ marginBottom: 20 }}
          />
        ))}

        {/* 路由跳转 */}
        <Row gutter={[20, 20]}>
          {initialState?.scenicInfo?.isBlockChain != 1 ? (
            <Col className={styles.card} {...responsiveStyle}>
              <Space direction="vertical" size={"middle"} style={{ width: "100%" }}>
                {cardList.map(({ icon, onClick, title, color, value, tooltip }) => (
                  <Row key={icon} align="middle" className={styles[icon]}>
                    <Col flex={"24px"} style={{ marginRight: 10, marginLeft: 14 }}>
                      <ConsumeIcon name={icon} style={{ fontSize: 24 }} />
                    </Col>
                    <Col flex={"115px"}>
                      <span className={styles.text}> {title} </span>
                      <Tooltip title={tooltip}>
                        <QuestionCircleOutlined />
                      </Tooltip>
                    </Col>
                    <Col flex={"auto"}>
                      <span style={{ fontSize: 32, color, lineHeight: "32px" }}>{value}</span>
                    </Col>
                    <Col>
                      <Button type="link" onClick={onClick}>
                        <RightOutlined style={{ color, fontSize: 16 }} />
                      </Button>
                    </Col>
                  </Row>
                ))}
              </Space>
            </Col>
          ) : (
            <></>
          )}
          {/* 系统跳转 */}
          {linkList.map(({ key, title, subTitle, icon, button }) => (
            <Col key={key} {...responsiveStyle}>
              <Row className={styles[key]} justify="space-between" align="middle">
                <Col>
                  <Space direction="vertical">
                    <div
                      style={{
                        color: title.color,
                        fontSize: 24,
                        lineHeight: 1,
                        fontWeight: "bold"
                      }}
                    >
                      {title.text}
                    </div>
                    <div style={{ color: subTitle.color, marginBottom: 30 }}>{subTitle.text}</div>
                    <div
                      style={{
                        backgroundColor: button.bgColor,
                        color: button.textColor,
                        width: 58,
                        height: 24,
                        textAlign: "center",
                        lineHeight: "24px",
                        borderRadius: 2,
                        cursor: "pointer"
                      }}
                      onClick={() => {
                        button.onClick();
                        if (button.link != undefined) {
                          window.open(button.link || "");
                        }
                      }}
                    >
                      前往
                    </div>
                  </Space>
                </Col>
                <div style={{ position: "absolute", right: "24px" }}>
                  <ConsumeIcon name={icon} />
                </div>
              </Row>
            </Col>
          ))}
        </Row>

        {/* 景区列表弹窗 */}
        <Modal
          width={670}
          title="景区列表"
          open={scenicVisible}
          onCancel={() => {
            setScenicVisible(false);
          }}
          footer={null}
        >
          {scenicList.map((item: any, index: any) => (
            <div key={getUniqueId()}>
              <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                <div>
                  <Title level={5} style={{ margin: 0 }}>
                    {item.title}
                  </Title>
                  <Paragraph copyable style={{ margin: 0 }}>
                    {item.url}
                  </Paragraph>
                </div>
                <Button
                  type="primary"
                  onClick={() => {
                    addOperationLogRequest({
                      action: "link",
                      content: "跳转机构后台"
                    });
                    window.open(item.url);
                  }}
                >
                  前往
                </Button>
              </div>
              {index < scenicList.length - 1 && <Divider style={{ margin: "12px 0" }} />}
            </div>
          ))}
        </Modal>
      </Card>
      <Charts />
    </div>
  );
};

export default Welcome;
