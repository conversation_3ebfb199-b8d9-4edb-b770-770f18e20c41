import React, { useRef, useState } from "react";

import { PlusOutlined } from "@ant-design/icons";
import { Button, Input, message, Modal, Tag } from "antd";

import ProForm, { ModalForm, ProFormText } from "@ant-design/pro-form";
import type { ActionType } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
// import type { FormValueType } from './components/UpdateForm';
// import UpdateForm from './components/UpdateForm';
import {
  apiWiFiAddUpdate,
  apiWiFiDelete,
  apiWiFiDetails,
  apiWiFiPageList,
  apiWiFisEnable
} from "@/services/api/device";

import EditPop from "@/common/components/EditPop";
import { getUniqueId } from "@/common/utils/tool";
import { Access, useAccess, useModel } from "@umijs/max";
import Details from "./Details";
const { confirm } = Modal;

const Wifi: React.FC = () => {
  const formRef = useRef();
  const formObj = useRef();
  const access = useAccess();
  const actionRef = useRef<ActionType>();
  const [modalVisit, setModalVisit] = useState(false);
  //获取当前 ID
  const [Id, setId] = useState(undefined);
  // 【表单】数据绑定
  const [editVisible, setEditVisible] = useState(false);
  const [dataSource, setDataSource] = useState<any>({ id: "", isEnable: 0 });

  // 获取景区 ID
  const { initialState }: any = useModel("@@initialState");
  const scenicId = initialState?.scenicInfo?.scenicId;
  console.log(scenicId, initialState);
  //获取景区名称
  const scenicName = initialState?.scenicInfo?.scenicName;

  //初始视化数据
  const [initialValues, setInitialValues] = useState({});

  // 详情
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [detailData, setDetailsData] = useState([]);
  // //获取当前 id
  // const [isId,setIsId]=useState()

  const showModal = async (val: any) => {
    const id = val.id;
    const result: any = await apiWiFiDetails(id);
    const { data } = result;
    console.log("yyds", data, val);
    //回选
    // formObj?.current?.setFieldsValue(data);
    setDetailsData(data);
    setInitialValues(data);
    // setDataSource(data);
    setId(val.id);
    setIsEnableStatus(data.isEnable);
    // setModalVisit(true);
    setIsModalVisible(true);
  };

  const handleOk = () => {
    setIsModalVisible(false);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  const [isEnableStatus, setIsEnableStatus] = useState(undefined);
  //编辑
  const updateMethod = async (val: any) => {
    const id = val.id;
    const result: any = await apiWiFiDetails(id);
    const { data } = result;
    console.log("yyds", data, val);
    //回选
    // formObj?.current?.setFieldsValue(data);
    setInitialValues(data);
    // setDataSource(data);
    setId(val.id);
    setIsEnableStatus(data.isEnable);
    setIsModalVisible(false);
    setModalVisit(true);

    // setEditVisible(true);
  };

  // 新增
  const onAdd = () => {
    // setDataSource({ id: '', isEnable: 0 });
    setEditVisible(true);
  };
  //启用/禁用
  const onStatus = async (val: any) => {
    console.log("nsakbcjksbcjsbcjksb", val);
    confirm({
      title: `您确定${val.isEnable == 1 ? "禁用" : "启用"}吗？`,
      // content: 'Some descriptions',
      onOk: async () => {
        try {
          const result = await apiWiFisEnable(val.id);
          message.success(val.isEnable == 0 ? "启用成功" : "禁用成功");
          console.log(val);
          // getNoticeList()
          // 刷新
          actionRef?.current?.reload();
          setIsModalVisible(false);
        } catch (e) {
          console.error(e);
        }
      },
      onCancel() {
        console.log("Cancel");
      }
    });
  };
  //删除
  const onDelete = (id: any) => {
    confirm({
      title: `您确定删除吗？`,
      // content: 'Some descriptions',
      onOk: async () => {
        try {
          const result = await apiWiFiDelete(id);
          // console.log(result);
          message.success("删除成功");
          // 刷新
          actionRef?.current?.reload();
          setIsModalVisible(false);
        } catch (e) {
          console.log(e);
        }
      },
      onCancel() {
        console.log("Cancel");
      }
    });
  };

  // 品牌
  const brandMenus = {};

  // const [brandData, setBrandData] = useState([])

  // const sum = brandData.reduce((pre, cor): any => {

  //   if (pre[cor.brand] !== cor.brand) {
  //     return pre[cor.brand] = cor.brand
  //   }

  // }, {})

  // console.log(sum)

  // 保存数据
  const submitData = async (val: any) => {
    console.log("123131321321231", val.isEnable);
    const pras = {
      ...val,
      scenicId
      // isEnable: val.isEnable,
    };
    try {
      const result = await apiWiFiAddUpdate(pras);
      // message.success('保存成功');
      message.success(val.hasOwnProperty("id") ? "编辑成功" : "新增成功");
      actionRef?.current?.reload();
      setEditVisible(false);
    } catch (e) {
      console.error(e);
    }
  };

  //获取 wifi 列表
  const getWiFiPageList = async (params: any) => {
    const pars = { ...params, scenicId };
    try {
      const result = await apiWiFiPageList(pars);
      const { data } = result.data;
      console.log("hujiajia", result);
      // setBrandData(data)
      // 刷新
      return {
        data,
        success: true,
        total: data.total
      };
    } catch (e) {
      console.error(e);
    }
  };

  const columns: any = [
    {
      title: "所属景区",
      dataIndex: "scenicName",
      hideInSearch: true
    },
    {
      title: "点位名称",
      // ellipsis: true,
      dataIndex: "wifiName",
      fieldProps: {
        placeholder: "请输入点位名称"
      },
      render: (dom, record) => {
        return <a onClick={() => showModal(record)}>{dom}</a>;
      }
    },
    {
      title: "经度",
      dataIndex: "longitude",
      // valueType: 'select',
      hideInSearch: true,
      render: (dom, record) => {
        const str = record.longitude.charAt(0);
        if (str == "+" || str == "-") {
          if (str == "+") {
            return <span>{record.longitude.slice(1)}° E </span>;
          } else if (str == "-") {
            return <span>{record.longitude.slice(1)}° W</span>;
          }
        } else {
          return <span>{record.longitude}° E</span>;
        }
      }
    },
    {
      title: "纬度",
      dataIndex: "latitude",
      // valueType: 'select',
      hideInSearch: true,
      render: (dom, record) => {
        const str = record.latitude.charAt(0);
        if (str == "+" || str == "-") {
          if (str == "+") {
            return <span>{record.latitude.slice(1)}° N</span>;
          } else if (str == "-") {
            return <span>{record.latitude.slice(1)}° S</span>;
          }
        } else {
          return <span>{record.latitude}° N</span>;
        }
      }
    },

    {
      title: "品牌",
      dataIndex: "brand",
      // valueType: 'select',
      renderFormItem: () => {
        return <Input placeholder="请输入品牌" />;
      }
    },
    {
      title: "链接地址",
      dataIndex: "linkAddress",
      hideInSearch: true
    },
    {
      title: "状态",
      dataIndex: "isEnable",
      valueType: "select",
      // hideInSearch: true,
      valueEnum: {
        "0": {
          text: "禁用"
        },
        "1": {
          text: "启用"
        }
      },
      render: (dom: any, record: any) => {
        return <Tag color={record.isEnable == 1 ? "blue" : "red"}>{record.isEnable == 1 ? "已启用" : "已禁用"}</Tag>;
      }
    }
    // {
    //   title: '操作',
    //   dataIndex: 'option',
    //   width: '10%',
    //   hideInSearch: true,
    //   render: (dom, record) => {
    //     return (
    //       <Space>
    //         <a onClick={() => updateMethod(record)}>编辑</a>
    //         <a>
    //           {/* <Popconfirm
    //             title={`您确定${record.isEnable == 1 ? '禁用' : '启用'}吗？`}
    //             onConfirm={() => onStatus(record)}
    //             icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
    //             onCancel={cancel}
    //             okText="确认"
    //             cancelText="取消"
    //           >
    //             <a href="#"> {record.isEnable == 1 ? '禁用' : '启用'}</a>
    //           </Popconfirm> */}
    //         </a>

    //         {/* <a>
    //           <Popconfirm
    //             title="您确定删除吗？"
    //             onConfirm={() => onDelete(record.id)}
    //             icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
    //             onCancel={cancel}
    //             okText="确认"
    //             cancelText="取消"
    //           >
    //             <a href="#">删除</a>
    //           </Popconfirm>
    //         </a> */}
    //       </Space>
    //     );
    //   },
    // },
  ];
  const editColumns = [
    {
      columns: [
        {
          title: "所属景区",
          dataIndex: "scenicName",
          name: "scenicName",
          initialValue: `${scenicName}`,
          key: "scenicName",
          fieldProps: {
            disabled: true
          }
        },
        {
          title: "点位名称",
          dataIndex: "wifiName",
          ellipsis: true,
          name: "wifiName",
          key: "wifiName",
          formItemProps: {
            rules: [{ required: true }, { max: 100, message: "最多可输入 100 个字符" }]
          }
        },
        {
          title: "账号",
          dataIndex: "loginName",
          name: "loginName",
          key: "loginName",
          fieldProps: {},
          formItemProps: {
            rules: [{ required: true }, { max: 100, message: "最多可输入 100 个字符" }]
          }
        },
        {
          title: "密码",
          dataIndex: "loginPassword",
          name: "loginPassword",
          key: "loginPassword",
          fieldProps: {},
          formItemProps: {
            rules: [{ required: true }, { max: 100, message: "最多可输入 100 个字符" }]
          }
        },
        {
          title: "经度",
          dataIndex: "longitude",
          tooltip: '"-" 为西经，"+" 为东经',
          name: "longitude",
          key: "longitude",
          fieldProps: {
            // size: '50px',
            // suffix: <Button />,
          },
          formItemProps: {
            rules: [
              { required: true },
              {
                pattern:
                  /^(\-|\+)?(((\d|[1-9]\d|1[0-7]\d|0{1,3})\.\d{0,6})|(\d|[1-9]\d|1[0-7]\d|0{1,3})|180\.0{0,6}|180)$/,
                message: '(范围："-180°~ +180°",保留 6 位小数)'
              }
            ]
          }
        },
        {
          title: "纬度",
          tooltip: '"+"为北纬，"-"为南纬',
          dataIndex: "latitude",
          name: "latitude",
          key: "latitude",
          fieldProps: {},
          formItemProps: {
            rules: [
              { required: true },
              {
                pattern: /^(\-|\+)?([0-8]?\d{1}\.\d{0,6}|90\.0{0,6}|[0-8]?\d{1}|90)$/,
                message: '( 范围："-90°~+90°",保留 6 位小数)'
              }
            ]
          }
        },
        {
          title: "品牌",
          dataIndex: "brand",
          name: "brand",
          // valueEnum: {
          //     '0': '1',
          //     "1": '2'
          // },
          key: "brand",
          formItemProps: {
            rules: [{ required: true }, { max: 100, message: "最多可输入 100 个字符" }]
          },
          // valueType: 'select',
          fieldProps: {
            // mode: 'multiple',
            // options: positionValue2 == 1 ? scenicData2 : companyData2,
            // onChange: (value, option) => {
            //   console.log(value);
            //   setAcceptorData(value);
            // },
            // showArrow: true,
            // disabled: flag ? false : show ? false : true,
          }
        },
        {
          title: "链接地址",
          dataIndex: "linkAddress",
          name: "linkAddress",
          key: "linkAddress",
          fieldProps: {},
          formItemProps: {
            rules: [{ required: true }]
          }
        },
        {
          title: "管理端口",
          dataIndex: "point",
          name: "point",
          key: "point",
          formItemProps: {
            rules: [{ max: 100, message: "最多可输入 100 个字符" }]
          }
        },
        {
          title: "通道号",
          dataIndex: "channel",
          name: "channel",
          key: "channel",
          formItemProps: {
            rules: [{ max: 100, message: "最多可输入 100 个字符" }]
          }
        }
      ]
    }
  ];

  return (
    <>
      {/* 详情 */}
      <Modal
        title="WiFi 点详情"
        visible={isModalVisible}
        width={800}
        onOk={handleOk}
        onCancel={handleCancel}
        footer={[
          <Access key={getUniqueId()} accessible={access.canHardwareWIFI_openClose}>
            <Button
              type="primary"
              ghost
              danger={detailData.isEnable == 1 ? true : false}
              key="isEnable"
              onClick={() => onStatus(detailData)}
            >
              {detailData.isEnable == 1 ? "禁用" : "启用"}
            </Button>
          </Access>,
          <Access key={getUniqueId()} accessible={access.canHardwareWIFI_delete}>
            <Button key="del" type="primary" danger onClick={() => onDelete(Id)}>
              删除
            </Button>
          </Access>,
          <Access key={getUniqueId()} accessible={access.canHardwareWIFI_edit}>
            <Button key="update" type="primary" onClick={() => updateMethod(detailData)}>
              编辑
            </Button>
          </Access>,
          <Button key="col" onClick={handleCancel}>
            取消
          </Button>
        ]}
      >
        <Details detailData={detailData} />
      </Modal>
      {/* 编辑 */}
      {JSON.stringify(initialValues) !== "{}" ? (
        <ModalForm
          title="编辑 WIFI 点"
          visible={modalVisit}
          // formRef={formObj}
          initialValues={initialValues}
          onFinish={async val => {
            val.id = Id;
            // val.isEnable = isEnableStatus
            console.log("编辑", val.isEnable, isEnableStatus);

            submitData(val);
            return true;
          }}
          onVisibleChange={visit => {
            setModalVisit(visit);
            if (!visit) {
              setInitialValues({});
            }
          }}
        >
          <ProForm.Group>
            <ProFormText width="md" name="scenicName" label="所属景区" initialValue={scenicName} disabled={true} />
            <ProFormText
              width="md"
              name="wifiName"
              label="点位名称"
              placeholder="请输入点位名称"
              rules={[
                { required: true, message: "请输入点位名称" },
                { max: 100, message: "最多可输入100个字符" }
              ]}
            />
          </ProForm.Group>

          <ProForm.Group>
            <ProFormText
              width="md"
              name="loginName"
              label="账号"
              placeholder="请输入账号"
              rules={[
                { required: true, message: "请输入账号" },
                { max: 100, message: "最多可输入100个字符" }
              ]}
            />
            <ProFormText
              width="md"
              name="loginPassword"
              label="密码"
              placeholder="请输入密码"
              rules={[
                { required: true, message: "请输入密码" },
                { max: 100, message: "最多可输入100个字符" }
              ]}
            />
          </ProForm.Group>

          <ProForm.Group>
            <ProFormText
              width="md"
              name="longitude"
              label="经度"
              tooltip='"-" 为西经,"+" 为东经'
              placeholder="请输入经度"
              rules={[
                { required: true, message: "请输入经度" },
                {
                  pattern:
                    /^(\-|\+)?(((\d|[1-9]\d|1[0-7]\d|0{1,3})\.\d{0,6})|(\d|[1-9]\d|1[0-7]\d|0{1,3})|180\.0{0,6}|180)$/,
                  message: '(范围："-180°~ +180°",保留6位小数)'
                }
              ]}
            />
            <ProFormText
              width="md"
              name="latitude"
              label="纬度"
              tooltip='"+"为北纬，"-"为南纬'
              placeholder="请输入纬度"
              rules={[
                { required: true, message: "请输入纬度" },
                {
                  pattern: /^(\-|\+)?([0-8]?\d{1}\.\d{0,6}|90\.0{0,6}|[0-8]?\d{1}|90)$/,
                  message: '( 范围："-90°~+90°",保留6位小数)'
                }
              ]}
            />
          </ProForm.Group>

          <ProForm.Group>
            <ProFormText
              width="md"
              name="brand"
              label="品牌"
              placeholder="请输入品牌"
              rules={[
                { required: true, message: "请输入品牌" },
                { max: 100, message: "最多可输入100个字符" }
              ]}
            />
            <ProFormText
              width="md"
              name="linkAddress"
              label="链接地址"
              placeholder="请输入链接地址"
              rules={[
                { required: true, message: "请输入链接地址" },
                { max: 100, message: "最多可输入100个字符" }
              ]}
            />
          </ProForm.Group>

          <ProForm.Group>
            <ProFormText
              width="md"
              name="point"
              label="管理端口"
              placeholder="请输入管理端口"
              rules={[{ max: 100, message: "最多可输入100个字符" }]}
            />
            <ProFormText
              width="md"
              name="channel"
              label="通道号"
              placeholder="请输入通道号"
              rules={[{ max: 100, message: "最多可输入100个字符" }]}
            />
          </ProForm.Group>
        </ModalForm>
      ) : (
        ""
      )}
      {/* 新增编辑 */}
      <EditPop
        title="WIFI 点"
        visible={editVisible}
        setVisible={setEditVisible}
        columns={editColumns}
        dataSource={dataSource}
        // 新增/编辑
        onFinish={(val: any) => {
          console.log("新增", val);
          submitData(val);
        }}
      />
      <>
        <ProTable
          // headerTitle={'查询表格'}
          actionRef={actionRef}
          rowKey="id"
          options={false}
          columns={columns}
          request={getWiFiPageList}
          search={{
            labelWidth: 120,
            collapseRender: false,
            collapsed: false
          }}
          toolBarRender={() => [
            <Access key={getUniqueId()} accessible={access.canHardwareWIFI_insert}>
              <Button type="primary" key="primary" onClick={onAdd}>
                <PlusOutlined /> 新增
              </Button>
            </Access>
          ]}
          // dataSource={data}
          // params={{ scenicId }}
        />
      </>
    </>
  );
};

export default Wifi;
