import { Tabs } from 'antd';
import { useState } from 'react';
import Knowledge from './Knowledge';
export default () => {
  const [tabIndex, setTabIndex] = useState<any>('0');
  return (
    <>
      <Tabs
        style={{ marginTop: '-16px' }}
        tabBarStyle={{ padding: '0 24px', margin: '0', background: '#fff' }}
        defaultActiveKey="0"
        activeKey={tabIndex}
        onChange={(e) => {
          setTabIndex(e);
        }}
      >
        <Tabs.TabPane tab="知识库管理" key="0">
          <div>
            <Knowledge />
          </div>
        </Tabs.TabPane>
        {/* <Tabs.TabPane tab="页面装修" key="1">
          <div>页面装修</div>
        </Tabs.TabPane> */}
      </Tabs>
    </>
  );
};
