import DetailsPop from "@/common/components/DetailsPop";
import EditPop from "@/common/components/EditPop";
import { tableConfig } from "@/common/utils/config";
import { enableEnum } from "@/common/utils/enum";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import { getUniqueId } from "@/common/utils/tool";
import {
  AddTicketRetreat,
  delTicketRetreat,
  getTicketRetreatRulePageList,
  getTicketRetreatStatus,
  ticketRetreatXq
} from "@/services/api/ticket";
import { InfoCircleTwoTone, MinusCircleOutlined, PlusCircleOutlined, PlusOutlined } from "@ant-design/icons";
import type { ProFormColumnsType } from "@ant-design/pro-form";
import type { ActionType, ProColumns } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import { Button, Checkbox, InputNumber, Modal, Select, Space, Switch, Tag, Tooltip, message } from "antd";
import { trim } from "lodash";
import React, { useEffect, useRef, useState } from "react";
import { Access, useAccess, useModel } from "@umijs/max";
import RetreatRate from "./components/RetreatRate";
import "./index.less";
const { Option } = Select;

console.log(
  !require("lodash").intersectionWith(
    ...[
      [1, 2, 3],
      [2, 3, 4]
    ]
  ).length
);

let rulesList: any = [];
const defaultRules = {
  id: "",
  flag: 0,
  dayBeginHour: "0",
  dayBeginTime: "0",
  dayEndHour: "0",
  dayEndTime: "0",
  rate: "0",
  unique: getUniqueId()
};
let rulesBookList: any = [];
const defaultRulesBook = {
  id: "",
  flag: 2,
  dayBeginHour: "0",
  dayBeginTime: "0",
  dayEndHour: "0",
  dayEndTime: "0",
  rate: "0",
  unique: getUniqueId()
};
const TableList: React.FC = () => {
  const access = useAccess();
  const { initialState } = useModel("@@initialState");
  const { scenicId, scenicName }: any = initialState?.scenicInfo;

  // 【表格】数据绑定
  const actionRef = useRef<ActionType>();
  const columns: ProColumns[] = [
    {
      title: "退票规则名称",
      dataIndex: "name",
      search: {
        transform: val => trim(val)
      }
    },
    {
      title: "可退票",
      dataIndex: "isRetreat",
      valueEnum: {
        0: "否",
        1: "是"
      }
    },
    {
      title: "退票费率",
      search: false,
      colSpan: 3,
      dataIndex: "lawsData",
      renderText: (text = []) => {
        if (text.length < 3) {
          return <RetreatRate text={text} title="生效" />;
        }
        return (
          <Tooltip placement="top" overlayInnerStyle={{ width: 320 }} title={<RetreatRate text={text} title="生效" />}>
            <RetreatRate text={text.slice(0, 2)} title="生效" />
            <div>......</div>
          </Tooltip>
        );
      }
    },
    {
      search: false,
      dataIndex: "bookData",
      colSpan: 0,
      renderText: (text = []) => {
        if (text.length < 3) {
          return <RetreatRate text={text} title="预定" />;
        }
        return (
          <Tooltip placement="top" overlayInnerStyle={{ width: 320 }} title={<RetreatRate text={text} title="预定" />}>
            <RetreatRate text={text.slice(0, 2)} title="预定" />
            <div>......</div>
          </Tooltip>
        );
      }
    },
    {
      dataIndex: "defaultRate",
      search: false,
      colSpan: 0,
      renderText: text => {
        return `其他退票费率：${text ?? "无"}`;
      }
    },
    {
      title: "启用状态",
      dataIndex: "isEnable",
      fixed: "right",
      valueEnum: enableEnum,
      renderText: (dom: any, entity: any) => (
        <Switch
          disabled={!access.canRefundRule_openClose}
          checkedChildren="启用"
          unCheckedChildren="禁用"
          checked={!!dom}
          onChange={() => {
            getTicketRetreatStatus({
              id: entity.id,
              isEnable: 1 - entity.isEnable,
              scenicId
            })
              .then(() => {
                message.success(dom ? "已禁用" : "已启用");
                addOperationLogRequest({
                  action: "disable",
                  content: `${dom ? "禁用" : "启用"}【${entity.name}】退票规则`
                });
                actionRef.current?.reload();
              })
              .catch(console.log);
          }}
        />
      )
    },
    {
      title: "操作",
      valueType: "option",
      fixed: "right",
      render: (_: any, record: any) => (
        <Space size="large">
          <a
            onClick={async () => {
              setLoading(true);
              setDetailsVisible(true);
              try {
                const data = await ticketRetreatXq({ id: record.id });
                data.data.rules = data.data.data;
                data.data.rulesBook = data.data.bookData;
                setDataSource(data.data);
                rulesList = data.data.rules.length > 0 ? data.data.rules : [{ ...defaultRules }];
                rulesBookList = data.data.rulesBook.length > 0 ? data.data.rulesBook : [{ ...defaultRulesBook }];
                setRulesData(!rulesData);
                setLoading(false);
                addOperationLogRequest({
                  action: "info",
                  content: `查看【${record.name}】退票规则详情`
                });
              } catch (error) {
                setDetailsVisible(false);
              }
            }}
          >
            查看
          </a>
          <Access accessible={access.canRefundRule_edit && record?.isDigit != "1"}>
            <a
              onClick={async () => {
                const data = await ticketRetreatXq({ id: record.id });
                data.data.rules = data.data.data;
                data.data.rulesBook = data.data.bookData;
                setDataSource(data.data);
                rulesList = data.data.rules.length > 0 ? data.data.rules : [{ ...defaultRules }];
                rulesBookList = data.data.rulesBook.length > 0 ? data.data.rulesBook : [{ ...defaultRulesBook }];
                setRulesData(!rulesData);
                setIsTimeRestrict(data.data.isRetreat);
                setCheckData(data.data?.data?.length > 0);
                setCheckBookData(data.data?.bookData?.length > 0);
                setEditVisible(true);
              }}
            >
              编辑
            </a>
          </Access>
          <Access accessible={access.canRefundRule_delete && record?.isDigit != "1"}>
            <a
              style={{ color: "red" }}
              onClick={async () => {
                if (record.isEnable) {
                  Modal.warning({
                    title: "不可删除",
                    content: "请先禁用后删除"
                  });
                  return;
                }
                Modal.confirm({
                  title: "确认删除吗？",
                  icon: <InfoCircleTwoTone />,
                  content: "删除后不可恢复",
                  okText: "确认",
                  cancelText: "取消",
                  onOk: () => {
                    delTicketRetreat(record.id)
                      .then(() => {
                        message.success("删除成功");
                        addOperationLogRequest({
                          action: "del",
                          content: `删除【${record.name}】退票规则`
                        });

                        actionRef.current?.reload();
                      })
                      .catch(() => {});
                  }
                });
              }}
            >
              删除
            </a>
          </Access>
        </Space>
      )
    }
  ];

  const [rulesData, setRulesData] = useState(false);
  const [checkData, setCheckData] = useState(false);
  const [checkBookData, setCheckBookData] = useState(false);
  const changeValue = (index: any, type: any, e: any) => {
    rulesList[index][type] = e;
    setRulesData(!rulesData);
  };
  const changeBookValue = (index: any, type: any, e: any) => {
    rulesBookList[index][type] = e;
    setRulesData(!rulesData);
  };
  const checkChange = (e: any) => {
    setCheckData(e.target.checked);
  };
  const checkBookChange = (e: any) => {
    setCheckBookData(e.target.checked);
  };
  // 列表组件
  const RulesList: any = () => {
    return (
      <div>
        <div style={{ width: "100%" }}>
          <Checkbox checked={checkData} style={{ marginBottom: "12px" }} onChange={checkChange}>
            按生效时间
          </Checkbox>
          <span style={{ fontSize: "14px", color: "#f00" }}>含 ~ 不含，0 表示生效当天可退</span>
        </div>
        {checkData
          ? rulesList?.map((item: any, index: any) => (
              <div
                key={item.unique ? item.unique : item.id}
                style={{
                  width: "100%",
                  display: "flex",
                  marginBottom: "12px",
                  alignItems: "center"
                }}
              >
                <div style={{ display: "flex" }}>
                  <Select
                    style={{ width: "84px" }}
                    value={item.flag}
                    onChange={e => {
                      changeValue(index, "flag", e);
                    }}
                  >
                    <Option value={0}>生效前</Option>
                    <Option value={1}>生效后</Option>
                  </Select>
                  <InputNumber
                    className="my-input"
                    style={{ width: "80px", marginLeft: "8px" }}
                    min={0}
                    max={365}
                    precision={0}
                    controls={false}
                    value={item.dayBeginTime}
                    onChange={e => {
                      changeValue(index, "dayBeginTime", e);
                    }}
                    addonAfter="天"
                  />
                  <InputNumber
                    className="my-input"
                    style={{ width: "80px", marginLeft: "4px" }}
                    min={0}
                    max={23}
                    precision={0}
                    controls={false}
                    value={item.dayBeginHour}
                    onChange={e => {
                      changeValue(index, "dayBeginHour", e);
                    }}
                    addonAfter="时"
                  />
                  <span
                    style={{
                      height: "32px",
                      lineHeight: "32px",
                      padding: "0 8px"
                    }}
                  >
                    ~
                  </span>
                  <InputNumber
                    className="my-input"
                    style={{ width: "80px" }}
                    min={0}
                    max={365}
                    precision={0}
                    controls={false}
                    value={item.dayEndTime}
                    onChange={e => {
                      changeValue(index, "dayEndTime", e);
                    }}
                    addonAfter="天"
                  />
                  <InputNumber
                    className="my-input"
                    style={{ width: "80px", marginLeft: "4px" }}
                    min={0}
                    max={23}
                    precision={0}
                    controls={false}
                    value={item.dayEndHour}
                    onChange={e => {
                      changeValue(index, "dayEndHour", e);
                    }}
                    addonAfter="时"
                  />
                </div>
                <InputNumber
                  className="my-input"
                  addonBefore="退票费率"
                  style={{ width: "150px", textAlign: "center", marginLeft: "16px" }}
                  min={0}
                  max={1}
                  precision={2}
                  controls={false}
                  value={item.rate}
                  onChange={e => {
                    changeValue(index, "rate", e);
                  }}
                />
                <span style={{ marginLeft: "8px", fontSize: "14px", color: "#f00" }}>0~1</span>
                <div
                  style={{
                    flex: 1,
                    display: "flex",
                    justifyContent: "flex-end",
                    alignItems: "center"
                  }}
                >
                  <PlusCircleOutlined
                    style={{ marginLeft: "16px", cursor: "pointer", fontSize: "16px" }}
                    onClick={() => {
                      defaultRules.unique = getUniqueId();
                      rulesList.splice(index + 1, 0, { ...defaultRules });
                      console.log(rulesList);

                      setRulesData(!rulesData);
                    }}
                  />
                  <MinusCircleOutlined
                    style={{
                      marginLeft: "16px",
                      cursor: rulesList.length > 1 ? "pointer" : "not-allowed",
                      fontSize: "16px"
                    }}
                    onClick={() => {
                      if (rulesList.length > 1) {
                        rulesList.splice(index, 1);
                        setRulesData(!rulesData);
                      }
                    }}
                  />
                </div>
              </div>
            ))
          : ""}
        <div style={{ width: "100%", marginTop: "24px" }}>
          <Checkbox checked={checkBookData} style={{ marginBottom: "12px" }} onChange={checkBookChange}>
            按预订时间
          </Checkbox>
          <span style={{ fontSize: "14px", color: "#f00" }}>含 ~ 不含，0 表示购买当天可退</span>
        </div>
        {checkBookData
          ? rulesBookList?.map((item: any, index: any) => (
              <div
                key={item.unique ? item.unique : item.id}
                style={{
                  width: "100%",
                  display: "flex",
                  marginBottom: "12px",
                  alignItems: "center"
                }}
              >
                <div style={{ display: "flex" }}>
                  <Select style={{ width: "84px" }} value={2}>
                    <Option value={2}>预订后</Option>
                  </Select>
                  <InputNumber
                    className="my-input"
                    style={{ width: "80px", marginLeft: "8px" }}
                    min={0}
                    max={365}
                    precision={0}
                    controls={false}
                    value={item.dayBeginTime}
                    onChange={e => {
                      changeBookValue(index, "dayBeginTime", e);
                    }}
                    addonAfter="天"
                  />
                  <InputNumber
                    className="my-input"
                    style={{ width: "80px", marginLeft: "4px" }}
                    min={0}
                    max={23}
                    precision={0}
                    controls={false}
                    value={item.dayBeginHour}
                    onChange={e => {
                      changeBookValue(index, "dayBeginHour", e);
                    }}
                    addonAfter="时"
                  />
                  <span
                    style={{
                      height: "32px",
                      lineHeight: "32px",
                      padding: "0 8px"
                    }}
                  >
                    ~
                  </span>
                  <InputNumber
                    className="my-input"
                    style={{ width: "80px" }}
                    min={0}
                    max={365}
                    precision={0}
                    controls={false}
                    value={item.dayEndTime}
                    onChange={e => {
                      changeBookValue(index, "dayEndTime", e);
                    }}
                    addonAfter="天"
                  />
                  <InputNumber
                    className="my-input"
                    style={{ width: "80px", marginLeft: "4px" }}
                    min={0}
                    max={23}
                    precision={0}
                    controls={false}
                    value={item.dayEndHour}
                    onChange={e => {
                      changeBookValue(index, "dayEndHour", e);
                    }}
                    addonAfter="时"
                  />
                </div>
                <InputNumber
                  className="my-input"
                  addonBefore="退票费率"
                  style={{ width: "150px", textAlign: "center", marginLeft: "16px" }}
                  min={0}
                  max={1}
                  precision={2}
                  controls={false}
                  value={item.rate}
                  onChange={e => {
                    changeBookValue(index, "rate", e);
                  }}
                />
                <span style={{ marginLeft: "8px", fontSize: "14px", color: "#f00" }}>0~1</span>
                <div
                  style={{
                    flex: 1,
                    display: "flex",
                    justifyContent: "flex-end",
                    alignItems: "center"
                  }}
                >
                  <PlusCircleOutlined
                    style={{ marginLeft: "16px", cursor: "pointer", fontSize: "16px" }}
                    onClick={() => {
                      defaultRulesBook.unique = getUniqueId();
                      rulesBookList.splice(index + 1, 0, { ...defaultRulesBook });
                      console.log(rulesBookList);

                      setRulesData(!rulesData);
                    }}
                  />
                  <MinusCircleOutlined
                    style={{
                      marginLeft: "16px",
                      cursor: rulesBookList.length > 1 ? "pointer" : "not-allowed",
                      fontSize: "16px"
                    }}
                    onClick={() => {
                      if (rulesBookList.length > 1) {
                        rulesBookList.splice(index, 1);
                        setRulesData(!rulesData);
                      }
                    }}
                  />
                </div>
              </div>
            ))
          : ""}
      </div>
    );
  };
  // 是否可退票
  const [isTimeRestrict, setIsTimeRestrict] = useState<boolean>(false);
  // 【表单】数据绑定
  const [editVisible, setEditVisible] = useState<boolean>(false);
  const editColumns: ProFormColumnsType<unknown, "text">[] = [
    {
      title: "基础信息",
      columns: [
        {
          title: "景区名称",
          dataIndex: "scenicId",
          valueType: "select",
          valueEnum: { [scenicId]: scenicName },
          initialValue: scenicId,
          fieldProps: { disabled: true }
        },
        {
          title: "退票规则名称",
          dataIndex: "name",
          formItemProps: { rules: [{ required: true, max: 30 }] }
        }
      ]
    },
    {
      title: "退票信息",
      columns: [
        {
          title: "是否可退票",
          dataIndex: "isRetreat",
          valueType: "switch",
          fieldProps: {
            onChange: (e: any) => {
              setIsTimeRestrict(e);
            }
          }
        },
        {
          colProps: { span: 24 },
          hideInForm: !isTimeRestrict,
          title: "",
          renderFormItem: RulesList
        },
        {
          hideInForm: !isTimeRestrict,
          valueType: "digit",
          title: (
            <div>
              其他退票费率 <span style={{ color: "#f00" }}> 0~1 的小数，到后两位</span>
            </div>
          ),
          dataIndex: "defaultRate",
          formItemProps: { rules: [{ required: true, message: "请输入 0~1 的小数，到后两位" }] },
          fieldProps: {
            min: 0,
            max: 1,
            precision: 2
          }
        }
      ]
    },
    {
      title: "说明信息",
      columns: [
        {
          title: "备注",
          dataIndex: "remark",
          valueType: "textarea",
          colProps: { span: 24 },
          fieldProps: {
            showCount: true,
            maxLength: 1000
          }
        }
      ]
    }
  ];

  // 【列表】数据绑定
  const [detailsVisible, setDetailsVisible] = useState<boolean>(false);
  const [isLoading, setLoading] = useState<boolean>(true);
  const [dataSource, setDataSource] = useState<any>({ id: "", isEnable: 0 });
  const columnsInitial = [
    {
      title: "基础信息",
      columns: [
        {
          title: "景区名称",
          dataIndex: "scenicName",
          render: () => scenicName
        },
        {
          title: "退票规则名称",
          dataIndex: "name"
        }
      ]
    },
    {
      title: "退票信息",
      columns: [
        {
          span: 2,
          title: "是否可退票",
          dataIndex: "isRetreat",
          render: (dom: any) => ["否", "是"][dom]
        },
        {
          span: 2,
          title: "",
          dataIndex: "data",
          render: (dom: any) =>
            dom ? (
              <div style={{ display: "flex", flexDirection: "column" }}>
                {dom.map((item: any, index: any) => (
                  <div key={getUniqueId()} style={{ marginBottom: "8px" }}>
                    距生效时间
                    <Tag style={{ marginLeft: "8px" }}>{["前", "后"][dom[index].flag]}</Tag>
                    <Tag style={{ width: 150 }}>
                      {dom[index].dayBeginTime.padStart(2, "0")} 天 {dom[index].dayBeginHour.padStart(2, "0")} 时 ~{" "}
                      {dom[index].dayEndTime.padStart(2, "0")} 天 {dom[index].dayEndHour.padStart(2, "0")} 时
                    </Tag>
                    退票费率：
                    <Tag style={{ width: 38 }}>{dom[index].rate.padEnd(3, ".0").padEnd(4, "0")}</Tag>
                  </div>
                ))}
              </div>
            ) : (
              "-"
            )
        },
        {
          span: 2,
          title: "",
          dataIndex: "bookData",
          render: (dom: any) =>
            dom ? (
              <div style={{ display: "flex", flexDirection: "column" }}>
                {dom.map((item: any, index: any) => (
                  <div key={getUniqueId()} style={{ marginBottom: "8px" }}>
                    预订成功后
                    <Tag style={{ marginLeft: "8px", width: 150 }}>
                      {dom[index].dayBeginTime.padStart(2, "0")} 天 {dom[index].dayBeginHour.padStart(2, "0")} 时 ~{" "}
                      {dom[index].dayEndTime.padStart(2, "0")} 天 {dom[index].dayEndHour.padStart(2, "0")} 时
                    </Tag>
                    退票费率：
                    <Tag style={{ width: 38 }}>{dom[index].rate.padEnd(3, ".0").padEnd(4, "0")}</Tag>
                  </div>
                ))}
              </div>
            ) : (
              "-"
            )
        },
        {
          title: "其他退票费率",
          dataIndex: "isRetreat",
          render: (dom: any, entity: any) => (dom ? entity.defaultRate.padEnd(3, ".0").padEnd(4, "0") : "-")
        }
      ]
    },
    {
      title: "说明信息",
      columns: [
        {
          title: "备注",
          dataIndex: "remark"
        }
      ]
    }
  ];

  const logList = [
    ...columnsInitial[0].columns,
    {
      title: "是否可退票",
      dataIndex: "isRetreat",
      valueEnum: {
        0: "否",
        1: "是"
      }
    },
    {
      title: "其他退票费率",
      dataIndex: "defaultRate"
    }
  ];

  // 展示新增弹窗
  const onAdd = () => {
    // 初始化表单
    setIsTimeRestrict(false);
    setDataSource({ id: "", isEnable: 0 });
    rulesList = [{ ...defaultRules }];
    rulesBookList = [{ ...defaultRulesBook }];
    setCheckData(false);
    setCheckBookData(false);
    setRulesData(!rulesData);
    setEditVisible(true);
  };

  useEffect(() => {
    const pageOperateType = localStorage.getItem("pageOperateType");
    if (pageOperateType === "add") {
      onAdd();
    }
  }, []);

  return (
    <>
      <ProTable<API.RuleListItem, API.PageParams>
        {...tableConfig}
        rowClassName={(row: any) => (row.isEnable == 1 ? "" : "disableRow")}
        actionRef={actionRef}
        toolBarRender={() => [
          <Access key={getUniqueId()} accessible={access.canRefundRule_insert}>
            <Button
              type="primary"
              key="primary"
              onClick={() => {
                onAdd();
              }}
            >
              <PlusOutlined /> 新增
            </Button>
          </Access>
        ]}
        params={{ scenicId }}
        request={async params => {
          const { data } = await getTicketRetreatRulePageList(params);
          return data;
        }}
        columns={columns}
      />

      {/* 新增编辑 */}
      <EditPop
        width={800}
        title="退票规则"
        visible={editVisible}
        setVisible={(v: boolean) => {
          setEditVisible(v);
          localStorage.removeItem("pageOperateType");
        }}
        columns={editColumns}
        dataSource={dataSource}
        // 新增/编辑
        onFinish={async (val: any) => {
          try {
            val.rules = checkData ? rulesList : [];
            val.rulesBook = checkBookData ? rulesBookList : [];
            const addZeo = (e: any) => {
              return (e > 9 ? "" : "0") + e;
            };
            const getNum = (item: any) => {
              let time1 = +(item.dayBeginTime + addZeo(item.dayBeginHour));
              let time2 = +(item.dayEndTime + addZeo(item.dayEndHour));
              if (item.flag == 0) {
                time1 *= -1;
                time2 *= -1;
              }
              return [time1, time2];
            };
            const checkout = (list: any) => {
              if (!val.isRetreat) return; // 非可退票无需校验
              let sum: number[][] = [];
              for (const item of list) {
                // 校验空值
                if (
                  (!item.dayBeginTime && item.dayBeginTime != 0) ||
                  (!item.dayEndTime && item.dayEndTime != 0) ||
                  (!item.rate && item.rate != 0)
                ) {
                  message.info("时间区间与费率不能为空");
                  throw new Error();
                }
                // 校验大小
                const [time1, time2] = getNum(item);
                sum.push([time1, time2]);
                if (time1 >= time2) {
                  message.info(
                    `${["生效前", "生效后", "预定后"][item.flag == 0 ? 0 : item.flag || 2]}时间区间范围应由${
                      ["大到小", "小到大", "小到大"][item.flag == 0 ? 0 : item.flag || 2]
                    }`
                  );
                  throw new Error();
                }
              }
              // 校验交集
              sum = sum.sort((a, b) => a[0] - b[0]);
              console.log(sum);
              sum.forEach((_, index) => {
                if (index > 0 && sum[index][0] < sum[index - 1][1]) {
                  message.info("时间区间存在交集");
                  throw new Error();
                }
              });
            };

            checkout(val.rules);
            checkout(val.rulesBook);

            if (dataSource.id) val.id = dataSource.id;
            val.scenicName = scenicName;
            if (val.isRetreat) {
              val.isRetreat = 1;
            } else {
              val.isRetreat = 0;
            }
            if (val.isRetreat) val.rules.id = val.id;
            if (!val.defaultRate && val.defaultRate != 0) val.defaultRate = 1;
            const msgType = val.id ? "编辑" : "新增";
            const hide = message.loading("正在" + msgType);
            try {
              await AddTicketRetreat({ ...val, remark: val.remark || " " });
              if (val.id) {
                addOperationLogRequest({
                  action: "edit",
                  changeConfig: {
                    list: logList,
                    beforeData: dataSource,
                    afterData: val
                  },
                  content: `编辑【${val.name}】退票规则`
                });
              } else {
                addOperationLogRequest({
                  action: "add",
                  content: `新增【${val.name}】退票规则`
                });
              }

              message.success(msgType + "成功");
              // 关闭弹窗并刷新列表
              setEditVisible(false);
              actionRef?.current?.reload();
            } catch (error) {}
            hide();
          } catch (error) {}
        }}
      />

      {/* 详情 */}
      <DetailsPop
        title="退票规则详情"
        visible={detailsVisible}
        isLoading={isLoading}
        setVisible={setDetailsVisible}
        columnsInitial={columnsInitial}
        dataSource={dataSource}
      />
    </>
  );
};

export default TableList;
