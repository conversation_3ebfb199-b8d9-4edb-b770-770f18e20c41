/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-10-10 18:20:29
 * @LastEditTime: 2022-10-26 14:05:19
 * @LastEditors: zhangfengfei
 */
import type { WorkOrderTempType } from "@/pages/workOrder/common/data";
import { WorkOrderTypeEnum } from "@/pages/workOrder/common/data";
import { getUserList } from "@/services/api/workOrder";
import ProForm, { ProFormSelect, ProFormText } from "@ant-design/pro-form";
import { Alert, Modal } from "antd";
import { useForm } from "antd/es/form/Form";
import type { FC } from "react";
import { useEffect } from "react";
import { useModel, useRequest } from "@umijs/max";

interface WorkOrderModalProps {
  visible: boolean;
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
  onFinish: (values: Record<string, string>) => void;
  workOrderTempData: WorkOrderTempType | undefined;
  buttonLoading: boolean;
}

const WorkOrderModal: FC<WorkOrderModalProps> = ({
  visible,
  setVisible,
  onFinish,
  workOrderTempData,
  buttonLoading
}) => {
  const [form] = useForm();

  const { nodeData = [], workOrderType = WorkOrderTypeEnum.商品价格编辑审批 } = workOrderTempData || {};

  const { initialState } = useModel("@@initialState");
  const { coId = "" } = initialState?.currentCompanyInfo || {};

  const { nickname } = initialState?.userInfo || {};
  const { scenicId = "", appId = "" } = initialState?.scenicInfo || {};
  // 公司部门或者成员列表
  const departmentListReq = useRequest(getUserList, {
    manual: true,
    initialData: [],
    formatResult(res) {
      const data = (res.data || []).map(item => ({
        ...item,
        label: `${item.nickname || ""} ${item.username || ""}`,
        value: item.userId
      }));
      // const result = removeEmpty(arrayToTree(data, '0', 'deptId', 'parentDeptId'));
      return data;
    }
  });

  useEffect(() => {
    if (visible) {
      form.resetFields();
      departmentListReq.run({
        scenicId,
        companyId: coId,
        appId
      });
    }
  }, [visible]);

  return (
    <Modal
      title={"创建工单"}
      visible={visible}
      onCancel={() => {
        setVisible(false);
      }}
      onOk={async () => {
        const values = await form.validateFields();
        onFinish(values);
      }}
      okButtonProps={{ loading: buttonLoading }}
      destroyOnClose
    >
      <Alert
        message="此操作已设置审批流程，审批通过后即可生效。确认提交？"
        closable
        type="error"
        style={{
          marginBottom: 10
        }}
      />
      <ProForm
        form={form}
        layout="horizontal"
        preserve={false}
        submitter={false}
        labelAlign="right"
        labelCol={{
          span: 6
        }}
      >
        <ProFormText
          label="创建人"
          readonly
          fieldProps={{
            value: nickname
          }}
        />
        <ProFormText
          label="工单名称"
          readonly
          fieldProps={{
            value: WorkOrderTypeEnum[workOrderType]
          }}
        />

        {nodeData.map((item, index) => {
          return (
            <ProFormSelect
              key={item.id}
              width="md"
              label={`选择审批人${index + 1}`}
              name={`userId${index + 1}`}
              showSearch
              fieldProps={{
                options: departmentListReq.data,
                loading: departmentListReq.loading
              }}
              rules={[{ required: true }]}
            />
          );
        })}
      </ProForm>
    </Modal>
  );
};

export default WorkOrderModal;
