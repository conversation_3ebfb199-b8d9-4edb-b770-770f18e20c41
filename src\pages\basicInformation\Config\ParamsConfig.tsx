/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-07-05 11:41:45
 * @LastEditTime: 2023-08-28 18:38:24
 * @LastEditors: zhang<PERSON><PERSON>i
 */
import { modelWidth } from "@/common/utils/gConfig";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import { numberValidator, stringLengthValidator } from "@/common/utils/validate";
import { updateParamsConfig } from "@/services/api/settings";
import { ProForm, ProFormDigit, ProFormText } from "@ant-design/pro-components";
import type { ProDescriptionsActionType, ProDescriptionsItemProps } from "@ant-design/pro-descriptions";
import ProDescriptions from "@ant-design/pro-descriptions";
import type { ProFormInstance } from "@ant-design/pro-form";
import { Card, Modal, message } from "antd";
import type { FC } from "react";
import { useEffect, useRef, useState } from "react";
import { Access, useAccess, useModel, useRequest } from "@umijs/max";

const logList = [
  {
    title: "日承载量",
    dataIndex: "totalServiceNum"
  },
  {
    title: "单次最大库存发行数量",
    dataIndex: "totalSalesNum"
  },
  {
    title: "检票设备密码",
    dataIndex: "checkEquipmentKey"
  },
  {
    dataIndex: "printCode",
    title: "景区门票打印模板",
    valueEnum: {
      usually: "模板一",
      template1: "模板二"
    }
  }
];

interface ParamsConfigProps {}

const ParamsConfig: FC<ParamsConfigProps> = () => {
  const { scenicInfo } = useModel("@@initialState", model => model.initialState) || {};
  const paramsConfig = useModel("useGlobalModel", model => model.paramsConfig);
  const access = useAccess();

  const [visible, setVisible] = useState(false);
  const formRef = useRef<ProFormInstance<any>>();
  const actionRef = useRef<ProDescriptionsActionType>();

  const updateParamsConfigReq = useRequest(updateParamsConfig, {
    manual: true,
    onSuccess(data, params) {
      setVisible(false);
      message.success("编辑成功");

      // 刷新数据
      paramsConfig.refresh();
      addOperationLogRequest({
        action: "edit",
        changeConfig: {
          list: logList,
          beforeData: paramsConfig.data,
          afterData: params[0]
        },
        content: `编辑景区参数配置`
      });
    }
  });

  const columns: ProDescriptionsItemProps<API.ParamsConfigType>[] = [
    {
      title: "日承载量",
      dataIndex: "totalServiceNum"
    },
    {
      title: "单次最大库存发行数量",
      dataIndex: "totalSalesNum"
    },
    {
      title: "近五年平均游客量",
      dataIndex: "averageTouristNum"
    },
    {
      title: "交易所发行比例",
      dataIndex: "exchangePublishRate",
      hideInDescriptions: scenicInfo?.isBlockChain != 1
    }
  ];

  // 提交
  const onSubmit = async () => {
    const values = await formRef.current?.validateFields();
    updateParamsConfigReq.run({
      ...values,
      paramId: paramsConfig.data?.paramId,
      scenicId: scenicInfo?.scenicId ?? ""
    });
  };

  // 表单赋值
  useEffect(() => {
    if (visible) {
      console.log(paramsConfig);

      formRef.current?.setFieldsValue(paramsConfig.data);
    }
  }, [visible]);

  return (
    <Card
      style={{ marginBottom: "24px" }}
      title={
        <div style={{ position: "relative" }}>
          <span
            style={{
              display: "inline-block",
              width: "2px",
              height: "16px",
              background: "#1890ff",
              marginRight: 8,
              marginBottom: -2
            }}
          />
          <span style={{ fontWeight: 600 }}>{"景区参数设置"}</span>
          <div style={{ float: "right" }}>
            <Access key="link" accessible={access.canGlobalSettings_paramEdit}>
              <a
                style={{
                  fontWeight: "400"
                }}
                onClick={() => {
                  setVisible(true);
                }}
              >
                编辑
              </a>
            </Access>
          </div>
        </div>
      }
    >
      <ProDescriptions<API.ParamsConfigType>
        actionRef={actionRef}
        column={{ xs: 1, sm: 2 }}
        columns={columns}
        dataSource={paramsConfig.data}
      />
      <Modal
        title="编辑景区参数设置"
        open={visible}
        width={modelWidth.md}
        onCancel={() => {
          setVisible(false);
        }}
        onOk={onSubmit}
      >
        <ProForm formRef={formRef} preserve={false} submitter={false} layout="vertical" grid>
          <ProForm.Group
            rowProps={{
              gutter: 24
            }}
          >
            <ProFormDigit
              colProps={{
                xs: 24,
                sm: 12
              }}
              label="日承载量"
              name="totalServiceNum"
              min={1}
              rules={[
                {
                  required: true
                },
                {
                  type: "number",
                  max: 9999999,
                  message: "日承载量不能超过9,999,999"
                }
              ]}
              fieldProps={{ precision: 0 }}
            />
            <ProFormDigit
              colProps={{
                xs: 24,
                sm: 12
              }}
              label="单次最大库存发行数量"
              name="totalSalesNum"
              min={1}
              rules={[
                {
                  required: true
                },
                {
                  type: "number",
                  max: 99999999,
                  message: "单次库存最大发行量不能超过99,999,999"
                }
              ]}
              fieldProps={{ precision: 0 }}
            />
            <ProFormDigit
              colProps={{
                xs: 24,
                sm: 12
              }}
              label="近五年平均游客量"
              name="averageTouristNum"
              rules={[
                {
                  required: true
                },
                {
                  type: "number",
                  min: 1,
                  message: "只能输入正整数"
                }
              ]}
              fieldProps={{ precision: 0 }}
            />
            {scenicInfo?.isBlockChain == 1 ? (
              <ProFormDigit
                colProps={{
                  xs: 24,
                  sm: 12
                }}
                label="交易所发行比例"
                name="exchangePublishRate"
                rules={[
                  {
                    required: true
                  },
                  {
                    type: "number",
                    min: 0,
                    message: "只能输入0-100"
                  },
                  {
                    type: "number",
                    max: 100,
                    message: "只能输入0-100"
                  }
                ]}
                fieldProps={{ precision: 0, addonAfter: "%" }}
              />
            ) : null}

            <ProFormText.Password
              colProps={{
                xs: 24,
                sm: 12
              }}
              name="checkEquipmentKey"
              label="检票设备密码"
              rules={[
                {
                  required: true
                },
                numberValidator(),
                stringLengthValidator(6, 6)
              ]}
              fieldProps={{
                autoComplete: "new-password"
              }}
            />
          </ProForm.Group>
        </ProForm>
      </Modal>
    </Card>
  );
};

export default ParamsConfig;
