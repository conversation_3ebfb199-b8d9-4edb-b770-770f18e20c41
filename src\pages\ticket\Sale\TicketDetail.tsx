/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-09-08 10:43:18
 * @LastEditTime: 2023-10-20 14:56:10
 * @LastEditors: zhang<PERSON><PERSON>i
 */
import DetailsPop from "@/common/components/DetailsPop";
import { productTypeEnum, ticketStatusEnum, ticketTypeEnum, whetherEnum } from "@/common/utils/enum";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import DataMask from "@/components/DataMask";
import type { ProDescriptionsGroup } from "@/components/ModalDescriptions";
import useMask from "@/hooks/useMask";
import type { ModalState } from "@/hooks/useModal";
import { getOrderTicketCheckDetail } from "@/services/api/ticket";
import { Modal, Table } from "antd";
import dayjs from "dayjs";
import QRCode from "qrcode.react";
import type { FC } from "react";
import { useEffect } from "react";
import { useRequest } from "@umijs/max";

const showQRCode = (text: string) => {
  Modal.info({
    icon: null,
    maskClosable: true,
    content: <QRCode value={text} size={350} fgColor="#000000" />,
    okButtonProps: {
      style: {
        display: "none"
      }
    }
  });
};

interface TicketDetailProps {
  modalState: ModalState;
  ticketId?: string;
}

const TicketDetail: FC<TicketDetailProps> = ({ modalState: { visible, setVisible }, ticketId }) => {
  // 查看票详情
  const getTicketInfoReq = useRequest(getOrderTicketCheckDetail, {
    manual: true,
    onSuccess: data => {
      addOperationLogRequest({
        action: "info",
        content: `查看【${data.id}】门票销售详情`
      });
    }
  });

  const ticketInfoColumns: ProDescriptionsGroup<Ticket.TicketInfo>[] = [
    {
      title: "基础信息",
      columns: [
        {
          title: "票号",
          dataIndex: "id"
        },
        {
          title: "服务商名称",
          dataIndex: "providerName"
        },
        {
          title: "景区名称",
          dataIndex: "scenicName"
        },
        {
          title: "状态",
          dataIndex: "status",
          renderText: dom => ticketStatusEnum[dom]
        },
        {
          title: "产品名称",
          dataIndex: "proName"
        },
        {
          title: "产品类型",
          dataIndex: "proType",
          renderText: dom => productTypeEnum[dom]
        },
        {
          title: "商品名称",
          dataIndex: "goodsName"
        },
        {
          title: "票种",
          dataIndex: "goodsType",
          renderText: dom => ticketTypeEnum[dom]
        },
        {
          title: "数字资产",
          dataIndex: "isChainTicket",
          renderText: dom => whetherEnum[dom]
        },

        {
          title: "入园时间",
          dataIndex: "enterDate"
        },
        {
          title: "订单号",
          dataIndex: "orderId"
        },
        {
          title: "出票时间",
          dataIndex: "createTime",
          renderText: dom => dayjs(dom).format("YYYY-MM-DD HH:mm:ss")
        },

        {
          title: "人数",
          dataIndex: "playerNum"
        },
        {
          title: "有效时长天数（天）",
          dataIndex: "validityDay",
          renderText: text => `${text} 天`
        },
        {
          title: "是否首日激活",
          dataIndex: "isActivate",
          valueEnum: {
            0: "否",
            1: "是"
          }
        },
        {
          title: "可入园天数",
          dataIndex: "availableDays",
          renderText: text => `${text} 天`
        },
        {
          title: "使用次数",
          dataIndex: "useCount",
          renderText: (_, { useType = 0, useCount = 0 }) => {
            const useTypeArr = ["每天", "一共"];
            return `${useTypeArr[useType]}${useCount || "无限"}次`;
          }
        }
      ]
    },
    {
      title: "游客信息",
      className: "no-bgColor",
      columns: [
        {
          span: 2,
          renderText: () => {
            return (
              <Table
                bordered
                size="small"
                style={{ width: "100%" }}
                columns={childColumns}
                pagination={false}
                dataSource={getTicketInfoReq.data?.realNameList || []}
              />
            );
          }
        }
      ]
    },
    {
      title: "二维码",
      className: "no-bgColor",
      columns: [
        {
          dataIndex: "printStr",
          renderText: (text, { enterWay }) => {
            if (enterWay?.includes("票")) {
              return (
                <QRCode
                  onClick={() => {
                    showQRCode(text || "");
                  }}
                  style={{ cursor: "pointer" }}
                  value={text || ""}
                  size={100}
                  fgColor="#000000"
                />
              );
            }
            return "-";
          }
        }
      ]
    }
  ];
  const [handleDetailsMaskChange, maskDetailsDataFn] = useMask();

  const childColumns = [
    {
      title: "姓名",
      dataIndex: "idCardName",
      width: "50%",
      render: text => maskDetailsDataFn(text)
    },
    {
      title: "身份证",
      dataIndex: "idCardNumber",
      width: "50%",
      render: text => maskDetailsDataFn(text)
    }
  ];

  useEffect(() => {
    if (visible && ticketId) {
      getTicketInfoReq.run(ticketId);
    }
  }, [ticketId, visible]);

  return (
    <DetailsPop
      title={
        <>
          <span>门票详情</span>
          <DataMask onDataMaskChange={handleDetailsMaskChange} logContent="查看【门票详情】用户隐私信息" />
        </>
      }
      visible={visible}
      setVisible={setVisible}
      columnsInitial={ticketInfoColumns}
      isLoading={getTicketInfoReq.loading}
      dataSource={getTicketInfoReq.data}
    />
  );
};

export default TicketDetail;
