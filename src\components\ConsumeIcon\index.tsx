/*
 * @Author: z<PERSON><PERSON>fei
 * @Date: 2023-08-15 14:38:59
 * @LastEditTime: 2023-08-15 18:46:24
 * @LastEditors: zhangfengfei
 */

import data from '@/assets/imgs/data_link.png';
import exchange from '@/assets/imgs/exchange_link.png';
import settle from '@/assets/imgs/settle_link.png';
import trading from '@/assets/imgs/trading_link.png';
import approve from '@/assets/svg/approve.svg';
import warning from '@/assets/svg/warning.svg';

import type { FC } from 'react';
import React from 'react';

const iconMap = {
  approve,
  warning,
  settle,
  trading,
  exchange,
  data,
};

type ConsumeIconProps = {
  name: keyof typeof iconMap;
} & React.ImgHTMLAttributes<HTMLImageElement>;

const ConsumeIcon: FC<ConsumeIconProps> = ({ name, ...otherProps }) => {
  let content = null;

  if (name) {
    // content = <Icon component={iconMap[name]} {...otherProps} />;
    content = <img src={iconMap[name]} {...otherProps} />;
  }

  return content;
};

export default ConsumeIcon;
