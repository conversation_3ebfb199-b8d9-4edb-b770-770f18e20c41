/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-11-01 16:38:34
 * @LastEditTime: 2022-12-05 10:39:20
 * @LastEditors: z<PERSON><PERSON><PERSON>i
 */
import { Result, Space } from "antd";
import { parse } from "querystring";
import { history } from "@umijs/max";

const ApplicationApproved = () => {
  const { name } = parse(history.location.hash.split("?")[1]) || {};
  const text = <span style={{ fontWeight: "bolder", color: "black" }}>加入{name}</span>;
  return (
    <>
      <Result
        status="success"
        title="申请成功"
        // subTitle={`你已经完成${aaa}的申请。管理员审批通过后，你将收到短信通知，敬请留意`}
        subTitle={<Space>你已经完成{text}的申请。管理员审批通过后，你将收到短信通知，敬请留意。</Space>}
        // extra={[
        //   <Button type="primary" key="console">
        //     Go Console
        //   </Button>,
        //   <Button key="buy">Buy Again</Button>,
        // ]}
      />
    </>
  );
};

export default ApplicationApproved;
