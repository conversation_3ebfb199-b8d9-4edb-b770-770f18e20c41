import Disabled from "@/common/components/Disabled";
import { tableConfig } from "@/common/utils/config";
import { modelWidth } from "@/common/utils/gConfig";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import { getUniqueId } from "@/common/utils/tool";
import ProForm, { ModalForm, ProFormText } from "@ant-design/pro-form";
import ProTable from "@ant-design/pro-table";
import { Button, Modal, message } from "antd";
import { useRef, useState } from "react";
import { Access, useAccess, useModel } from "@umijs/max";
import { apiControlConf, apiPostScenicPerson, apiScenicControlList, apiScenicPerson } from "../../../services/api/erp";

const { confirm } = Modal;

const logList = [
  {
    title: "姓名",
    dataIndex: "name"
  },
  {
    title: "手机号",
    dataIndex: "phone"
  }
];

const RiskContro = () => {
  const { initialState } = useModel("@@initialState");
  const { scenicId, uniqueIdentity } = initialState?.scenicInfo;

  const [modalVisit, setModalVisit] = useState(false);

  const [formObj] = ProForm.useForm();
  const actionRef = useRef();
  const access = useAccess();

  //获取删除列表
  const getList = async () => {
    try {
      const { data } = await apiScenicControlList(scenicId);
      console.log("datadtatqaaaa", data);
      return {
        success: true,
        data: data
      };
    } catch (e) {
      console.error(e);
    }
  };

  const [infoData, setInfoData] = useState<Record<string, any>>();
  //查询复核员信息
  const onChecker = async () => {
    try {
      const { data } = await apiScenicPerson(scenicId);
      formObj.setFieldsValue(data);
      setInfoData(data);
    } catch (e) {
      console.error(e);
    }
    setModalVisit(true);
  };

  //编辑复核员
  const onSubmit = async val => {
    const { name, phone } = val;
    // localStorage.setItem('users',JSON.stringify({name:name,phone:phone}))
    const pars = {
      businessId: scenicId,
      name,
      phone
    };
    try {
      await apiPostScenicPerson(pars);
      addOperationLogRequest({
        action: "edit",
        changeConfig: {
          list: logList,
          afterData: pars,
          beforeData: infoData
        },

        content: `编辑【${name}】复核员信息`
      });
      message.success("编辑成功");
    } catch (e) {
      console.error(e);
    }
  };

  const editColumns = [
    {
      title: "操作类型",
      key: "operateType",
      dataIndex: "operateType",
      width: "10%",
      hideInSearch: true
      // render: (dom: any, record: any) => {
      //   return (
      //     <span>
      //       删除
      //     </span>
      //   );
      // },
    },
    {
      title: "操作菜单",
      key: "operateMenu",
      dataIndex: "operateMenu",
      width: "25%",
      hideInSearch: true
      // render: (dom: any) => <div>{['否', '是'][dom]}</div>,
    },
    {
      title: "操作内容",
      key: "operateContent",
      dataIndex: "operateContent",
      hideInSearch: true
    },
    {
      title: "验证码审核",
      key: "openGateQueue",
      dataIndex: "isEnable",
      hideInSearch: true,
      render: (_: any, entity: any) => (
        <Disabled
          access={access.canRiskManagement_openClose}
          status={entity.isEnable == 1}
          params={{
            controlConfId: entity.controlConfId,
            id: scenicId,
            isEnable: 1 - entity.isEnable
          }}
          request={async params => {
            const data = await apiControlConf(params);

            addOperationLogRequest({
              action: "disable",

              content: `${entity.isEnable == 1 ? "禁用" : "启用"}【${entity.operateContent}】风控`
            });

            return data;
          }}
          actionRef={actionRef}
        />
      )
    }
  ];

  return (
    <>
      <ProTable
        {...tableConfig}
        rowClassName={(row: any) => (row.isEnable == 1 ? "" : "disableRow")}
        actionRef={actionRef}
        params={{ id: scenicId }}
        request={getList}
        rowKey="controlConfId"
        columns={editColumns}
        search={false}
        toolBarRender={() => {
          return [
            <Access key={getUniqueId()} accessible={access.canRiskManagement_accountant}>
              <Button type="primary" key="save" onClick={onChecker}>
                复核员信息
              </Button>
            </Access>
          ];
        }}
      />
      <ModalForm
        title="复核员信息"
        visible={modalVisit}
        form={formObj}
        width={modelWidth.md - 200}
        onFinish={async val => {
          onSubmit(val);
          return true;
        }}
        onVisibleChange={setModalVisit}
        submitter={{
          searchConfig: { submitText: "保存" }
        }}
        grid
      >
        <ProForm.Group>
          <ProFormText
            label="姓名"
            name="name"
            placeholder="请输入姓名"
            rules={[
              {
                required: true,
                message: "请输入姓名"
              }
            ]}
          />
          <ProFormText
            label="手机号码"
            name="phone"
            placeholder="请输入手机号码"
            rules={[
              {
                required: true,
                message: "请输入手机号码"
              },
              {
                pattern: /^[1][3,4,5,6,7,8,9][0-9]{9}$/,
                message: "不符合手机号格式"
              }
            ]}
          />
        </ProForm.Group>
      </ModalForm>
    </>
  );
};
export default RiskContro;
