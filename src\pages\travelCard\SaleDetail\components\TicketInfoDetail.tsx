/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-04-11 17:05:13
 * @LastEditTime: 2023-04-12 11:17:23
 * @LastEditors: z<PERSON><PERSON><PERSON>i
 */

import DetailsPop from "@/common/components/DetailsPop";
import { productTypeEnum, ticketStatusEnum, ticketTypeEnum } from "@/common/utils/enum";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import DataMask from "@/components/DataMask";
import type { ProDescriptionsGroup } from "@/components/ModalDescriptions";
import useMask from "@/hooks/useMask";
import type { ModalState } from "@/hooks/useModal";
import { getOrderTicketCheckDetail } from "@/services/api/ticket";
import { Modal } from "antd";
import dayjs from "dayjs";
import QRCode from "qrcode.react";
import type { FC } from "react";
import { useEffect } from "react";
import { useRequest } from "@umijs/max";

type TicketInfoDetailProps = ModalState & {
  ticketId?: string;
};

/**
 * @description: 门票详情
 */
const TicketInfoDetail: FC<TicketInfoDetailProps> = ({ visible, setVisible, ticketId }) => {
  const getTicketInfoReq = useRequest(getOrderTicketCheckDetail, {
    manual: true,
    onSuccess(data, params) {
      addOperationLogRequest({
        action: "info",
        content: `查看【${data.orderId}】权益票订单详情`
      });
    }
  });

  const [handleDetailsMaskChange, maskDetailsDataFn] = useMask();

  const showQRCode = (text: string) => {
    Modal.info({
      icon: null,
      maskClosable: true,
      content: <QRCode value={text} size={350} fgColor="#000000" />,
      okButtonProps: {
        style: {
          display: "none"
        }
      }
    });
  };

  const ticketInfoColumns: ProDescriptionsGroup<API.OrderTicketInfo>[] = [
    {
      title: "基础信息",
      columns: [
        {
          title: "票号",
          dataIndex: "id"
        },
        {
          title: "服务商名称",
          dataIndex: "providerName"
        },
        {
          title: "产品名称",
          dataIndex: "proName"
        },
        {
          title: "产品类型",
          dataIndex: "proType",
          renderText: dom => productTypeEnum[dom]
        },
        {
          title: "商品名称",
          dataIndex: "goodsName"
        },
        {
          title: "票种",
          dataIndex: "goodsType",
          renderText: dom => ticketTypeEnum[dom]
        },

        {
          title: "联系人姓名",
          dataIndex: "pilotName",
          renderText: text => maskDetailsDataFn(text)
        },
        {
          title: "联系人身份证号",
          dataIndex: "pilotIdentity",
          renderText: text => maskDetailsDataFn(text)
        },
        {
          title: "入园时间",
          dataIndex: "enterDate"
        },
        {
          title: "状态",
          dataIndex: "status",
          renderText: dom => ticketStatusEnum[dom]
        },

        {
          title: "出票时间",
          dataIndex: "createTime",
          renderText: dom => dayjs(dom).format("YYYY-MM-DD HH:mm:ss")
        },
        {
          title: "订单号",
          dataIndex: "orderId"
        },
        {
          title: "二维码",
          dataIndex: "printStr",
          renderText: text => (
            <div
              onClick={() => {
                showQRCode(text);
              }}
              style={{ cursor: "pointer" }}
            >
              <QRCode value={text} size={100} fgColor="#000000" />
            </div>
          )
        },
        {
          title: "景区名称",
          dataIndex: "scenicName"
        }
      ]
    }
  ];

  useEffect(() => {
    if (visible && ticketId) {
      getTicketInfoReq.run(ticketId);
    }
  }, [ticketId, visible]);

  return (
    <DetailsPop
      title={
        <>
          <span>门票详情</span>
          <DataMask onDataMaskChange={handleDetailsMaskChange} logContent="查看【权益票门票详情】用户隐私信息" />
        </>
      }
      visible={visible}
      setVisible={setVisible}
      columnsInitial={ticketInfoColumns}
      isLoading={getTicketInfoReq.loading}
      dataSource={getTicketInfoReq.data}
    />
  );
};

export default TicketInfoDetail;
