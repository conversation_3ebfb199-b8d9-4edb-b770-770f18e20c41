/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-08-31 10:36:24
 * @LastEditTime: 2023-02-21 17:54:22
 * @LastEditors: zhangfengfei
 */
import { modelWidth } from "@/common/utils/gConfig";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import { addChargeApproval, getServiceChargeInfo } from "@/services/api/ticket";
import { FormOutlined } from "@ant-design/icons";
import type { ProFormInstance } from "@ant-design/pro-form";
import ProForm, { ProFormDigit } from "@ant-design/pro-form";
import { Modal, Popconfirm, message } from "antd";
import { ceil, isNil, round } from "lodash";
import type { FC } from "react";
import { useMemo, useRef, useState } from "react";
import { useModel, useRequest } from "@umijs/max";

const approvalStateMap = {
  1: "通过",
  2: "拒绝"
};
interface UpdateContentItem {
  key: string;
  front: number;
  after: number;
}

interface EditChargeRatePopProps {
  currentItem: Record<string, any>;
  // 1 权益票 2 旅游卡
  dataSourcesType: 1 | 2;
  marketPrice: number;
}

const EditChargeRatePop: FC<EditChargeRatePopProps> = ({ dataSourcesType, marketPrice, currentItem }) => {
  const { id, travelCardName: goodsName, name } = currentItem;
  const { scenicInfo, userInfo } = useModel("@@initialState").initialState || {};
  const { username = "" } = userInfo || {};
  const { scenicName = "" } = scenicInfo || {};
  const formRef = useRef<ProFormInstance<any>>();

  const [modalVisible, setModalVisible] = useState(false);

  const [disabled, setDisabled] = useState(true);

  // 审批状态请求
  const serviceChargeInfoReq = useRequest(getServiceChargeInfo, {
    manual: true,
    formatResult(res) {
      // 10001 为正常返回 表未提交过审批
      const isSuccess = res.code === 10001 || res.code === 20000 || res.code === 200;
      if (!isSuccess) {
        message.warning(res.msg);
        return res.data;
      } else {
        const arr = JSON.parse(res.data?.updateContent || "[]") as UpdateContentItem[];
        return {
          ...(res.data || {}),
          serviceChargeRate: arr.find(item => item.key === "serviceChargeRate")?.after,
          serviceCharge: arr.find(item => item.key === "serviceCharge")?.after
        };
      }
    },
    onSuccess(data, params) {
      // 正确的返回
      formRef.current?.setFieldsValue(data || {});
      if (!isNil(data)) {
        const { approvalType = 0 } = data;
        if (approvalType !== 1) {
          setDisabled(false);
        }
      } else {
        setDisabled(false);
      }
    },
    onError(res) {
      const isSuccess = res?.data?.code === 10001 || res?.data?.code === 20000 || res?.data?.code === 200;
      if (isSuccess && isNil(res?.data?.data)) {
        setDisabled(false);
      }
    }
  });

  // 新增审批
  const addChargeApprovalReq = useRequest(addChargeApproval, {
    manual: true,
    onSuccess(data, params) {
      message.success("提交成功");
      addOperationLogRequest({
        action: "edit",
        content: `编辑【${params[0].goodsName}】发行服务费`
      });

      setModalVisible(false);
    }
  });

  const onValuesChange = ({ serviceChargeRate, serviceCharge }: any) => {
    // 值联动
    if (!!serviceChargeRate) {
      formRef.current?.setFieldsValue({
        serviceCharge: round((marketPrice * serviceChargeRate) / 100, 2)
      });
    }
    if (!!serviceCharge) {
      formRef.current?.setFieldsValue({
        serviceChargeRate: marketPrice ? round((serviceCharge * 100) / marketPrice, 0) : 0
      });
    }
  };

  const onConfirm = async () => {
    const { serviceChargeRate } = await formRef.current?.validateFields();
    const isValid = serviceChargeRate <= 100 && serviceChargeRate >= 0 && !isNil(serviceChargeRate);
    if (isValid) {
      setModalVisible(true);
    } else {
      message.warning("请设置合理的发行服务费");
    }
  };
  // 审批状态
  const approvalStatus = useMemo(() => {
    const { approvalState = 0, approvalType = 0 } = serviceChargeInfoReq.data || {};
    // 已审批
    if (approvalType === 2) {
      return approvalStateMap[approvalState] || "错误";
    } else if (approvalType === 1) {
      return "审批中";
    }
    // 未提交
    return "无";
  }, [serviceChargeInfoReq.loading]);

  const onSubmit = () => {
    const { serviceChargeRate, serviceCharge } = formRef.current?.getFieldsValue();
    const newUpdateContent = [
      {
        key: "marketPrice",
        front: marketPrice,
        after: null
      },
      {
        key: "serviceChargeRate",
        front: currentItem.serviceChargeRate ?? null,
        after: serviceChargeRate
      },
      {
        key: "serviceCharge",
        front: ceil((marketPrice * currentItem.serviceChargeRate) / 100, 2) ?? null,
        after: null
      }
    ];
    addChargeApprovalReq.run({
      dataSourcesType,
      createUserName: username,
      goodsId: id,
      goodsName,
      productName: name,
      scenicName,
      serviceChargeRate,
      serviceCharge,
      marketPrice,
      updateContent: JSON.stringify(newUpdateContent)
    });
  };
  return (
    <>
      <Popconfirm
        title={
          <>
            <div>说明：此处可对该票的发行服务费进行设置</div>
            <div style={{ color: "red" }}>请依照合同规定，谨慎填写</div>
            <ProForm
              layout="horizontal"
              formRef={formRef}
              submitter={false}
              preserve={false}
              onValuesChange={onValuesChange}
            >
              <ProForm.Group size="small">
                <ProFormDigit
                  min={0}
                  max={100}
                  width={80}
                  label="编辑发行服务费"
                  name="serviceChargeRate"
                  addonAfter="%"
                  formItemProps={{
                    style: {
                      marginBottom: 0
                    }
                  }}
                />

                <ProFormDigit
                  addonBefore="≈"
                  addonAfter="元"
                  name="serviceCharge"
                  min={0}
                  width={80}
                  formItemProps={{
                    style: {
                      marginBottom: 0
                    }
                  }}
                />
              </ProForm.Group>
            </ProForm>
            <div>技术服务商审批状态：{approvalStatus}</div>
          </>
        }
        okButtonProps={{
          disabled
        }}
        onConfirm={onConfirm}
        onOpenChange={open => {
          if (open) {
            formRef.current?.resetFields();
            setDisabled(true);
            serviceChargeInfoReq.run({ id });
          }
        }}
      >
        <FormOutlined style={{ color: "#1890ff" }} />
      </Popconfirm>

      <Modal
        title="提示"
        visible={modalVisible}
        width={modelWidth.sm}
        onCancel={() => {
          setModalVisible(false);
        }}
        onOk={onSubmit}
        okButtonProps={{
          loading: addChargeApprovalReq.loading
        }}
        closable
      >
        编辑发行服务费需走审批流程，技术服务方审批通过后才可生效
      </Modal>
    </>
  );
};

export default EditChargeRatePop;
