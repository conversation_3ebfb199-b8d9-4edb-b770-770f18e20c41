import { tableConfig } from '@/common/utils/config';
import {
  ArticleType,
  ServiceGrade,
  StoreGoodsTypeEnum,
  TicketTypeEnum,
  whetherEnum,
} from '@/common/utils/enum';
import { modelWidth } from '@/common/utils/gConfig';
import { getStoreGoodsTypeText, toValueEnum } from '@/common/utils/tool';
import { pageArticle } from '@/services/api/article';
import { apiGetRenovateList, apiGetScenicDetailList } from '@/services/api/erp';
import { PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Button, Modal, Space, Tag, Typography } from 'antd';
import type { ModalProps } from 'antd/lib/modal';
import { useRef } from 'react';
import { AddPageUrlEnum, UrlNavType } from './datas';
import styles from './index.module.less';
import { getHelpDetailListRequest } from './index.service';
import type { ModalStateProps } from './interface';

interface Props extends ModalProps {
  modalState: ModalStateProps;
  setModalState: any;
}

const UrlNameModal = ({ modalState, setModalState, ...lastProps }: Props) => {
  const { initialState } = useModel('@@initialState');
  const { scenicId } = initialState?.scenicInfo || {};
  const actionRef = useRef<ActionType>();

  // 活动页面
  const activityColumns: ProColumns[] = [
    {
      title: '页面名称',
      dataIndex: 'pageName',
      width: '65%',
      render: (dom: any, entity: any) => (
        <Space>
          {dom}
          {entity.homePageState == 2 && <Tag color="blue">主页</Tag>}
        </Space>
      ),
    },
    {
      title: '页面状态',
      dataIndex: 'pageState',
      width: '35%',
      valueEnum: { 1: '未发布', 2: '已发布' },
      search: false,
    },
  ];

  // 景区详情页
  const scenicDetailColumns: ProColumns[] = [
    {
      title: '景区名称',
      dataIndex: 'scenicName',
      width: '65%',
    },
    {
      title: '景区等级',
      dataIndex: 'scenicGradle',
      width: '35%',
      valueType: 'select',
      valueEnum: ServiceGrade,
      search: false,
    },
  ];
  // 商品详情页
  const goodsDetailColumns: ProColumns[] = [
    {
      title: '景区名称',
      dataIndex: 'scenicName',
    },
    {
      title: '商品名称',
      dataIndex: 'goodsName',
    },
    {
      title: '类别',
      dataIndex: 'storeGoodsType',
      valueEnum: toValueEnum(StoreGoodsTypeEnum),
    },
    {
      title: '票种',
      dataIndex: 'goodsType',
      valueEnum: whetherEnum,
      render: (text: any, { unitType, storeGoodsType, goodsType }) => {
        if (storeGoodsType === 2) {
          return text
            .split(',')
            .map((i: any) => TicketTypeEnum[i])
            .join('，');
        }
        return getStoreGoodsTypeText(unitType, storeGoodsType, goodsType);
      },
      search: false,
    },
    {
      title: '数字资产',
      valueType: 'select',
      dataIndex: 'isDigit',
      valueEnum: whetherEnum,
      search: false,
    },
    {
      title: '购买有效时间',
      dataIndex: 'purchaseTime',
      hideInSearch: true,
      render: (dom: any, record: any) =>
        record.purchaseBeginTime ? (
          <span>
            {record.purchaseBeginTime} 至 {record.purchaseEndTime}
          </span>
        ) : (
          '-'
        ),
    },
    {
      title: '入园有效时间',
      dataIndex: 'time',
      hideInSearch: true,
      render: (dom: any, record: any) =>
        record.dayBegin ? (
          <span>
            {record.dayBegin} 至 {record.dayEnd}
          </span>
        ) : (
          '-'
        ),
    },
    {
      title: '分时预约时间',
      search: false,
      editable: false,
      render: (_, entity: any) =>
        entity.timeShareBeginTime && entity.timeShareEndTime
          ? entity.timeShareBeginTime + ' - ' + entity.timeShareEndTime
          : '-',
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierNames',
      search: false,
    },
    {
      title: '可用库存',
      dataIndex: 'quantity',
      hideInSearch: true,
      align: 'right',
      valueType: 'digit',
    },
    {
      title: '用户售价（元）',
      dataIndex: 'sellingPrice',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
      valueType: 'digit',
      hideInSearch: true,
    },
  ];
  // 文章详情页
  const articleDetailColumns: ProColumns[] = [
    {
      title: '文章名称',
      dataIndex: 'articleName',
      width: '65%',
      renderText: (text: string) => (
        <Typography.Text style={{ maxWidth: 200 }} ellipsis={true}>
          {text}
        </Typography.Text>
      ),
    },
    {
      title: '文章类型',
      dataIndex: 'articleType',
      width: '35%',
      valueEnum: ArticleType,
    },
  ];
  // 帮助中心详情页
  const helpDetailColumns: ProColumns[] = [
    {
      title: '文章名称',
      dataIndex: 'title',
      width: '65%',
    },
    {
      title: '发布时间',
      dataIndex: 'publishTime',
      width: '35%',
      search: false,
    },
  ];

  const columnsEnum = {
    // 活动页面
    [UrlNavType.activity]: {
      title: '活动页',
      columns: activityColumns,
      api: apiGetRenovateList,
      params: {
        businessId: scenicId,
        pageState: '2', // 已发布
      },
      navFields: {
        pageId: 'id',
      },
      nameField: 'pageName',
      rowKey: 'id',
    },
    // 景区详情页
    [UrlNavType.scenicDetail]: {
      title: '景区',
      columns: scenicDetailColumns,
      api: apiGetScenicDetailList,
      params: {
        scenicId,
      },
      navFields: {
        sId: 'scenicId',
      },
      nameField: 'scenicName',
      rowKey: 'scenicId',
    },
    // 文章详情页
    [UrlNavType.articleDetail]: {
      title: '文章',
      columns: articleDetailColumns,
      api: pageArticle,
      params: {
        scenicId,
        enableState: '2', // 启用
      },
      navFields: {
        aId: 'id',
      },
      nameField: 'articleName',
      rowKey: 'id',
    },
    // 帮助中心详情页
    [UrlNavType.helpDetail]: {
      title: '帮助中心文章',
      columns: helpDetailColumns,
      api: getHelpDetailListRequest,
      params: {
        scenicId,
        enableState: 2, // 1禁用 2启用
      },
      navFields: {
        hId: 'id',
      },
      nameField: 'title',
      rowKey: 'id',
    },
  };

  const onRowSelectChange = (keys: any, record: any) => {
    setModalState((prev) => {
      const currentColumns: any = columnsEnum[modalState.type];
      const _navigationUrlObj: any = {
        type: modalState.type,
        params: {},
      };
      Object.keys(currentColumns.navFields)?.forEach((key) => {
        const valKey = currentColumns.navFields?.[key];
        _navigationUrlObj.params[key] = record?.[0]?.[valKey] || '';
      });
      const showNavName = record?.[0]?.[currentColumns?.nameField];

      return {
        ...prev,
        navigationUrl: JSON.stringify(_navigationUrlObj),
        selectRowKeys: keys?.[0] || '',
        selectRowRecord: {
          ...record?.[0],
          showNavName,
        },
      };
    });
  };

  // 新增
  const onAdd = () => {
    const _url = AddPageUrlEnum[modalState.type];
    window.open(_url, '_blank');
  };

  // 获取弹窗open
  const getOpen = () => {
    return [
      UrlNavType.scenicDetail,
      UrlNavType.activity,
      UrlNavType.articleDetail,
      UrlNavType.helpDetail,
    ].includes(modalState.type);
  };

  return (
    <Modal
      title={`添加${columnsEnum[modalState.type]?.title}`}
      open={getOpen()}
      width={modelWidth.lg}
      destroyOnClose
      {...lastProps}
    >
      <ProTable
        {...tableConfig}
        type="table"
        tableLayout="fixed"
        rowKey={columnsEnum[modalState.type]?.rowKey || 'id'}
        columns={columnsEnum[modalState.type]?.columns || []}
        params={columnsEnum[modalState.type]?.params || {}}
        request={columnsEnum[modalState.type]?.api}
        toolBarRender={() => [
          [UrlNavType.articleDetail, UrlNavType.helpDetail]?.includes(modalState.type) ? (
            <Button key="k1" type="primary" onClick={onAdd}>
              <PlusOutlined /> 新增
            </Button>
          ) : (
            <></>
          ),
        ]}
        actionRef={actionRef}
        pagination={{
          pageSizeOptions: [10, 20, 50, 100],
          defaultPageSize: 10,
        }}
        rowSelection={{
          type: 'radio',
          onChange: onRowSelectChange,
        }}
        className={styles.modalTable}
      />
    </Modal>
  );
};

export default UrlNameModal;
