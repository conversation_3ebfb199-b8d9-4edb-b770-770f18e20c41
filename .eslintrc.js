module.exports = {
  extends: [require.resolve("@umijs/fabric/dist/eslint")],

  // in antd-design-pro
  globals: {
    ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION: true,
    page: true,
    PROXY_ENV: true
  },

  rules: {
    // your rules
    "no-unused-vars": "off",
    "@typescript-eslint/no-unused-vars": "off",
    "no-shadow": "off",
    "@typescript-eslint/no-shadow": "off",
    "no-use-before-define": "off",
    "@typescript-eslint/no-use-before-define": "off"
  }
};
