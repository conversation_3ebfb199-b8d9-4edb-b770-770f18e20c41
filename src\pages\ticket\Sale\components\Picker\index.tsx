/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-10-17 17:34:24
 * @LastEditTime: 2023-10-25 11:16:59
 * @LastEditors: zhangfeng<PERSON>i
 */
import { DatePicker } from "antd";
import type { Dayjs } from "dayjs";
import dayjs from "dayjs";
import type { FC } from "react";
import { useEffect } from "react";
import styles from "./index.less";

interface FCNameProps {
  checkInfo: Ticket.AvailableCheckItem;
  ticket: Ticket.SaleTicketItem;
  date: Dayjs;
  setDate: React.Dispatch<React.SetStateAction<dayjs.Dayjs>>;
}

const FCName: FC<FCNameProps> = ({ checkInfo, ticket, setDate, date }) => {
  const { enterTime, validityDay, availableDays, playerNum, useType, useCount } = ticket;

  function isDateInRange(date: Dayjs) {
    return dayjs(enterTime) <= date && dayjs(enterTime).add(validityDay - 1, "days") >= date;
  }

  function isOverUsedCount(current: Dayjs) {
    // 无限次
    if (useCount === 0) {
      return false;
    }

    // '每天' 0    '一共' 1
    if (useType == 1) {
      const sum = (checkInfo.useList || []).reduce((pre, next) => pre + next.usedCount, 0);
      return sum >= useCount;
    } else {
      const checkItem = checkInfo.useList?.find(
        i => dayjs(i.useDate).format("YYYY-MM-DD") === current.format("YYYY-MM-DD")
      );
      return (checkItem?.usedCount ?? 0) >= playerNum * useCount;
    }
  }

  useEffect(() => {
    // 选第一个不被禁选的日期
    for (let index = 0; index < availableDays; index++) {
      const current = dayjs(enterTime).add(index, "days");
      if (!isOverUsedCount(current)) {
        console.log(current);
        setDate(current);
        break;
      }
    }
  }, []);

  return (
    <DatePicker
      showToday={false}
      popupClassName={styles.picker}
      value={date}
      dateRender={current => {
        const useInfo = checkInfo.useList?.find(
          i => dayjs(i.useDate).format("YYYY-MM-DD") === current.format("YYYY-MM-DD")
        );
        return (
          <div
            className="ant-picker-cell-inner"
            style={{
              width: 100,
              height: 60,
              margin: 4
            }}
          >
            <div>{current.date()}</div>
            <div>{isDateInRange(current) && <span>核销次数：{useInfo?.usedCount ?? 0}</span>}</div>
          </div>
        );
      }}
      allowClear={false}
      onChange={val => {
        if (val) {
          setDate(val);
        }
      }}
      disabledDate={current => {
        // 不在时间范围内  超出核销次数
        if (!isDateInRange(current) || isOverUsedCount(current)) {
          return true;
        }

        return false;
      }}
    />
  );
};

export default FCName;
