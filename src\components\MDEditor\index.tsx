/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-07-05 16:07:28
 * @LastEditTime: 2022-11-18 15:16:14
 * @LastEditors: zhangfengfei
 */
import { uploadFile } from "@/services/api/file";
import gfm from "@bytemd/plugin-gfm";
import zh_<PERSON>_ from "@bytemd/plugin-gfm/locales/zh_Hans.json";
import { Editor, Viewer } from "@bytemd/react";
import "bytemd/dist/index.css";
import zh_Hans from "bytemd/locales/zh_Hans.json";
import "github-markdown-css/github-markdown.css";
import type { FC } from "react";
import styles from "./index.less";
import { getEnv } from "@/common/utils/getEnv";

const plugins = [
  gfm({ locale: zh_Hans_ })
  // 其他插件
];

//
interface MDEditorProps {
  value?: string;
  onChange?: (value: string) => void;
  /**只读模式 value 必传 */
  readonly?: boolean;
  maxLength?: number;
}

/**
 * @params 如被 FormItem 嵌套，value 和 onChange 无需再定义
 * @description  markdown 编辑器
 * @see https://github.com/bytedance/bytemd
 *
 */
const MDEditor: FC<MDEditorProps> = ({ value = "", onChange, maxLength, readonly }) => {
  if (readonly) {
    return <Viewer value={value} plugins={plugins} />;
  }

  return (
    <div className={styles.editor}>
      <Editor
        // previewDebounce={300}
        mode="split"
        maxLength={maxLength}
        value={value}
        plugins={plugins}
        locale={zh_Hans}
        onChange={v => {
          if (onChange) {
            onChange(v);
          }
        }}
        // 图片上传 支持多选
        uploadImages={async (files: File[]) => {
          console.log(files);

          try {
            const data = await uploadFile(files);
            return data.map(item => ({
              alt: item.name,
              url: `${getEnv().IMG_HOST}${item.path}`
            }));
          } catch (error) {
            return [];
          }
        }}
      />
    </div>
  );
};
export default MDEditor;
