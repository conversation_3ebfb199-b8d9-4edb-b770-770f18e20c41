/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-10-17 14:25:52
 * @LastEditTime: 2023-10-17 14:33:15
 * @LastEditors: zhang<PERSON><PERSON>i
 */

import { addOperationLogRequest } from "@/common/utils/operationLog";
import type { ModalState } from "@/hooks/useModal";
import { getMultipleTicketInfo, refundMultipleTicket } from "@/services/api/ticket";
import type { ActionType } from "@ant-design/pro-table";
import { Modal, Skeleton, Space, Tag, message } from "antd";
import TextArea from "antd/lib/input/TextArea";
import { isEmpty, pickBy } from "lodash";
import type { FC } from "react";
import { useEffect, useMemo, useState } from "react";
import { useRequest } from "@umijs/max";
import { useImmer } from "use-immer";
import RealNameTable from "./RealNameTable";

const { CheckableTag } = Tag;

const tagsData = [
  "#需要改出行时间",
  "#信息填错重新购买",
  "#证件未带/带错",
  "#景区关闭",
  "#商家主动要求退订",
  "#未收到入园凭证",
  "#取票不方便",
  "#计划有变不去了",
  "#客户不便告知原因",
  "#其他原因/手动填入"
];

interface TicketModalProps {
  modalState: ModalState;
  selectedRows: Ticket.SaleTicketItem[];
  actionRef: React.MutableRefObject<ActionType | undefined>;
}

const TicketModal: FC<TicketModalProps> = ({ modalState: { visible, setVisible }, selectedRows = [], actionRef }) => {
  // 批量查询票的信息
  const { data, run, loading } = useRequest(getMultipleTicketInfo, {
    manual: true,
    initialData: [],
    formatResult(res) {
      return (res.data || []).map(item => {
        const { checkedNum = 0, playerNum = 0 } = selectedRows.find(i => i.id === item.id) || {};

        // 实名
        return {
          ...item,
          checkedNum,
          playerNum
        };
      });
    }
  });

  // 退票请求
  const refundMultipleTicketReq = useRequest(refundMultipleTicket, {
    manual: true,
    onSuccess(data, params) {
      message.success("退票成功");
      addOperationLogRequest({
        action: "edit",
        content: `编辑【${params[0].data.map(i => i.ticketNumber).join(",")}】批量退票`
      });
      setVisible(false);
      actionRef.current?.reload();
      if (actionRef.current?.clearSelected) {
        actionRef.current?.clearSelected();
      }
    }
  });

  // 选中的数据
  const [ticketData, setTicketData] = useImmer<Record<string, Ticket.RealNameListItem[]>>({});

  // 退票金额数据
  const sum = useMemo(() => {
    // 非实名票数据  realNameList 为空
    const isNotRealTickets = data?.filter(i => isEmpty(i.realNameList)) || [];

    // 过滤已选的实名票
    const selectedTickets = pickBy(ticketData, (value, key) => !isEmpty(value));
    const realTickets = data?.filter(i => Object.keys(selectedTickets).includes(i.id)) || [];

    const tickets = isNotRealTickets.concat(realTickets);

    // 原支付金额
    const amount = tickets.reduce(
      (pre, { checkRetreatInfo, checkedNum, playerNum }) =>
        pre + checkRetreatInfo.amount * (playerNum - checkedNum) ?? 0,
      0
    );
    // 退票手续费
    const refundFee = tickets.reduce(
      (pre, { checkRetreatInfo, playerNum, checkedNum }) =>
        pre + checkRetreatInfo.refundFee * (playerNum - checkedNum) ?? 0,
      0
    );
    // 退款金额
    const refundAmount = tickets.reduce(
      (pre, { checkRetreatInfo, playerNum, checkedNum }) =>
        pre + checkRetreatInfo.refundAmount * (playerNum - checkedNum) ?? 0,
      0
    );
    return [amount, refundFee, refundAmount];
  }, [data, ticketData]);

  // 退票原因
  const [otherReason, setOtherReason] = useState("");
  const [selectedTags, setSelectedTags] = useState<string[]>([]);

  const handleChange = (tag: string, checked: boolean) => {
    const nextSelectedTags = checked ? [...selectedTags, tag] : selectedTags.filter(t => t !== tag);
    setSelectedTags(nextSelectedTags);
  };

  // 批量退票
  const onRefund = () => {
    // 非实名票数据  realNameList 为空
    const isNotRealTickets = data?.filter(i => isEmpty(i.realNameList)) || [];
    const isNotRealTicketsParams = isNotRealTickets.map(i => ({
      idCardNumber: [],
      ticketNumber: i.id
    }));

    // 实名票数据
    // 过滤已选的票
    const selectedData = Object.entries(ticketData).filter(item => !isEmpty(item[1]));
    const realTicketsParams = selectedData.map(item => {
      return {
        ticketNumber: item[0],
        idCardNumber: item[1].map(i => i.idCardNumber)
      };
    });
    const params = realTicketsParams.concat(isNotRealTicketsParams);
    if (!isEmpty(params)) {
      const remark = selectedTags.join(";") + otherReason;
      refundMultipleTicketReq.run({
        remark,
        refundSource: "BACKSTAGE",
        data: params
      });
    } else {
      message.warning("至少选择一张票");
    }
  };

  useEffect(() => {
    if (visible && !isEmpty(selectedRows)) {
      run(selectedRows.map(i => i.id));
    }
    if (!visible) {
      setTicketData({});
      setSelectedTags([]);
      setOtherReason("");
    }
  }, [visible]);

  return (
    <Modal
      title="批量退票"
      width={900}
      visible={visible}
      onCancel={() => {
        setVisible(false);
      }}
      onOk={onRefund}
      okButtonProps={{
        loading: refundMultipleTicketReq.loading
      }}
      destroyOnClose
    >
      <Skeleton loading={loading}>
        <Space direction="vertical" style={{ width: "100%" }} size="large">
          {data?.map((item, index) => {
            return <RealNameTable key={item.id} ticket={item} setTicketData={setTicketData} />;
          })}

          <Space size={64}>
            <span>
              原购买金额：<span style={{ color: "red" }}>{sum[0].toFixed(2)} 元</span>
            </span>
            <span>
              应扣手续费：<span style={{ color: "red" }}>{sum[1].toFixed(2)} 元</span>
            </span>
            <span>
              应退款金额：<span style={{ color: "red" }}>{sum[2].toFixed(2)} 元</span>
            </span>
          </Space>
          <div>
            <div style={{ fontWeight: "bold", marginBottom: 12 }}> 请选择退款原因</div>
            <Space size="large" wrap>
              {tagsData.map(tag => (
                <CheckableTag
                  key={tag}
                  checked={selectedTags.indexOf(tag) > -1}
                  onChange={checked => handleChange(tag, checked)}
                >
                  {tag}
                </CheckableTag>
              ))}
              {selectedTags.includes("#其他原因/手动填入") && (
                <TextArea
                  maxLength={200}
                  placeholder="#其他退款原因"
                  style={{ width: 736, height: 100 }}
                  onChange={e => {
                    setOtherReason(e.target.value);
                  }}
                />
              )}
            </Space>
          </div>
        </Space>
      </Skeleton>
    </Modal>
  );
};

export default TicketModal;
