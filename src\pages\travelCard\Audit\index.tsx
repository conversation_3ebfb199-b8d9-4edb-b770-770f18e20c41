/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-04-22 17:54:22
 * @LastEditTime: 2023-07-10 18:18:30
 * @LastEditors: zhang<PERSON><PERSON>i
 */

import Tag from "@/common/components/Tag";
import { tableConfig } from "@/common/utils/config";
import {
  RightsTicketStatus,
  RightsTicketStatusEnum,
  ticketTypeEnum,
  TravelCardApproveStatus
} from "@/common/utils/enum";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import { getUniqueId } from "@/common/utils/tool";
import useModal from "@/hooks/useModal";
import { getAuditList, getTicketRightsList, switchAudit, updateRightsStatus } from "@/services/api/auditManage";
import type { ActionType, ProColumns } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import type { TabsProps } from "antd";
import { Input, message, Modal, Space, Tabs } from "antd";
import { omit } from "lodash";
import type { FC } from "react";
import React, { useEffect, useRef, useState } from "react";
import { Access, useAccess, useModel, useRequest } from "@umijs/max";
import AuditModal from "./components/AuditModal";
import RightsModal from "./components/RightsModal";

export const TCardItems: TabsProps["items"] = [
  {
    label: "权益卡审核",
    key: "qH3sL7pM"
  },
  {
    label: "权益票审核",
    key: "tZ8wV2xG"
  }
];

type AuditManageProps = Record<string, never>;

/**
 * @description: 权益卡审核
 */
const AuditManage: FC<AuditManageProps> = () => {
  const access = useAccess();
  const [tabKey, setTabKey] = useState<React.Key>(TCardItems[0].key);

  const { initialState } = useModel("@@initialState");

  const { currentCompanyInfo } = useModel("@@initialState").initialState || {};

  const { scenicId } = initialState!.scenicInfo!;
  const { username, phone } = initialState!.userInfo!;

  const actionRef = useRef<ActionType>();

  // 权益卡审核 modal
  const auditModal = useModal();

  const rightsModal = useModal();

  const [auditItem, setAuditItem] = useState<API.AuditListItem>();
  const [rightsItem, setRightsItem] = useState<API.TicketRightsListItem>();

  // 通过/驳回 - 权益卡
  const reasonRef = useRef<string>("");
  const handleApproval = async (action: "pass" | "reject", record: any) => {
    if (!record) return;
    try {
      await switchAudit({
        id: record.id,
        status: action === "pass" ? 2 : 1,
        reason: reasonRef.current
      });
      addOperationLogRequest({
        action: "audit",
        module: tabKey,
        content: `${action === "pass" ? "通过" : "驳回"}【${record.travelGoodsName}】审核`
      });
      message.success("操作成功");
      actionRef.current?.reload();
    } catch (error) {}
    reasonRef.current = "";
  };
  const passConfig = (record: any) => ({
    width: 500,
    title: "该操作将通过该用户购买权限，并短信通知用户",
    content: <span style={{ color: "gray" }}>你还要继续吗？</span>,
    okText: "确认",
    cancelText: "取消",
    onOk: () => {
      handleApproval("pass", record);
    }
  });
  const rejectConfig = (record: any) => ({
    width: 500,
    title: "该操作将驳回该用户购买权限，并短信通知用户",
    content: (
      <>
        <div style={{ color: "gray" }}>你还要继续吗？</div>
        <div className="margin-bottom-small">请填写驳回原因：</div>
        <Input.TextArea
          style={{ height: 100, marginBottom: 12 }}
          showCount
          maxLength={1000}
          onChange={({ target: { value } }) => {
            /**
             * @description 有坑 不要用 setState 去保存值，modal.confirm 是函数实现
             * @see https://github.com/ant-design/ant-design/issues/29291#issuecomment-775869367
             */
            reasonRef.current = value;
          }}
        />
      </>
    ),
    okText: "确认",
    cancelText: "取消",
    onOk: () => {
      handleApproval("reject", record);
    }
  });

  // 通过/驳回 - 权益票
  const reasonRef2 = useRef<string>("");
  const updateRightsStatusReq = useRequest(updateRightsStatus, {
    manual: true,
    onSuccess(data, params) {
      addOperationLogRequest({
        action: "audit",
        module: tabKey,
        content: `${params[0].rightsStatus === 2 ? "通过" : "驳回"}【${rightsItem?.goodsName}】审核`
      });
      message.success("操作成功");
      actionRef.current?.reload();
    }
  });
  const handleApproval2 = async (action: "pass" | "reject", record: any) => {
    console.log(record);

    if (record?.approveId) {
      try {
        updateRightsStatusReq.run({
          reason: reasonRef2.current,
          id: record.approveId,
          name: username,
          phone: phone,
          userId: record.createUserId,
          rightsStatus: action === "pass" ? RightsTicketStatus.审核通过 : RightsTicketStatus.审核失败
        });
      } catch (error) {}
      reasonRef2.current = "";
    }
  };
  const passConfig2 = (record: any) => ({
    width: 500,
    title: "点击确定，将同意该门票关联到该权益",
    content: <span style={{ color: "red" }}>该操作不可撤回，请谨慎填写</span>,
    okText: "确认",
    cancelText: "取消",
    onOk: () => {
      handleApproval2("pass", record);
    },
    okButtonProps: {
      loading: updateRightsStatusReq.loading
    }
  });
  const rejectConfig2 = (record: any) => ({
    width: 500,
    title: "点击确定，将拒绝该门票关联到该权益",
    content: (
      <Space direction="vertical">
        <div style={{ color: "red" }}>该操作不可撤回，请谨慎填写</div>
        <div>请填写驳回原因：</div>
        <Input.TextArea
          style={{ height: 100, width: 350, marginBottom: 12 }}
          showCount
          maxLength={1000}
          onChange={({ target: { value } }) => {
            /**
             * @description 有坑 不要用 setState 去保存值，modal.confirm 是函数实现
             * @see https://github.com/ant-design/ant-design/issues/29291#issuecomment-775869367
             */
            reasonRef2.current = value;
          }}
        />
      </Space>
    ),
    okText: "确认",
    cancelText: "取消",
    onOk: () => {
      handleApproval2("reject", record);
    },
    okButtonProps: {
      loading: updateRightsStatusReq.loading
    }
  });

  // 权益卡审核 columns
  const columns: ProColumns<API.AuditListItem>[] = [
    {
      title: "编号",
      dataIndex: "productId",
      search: false
    },
    {
      title: "权益卡名称",
      key: "name",
      renderText: (_, record) => {
        const { goodsName, ticketName, travelGoodsName, typeProduct } = record;
        return typeProduct === 1 ? `${ticketName} - ${goodsName}` : travelGoodsName;
      }
    },

    {
      title: "用户名",
      width: 250,
      dataIndex: "userName"
    },
    {
      title: "手机号",
      dataIndex: "phone"
    },

    {
      title: "身份证号",
      dataIndex: "idCard",
      copyable: true
    },
    {
      title: "审核状态",
      dataIndex: "status",
      fixed: "right",
      valueEnum: {
        0: "待审核",
        1: "审核失败",
        2: "审核通过",
        3: "未开通",
        4: "待支付",
        5: "已过期"
      },
      renderText: (dom: any) => <Tag type="equityCardAuditStatus" value={dom} />
    },
    {
      width: "auto",
      title: "操作",
      valueType: "option",
      key: "option",
      fixed: "right",
      renderText: (_, record) => (
        <Space>
          <Access accessible={access.canCheckList_selectDetail}>
            <a
              onClick={() => {
                setAuditItem(record);
                auditModal.setTypeWithVisible("info");
              }}
            >
              查看
            </a>
          </Access>
          <Access accessible={access.canCheckList_passReject && TravelCardApproveStatus.待审核 === record?.status}>
            <a
              onClick={() => {
                Modal.confirm(passConfig(record));
              }}
            >
              通过
            </a>
          </Access>
          <Access accessible={access.canCheckList_passReject && TravelCardApproveStatus.待审核 === record?.status}>
            <a
              style={{ color: "red" }}
              onClick={() => {
                Modal.confirm(rejectConfig(record));
              }}
            >
              驳回
            </a>
          </Access>
        </Space>
      )
    }
  ];

  // 权益票审核 columns
  const rightsColumns: ProColumns<API.TicketRightsListItem>[] = [
    {
      title: "编号",
      dataIndex: "id",
      search: false
    },
    {
      title: "旅游场景名称",
      dataIndex: "scenicName"
    },

    {
      title: "所属产品名称",
      dataIndex: "name"
    },
    {
      title: "商品名称",
      dataIndex: "goodsName"
    },

    {
      title: "票种",
      dataIndex: "type",
      valueType: "select",
      valueEnum: ticketTypeEnum
    },
    {
      title: "申请时间",
      dataIndex: "approveTime",
      search: false
    },
    {
      title: "申请时间",
      dataIndex: "time",
      valueType: "dateTimeRange",
      hideInTable: true,
      search: {
        transform: values => {
          return {
            beginTime: values[0],
            endTime: values[1]
          };
        }
      }
    },
    {
      title: "审核状态",
      dataIndex: "rightsStatus",
      valueEnum: omit(RightsTicketStatusEnum, "0"),
      valueType: "select",
      fixed: "right",
      renderText: (dom: any) => <Tag type="equityTicketAuditStatus" value={dom} />
    },
    {
      width: "auto",
      title: "操作",
      valueType: "option",
      key: "option",
      fixed: "right",
      renderText: (_, record) => (
        <Space
          onClick={() => {
            setRightsItem(record);
          }}
        >
          <Access accessible={true}>
            <a
              onClick={() => {
                rightsModal.setTypeWithVisible("info");
              }}
            >
              查看
            </a>
          </Access>
          <Access accessible={access.canCheckList_passReject && RightsTicketStatus.待审核 === record?.rightsStatus}>
            <a
              onClick={() => {
                Modal.confirm(passConfig2(record));
              }}
            >
              通过
            </a>
          </Access>
          <Access accessible={access.canCheckList_passReject && RightsTicketStatus.待审核 === record?.rightsStatus}>
            <a
              style={{ color: "red" }}
              onClick={() => {
                Modal.confirm(rejectConfig2(record));
              }}
            >
              驳回
            </a>
          </Access>
        </Space>
      )
    }
  ];

  const tableListReq = async (params: API.AuditListParams | API.TicketRightsListParams) => {
    try {
      if (tabKey === TCardItems[0].key) {
        const { data } = await getAuditList(params as API.AuditListParams);
        return data;
      }
      const { data } = await getTicketRightsList(params as API.TicketRightsListParams);
      return data;
    } catch (error) {
      return {
        data: []
      };
    }
  };

  useEffect(() => {
    actionRef.current?.reload();
  }, [tabKey]);

  return (
    <>
      <Tabs
        tabBarStyle={{ padding: "0 24px", margin: "0", background: "#fff" }}
        onChange={activeKey => {
          setTabKey(activeKey);
        }}
        activeKey={tabKey as string}
        items={TCardItems}
      />
      <ProTable<API.AuditListItem | API.TicketRightsListItem, API.AuditListParams | API.TicketRightsListParams>
        {...tableConfig}
        actionRef={actionRef}
        rowKey={getUniqueId()}
        columns={tabKey === TCardItems[0].key ? columns : (rightsColumns as any)}
        pagination={{
          defaultPageSize: 10
        }}
        params={{ scenicId, operatorId: currentCompanyInfo?.coId }}
        request={tableListReq}
      />

      {/* 权益卡审核查看详情 */}
      <AuditModal actionRef={actionRef} dataItem={auditItem} {...auditModal} tabKey={tabKey} />
      {/* 权益票审核查看详情 */}
      <RightsModal actionRef={actionRef} dataItem={rightsItem} {...rightsModal} tabKey={tabKey} />
    </>
  );
};

export default AuditManage;
