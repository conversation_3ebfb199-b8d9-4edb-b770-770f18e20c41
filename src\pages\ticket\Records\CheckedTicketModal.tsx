/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-10-08 16:54:25
 * @LastEditTime: 2023-10-24 17:07:05
 * @LastEditors: zhangfengfei
 */
import { equipmentTypeEnum, identityType } from "@/common/utils/enum";
import { modelWidth } from "@/common/utils/gConfig";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import type { ModalState } from "@/hooks/useModal";
import { getCheckedTicketRecordsPageList } from "@/services/api/ticket";
import type { ProColumns } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import { Modal, Space } from "antd";
import type { FC } from "react";
import { getEnv } from "@/common/utils/getEnv";

interface CheckedTicketModalProps {
  modalState: ModalState;
  currentRow: Record<string, any> | undefined;
}

const CheckedTicketModal: FC<CheckedTicketModalProps> = ({ modalState: { setVisible, visible }, currentRow }) => {
  const columns: ProColumns[] = [
    {
      title: "核销终端类型",
      dataIndex: "terminalType",
      renderText: (text, { checkType }) => {
        if (checkType == 2) {
          return "后台";
        }
        return equipmentTypeEnum[text];
      }
    },
    {
      title: "核销设备名称",
      dataIndex: "terminalName",
      renderText: (text, { checkType, remark }) => {
        if (checkType == 2) {
          return remark || "-";
        }
        return text;
      }
    },
    {
      title: "身份识别类型",
      dataIndex: "enterWay",
      valueEnum: identityType
    },
    {
      title: "核销时间",
      dataIndex: "createDate"
    },
    {
      title: "核销次数",
      dataIndex: "passCount",
      valueType: "digit"
    },
    {
      title: "核销哈希",
      dataIndex: "txId",
      hideInTable: !(currentRow?.isChainTicket == 1),
      renderText: text =>
        text ? (
          <a target="_blank" href={`${getEnv().CHAIN_URL}/#/tx_list?tx=${text}`} rel="noreferrer">
            {text.length > 20 ? text.slice(0, 10) + "..." + text.slice(-10) : text}
          </a>
        ) : (
          "-"
        )
    }
  ];

  return (
    <Modal
      title="核销记录"
      width={modelWidth.lg}
      open={visible}
      destroyOnClose
      onCancel={() => setVisible(false)}
      footer={null}
    >
      <Space
        size={48}
        style={{
          fontWeight: "bold",
          marginBottom: 8
        }}
      >
        <div>票号：{currentRow?.id} </div>
        <div>商品名称：{currentRow?.goodsName} </div>
      </Space>
      <ProTable
        bordered
        cardProps={{
          bodyStyle: {
            padding: 0
          }
        }}
        pagination={{
          defaultPageSize: 10
        }}
        params={{ ticketNumber: currentRow?.id }}
        search={false}
        options={false}
        columns={columns}
        request={async params => {
          const { data } = await getCheckedTicketRecordsPageList(params);
          addOperationLogRequest({
            action: "info",
            content: `查看【${currentRow?.id}】门票核销记录`
          });

          return data;
        }}
      />
    </Modal>
  );
};

export default CheckedTicketModal;
