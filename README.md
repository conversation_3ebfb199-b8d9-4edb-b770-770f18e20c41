# 慧景云

项目使用 [Ant Design Pro](https://pro.ant.design) 创建，使用文档点击 [这里](https://pro.ant.design/zh-CN/docs/title-landing)  
**开发人员请阅读详细的[《开发文档》 ](https://git.shukeyun.com/scenic/common/-/blob/master/docs/index.md)**

## 安装

这个项目使用 [node](http://nodejs.org/) 和 [yarn](https://yarnpkg.com/)。请确保你本地安装了它们。

安装 yarn

```
npm install yarn -g
```

安装依赖

```bash
yarn
```

## 运行

分 dev 开发、test 测试、prod 生产环境，使用不同后缀可接入对应的后台环境接口，如：

```bash
开发环境: yarn start:dev
```

**注意：启动后需要手动新增对应景区的标志符**，如 wei 景区：

```
http://localhost:8000/#/wei/welcome
```

## 部署

上传到 gitlab 会自动构建流水线打包并部署

不同环境的访问地址如下：

- 开发环境：https://dev.shukeyun.com/scenic/backend-v2/#/q/welcome
- 测试环境：https://test.shukeyun.com/scenic/backend-v2/#/wei/welcome
- 生产环境：https://huijingyun.net/#/dtbyc/welcome

## 项目相关

### 技术栈

- ❤️ [React](https://react.docschina.org/docs/getting-started.html)
- ❤️ [Umi](https://umijs.org/docs/introduce/introduce) + [ProComponents](https://procomponents.ant.design/components/) + [ Antd Design 4.x](https://4x.ant.design/components/overview-cn/)
- ❤️ [TypeScript](https://www.tslang.cn/index.html)

### 目录

```bash
config
  └── config.xxx.ts     // xxx 环境配置
  └── routes.ts         // 路由文件
  └── proxy.ts          // 代理配置文件
mock					// mock 数据文件
public					// 公共资源
src
  └── common         	// 公共组件
  └── components        // 公共组件
  └── hooks             // 自定义 hooks
  └── pages             // 页面组件
  └── services          // 接口 API
  └── access.ts         // 权限配置文件
  └── app.tsx           // 主入口文件
  └── *                 // 其它
```

### 常用命令

| 名称               | 描述                     | 备注                                                               |
| ------------------ | ------------------------ | ------------------------------------------------------------------ |
| `yarn start`       | 项目启动                 | -                                                                  |
| `yarn analyze`     | 项目打包分析             | -                                                                  |
| `yarn tsc`         | TS 错误检查              | -                                                                  |
| `yarn deps`        | 依赖更新检查             | -                                                                  |
| `yarn openapi`     | OpenAPI 和 Mock 代码生成 | [相关文档](https://infore-web-sdk.dev.inrobot.cloud/guide/openapi) |
| `npm run prettier` | 代码格式化               | [相关文档](https://infore-web-sdk.dev.inrobot.cloud/guide/quality) |
| `npm run lint`     | 代码风格检查             | [相关文档](https://infore-web-sdk.dev.inrobot.cloud/guide/quality) |

### 统一错误处理

```js
// app.tsx
// 统一错误处理
const errorConfig = {
  adaptor: (resData: any) => {
    // 此配置只用于错误处理，不影响最终传递给页面的数据格式
    return {
      ...resData,
      success: resData.code === 20000 || resData.return_code === "SUCCESS",
      showType: 1, // error display type： 0 silent; 1 message.warn; 2 message.error; 4 notification; 9 page
      errorMessage: resData.msg
    };
  }
};
```

```js
// 业务中请求数据的代码
try {
  const { data } = await getUserInfo(); // 请求接口
  // 只有 code 等于 20000 才会执行下面的代码，其他的 code 在 app.tsx 被拦截掉了，所以在这里不需要判断 code
  console.log(data); //对数据一顿操作...
} catch (e) {
  /**
   * @e.data 后端返回的原始数据
   * @e.name 错误的类型，为 BizError 则是 code 不等于 20000 时抛出的错误
   **/
  if (e.name === "BizError") {
    // 当接口 code 不等于 20000 走这里
  } else {
    // 剩下的错误走这里
  }
}
```

### 接口

- 请求参数和返回参数使用 TypeScript 做类型限制
- 返回结果使用 `src\services\api\typings.d.ts` 中的 ResponseData 类型包裹，开发只关注返回中 data 的内容
- 接口文档地址：[Swagger 接口文档](https://dev.shukeyun.com/scenic/api-v2/doc.html#/%E7%A5%A8%E5%8A%A1%E6%A8%A1%E5%9D%97/%E5%95%86%E5%93%81%E6%A8%A1%E5%9D%97/batchAddGoodsUsingPOST)

示例：

```js
  interface RightsListParams {
    scenicId: string;
  };

  interface RightsListItem {
    id: string;
    isEnable: number;
    rightsName: string;
    scenicType: number;
    travelCardNames: string[];
  };

/** 查询权益列表 */
export function getRightsList(
  params: API.RightsListParams,
  options: Record<string, any> = {},
) {
  return request<ResponseListData<API.RightsListItem[]>>(
    `${getEnv().API_HOST}/rightsService/rights/pageList`,
    {
      method: 'GET',
      params,
      ...options,
    },
  );
}
```

### 注意事项

- 本地开发登录时会出现是否跳转登录页，点击是，跳转到 cas 登录页，登录成功后还会弹出是否跳转，此时点击否，正确跳转到 localhost 进行开发
- 开发过程中请严格遵守代码规范，具体规范 [《JavaScript 风格指南》](https://git.shukeyun.com/scenic/backend-v2/-/blob/test/src/common/docs/JavaScript.md)、[《TypeScript 风格指南》](https://git.shukeyun.com/scenic/backend-v2/-/blob/test/src/common/docs/typeScript.md)
- **所有需要初始化的数据请在 app.tsx 里的 getInitialState 方法里执行初始化，不要放在其他地方初始化，这样可以避免一些 BUG，因为有的地方会手动执行初始化**

### 登录功能

本系统没有内置登录功能，登录功能抽离成独立的 cas 系统，它的运行方式是这样的：

- 进入业务系统时，如没有登录，接口会返回 40001 状态码
- 业务系统携带 `appId` 参数（唯一标识符，在 cas 系统中申请），跳转到 cas 系统去登录
- 登录成功，跳转回业务系统并携带参数 `tk`
- 业务系统拿 `tk` 请求后端接口 `setCookie`，完成登录

## 相关仓库

- [易旅通](https://git.shukeyun.com/scenic/exchange)
- [慧旅云](https://git.shukeyun.com/scenic/paas)
- [窗口售票系统](https://git.shukeyun.com/scenic/house)
- [CAS](https://git.shukeyun.com/cas/web)
-
