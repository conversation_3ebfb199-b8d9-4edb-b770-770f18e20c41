/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-08-30 10:54:24
 * @LastEditTime: 2022-12-15 17:42:00
 * @LastEditors: zhangfengfei
 */

import type { Rule } from 'antd/lib/form';
import { isNumber, isString } from 'lodash';
// import  from 'antd';

// 正则表达式

/**
 * 中英文字符长度，中文算两个长度
 * @param {*} str
 */
export function strlen(str: string) {
  return str.length;
}

/**
 * 验证是否包含数字和字母
 * @param {string} value
 */
export function validateLetterAndNumber(value: string) {
  return new RegExp('^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]*$').test(value);
}

/**
 * 验证是否为正确 ip
 * @param {string} value
 */
export function validateIp(value: string) {
  const reg =
    /^([1-9]?\d|1\d\d|2[0-4]\d|25[0-5])\.([1-9]?\d|1\d\d|2[0-4]\d|25[0-5])\.([1-9]?\d|1\d\d|2[0-4]\d|25[0-5])\.([1-9]?\d|1\d\d|2[0-4]\d|25[0-5])$/;
  return reg.test(value);
}

/**
 * 验证是否为正确手机号
 * @param {string} value
 */
export function validateMobile(value: any) {
  const reg = /^1[3-9]\d{9}$/;
  return reg.test(value);
}

/* ------------------------------------------------------------------------------------------------------------------------- */

/**
 * 验证器 只允许输入英文或者数字
 * @param {string} errorMessage
 */
export function letterOrNumberValidator(errorMessage: string = '只允许输入英文或者数字'): Rule {
  const result: Rule = {
    validator: (rule, value) => {
      if (value && !/^[A-Za-z0-9]+$/.test(value)) {
        return Promise.reject(errorMessage);
      }
      return Promise.resolve();
    },
  };

  return result;
}

/**
 * 验证器 字符串是否包含数字和字母
 * @param {string} erorrMessage
 */
export function letterAndNumberValidator(erorrMessage: string = '必须是字母和数字结合'): Rule {
  const result: Rule = {
    validator: (rule, value) => {
      const pass = validateLetterAndNumber(value);
      if (!pass && value) {
        return Promise.reject(erorrMessage);
      }
      return Promise.resolve();
    },
  };

  return result;
}
/**
 * 验证器 字符串只含数字
 * @param {string} erorrMessage
 */
export function numberValidator(erorrMessage: string = '只允许输入数字'): Rule {
  const result: Rule = {
    validator: (rule, value) => {
      if (value && !/^[0-9]+$/.test(value)) {
        return Promise.reject(erorrMessage);
      }
      return Promise.resolve();
    },
  };

  return result;
}

/**
 * 验证器 ip 是否正确
 * @param {*} erorrMessage
 */
export function ipValidator(erorrMessage: string = 'ip 格式有误'): Rule {
  const result: Rule = {
    validator: (rule, value) => {
      const pass = validateIp(value);
      if (!pass && value) {
        return Promise.reject(erorrMessage);
      }
      return Promise.resolve();
    },
  };

  return result;
}

/**
 * 验证器 手机号是否正确
 * @param {*} erorrMessage
 */
export function mobileValidator(erorrMessage: string = '手机号格式有误'): Rule {
  const result: Rule = {
    validator: (rule, value) => {
      const pass = validateMobile(value);
      if (!pass && value) {
        return Promise.reject(erorrMessage);
      }
      return Promise.resolve();
    },
  };

  return result;
}

/**
 * 验证器 字符串长度验证
 * @param {*} min
 * @param {*} max
 * @param {*} formatMessage
 * @returns
 */
export function stringLengthValidator(
  min?: number | boolean,
  max?: number,
  formatMessage?: string,
) {
  let message = '';
  const result: Rule = {
    validator(rule, value) {
      if (isString(value)) {
        const valueLength = strlen(value);
        let flag = true;
        if (min !== max) {
          if (min && max) {
            if (valueLength < min || valueLength > max) {
              flag = false;
              message = formatMessage || `介于${min}到${max}个字符`;
            }
          } else if (min) {
            if (valueLength < min) {
              flag = false;
              message = formatMessage || `至少要${min}个字符`;
            }
          } else if (max) {
            if (valueLength > max) {
              flag = false;
              message = formatMessage || `不能超过${max}个字符`;
            }
          }
        }

        if (min === max && valueLength !== min && valueLength > 0) {
          flag = false;
          message = formatMessage || `请输入${min}个字符`;
        }
        if (!flag) {
          return Promise.reject(message);
        }
      }
      return Promise.resolve();
    },
  };

  return result;
}

/**
 * 验证器 数字验证
 * @param {*} min
 * @param {*} max
 * @param {*} formatMessage
 * @returns
 */
export function numValueValidator(min: number, max: number, formatMessage?: string) {
  const result: Rule = {
    validator(rule, value) {
      if (isNumber(value)) {
        if (value < min || value > max) {
          const message = formatMessage || `介于 ${min} 到 ${max} 之间`;
          Promise.reject(message);
        }
        return Promise.resolve();
      } else {
        return Promise.reject('值必须是数字');
      }
    },
  };

  return result;
}
