.common {
  height: 156px;
  padding: 0 24px;
  border-radius: 2px;
}

.main {
  .card {
    .approve {
      width: 100%;
      height: 70px;
      background: linear-gradient(270deg, #f8fbff 0%, #d5e6ff 100%);
      border-radius: 2px;
    }
    .warning {
      width: 100%;
      height: 70px;
      background: linear-gradient(270deg, rgba(255, 233, 226, 0.3) 0%, #ffe8cf 100%);
      border-radius: 2px;
    }
    .text {
      margin-right: 5px;
      color: #000;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
    }
  }
  .exchange {
    .common();

    background: linear-gradient(316deg, #d5e6ff 0%, #fff 66%, #f8fbff 100%);
    border: 1px solid #dae2fa;
  }

  .settle {
    .common();

    background: linear-gradient(316deg, #ffead5 0%, #fff 66%, #fff9f8 100%);
    border: 1px solid #ffece4;
  }
  .data {
    .common();

    height: 156px;
    background: linear-gradient(316deg, #ead5ff 0%, #fff 66%, #fbf8ff 100%);
    border: 1px solid #f7e4ff;
  }

  .trading {
    .common();

    height: 156px;
    background: linear-gradient(316deg, #cbf0ff 0%, #fff 66%, #f8fcff 100%);
    border: 1px solid #d0f2ff;
  }
}
