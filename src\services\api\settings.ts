/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-04-19 09:42:42
 * @LastEditTime: 2023-08-29 11:54:32
 * @LastEditors: z<PERSON><PERSON><PERSON>i
 */
import { request } from "@umijs/max";
import { scenicHost } from ".";
import { getEnv } from "@/common/utils/getEnv";

/* 【系统设置模块】 */
const { OPERATION_HOST } = getEnv();
// 全局配置 - 详情
export async function apiScenicConfig(params: any) {
  const { data } = await request(`${scenicHost}/scenicConfig/info/${params.id}`, {
    method: "GET"
  });
  return data;
}
// 全局配置 - 编辑
export async function apiScenicConfigInfo(params: any) {
  return request(`${scenicHost}/scenicConfig/info`, {
    method: "POST",
    data: params
  });
}

// 全局配置 - 分页条件查询埋点日志信息
export function apiSensorLogList(params: any) {
  return request<
    ResponseData<
      {
        records: Scenic.OperationLogItem[];
      } & API.PageParams
    >
  >(`${OPERATION_HOST}/page`, {
    method: "GET",
    params: params
  });
}

// 查询模块列表
export async function apiModuleList(params: any) {
  return await request(`${OPERATION_HOST}/moduleList`, {
    method: "GET",
    params: params
  });
}

// 获取景区参数配置
export function getParamsConfig(params: { id: string }) {
  return request<ResponseData<API.ParamsConfigType>>(`${scenicHost}/scenicConfig/parameter/info/${params.id}`, {
    method: "GET",
    params
  });
}

// 编辑景区参数配置
export function updateParamsConfig(params: API.ParamsConfigType) {
  return request(`${scenicHost}/scenicConfig/parameter/info`, {
    method: "POST",
    data: params
  });
}

// 获取门票模板列表
export function getTicketTemplateList(params: any) {
  return request(`${scenicHost}/ticketTemplate/gettemplates`, {
    method: "GET",
    params
  });
}
// 删除门票模板
export function deleteTicketTemplate(params: { id: string; scenicId: string }) {
  return request(`${scenicHost}/ticketTemplate/deltemplate`, {
    method: "DELETE",
    data: params
  });
}
