/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-10-27 14:27:38
 * @LastEditTime: 2023-10-30 11:34:45
 * @LastEditors: zhang<PERSON><PERSON>i
 */

import { tableConfig } from "@/common/utils/config";
import { baseProductTypeEnum, IssueTypeEnum, realEnum, RuleTypeEnum, whetherEnum } from "@/common/utils/enum";
import type { ModalState } from "@/hooks/useModal";
import RetreatRate from "@/pages/ruleSet/RefundTickets/components/RetreatRate";
import {
  getIssueTicketRulePageList,
  getTicketCheckRulePageList,
  getTicketRetreatRulePageList
} from "@/services/api/ticket";
import type { ActionType, ProColumns } from "@ant-design/pro-components";
import ProTable from "@ant-design/pro-table";
import { Button, Modal, Tooltip } from "antd";
import { omit, pick } from "lodash";
import type { FC } from "react";
import { useCallback, useEffect, useRef } from "react";
import { useModel } from "@umijs/max";

const checkColumns: ProColumns[] = [
  {
    title: "检票规则名称",
    dataIndex: "name"
  },
  {
    title: "产品类型",
    dataIndex: "proType",
    valueEnum: baseProductTypeEnum,
    hideInSearch: true
  },
  {
    title: "检票控制方式",
    dataIndex: "controlType",
    hideInSearch: true,
    render: (dom: any) => "平台检票"
  },
  {
    title: "检票通行方式",
    dataIndex: "adoptType",
    hideInSearch: true,
    render: (dom: any) => ["一检一人", "一检多人"][dom]
  },
  {
    title: "身份识别类型",
    dataIndex: "identityType",
    hideInSearch: true,
    render: (dom: any) => dom.join(" + ")
  }
];

const retreatColumns: ProColumns[] = [
  {
    title: "退票规则名称",
    dataIndex: "name"
  },
  {
    title: "可退票",
    dataIndex: "isRetreat",
    search: false,
    valueEnum: {
      0: "否",
      1: "是"
    }
  },
  {
    title: "退票费率",
    search: false,
    colSpan: 3,
    dataIndex: "lawsData",
    renderText: (text = []) => {
      if (text.length < 3) {
        return <RetreatRate text={text} title="生效" />;
      }
      return (
        <Tooltip placement="top" overlayInnerStyle={{ width: 320 }} title={<RetreatRate text={text} title="生效" />}>
          <RetreatRate text={text.slice(0, 2)} title="生效" />
          <div>......</div>
        </Tooltip>
      );
    }
  },
  {
    search: false,
    dataIndex: "bookData",
    colSpan: 0,
    renderText: (text = []) => {
      if (text.length < 3) {
        return <RetreatRate text={text} title="预定" />;
      }
      return (
        <Tooltip placement="top" overlayInnerStyle={{ width: 320 }} title={<RetreatRate text={text} title="预定" />}>
          <RetreatRate text={text.slice(0, 2)} title="预定" />
          <div>......</div>
        </Tooltip>
      );
    }
  },
  {
    dataIndex: "defaultRate",
    search: false,
    colSpan: 0,
    renderText: text => {
      return `其他退票费率：${text ?? "无"}`;
    }
  }
];

interface RuleSelectModalProps {
  modalState: ModalState;
  onSelect: (val: Record<string, any>) => void;
  productInfo?: Record<string, any>;
  isTCard?: boolean;
}

const RuleSelectModal: FC<RuleSelectModalProps> = ({
  onSelect,
  productInfo,
  modalState: { visible, setVisible, type },
  isTCard
}) => {
  const actionRef = useRef<ActionType>();
  const { initialState } = useModel("@@initialState");
  const { scenicId = "", appId = "" } = initialState?.scenicInfo || {};

  const issueColumns: ProColumns[] = [
    {
      title: "出票规则名称",
      dataIndex: "name"
    },
    {
      title: "出票类型",
      dataIndex: "type",
      valueEnum: IssueTypeEnum
    },
    {
      title: "规则类型",
      dataIndex: "ruleType",
      valueType: "select",
      valueEnum: isTCard ? pick(RuleTypeEnum, "2") : omit(RuleTypeEnum, "2")
    },
    {
      title: "实名方式",
      dataIndex: "isRealName",
      search: false,
      valueEnum: realEnum
    },
    {
      title: "检验账户实名认证",
      dataIndex: "realName",
      search: false,
      valueEnum: whetherEnum
    },
    {
      title: "限本人购买",
      dataIndex: "buyOwner",
      valueEnum: {
        0: "否",
        1: "是"
      }
    },
    {
      title: "出票即核销",
      dataIndex: "isCheck",
      search: false,
      valueEnum: {
        0: "否",
        1: "是"
      }
    }
  ];

  const dataMap = {
    add: {
      title: "选择出票规则",
      func: params => {
        return getIssueTicketRulePageList({
          ruleType: isTCard ? "2" : "1,3",
          ...params
        });
      },
      columns: issueColumns,
      params: {
        scenicId,
        isEnable: 1
      }
    },
    update: {
      title: "选择检票规则",
      func: getTicketCheckRulePageList,
      columns: checkColumns,
      params: {
        scenicId,
        isEnable: 1,
        proType: productInfo?.proType || "0"
      }
    },
    info: {
      title: "选择退票规则",
      func: getTicketRetreatRulePageList,
      columns: retreatColumns,
      params: { scenicId }
    }
  };

  const optionColumn: ProColumns[] = [
    {
      width: "auto",
      title: "操作",
      valueType: "option",
      render: (_: any, record: any) => {
        return (
          <Button
            type="link"
            onClick={() => {
              onSelect(record);
              setVisible(false);
            }}
          >
            选择
          </Button>
        );
      }
    }
  ];
  /** type:  add 出票   update 检票   info 退票  */
  const tableRequest = useCallback(
    async (params: any) => {
      const { data } = await dataMap[type].func(params);
      return data;
    },
    [type]
  );

  // 监听页签可见性变化
  const handleVisibilityChange = () => {
    if (document.visibilityState === "visible" && visible) {
      actionRef.current?.reload();
    }
  };

  useEffect(() => {
    document.addEventListener("visibilitychange", handleVisibilityChange);
    // 清理函数，移除事件监听器
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [visible]);

  return (
    <Modal
      width={1200}
      title={dataMap[type].title}
      open={visible}
      onCancel={() => setVisible(false)}
      destroyOnClose
      maskClosable={false}
      footer={false}
    >
      <ProTable
        {...tableConfig}
        actionRef={actionRef}
        params={dataMap[type].params}
        request={tableRequest}
        columns={dataMap[type].columns.concat(optionColumn)}
      />
    </Modal>
  );
};

export default RuleSelectModal;
