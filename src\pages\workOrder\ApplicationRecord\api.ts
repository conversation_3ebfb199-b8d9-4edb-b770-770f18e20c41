import { scenicHost } from "@/services/api";
import { request } from "@umijs/max";

// 工单列表
export async function apiIssueList(params: any) {
  const { data } = await request(`${scenicHost}/issue/list`, { params });
  return {
    data: data.list,
    total: data.page.total,
    success: true
  };
}

// 创建工单
export function apiIssueInfo(params: any) {
  return request(`${scenicHost}/issue/info`, {
    method: "POST",
    data: params
  });
}
