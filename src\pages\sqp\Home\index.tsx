import { useEffect, useState } from 'react';
import './index.less';
import { getEnv } from '@/common/utils/getEnv';

export default function Index() {
  const [countDown, setCountDown] = useState(10);
  const year = new Date().getFullYear();
  useEffect(() => {
    const timer = setTimeout(() => {
      if (countDown > 0) {
        setCountDown(countDown - 1);
      } else {
        clearTimeout(timer);
        window.location.href = getEnv().HLY_HOST;
      }
    }, 1000);
    return () => {
      clearTimeout(timer);
    };
  }, [countDown]);

  return (
    <div style={{ position: 'fixed', top: 0, left: 0, right: 0, bottom: 0 }}>
      <div style={{ textAlign: 'center', color: '#fff' }}>
        <h1 style={{ textAlign: 'center', color: '#fff' }}>慧景云</h1>
        <h1 style={{ textAlign: 'center', color: '#fff' }}>正在准备中，敬请期待！</h1>
      </div>
      <div>
        <div>{/* <img src="./work.png" alt="under-construction" /> */}</div>
        <div>
          <div className="timer_wrap">
            <div id="counter"> </div>
          </div>
        </div>
        <div>
          <p style={{ textAlign: 'center', color: '#fff' }}>
            {countDown > 0 ? `${countDown} 秒后` : '正在'}跳转慧旅云主页
          </p>
        </div>
      </div>
      <div
        className="footer"
        style={{
          textAlign: 'center',
          color: '#fff',
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
        }}
      >
        <p>
          Copyright © 2001-{year} All Rights Reserved{' '}
          <a href="http://beian.miit.gov.cn/">粤 ICP 备 09156180 号 -25</a>
        </p>
        <p>环球数科股份有限公司 地址：深圳湾科技生态园 10 栋 B 座 17 层 01-03 号</p>
        <p>电话:40088-11138(+86)755-88328999</p>
      </div>
    </div>
  );
}
