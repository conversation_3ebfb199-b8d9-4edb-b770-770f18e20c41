/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-08-24 10:11:04
 * @LastEditTime: 2023-08-29 15:54:55
 * @LastEditors: zhangfeng<PERSON>i
 */
import { DatePicker, Space, Tabs } from "antd";
import type { Dayjs } from "dayjs";
import dayjs from "dayjs";
import type { FC } from "react";
import { useEffect, useRef, useState } from "react";

const { RangePicker } = DatePicker;

type DateType = "date" | "month";
type PickerType = "rangePicker" | "datePicker";

const options = [
  {
    label: "按日",
    key: "date"
  },
  {
    label: "按月",
    key: "month"
  }
];
export interface ChartDatePickerProps {
  sort?: DateType;
  defaultValue?: Dayjs | [Dayjs, Dayjs] | null;
  picker: PickerType;
  showMenu?: boolean;
  onValuesChange: (sort: DateType, value: Dayjs | [Dayjs, Dayjs] | null) => void;
  disabledDate?: (currentDate: Dayjs) => boolean;
}
const ChartDatePicker: FC<ChartDatePickerProps> = ({
  sort = "date",
  defaultValue = null,
  picker = "datePicker",
  showMenu = true,
  onValuesChange
}) => {
  const ref: any = useRef<[Dayjs, Dayjs]>();
  const [key, setKey] = useState<DateType>("date");
  const [dateValue, setDateValue] = useState<Dayjs | [Dayjs, Dayjs] | null>(null);

  // 动态禁选用
  const [currentDate, setCurrentDate] = useState<[Dayjs | null, Dayjs | null]>([null, null]);

  // 动态禁用七天 半年
  const banMap = {
    date: {
      num: 6,
      unit: "days"
    },
    month: {
      num: 5,
      unit: "months"
    }
  };
  const disabledRangeDate = (current: Dayjs) => {
    if (!currentDate) {
      return false;
    }

    const tooLate = currentDate[0] && current.diff(currentDate[0], banMap[key].unit) > banMap[key].num;
    const tooEarly = currentDate[1] && currentDate[1].diff(current, banMap[key].unit) > banMap[key].num;
    return !!tooEarly || !!tooLate;
  };

  const disabledDate = (current: Dayjs) => {
    // 禁选当天之后
    return current && current > dayjs().endOf("day");
  };

  useEffect(() => {
    setKey(sort);
  }, [sort]);

  useEffect(() => {
    if (defaultValue && !dateValue) {
      setDateValue(defaultValue);
    }
  }, [defaultValue]);

  return (
    <Space>
      {showMenu && (
        <Tabs
          size="small"
          tabBarGutter={20}
          activeKey={key}
          items={options}
          onChange={key => {
            setKey(key);
            setDateValue(null);
          }}
        />
      )}
      {picker === "datePicker" ? (
        <DatePicker
          value={dateValue}
          picker={key}
          disabledDate={disabledDate}
          onChange={value => {
            setDateValue(value);
            onValuesChange(key, value);
          }}
        />
      ) : (
        <RangePicker
          picker={key}
          value={dateValue}
          disabledDate={disabledRangeDate}
          onChange={value => {
            setDateValue(value);
            onValuesChange(key, value);
          }}
          onCalendarChange={values => setCurrentDate(values)}
        />
      )}
    </Space>
  );
};

export default ChartDatePicker;
