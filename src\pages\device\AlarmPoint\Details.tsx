import ProDescriptions from "@ant-design/pro-descriptions";
import { useModel } from "@umijs/max";

export default function Details(props) {
  const { detailData } = props;
  // 获取景区 ID
  const { initialState }: any = useModel("@@initialState");
  const scenicId = initialState?.scenicInfo?.scenicId;
  console.log(scenicId, initialState);
  //获取景区名称
  const scenicName = initialState?.scenicInfo?.scenicName;
  console.log("景区名称", scenicName);
  const columns = [
    {
      title: "所属景区",
      dataIndex: "scenicName",
      name: "scenicName",
      initialValue: `${scenicName}`,
      key: "scenicName",
      render: () => {
        return scenicName;
      },
      // valueEnum: {
      //   '0': '公告',
      // },
      formItemProps: {
        // rules: [{ required: true }],
        // disable: false
      },
      fieldProps: {
        disabled: true
      }
    },
    {
      dataIndex: "sosName",
      title: "点位名称",
      name: "sosName",
      key: "sosName",

      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      dataIndex: "loginName",
      title: "账号",
      name: "loginName",
      key: "loginName",
      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      dataIndex: "loginPassword",
      title: "密码",
      name: "loginPassword",
      key: "loginPassword",
      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    // {
    //   dataIndex: 'longitude',
    //   title: '经度',
    //   name: 'longitude',
    //   key: 'longitude',
    //   fieldProps: {},
    //   formItemProps: {
    //     rules: [{ required: true }],
    //   },
    // },
    // {
    //   dataIndex: 'latitude',
    //   title: '纬度',
    //   name: 'latitude',
    //   key: 'latitude',
    //   fieldProps: {},
    //   formItemProps: {
    //     rules: [{ required: true }],
    //   },
    // },
    {
      title: "品牌",
      dataIndex: "brand",
      name: "brand",
      // valueEnum: {
      //     '0': '1',
      //     "1": '2'
      // },
      key: "brand",
      formItemProps: {
        rules: [{ required: true }]
      },
      // valueType: 'select',
      fieldProps: {
        // mode: 'multiple',
        // options: positionValue2 == 1 ? scenicData2 : companyData2,
        // onChange: (value, option) => {
        //   console.log(value);
        //   setAcceptorData(value);
        // },
        // showArrow: true,
        // disabled: flag ? false : show ? false : true,
      }
    },
    {
      dataIndex: "linkAddress",
      title: "链接地址",
      name: "linkAddress",
      key: "linkAddress",
      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      dataIndex: "point",
      title: "管理端口",
      name: "point",
      key: "point",
      fieldProps: {}
    },
    {
      dataIndex: "channel",
      title: "通道号",
      name: "channel",
      key: "channel",
      fieldProps: {}
      // formItemProps: {
      //   rules: [{ required: true }],
      // },
    }
  ];
  return (
    <ProDescriptions
      title="基础信息"
      //   request={async () => {}}
      dataSource={detailData}
      columns={columns}
      column={2}
    >
      {/* <ProDescriptions.Item dataIndex="percent" label="百分比" valueType="percent">
                100
            </ProDescriptions.Item> */}
    </ProDescriptions>
  );
}
