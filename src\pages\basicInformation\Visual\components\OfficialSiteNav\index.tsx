import { tableConfig } from '@/common/utils/config';
import { modelWidth } from '@/common/utils/gConfig';
import { getUniqueId, transformArrToString } from '@/common/utils/tool';
import { apiGetNavigationList, apiSaveNavigationList } from '@/services/api/erp';
import { InfoCircleTwoTone, PlusOutlined } from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-components';
import { ProFormText, ProTable, type ActionType } from '@ant-design/pro-components';
import type { DragEndEvent } from '@dnd-kit/core';
import { DndContext, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { useModel, useRequest } from '@umijs/max';
import { Button, Cascader, Form, Input, message, Modal, Select, Space, Tooltip } from 'antd';
import { cloneDeep } from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import { UrlNavEnum, UrlNavType } from './datas';
import styles from './index.module.less';
import type { ModalStateProps, RowProps, SecondList, TableItem, TableItemKeys } from './interface';
import UrlNavModal from './UrlNameModal';

const Row: React.FC<Readonly<RowProps>> = (props) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: props['data-row-key'],
  });

  const style: React.CSSProperties = {
    ...props.style,
    transform: CSS.Translate.toString(transform),
    transition,
    cursor: 'move',
    ...(isDragging ? { position: 'relative', zIndex: 9999 } : {}),
  };
  return <tr {...props} ref={setNodeRef} style={style} {...attributes} {...listeners} />;
};

const App: React.FC = () => {
  const [customform] = Form.useForm();
  const actionRef = useRef<ActionType>();
  const { initialState } = useModel('@@initialState');
  const { scenicId } = initialState?.scenicInfo || {};

  const [dataSource, setDataSource] = useState<TableItem[]>([]);
  const [rowItems, setRowItems] = useState<string[]>([]);
  const [modalState, setModalState] = useState<ModalStateProps>({
    type: '',
    selectRowKeys: '',
    selectRowRecord: {},
    navigationUrl: '',
    navigationUrlName: '',
  });
  const [currentRecord, setCurrentRecord] = useState<any>({});

  /*************************************** 获取导航列表 **************************************/
  const dealInitData = (list) => {
    const _newDataSource = (list || [])?.map((item: TableItem, index: number) => {
      const _item = cloneDeep(item);
      if (item?.secondList?.length) {
        _item.children = item?.secondList?.map((cItem: SecondList, cIndex: number) => {
          return {
            ...cItem,
            key: `${index}-${cIndex}`,
            uuid: getUniqueId(),
          };
        });
      }
      return {
        ..._item,
        uuid: getUniqueId(),
        key: String(index),
        isExpand: true,
      };
    });
    setDataSource(_newDataSource);
  };
  const getNavListReq = useRequest(apiGetNavigationList, {
    manual: true,
    onSuccess: (res) => {
      /**
       * 初始化数据
       */
      dealInitData(res?.list);
    },
  });

  // 新增导航
  const onAddNav = () => {
    setDataSource((prev) => {
      return prev.concat({
        navigationName: '',
        navigationUrl: '',
        navigationUrlName: '',
        openMode: '_self',
        key: String(prev.length),
        uuid: getUniqueId(),
        isExpand: true,
      });
    });
  };

  // 新增二级导航
  const onAddSecondNav = (record: TableItem) => {
    setDataSource((prev) => {
      const _newDataSource = cloneDeep(prev)?.map((item, i) => {
        if (item?.key === record?.key) {
          const _newItem: any = {
            navigationName: '',
            navigationUrl: '',
            navigationUrlName: '',
            openMode: '_self',
            key: `${i}-${item?.children?.length || 0}`,
            uuid: getUniqueId(),
          };
          return {
            ...item,
            children: item?.children ? item.children.concat(_newItem) : [].concat(_newItem),
          };
        }
        return item;
      });
      return _newDataSource;
    });
  };

  // 删除
  const onDel = (record: TableItem) => {
    console.log('onDel..........');
    console.log(record);

    Modal.confirm({
      title: '确认删除吗？',
      icon: <InfoCircleTwoTone />,
      content: (record?.key || '')?.includes('-')
        ? '删除后不可恢复'
        : '该导航下包含二级导航，删除后将无法恢复。您确定要删除全部内容吗？',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        setDataSource((prev) => {
          const _newDataSource = cloneDeep(prev);
          _newDataSource.forEach((item: TableItem, index: number) => {
            if (record?.key?.includes?.('-')) {
              item?.children?.forEach((cItem: SecondList, cIndex: number) => {
                if (cItem?.uuid === record?.uuid) {
                  item.children?.splice(cIndex, 1);
                }
              });
            } else {
              if (item?.uuid === record?.uuid) {
                _newDataSource?.splice(index, 1);
              }
            }
          });

          return _newDataSource;
        });
      },
    });
  };

  // 修改
  const onChangeData = (value: any, field: string, record: TableItem) => {
    setDataSource((prev) => {
      const _newDataSource = cloneDeep(prev);
      _newDataSource.forEach((item: TableItem) => {
        if (record?.key?.includes?.('-')) {
          item?.children?.forEach((cItem: SecondList) => {
            if (cItem?.uuid === record?.uuid) {
              cItem[field] = value;
            }
          });
        } else {
          if (item?.uuid === record?.uuid) {
            item[field] = value;
          }
        }
      });
      return _newDataSource;
    });
  };

  // 选择链接名称
  const onSelectUrlNavChange = (item, entity: TableItem) => {
    console.log('onSelectUrlNavChange............', item, entity);
    if (!item?.length) {
      // 清空
      onChangeData('', 'navigationUrlName', entity);
      onChangeData('', 'navigationUrl', entity);
      return;
    }
    setCurrentRecord(entity);
    if (item?.[0]?.value === UrlNavType.system) {
      // 系统
      const _label = transformArrToString(
        item?.map((e) => e?.label),
        '/',
      );
      const _navigationUrlObj: any = {
        type: item?.[1]?.value || '',
        params: {},
      };
      onChangeData(_label, 'navigationUrlName', entity);
      onChangeData(JSON.stringify(_navigationUrlObj), 'navigationUrl', entity);
    } else if (item?.[0]?.value === UrlNavType.custom) {
      // 自定义
      setModalState({
        type: item?.[0]?.value || '',
      });
      customform.setFieldsValue({
        navigationUrl: entity?.navigationUrl,
        navigationUrlName: entity?.navigationUrlName,
      });
    } else {
      setModalState({
        type: item?.[0]?.value || '',
        navigationUrl: item?.[0]?.value,
        navigationUrlName: item?.[0]?.label,
      });
    }
  };

  const columns: ProColumns[] = [
    {
      title: '导航名称',
      dataIndex: 'navigationName',
      width: 260,
      fixed: 'left',
      render: (_: any, entity: any) => {
        return (
          <div style={{ minWidth: 200 }}>
            <Input
              value={entity?.navigationName}
              className={entity?.key?.includes?.('-') ? styles.secondNav : styles.oneNav}
              onChange={(e) => onChangeData(e?.target?.value, 'navigationName', entity)}
            />
          </div>
        );
      },
    },
    {
      title: '链接名称',
      width: 260,
      dataIndex: 'navigationUrlName',
      render: (_: any, entity: any) => {
        return (
          <Cascader
            value={entity?.navigationUrlName}
            style={{ width: '80%' }}
            options={UrlNavEnum}
            onChange={(e, item) => {
              onSelectUrlNavChange(item, entity);
            }}
          />
        );
      },
    },
    {
      title: '打开方式',
      width: 150,
      dataIndex: 'openMode',
      render: (_: any, entity: any) => {
        return (
          <Select
            value={entity?.openMode}
            style={{ width: '80%' }}
            defaultValue={entity?.openMode}
            options={[
              {
                label: '当前窗口',
                value: '_self',
              },
              {
                label: '新窗口',
                value: '_blank',
              },
            ]}
            onChange={(e) => {
              onChangeData(e, 'openMode', entity);
            }}
          />
        );
      },
    },
    {
      title: '操作',
      width: 100,
      valueType: 'option',
      fixed: 'right',
      render: (_: any, entity: any) => [
        <>
          {!entity?.key?.includes?.('-') ? (
            <Tooltip
              placement="top"
              title={entity?.children?.length >= 10 ? '每个一级导航下最多可添加10个二级导航' : ''}
              trigger="hover"
              key="add"
            >
              <Button
                type="link"
                onClick={() => {
                  onAddSecondNav(entity);
                }}
                key="add"
                disabled={entity?.children?.length >= 10}
                style={{ padding: 0 }}
              >
                添加下级
              </Button>
            </Tooltip>
          ) : null}
        </>,
        <a key="del" style={{ color: 'red' }} onClick={() => onDel(entity)}>
          删除
        </a>,
      ],
    },
  ];

  /*************************************** 拖拽的一些功能 **************************************/
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 1,
      },
    }),
  );

  // 拖拽结束
  const onDragEnd = ({ active, over }: DragEndEvent) => {
    console.log('onDragEnd................', active, over);

    if (active.id !== over?.id) {
      setDataSource((prev) => {
        if (active.id?.includes?.('-')) {
          /********************************* 拖动2级  *********************************/
          // 拆解
          const _prevList: any[] = [];
          prev.forEach((item: any) => {
            const _item = cloneDeep(item);
            delete _item?.children;
            _prevList.push(_item);
            if (item?.children) {
              item.children.forEach((cItem: any) => {
                _prevList.push(cItem);
              });
            }
          });
          console.log('修改前：_newList........', _prevList);
          // 移动
          const activeIndex = _prevList.findIndex((i) => i.key === active.id);
          const overIndex = _prevList.findIndex((i) => i.key === over?.id);
          const _nextList = arrayMove(_prevList, activeIndex, overIndex);
          console.log('修改后：_nextList.............', _nextList);
          // 合并
          const _newDataSource: any[] = [];
          _nextList.forEach((_item, index) => {
            if (_item.key?.includes?.('-') && _newDataSource.length) {
              // 二级
              const _currentList = _newDataSource[_newDataSource.length - 1];
              const _newItem = {
                ..._item,
                key: `${_currentList.key}-${_currentList?.children?.length || 0}`,
              };
              _currentList.children = _currentList?.children ? _currentList?.children : [];
              _currentList.children.push(_newItem);
            } else {
              // 一级
              const _newItem = {
                ..._item,
                key: String(_newDataSource.length || 0),
              };
              _newDataSource.push(_newItem);
            }
          });
          console.log('finish....._newDataSource....', _newDataSource);
          return _newDataSource;
        } else {
          /********************************* 拖动1级  *********************************/
          const activeIndex = prev.findIndex((i) => i.key === active.id);
          const overIndex = prev.findIndex((i) => i.key === over?.id);
          return arrayMove(prev, activeIndex, overIndex);
        }
      });
    }
  };

  // 获取table的rowKey
  const getItems = () => {
    const items: string[] = [];
    dataSource.forEach((e: any) => {
      if (e.isExpand) {
        items.push(e.key);
      }
    });
    return items;
  };

  /*************************************** 链接名称：modal的一些操作 **************************************/
  // 取消选择链接名称
  const onSelectUrlNavCancel = () => {
    setModalState({
      type: '',
      navigationUrl: '',
      selectRowKeys: '',
      selectRowRecord: {},
      navigationUrlName: '',
    });
    setCurrentRecord({});
  };
  // 确认选择链接名称
  const onSelectUrlNavOk = () => {
    console.log('onSelectUrlNavOk............', modalState, currentRecord);

    if (!modalState.selectRowKeys) {
      message.error('请先选择数据');
      return;
    }
    setDataSource((prev) => {
      prev.forEach((item: any) => {
        if (currentRecord?.key?.includes?.('-')) {
          item?.children?.forEach((cItem: any) => {
            if (cItem?.key === currentRecord?.key) {
              cItem.navigationUrl = modalState.navigationUrl;
              cItem.navigationUrlName = `${modalState.navigationUrlName}【${modalState.selectRowRecord?.showNavName}】`;
            }
          });
        } else {
          if (item?.key === currentRecord?.key) {
            item.navigationUrl = modalState.navigationUrl;
            item.navigationUrlName = `${modalState.navigationUrlName}【${modalState.selectRowRecord?.showNavName}】`;
          }
        }
      });
      return prev;
    });
    // 清除缓存数据
    onSelectUrlNavCancel();
  };

  // 确认 添加自定义链接
  const onAddCustomUrl = async () => {
    try {
      const { navigationUrlName } = await customform.validateFields();
      console.log('Success:', navigationUrlName);
      const navigationUrl = JSON.stringify({
        type: 'custom',
        url: navigationUrlName,
      });
      setDataSource((prev) => {
        const _newDataSource = cloneDeep(prev);
        _newDataSource.forEach((item: any) => {
          if (currentRecord?.key?.includes?.('-')) {
            item?.children?.forEach((cItem: any) => {
              if (cItem?.key === currentRecord?.key) {
                cItem.navigationUrl = navigationUrl;
                cItem.navigationUrlName = navigationUrlName;
              }
            });
          } else {
            if (item?.key === currentRecord?.key) {
              item.navigationUrl = navigationUrl;
              item.navigationUrlName = navigationUrlName;
            }
          }
        });
        return _newDataSource;
      });
      // 清除缓存数据
      onSelectUrlNavCancel();
    } catch (errorInfo) {
      console.log('Failed:', errorInfo);
    }
  };

  /*************************************** 重置数据 **************************************/
  const onReset = () => {
    dealInitData(getNavListReq.data?.list);
    setModalState({
      type: '',
      navigationUrl: '',
      selectRowKeys: '',
      selectRowRecord: '',
    });
  };

  /*************************************** 保存导航列表 **************************************/
  const saveNavListReq = useRequest(apiSaveNavigationList, {
    manual: true,
    onSuccess: (res) => {
      message.success('操作成功');
    },
  });
  const onSave = () => {
    console.log('onSave: ***************************************');
    console.log('dataSource:', dataSource);
    const validateFields: TableItemKeys[] = [
      'navigationName',
      'navigationUrl',
      'navigationUrlName',
      'openMode',
    ];
    let validateError = false;
    dataSource.forEach((item) => {
      if (!validateError) {
        validateFields.forEach((f: TableItemKeys) => {
          if (!item?.[f]) {
            validateError = true;
          }
        });
        (item.children || [])?.forEach((cItem) => {
          validateFields.forEach((f: TableItemKeys) => {
            if (!cItem?.[f]) {
              validateError = true;
            }
          });
        });
      }
    });
    if (validateError) {
      return message.error('请先完善数据');
    }

    const _params = dataSource.map((item: TableItem) => {
      const _newItem = {
        ...item,
        businessId: scenicId,
        navigationSort: item?.key,
        secondList: (item?.children || [])?.map((cItem) => ({
          ...cItem,
          businessId: scenicId,
          navigationSort: cItem?.key,
        })),
      };
      return _newItem;
    });
    console.log('ToSaveList：..........');
    console.log(_params);
    saveNavListReq.run(_params);
  };

  useEffect(() => {
    const items: string[] = [];
    dataSource.forEach((item: TableItem) => {
      items.push(item.key);
      if (item.children) {
        item.children.forEach((cItem: SecondList) => {
          items.push(cItem.key);
        });
      }
    });
    setRowItems(items);
  }, [dataSource]);

  useEffect(() => {
    // 调用接口获取dataSource
    getNavListReq.run({ scenicId });
  }, []);

  return (
    <>
      <DndContext sensors={sensors} modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
        <SortableContext items={rowItems} strategy={verticalListSortingStrategy}>
          <ProTable
            {...tableConfig}
            type="table"
            tableLayout="fixed"
            actionRef={actionRef}
            search={false}
            headerTitle={
              <div className={styles.headerTitle}>
                拖拽选择导航可以对其排序，最多可以添加8个一级导航，每个一级导航下最多可添加10个二级导航
              </div>
            }
            components={{
              body: { row: Row },
            }}
            pagination={false}
            rowKey="key"
            columns={columns}
            dataSource={dataSource}
            loading={getNavListReq.loading}
            expandable={{
              defaultExpandAllRows: true,
              expandedRowKeys: getItems(),
              onExpand: (expanded: boolean, record: any) => {
                setDataSource((prev) => {
                  return prev.map((item) => {
                    if (item?.uuid === record?.uuid) {
                      item.isExpand = expanded;
                    }
                    return item;
                  });
                });
              },
            }}
            options={{
              reload: () => {
                getNavListReq.run({ scenicId });
              },
            }}
            toolBarRender={() => [
              <Tooltip
                placement="top"
                title={dataSource?.length >= 8 ? '最多可以添加8个一级导航' : ''}
                trigger="hover"
                key="add"
              >
                <Button
                  key="k1"
                  type="primary"
                  onClick={onAddNav}
                  disabled={dataSource?.length >= 8}
                >
                  <PlusOutlined /> 新增导航
                </Button>
              </Tooltip>,
            ]}
            className={styles.mainTable}
          />
        </SortableContext>
      </DndContext>

      <Space className={styles.formBtns} size="large">
        <Button htmlType="button" onClick={onReset}>
          重置
        </Button>
        <Button type="primary" htmlType="submit" loading={saveNavListReq.loading} onClick={onSave}>
          保存
        </Button>
      </Space>

      {/* 链接名称的modal  */}
      <UrlNavModal
        modalState={modalState}
        setModalState={setModalState}
        onOk={onSelectUrlNavOk}
        onCancel={onSelectUrlNavCancel}
        onClose={onSelectUrlNavCancel}
      />

      <Modal
        title={'添加自定义链接'}
        open={modalState.type === UrlNavType.custom}
        width={modelWidth.md}
        onOk={() => onAddCustomUrl()}
        onCancel={() => onSelectUrlNavCancel()}
      >
        <Form form={customform} className={styles.customForm}>
          <ProFormText
            name="navigationUrlName"
            width="md"
            label="自定义链接"
            placeholder="请输入自定义链接"
            rules={[
              { required: true },
              {
                validator: async (e, data) => {
                  if (data) {
                    const pattern = new RegExp(
                      /^(((ht|f)tps?):\/\/)([^!@#$%^&*?.\s-]([^!@#$%^&*?.\s]{0,63}[^!@#$%^&*?.\s])?\.)+[a-z]{2,6}\/?/,
                    );
                    if (!pattern.test(data)) {
                      throw new Error('请完善链接');
                    }
                  }
                },
              },
            ]}
          />
        </Form>
      </Modal>
    </>
  );
};

export default App;
