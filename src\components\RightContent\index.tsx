import { Select, Space } from "antd";
import React from "react";
import { history, useModel } from "@umijs/max";

import { jumpPage } from "@/common/utils/tool";
import { apiProcessingFlowBizCount, apiUnReadMessageCount } from "@/services/api/erp";
import AiService from "./AiService";
import Avatar from "./AvatarDropdown";
import "./select.less";

export type SiderTheme = "light" | "dark";

const messageTimer = null;
const GlobalHeaderRight: React.FC = () => {
  // 初始化
  React.useEffect(() => {
    // getScenicData();
    // getUnReadMessageCount();
    // messageTimer = setInterval(() => {
    //   //TODO：是否需要销毁
    //   getUnReadMessageCount();
    // }, 1000 * 60 * 5);
    return () => {
      //卸载消息轮训器
      // messageTimer = null;
    };
  }, []);
  /* 下拉框 */
  const { initialState } = useModel("@@initialState");
  const { setMessageCount, setBacklogCount } = useModel("messageCount");
  if (!initialState || !initialState.settings) {
    return null;
  }

  // 获取【未读消息】
  const getUnReadMessageCount = () => {
    const userId = localStorage.getItem("user_id");
    const promises = [apiUnReadMessageCount({ userId }), apiProcessingFlowBizCount({ userId })];
    Promise.all(promises).then(res => {
      if (res[0].data) {
        setMessageCount(res[0].data);
      }
      if (res[1].data) {
        setMessageCount(res[1].data);
      }
    });
  };

  return (
    <Space align="center">
      {initialState?.currentCompanyInfo?.coId && (
        <Space align="center">
          <span>当前企业：</span>
          <Select
            style={{
              minWidth: 200
            }}
            getPopupContainer={trigger => (trigger?.parentElement as HTMLElement) || document.body}
            defaultValue={initialState?.currentCompanyInfo?.coId}
            onChange={e => {
              localStorage.setItem("currentCompanyId", e);
              if (history?.location?.pathname.includes("/welcome")) jumpPage.push("/welcome");
              location.reload();
            }}
          >
            {initialState?.companyList?.map(e => (
              <Select.Option value={e.coId} key={e.coId}>
                {e.coName}
              </Select.Option>
            ))}
          </Select>
        </Space>
      )}

      <AiService />

      <Avatar />
    </Space>
  );
};

export default GlobalHeaderRight;
