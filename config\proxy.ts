/**
 * @name 代理的配置
 * @see 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 * -------------------------------
 * The agent cannot take effect in the production environment
 * so there is no configuration of the production environment
 * For details, please see
 * https://pro.ant.design/docs/deploy
 *
 * @doc https://umijs.org/docs/guides/proxy
 */

// 本地调试代理
export default {
  dev: {
    '/api': {
      target: 'https://dev.shukeyun.com/common/common-gateway',
      changeOrigin: true,
      pathRewrite: { '^/api': '' },
    },
    '/chartApi': {
      target: 'https://prod.shukeyun.com/data/api/model/exec',
      changeOrigin: true,
      pathRewrite: { '^/chartApi': '' },
    },
    '/casApi': {
      target: 'https://dev.shukeyun.com/cas/api/v1',
      changeOrigin: true,
      pathRewrite: { '^/casApi': '' },
    },
    '/loginHost': {
      target: 'https://dev.shukeyun.com/cas/login',
      changeOrigin: true,
      pathRewrite: { '^/loginHost': '' },
    },
    '/upload': {
      target: 'http://test.shukeyun.com/maintenance/deepfile/',
      changeOrigin: true,
      pathRewrite: { '^/upload': '' },
    },

    '/operationApi': {
      target: 'https://dev.shukeyun.com/data/sensor/db',
      changeOrigin: true,
      pathRewrite: { '^/operationApi': '' },
    },
    '/aiApi': {
      target: 'https://dev-gcluster.shukeyun.com',
      changeOrigin: true,
      pathRewrite: { '^/aiApi': '' },
    },
  },
  test: {
    '/api': {
      target: 'https://test.shukeyun.com/common/common-gateway',
      // target: 'http://**************:8080',
      changeOrigin: true,
      pathRewrite: { '^/api': '' },
    },
    '/casApi': {
      target: 'https://test.shukeyun.com/cas/api/v1',
      changeOrigin: true,
      pathRewrite: { '^/casApi': '' },
    },
    '/loginHost': {
      target: 'https://test.shukeyun.com/cas/login',
      changeOrigin: true,
      pathRewrite: { '^/loginHost': '' },
    },
    '/upload': {
      target: 'http://test.shukeyun.com/maintenance/deepfile/',
      changeOrigin: true,
      pathRewrite: { '^/upload': '' },
    },

    '/chartApi': {
      target: 'https://prod.shukeyun.com/data/api/model/exec',
      changeOrigin: true,
      pathRewrite: { '^/chartApi': '' },
    },
    '/operationApi': {
      target: 'https://test.shukeyun.com/data/sensor/db',
      changeOrigin: true,
      pathRewrite: { '^/operationApi': '' },
    },
    '/aiApi': {
      target: 'https://dev-gcluster.shukeyun.com',
      changeOrigin: true,
      pathRewrite: { '^/aiApi': '' },
    },
  },
  canary: {
    '/api': {
      target: 'https://canary.shukeyun.com/common/common-gateway',
      changeOrigin: true,
      pathRewrite: { '^/api': '' },
    },
    '/casApi': {
      target: 'https://canary.shukeyun.com/cas/api/v1',
      changeOrigin: true,
      pathRewrite: { '^/casApi': '' },
    },
    '/loginHost': {
      target: 'https://canary.shukeyun.com/cas/login',
      changeOrigin: true,
      pathRewrite: { '^/loginHost': '' },
    },
    '/upload': {
      target: 'http://canary.shukeyun.com/maintenance/deepfile/',
      changeOrigin: true,
      pathRewrite: { '^/upload': '' },
    },
    '/chartApi': {
      target: 'https://prod.shukeyun.com/data/api/model/exec',
      changeOrigin: true,
      pathRewrite: { '^/chartApi': '' },
    },
    '/operationApi': {
      target: 'https://canary.shukeyun.com/data/sensor/db',
      changeOrigin: true,
      pathRewrite: { '^/operationApi': '' },
    },
    '/aiApi': {
      target: 'https://test-gcluster.shukeyun.com',
      changeOrigin: true,
      pathRewrite: { '^/aiApi': '' },
    },
  },
  prod: {
    '/api': {
      target: 'https://prod.shukeyun.com/common/common-gateway',
      changeOrigin: true,
      pathRewrite: { '^/api': '' },
    },
    '/casApi': {
      target: 'https://prod.shukeyun.com/cas/api/v1',
      changeOrigin: true,
      pathRewrite: { '^/casApi': '' },
    },
    '/loginHost': {
      target: 'https://prod.shukeyun.com/cas/login',
      changeOrigin: true,
      pathRewrite: { '^/loginHost': '' },
    },
    '/upload': {
      target: 'http://prod.shukeyun.com/maintenance/deepfile/',
      changeOrigin: true,
      pathRewrite: { '^/upload': '' },
    },
    '/chartApi': {
      target: 'https://prod.shukeyun.com/data/api/model/exec',
      changeOrigin: true,
      pathRewrite: { '^/chartApi': '' },
    },
    '/operationApi': {
      target: 'https://prod.shukeyun.com/data/sensor/db',
      changeOrigin: true,
      pathRewrite: { '^/operationApi': '' },
    },
  },
};
