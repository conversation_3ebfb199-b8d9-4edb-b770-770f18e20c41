/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-14 10:13:14
 * @LastEditTime: 2022-10-18 10:34:29
 * @LastEditors: zhang<PERSON><PERSON>i
 */
import Delete from "@/common/components/Delete";
import Disabled from "@/common/components/Disabled";
import { tableConfig } from "@/common/utils/config";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import { toValueEnum } from "@/common/utils/tool";
import useModal from "@/hooks/useModal";
import { deleteWorkOrderTemp, enableWorkOrderTemp, getWorkOrderTempList } from "@/services/api/workOrder";
import { PlusOutlined } from "@ant-design/icons";
import type { ActionType, ProColumns } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import { Button, Space } from "antd";
import type { FC } from "react";
import { useRef, useState } from "react";
import { Access, useAccess, useModel } from "@umijs/max";
import { WorkOrderStateEnum, WorkOrderTypeEnum } from "../common/data";
import WorkOrderModal from "./WorkOrderModal";

type TemplateListProps = Record<never, never>;

/**
 * @description: 工单管理 - 工单模板列表
 */
const TemplateList: FC<TemplateListProps> = () => {
  const { initialState } = useModel("@@initialState");
  const { currentCompanyInfo }: any = initialState || {};

  const { scenicId = "" } = initialState?.scenicInfo || {};
  const actionRef = useRef<ActionType>();

  const [currentRow, setCurrentRow] = useState<API.WorkOrderTempListItem>();

  // 工单 Modal 状态控制
  const workOrderModal = useModal();

  const tableListReq = async (params: API.WorkOrderTempListParams) => {
    const { data } = await getWorkOrderTempList(params);
    return data;
  };

  const columns: ProColumns<API.WorkOrderTempListItem>[] = [
    {
      title: "工单模板 Id",
      search: false,
      hideInTable: true,
      dataIndex: "workOrderId"
    },

    {
      title: "工单名称",
      dataIndex: "workOrderName",
      fieldProps: {
        maxLength: 100
      }
    },
    {
      title: "工单类型",
      dataIndex: "workOrderType",
      valueEnum: toValueEnum(WorkOrderTypeEnum)
    },
    {
      title: "创建人账号",
      dataIndex: "createUserName"
    },
    {
      title: "创建时间",
      dataIndex: "createTime",
      search: false
    },
    {
      title: "创建时间",
      dataIndex: "time",
      valueType: "dateRange",
      hideInTable: true,
      search: {
        transform: values => ({ startCreateTime: values[0], endCreateTime: values[1] })
      }
    },
    {
      title: "启用状态",
      dataIndex: "state",
      valueEnum: toValueEnum(WorkOrderStateEnum),
      render: (_: any, entity: any) => (
        <Disabled
          access={true}
          status={entity.state == 0}
          params={{
            scenicId,
            workOrderType: WorkOrderTypeEnum.商品价格编辑审批,
            providerId: currentCompanyInfo.coId,
            state: entity.state ^ 1,
            workOrderId: entity.workOrderId
          }}
          request={async params => {
            await enableWorkOrderTemp(params);
            addOperationLogRequest({
              action: "disable",
              content: `${entity.state == 0 ? "禁用" : "启用"}【${entity.workOrderName}】工单模板`
            });
            return true;
          }}
          actionRef={actionRef}
        />
      )
    },
    {
      width: "auto",
      title: "操作",
      valueType: "option",
      render: (_: any, entity: any) => (
        <Space>
          <a
            onClick={() => {
              setCurrentRow(entity);
              workOrderModal.setTypeWithVisible("info");
              addOperationLogRequest({
                action: "info",
                content: `查看【${entity.workOrderName}】工单模板`
              });
            }}
          >
            查看
          </a>
          <a
            onClick={() => {
              setCurrentRow(entity);
              workOrderModal.setTypeWithVisible("update");
            }}
          >
            编辑
          </a>
          <Delete
            access={access.canWorkOrder_edit}
            status={entity.state == 0}
            params={{ id: entity.workOrderId }}
            request={async params => {
              await deleteWorkOrderTemp(params);
              addOperationLogRequest({
                action: "del",
                content: `删除【${entity.workOrderName}】工单模板`
              });
              return true;
            }}
            actionRef={actionRef}
          />
        </Space>
      )
    }
  ];

  const access = useAccess();
  return (
    <>
      <ProTable<API.WorkOrderTempListItem, API.WorkOrderTempListParams>
        {...tableConfig}
        actionRef={actionRef}
        rowKey="workOrderId"
        params={{ providerId: currentCompanyInfo.coId, scenicId }}
        toolBarRender={(action, row) => [
          <Access key="add" accessible={access.canWorkOrder_add}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                workOrderModal.setTypeWithVisible("add");
                setCurrentRow(undefined);
              }}
            >
              新增
            </Button>
          </Access>
        ]}
        pagination={{ defaultPageSize: 10 }}
        request={tableListReq}
        columns={columns}
      />
      <WorkOrderModal actionRef={actionRef} modalState={workOrderModal} currentRow={currentRow} />
    </>
  );
};

export default TemplateList;
