import ProCard from "@ant-design/pro-card";
import { Button, Space, Tabs } from "antd";
import { createContext, useState } from "react";
import { useModel } from "@umijs/max";
import Article from "./components/Article";
import Help from "./components/HelpCenter";
import OfficialSiteNav from "./components/OfficialSiteNav/index";
import OfficialSiteRenovate from "./components/OfficialSiteRenovate";
import Product from "./components/Product";
import Service from "./components/Service";
import { getEnv } from "@/common/utils/getEnv";

export const TabKeyContext = createContext<string>("productManage");

export default () => {
  // 解析 URL 传参
  const urlObj: any = {};
  location.href
    .split("?")[1]
    ?.split("&")
    .map(item => {
      urlObj[item.split("=")[0]] = item.split("=")[1];
    });
  const { initialState } = useModel("@@initialState");
  const { coId }: any = initialState?.currentCompanyInfo || {};
  const { scenicId, uniqueIdentity } = initialState?.scenicInfo || {};
  const [key, setKey] = useState<string>(urlObj.tabKey || "officialSiteNav");
  const menuList: any = [
    {
      access: true,
      key: "officialSiteNav",
      label: "官网导航",
      components: <OfficialSiteNav />
    },
    {
      access: true,
      key: "officialSiteRenovate",
      label: "官网装修",
      components: <OfficialSiteRenovate />
    },
    {
      access: true,
      key: "product",
      label: "商品管理",
      components: <Product />
    },
    {
      access: true,
      key: "article",
      label: "文章管理",
      components: <Article />
    },
    {
      access: true,
      key: "help",
      label: "帮助中心",
      components: <Help />
    },
    {
      access: true,
      key: "service",
      label: "智能客服",
      components: <Service />
    }
  ].filter(item => item.access);

  return (
    <>
      <TabKeyContext.Provider value={key}>
        <Tabs
          tabBarStyle={{ padding: "0 24px", margin: "0", background: "#fff" }}
          items={menuList}
          activeKey={key}
          onChange={setKey}
          destroyInactiveTabPane
          tabBarExtraContent={{
            right: (
              <Space style={{ margin: "0 24px" }}>
                <Button
                  type="primary"
                  onClick={() => {
                    window.open(`${getEnv().OFFICIAL_HOST}${uniqueIdentity}/page?pageId=home`);
                  }}
                >
                  官网预览
                </Button>
              </Space>
            )
          }}
        />
        <ProCard>{menuList.find((item: any) => item.key == key).components}</ProCard>
      </TabKeyContext.Provider>
    </>
  );
};
