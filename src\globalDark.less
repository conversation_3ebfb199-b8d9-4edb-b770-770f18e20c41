// 侧栏背景
.ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {
  background: url('../public/chain/chain_nav.png') no-repeat bottom center;
  background-color: #15142a;
  background-size: contain;
}
// 内容背景
.ant-layout.ant-layout-has-sider > .ant-layout {
  background: url('../public/chain/chain_bg.png') no-repeat center;
  background-size: cover;
  background-attachment: fixed;
}
// 弹窗背景
.ant-modal-content {
  background: url('../public/chain/chain_pop_bg.png') no-repeat bottom center;
  background-size: cover;
}
.ant-modal-title {
  color: #239fff !important;
}
.ant-pro-table-search,
.ant-page-header,
.ant-pro-page-container-warp,
.ant-card,
.ant-card-actions,
.ant-picker,
.ant-pro-card,
.ant-table,
.ant-select-selector,
.ant-input-affix-wrapper,
.ant-input,
.ant-input-number,
.ant-input-number-group-addon,
.ant-table-thead > tr > th {
  background-color: rgba(30, 29, 64, 0.53) !important;
  // background-color: rgba(36, 37, 37, .5) !important;
}
.ant-table {
  background-color: rgba(36, 37, 37, 0) !important;
}
.ant-modal-header,
.ant-modal-content {
  background-color: rgba(30, 29, 64, 0.53) !important;
  backdrop-filter: blur(5px);
}
.ant-switch {
  background: #1890ff66 !important;
}
.ant-switch-checked {
  background: #1890ff !important;
}
