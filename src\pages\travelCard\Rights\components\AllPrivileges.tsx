/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-04-24 15:31:18
 * @LastEditTime: 2022-12-05 10:54:28
 * @LastEditors: zhangfengfei
 */

import { tableConfig } from '@/common/utils/config';
import { ticketTypeEnum } from '@/common/utils/enum';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import type { ModalState } from '@/hooks/useModal';
import { getPrivilegeGoodsList } from '@/services/api/rightsManage';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Modal, Tag } from 'antd';
import type { FC } from 'react';
import { useRef } from 'react';

type AllPrivilegesProps = ModalState & {
  rightsItem?: API.RightsListItem;
  actionRef?: React.MutableRefObject<ActionType | undefined>;
};

/**
 * @description: 所有特权
 */
const AllPrivileges: FC<AllPrivilegesProps> = ({ visible, rightsItem, setVisible }) => {
  const actionRef = useRef<ActionType>();

  const columns: ProColumns<API.PrivilegeGoodsListItem>[] = [
    {
      dataIndex: 'goodsId',
      hideInTable: true,
      search: false,
    },
    {
      title: '所属产品名称',
      dataIndex: 'productName',
      search: false,
    },
    {
      title: '商品名称',
      dataIndex: 'goodsName',
    },
    {
      title: '票种',
      dataIndex: 'goodsType',
      valueType: 'select',
      valueEnum: ticketTypeEnum,
    },
    // {
    //   search: false,
    //   title: '指导价格区间（￥）',
    //   render: (_, entity: any) =>
    //     !isNil(entity.beginPriceRange ?? entity.endPriceRange)
    //       ? entity.beginPriceRange + ' ~ ' + entity.endPriceRange
    //       : '-',
    // },
    // {
    //   title: '平日挂牌价',
    //   dataIndex: 'weekendPrice',
    //   search: false,
    // },
    // {
    //   title: '节假日挂牌价',
    //   dataIndex: 'holidayPrice',
    //   search: false,
    // },
    {
      title: '分时预约',
      dataIndex: 'timeRestrict',
      valueType: 'select',
      valueEnum: {
        0: '否',
        1: '是',
      },
      search: false,
    },
    {
      title: '分时时段',
      hideInSearch: true,
      dataIndex: 'timeShareVoList',
      render: (_, { timeShareVoList }: any) => {
        return timeShareVoList?.length
          ? timeShareVoList.map((item: any) => (
              <Tag key={item.id}>{[item.beginTime, item.endTime].join('-')}</Tag>
            ))
          : '-';
      },
    },
  ];

  return (
    <Modal
      width={1200}
      title="所享特权"
      visible={visible}
      footer={false}
      onCancel={() => {
        setVisible(false);
      }}
      destroyOnClose
    >
      {rightsItem && (
        <ProTable<API.PrivilegeGoodsListItem, API.PrivilegeGoodsListParams>
          {...tableConfig}
          actionRef={actionRef}
          rowKey="goodsId"
          pagination={{ defaultPageSize: 10 }}
          params={{ rightsId: rightsItem.id }}
          request={async (params) => {
            try {
              const { data } = await getPrivilegeGoodsList(params);
              addOperationLogRequest({
                action: 'info',
                content: `查看【${rightsItem?.rightsName}】所有特权`,
              });
              return data;
            } catch (error) {
              return [];
            }
          }}
          columns={columns}
        />
      )}
    </Modal>
  );
};

export default AllPrivileges;
