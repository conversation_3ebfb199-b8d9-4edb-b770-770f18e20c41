import { useEffect, useState } from 'react';

export interface ProModalState {
  type: 'add' | 'edit' | 'info' | null;
  setType: React.Dispatch<React.SetStateAction<'add' | 'edit' | 'info' | null>>;
  tableStytle: React.CSSProperties;
  setTableStytle: React.Dispatch<React.SetStateAction<React.CSSProperties>>;
}

export default function useModal(): ProModalState {
  const [type, setType] = useState<'add' | 'edit' | 'info' | null>(null);
  const [tableStytle, setTableStytle] = useState<React.CSSProperties>({});

  useEffect(() => {
    setTableStytle(type ? { display: 'none' } : {});
  }, [type]);

  return {
    /** 弹窗类型：{ add: '新增', edit: '编辑', info: '详情', null: '关闭' } */
    type,
    /** 设置弹窗类型 */
    setType,
    /** 表格样式 */
    tableStytle,
    /** 设置表格样式 */
    setTableStytle,
  };
}
