import PrefixTitle from "@/common/components/PrefixTitle";
import { GuideStepStatus, ticketTypeEnum, whetherEnum } from "@/common/utils/enum";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import { getAllPath, getUniqueId, jumpPage } from "@/common/utils/tool";
import MDEditor from "@/components/MDEditor";
import { useGuide } from "@/hooks/useGuide";
import useModal from "@/hooks/useModal";
import type { WorkOrderTempType } from "@/pages/workOrder/common/data";
import { ApproverRuleTypeEnum, WorkOrderTypeEnum } from "@/pages/workOrder/common/data";
import { getTicketTemplateList } from "@/services/api/settings";
import {
  apiSimpleGoodsAdd,
  apiSimpleGoodsDetail,
  apiSimpleGoodsUp,
  apiSimpleTicketInfo,
  getSimpleList
} from "@/services/api/ticket";
import { addWorkOrder, getWorkOrderNodes, getWorkOrderTempByType } from "@/services/api/workOrder";
import { LeftOutlined, MinusCircleOutlined, PlusCircleOutlined } from "@ant-design/icons";
import ProCard from "@ant-design/pro-card";
import { BetaSchemaForm, ProForm } from "@ant-design/pro-components";
import type { ProFormColumnsType, ProFormInstance } from "@ant-design/pro-form";
import type { ActionType } from "@ant-design/pro-table";
import { Button, InputNumber, Modal, Space, TimePicker, message } from "antd";
import { ceil, omit } from "lodash";
import dayjs from "dayjs";
import { parse } from "querystring";
import type { FC } from "react";
import { useEffect, useRef, useState } from "react";
import { useModel, useRequest } from "@umijs/max";
import RuleSelectModal from "./RuleSelectModal";
import WorkOrderModal from "./WorkOrderModal";
import { getEnv } from "@/common/utils/getEnv";

interface FCNameProps {
  type: string;
  id: string;
  actionRef: React.MutableRefObject<ActionType | undefined>;
}

let timeList: any = [];
const format = "HH:mm";
const defaultTime = {
  id: "",
  beginTime: "",
  endTime: "",
  unique: getUniqueId()
};

const FCName: FC<FCNameProps> = ({ type, id, actionRef }) => {
  const { initialState } = useModel("@@initialState");
  const {
    scenicId = "",
    appId = "",
    isBlockChain,
    scenicName = "",
    uniqueIdentity = ""
  } = initialState?.scenicInfo || {};
  const { coId = "" } = initialState?.currentCompanyInfo || {};
  const { userId = "" } = initialState?.userInfo || {};
  const { isDigit } = parse(window.location.hash.split("?")[1]) || {};
  const { updateGuideInfo } = useGuide();

  const formRef = useRef<ProFormInstance<any>>();

  const selectModalState = useModal();
  const [dataSource, setDataSource] = useState<any>({ id: "", isEnable: 0 });

  // 规则受控
  const [issueRuleItem, setIssueRuleItem] = useState<Record<string, any>>();
  const [checkItem, setCheckItem] = useState<Record<string, any>>();
  const [retreatRuleItem, setRetreatRuleItem] = useState<Record<string, any>>();

  // getTicketOfficeList
  // 获取门票模板
  const getTemplateListReq = useRequest(getTicketTemplateList, {
    manual: true,
    initialData: [],
    formatResult(res) {
      return res.data.map(item => ({
        label: item.templateName,
        value: item.id
      }));
    }
  });

  // 分时预约
  // const getSimpleLawsListReq = useRequest(getGoodsListApi, {
  //   manual: true,
  //   initialData: [],
  //   formatResult(res) {
  //     return (res.data.simpleLawsListVO ?? []).map((i) => ({
  //       label: i.name,
  //       value: i.id,
  //     }));
  //   },
  //   onSuccess(data, params) {
  //     // 新增 分时预约产品 默认选一个
  //     if (type === 'add') {
  //       formRef.current?.setFieldsValue({
  //         timeShareId: data[0]?.value,
  //       });
  //     }
  //   },
  // });

  // 产品下拉列表
  const productListReq = useRequest(getSimpleList, {
    manual: true,
    initialData: [],
    formatResult(res) {
      return res;
    }
  });

  // 产品详情
  const getProductInfoReq = useRequest(apiSimpleTicketInfo, {
    manual: true,
    initialData: {},
    formatResult(res) {
      return res;
    }
  });

  const { marketPrice = 0 } = getProductInfoReq.data || {};

  // 商品详情
  const getGoodsInfoReq = useRequest(apiSimpleGoodsDetail, {
    manual: true,
    formatResult(res) {
      return res;
    },
    onSuccess(data, params) {
      if (data.templateId === "0") data.templateId = null;

      getProductInfoReq.run(data.ticketId);
      // getSimpleLawsListReq.run({
      //   isChain: 1,
      //   id: data.ticketId,
      // });
      formRef.current?.setFieldsValue({
        ...data,
        useType: String(data.useType),
        isDigit: data.isDigit == "1"
      });

      setDataSource(data);
      setIssueRuleItem({
        id: data.issueId,
        name: data.issueName
      });
      setCheckItem({
        id: data.checkId,
        name: data.checkName
      });
      setRetreatRuleItem({
        id: data.retreatId,
        name: data.retreatName
      });
      timeList = data.timeShareVoList;
      setTimeData(!timeData);
    }
  });

  const [timeData, setTimeData] = useState(false);
  const changeValue = (index: any, type: any, e: any) => {
    timeList[index][type] = e;
    setTimeData(!timeData);
  };

  const TimeList: any = () => {
    if (!timeList?.length) timeList = [{ ...defaultTime }];
    return timeList?.map((item: any, index: any) => (
      <div
        key={item.unique ? item.unique : item.id}
        style={{ width: "100%", display: "flex", marginBottom: "12px", alignItems: "center" }}
      >
        {/* <TimePicker
          style={{ width: '300px' }}
          format={format}
          onChange={(time, timeString) => {
            changeValue(index, 'beginTime', timeString[0]);
          }}
        /> */}
        <TimePicker.RangePicker
          style={{ width: "calc(100% - 40px)" }}
          defaultValue={item.beginTime ? [dayjs(item.beginTime, format), dayjs(item.endTime, format)] : [null, null]}
          disabled={dataSource.id}
          format={format}
          onChange={(_, e) => {
            changeValue(index, "beginTime", e[0]);
            changeValue(index, "endTime", e[1]);
          }}
        />

        <div
          style={{
            flex: 1,
            display: "flex",
            justifyContent: "flex-end",
            alignItems: "center"
          }}
        >
          <PlusCircleOutlined
            style={{
              marginLeft: "16px",
              cursor: !dataSource.id ? "pointer" : "not-allowed",
              fontSize: "16px"
            }}
            onClick={() => {
              if (!dataSource.id) {
                defaultTime.unique = getUniqueId();
                timeList.splice(index + 1, 0, { ...defaultTime });
                setTimeData(!timeData);
              }
            }}
          />
          <MinusCircleOutlined
            style={{
              marginLeft: "16px",
              cursor: !dataSource.id && timeList.length > 1 ? "pointer" : "not-allowed",
              fontSize: "16px"
            }}
            onClick={() => {
              if (!dataSource.id && timeList.length > 1) {
                timeList.splice(index, 1);
                setTimeData(!timeData);
              }
            }}
          />
        </div>
      </div>
    ));
  };

  const columns: ProFormColumnsType<any>[] = [
    {
      title: "基础信息",
      valueType: "group",
      columns: [
        {
          title: (
            <div className="flex justify-content-between w-100">
              所属产品名称
              <a
                onClick={() => {
                  window.open(`${getAllPath()}/ticket/ticket-type/edit?type=add&tag=product`, "_blank");
                }}
              >
                新增
              </a>
            </div>
          ),
          dataIndex: "ticketId",
          valueType: "select",
          formItemProps: {
            rules: [{ required: true }]
          },
          fieldProps: {
            showSearch: true,
            virtual: false,
            disabled: type === "edit",
            options: productListReq.data?.map((item: any) => ({
              value: item.id,
              label: item.name
            })),
            loading: productListReq.loading,
            onDropdownVisibleChange: v => {
              if (v) {
                productListReq.run({ scenicId, operatorId: [coId] });
              }
            },
            onChange: val => {
              if (val) {
                // getSimpleLawsListReq.run({
                //   isChain: 1,
                //   id: val,
                // });
                getProductInfoReq.run(val);
              }
            }
          }
        },

        // {
        //   title: '分时预约时间',
        //   valueType: 'select',
        //   dataIndex: 'timeShareId',
        //   hideInForm: !getSimpleLawsListReq.data.length,
        //   fieldProps: {
        //     options: getSimpleLawsListReq.data,
        //     disabled: type === 'edit',
        //     allowClear: false,
        //   },
        // },
        {
          title: "商品名称",
          dataIndex: "goodsName",
          formItemProps: { rules: [{ required: true }] }
        },
        {
          title: "票种",
          dataIndex: "type",
          valueType: "select",
          valueEnum: ticketTypeEnum,
          formItemProps: { rules: [{ required: true }] },
          fieldProps: {
            onChange: value => {
              console.log(value);
            }
          }
        },
        {
          title: "数字资产",
          dataIndex: "isDigit",
          valueType: "switch",
          tooltip: "如需发布区块链库存，请将商品的数字资产开启",
          initialValue: false,
          transform: value => (value ? "1" : "0"),
          hideInForm: isBlockChain != 1,
          fieldProps: {
            disabled: type === "edit",
            checkedChildren: "是",
            unCheckedChildren: "否"
          }
        }
      ]
    },
    {
      title: "价格信息",
      valueType: "group",
      columns: [
        {
          valueType: "dependency",
          fieldProps: {
            name: ["overallDiscount"]
          },
          columns: ({ overallDiscount }) => {
            return [
              {
                title: (
                  <div className="flex justify-content-between w-100 ">
                    <span>特殊折扣率（%）</span>
                    <span className="text-red">
                      {marketPrice}元 * {overallDiscount ?? 0}% ={" "}
                      {ceil(marketPrice * ((overallDiscount ?? 0) / 100), 2)}元
                    </span>
                  </div>
                ),
                dataIndex: "overallDiscount",
                valueType: "digit",
                width: "100%",

                colProps: { xs: 24, sm: 12, xl: 8, xxl: 6 },
                fieldProps: {
                  max: 100,
                  min: 0,
                  precision: 2
                },
                formItemProps: {
                  rules: [
                    {
                      required: true,
                      message: "请输入"
                    }
                  ]
                }
              }
            ];
          }
        },

        {
          valueType: "dependency",
          fieldProps: {
            name: ["endDiscount", "beginDiscount", "overallDiscount"]
          },
          columns: ({ beginDiscount, endDiscount, overallDiscount = 0 }) => {
            return [
              {
                title: (
                  <div className="flex justify-content-between w-100 ">
                    <span>分销折扣区间（%）</span>
                    <span className="text-red">
                      {(marketPrice * (overallDiscount / 100) * (beginDiscount / 100)).toFixed(2)}元 ~{" "}
                      {(marketPrice * (overallDiscount / 100) * (endDiscount / 100)).toFixed(2)}元
                    </span>
                  </div>
                ),
                colProps: { xs: 24, sm: 12, xl: 8, xxl: 6 },
                renderFormItem: () => (
                  <div style={{ display: "flex" }}>
                    <div style={{ width: "calc(50% - 5px)" }}>
                      <ProForm.Item
                        name="beginDiscount"
                        rules={[{ required: true, message: "请输入" }]}
                        initialValue={100}
                      >
                        <InputNumber min={0} style={{ width: "100%" }} max={100} />
                      </ProForm.Item>
                    </div>
                    <span style={{ height: "32px", lineHeight: "32px", padding: "0 8px" }}>~</span>
                    <div style={{ width: "calc(50% - 5px)" }}>
                      <ProForm.Item
                        name="endDiscount"
                        rules={[{ required: true, message: "请输入" }]}
                        initialValue={100}
                      >
                        <InputNumber min={0} max={100} style={{ width: "100%" }} />
                      </ProForm.Item>
                    </div>
                  </div>
                )
              }
            ];
          }
        }
      ]
    },
    {
      title: "分时信息",
      valueType: "group",
      columns: [
        {
          title: "分时预约",
          dataIndex: "timeRestrict",
          valueType: "switch",
          convertValue: v => v == 1,
          transform: v => (v ? 1 : 0),
          initialValue: 0,
          fieldProps: {
            disabled: dataSource?.id
          }
        },
        {
          valueType: "dependency",
          fieldProps: {
            name: ["timeRestrict"]
          },
          columns: ({ timeRestrict }) => {
            if (!timeRestrict) {
              return [];
            }
            return [
              {
                title: "分时时段",
                colProps: { xs: 24, sm: 12, xl: 8, xxl: 6 },
                renderFormItem: TimeList
              }
            ];
          }
        }
      ]
    },
    {
      title: "其他信息",
      valueType: "group",
      columns: [
        {
          title: "购买数量控制",
          dataIndex: "isPeopleNumber",
          valueType: "switch"
        },
        {
          valueType: "dependency",
          fieldProps: {
            name: ["isPeopleNumber"]
          },
          columns: ({ isPeopleNumber }) => {
            if (!isPeopleNumber) {
              return [];
            }
            return [
              {
                width: "100%",
                colProps: { xs: 24, sm: 12, xl: 8, xxl: 6 },
                title: "最小起订量",
                dataIndex: "minPeople",
                valueType: "digit",
                formItemProps: {
                  rules: [{ required: true }]
                },
                fieldProps: {
                  min: 0,
                  precision: 0
                }
              },
              {
                width: "100%",
                colProps: { xs: 24, sm: 12, xl: 8, xxl: 6 },
                title: "单次最大预订量",
                dataIndex: "maxPeople",
                valueType: "digit",
                formItemProps: {
                  rules: [{ required: true }]
                },
                fieldProps: {
                  min: 0,
                  precision: 0
                }
              }
            ];
          }
        },
        {
          valueType: "dependency",
          fieldProps: {
            name: ["issueId", "isRights", "isTravel", "templateId"]
          },
          columns: ({ issueId, isTravel, isRights, templateId }) => {
            // let isShow = false;
            // if (issueRuleItem?.ruleType === 3) {
            //   // 用户手动选择出票规则
            //   isShow = true;
            // }
            // if (!issueRuleItem?.ruleType && isRights === 0 && isTravel === 0) {
            //   // 默认出票规则
            //   isShow = true;
            // }
            // if (!isShow) {
            //   return [];
            // }
            return [
              {
                title: (
                  <div className="flex justify-content-between w-100">
                    门票打印模板
                    <a
                      onClick={() => {
                        window.open(
                          `${getEnv().FIT_URL}?scenicName=${scenicName}&scenicId=${scenicId}&code=${uniqueIdentity}`,
                          "_blank"
                        );
                      }}
                    >
                      新增
                    </a>
                  </div>
                ),
                colProps: { xs: 24, sm: 12, xl: 8, xxl: 6 },
                dataIndex: "templateId",
                valueType: "select",
                formItemProps: {
                  rules: [{ required: true }]
                },
                params: { scenicid: scenicId },
                fieldProps: {
                  loading: getTemplateListReq.loading,
                  options: getTemplateListReq.data,
                  onDropdownVisibleChange: v => {
                    if (v) {
                      getTemplateListReq.run({ scenicid: scenicId });
                    }
                  }
                }
              }
            ];
          }
        }
      ]
    },
    {
      valueType: "group",
      title: "关联规则",
      columns: [
        {
          title: (
            <div className="flex justify-content-between w-100">
              出票规则
              <a
                onClick={() => {
                  selectModalState.setTypeWithVisible("add");
                  localStorage.setItem("pageOperateType", "add");
                  window.open(`${getAllPath()}/rule-set/sell-tickets`, "_blank");
                }}
              >
                新增
              </a>
            </div>
          ),
          dataIndex: "issueId",
          valueType: "select",
          fieldProps: {
            open: false,
            onClick: () => {
              selectModalState.setTypeWithVisible("add");
            },
            options: [{ label: issueRuleItem?.name, value: issueRuleItem?.id }]
          },
          formItemProps: {
            rules: [{ required: true }]
          }
        },
        {
          title: (
            <div className="flex justify-content-between w-100">
              检票规则
              <a
                onClick={() => {
                  selectModalState.setTypeWithVisible("update");
                  localStorage.setItem("pageOperateType", "add");
                  window.open(`${getAllPath()}/rule-set/manage`, "_blank");
                }}
              >
                新增
              </a>
            </div>
          ),
          dataIndex: "checkId",

          valueType: "select",
          formItemProps: {
            rules: [{ required: true }]
          },
          fieldProps: {
            open: false,
            onClick: () => {
              selectModalState.setTypeWithVisible("update");
            },
            options: [{ label: checkItem?.name, value: checkItem?.id }]
          }
        },
        {
          title: (
            <div className="flex justify-content-between w-100">
              退票规则
              <a
                onClick={() => {
                  selectModalState.setTypeWithVisible("info");
                  localStorage.setItem("pageOperateType", "add");
                  window.open(`${getAllPath()}/rule-set/refund-tickets`, "_blank");
                }}
              >
                新增
              </a>
            </div>
          ),
          dataIndex: "retreatId",
          valueType: "select",
          fieldProps: {
            open: false,
            onClick: () => {
              selectModalState.setTypeWithVisible("info");
            },
            options: [{ label: retreatRuleItem?.name, value: retreatRuleItem?.id }]
          },
          formItemProps: {
            rules: [{ required: true }]
          }
        },
        {
          dataIndex: "notice",
          colProps: {
            span: 24
          },
          renderFormItem: () => (
            <ProForm.Item
              name="notice"
              label="预订须知"
              rules={[
                {
                  type: "string",
                  max: 2000
                }
              ]}
            >
              <MDEditor />
            </ProForm.Item>
          )
        },
        {
          title: "备注",
          colProps: {
            span: 24
          },
          formItemProps: {},
          dataIndex: "remark",
          valueType: "textarea",
          fieldProps: {
            showCount: true,
            maxLength: 1000,
            style: {
              height: 100
            }
          }
        }
      ]
    }
  ];

  // 统一风格
  const newColumns = columns.map((item: any) => {
    const childColumns = (item.columns ?? []).map(i => ({
      width: "100%",
      colProps: { xs: 24, sm: 12, xl: 8, xxl: 6 },
      ...i
    }));

    return {
      rowProps: { gutter: 24 },
      valueType: "group",
      ...item,
      title: item.title && <PrefixTitle>{item.title} </PrefixTitle>,
      columns: childColumns
    };
  });

  const onRuleSelect = (val: Record<string, any>) => {
    const { type } = selectModalState;
    if (type === "add") {
      formRef.current?.setFieldsValue({
        issueId: val.id
      });

      setIssueRuleItem(val);
    } else if (type === "update") {
      formRef.current?.setFieldsValue({
        checkId: val.id
      });
      setCheckItem(val);
    } else {
      formRef.current?.setFieldsValue({
        retreatId: val.id
      });
      setRetreatRuleItem(val);
    }
  };

  // 工单数据准备
  const [workOrderModalVisible, setWorkOrderModalVisible] = useState(false);
  // 工单模板数据
  const [workOrderTempData, setWorkOrderTempData] = useState<WorkOrderTempType>();
  // 改变后的值
  const [modifiedValues, setModifiedValues] = useState<Record<string, any>>({});
  // 创建工单请求
  const addWorkOrderReq = useRequest(addWorkOrder, {
    manual: true,
    onSuccess(data, params) {
      message.success("工单创建成功");
      jumpPage.push("/ticket/ticket-type");
      addOperationLogRequest({
        action: "edit",
        content: `编辑【${params[0]?.goodsName}】商品`
      });
      setWorkOrderModalVisible(false);
    }
  });

  // 创建工单
  const createWorkOrder = async (params: { approver?: string[]; modifiedValues?: any }) => {
    const { data } = await getWorkOrderNodes({
      providerId: coId,
      scenicId: scenicId,
      group: "backend",
      platformId: `${scenicId}/${coId}`,
      workOrderType: WorkOrderTypeEnum.商品价格编辑审批,
      approver: params.approver || undefined,
      appId
    });

    // 编辑商品的准备数据
    const explains = Object.entries({
      ...(params.modifiedValues || modifiedValues),
      timeBeginTime: dataSource.timeBeginTime,
      timeEndTime: dataSource.timeEndTime
    }).map(item =>
      JSON.stringify({
        key: item[0],
        pre: dataSource[item[0]] ?? null,
        now: item[1] ?? dataSource[item[0]] ?? null
      })
    );

    addWorkOrderReq.run({
      explains,
      issueName: data.workOrderName,
      issueType: WorkOrderTypeEnum.商品价格编辑审批,
      nodes: data.nodes,
      group: "backend",
      explainId: dataSource.id,
      platformId: `${scenicId}/${coId}`,
      providerId: coId,
      scenicId,
      userId,
      goodsName: dataSource?.goodsName
    });
  };

  // 工单流程
  const handleWorkOrder = (val: any, data?: WorkOrderTempType) => {
    // 确认弹窗
    Modal.confirm({
      title: "此操作已设置审批流程，审批通过后即可生效",
      width: 450,
      content: <span style={{ color: "red" }}> 确认提交？</span>,
      onOk: () => {
        // 有待审批工单
        if (getGoodsInfoReq.data.workState === "1") {
          message.warning("商品价格修改审批中，暂时无法编辑，请审批完成后重试");
          return;
        }

        // 分两种情况 由发起人确定和预设
        if (data?.nodeApproverType === ApproverRuleTypeEnum.由发起人确定) {
          setWorkOrderModalVisible(true);
        } else {
          createWorkOrder({
            modifiedValues: val
          });
        }
      },
      okButtonProps: {
        loading: addWorkOrderReq.loading
      },
      zIndex: 10000
    });
  };

  const logList = [
    {
      title: "商品名称",
      dataIndex: "goodsName"
    },
    {
      title: "票种",
      dataIndex: "type",
      valueEnum: ticketTypeEnum
    },
    {
      title: "特殊折扣率",
      dataIndex: "overallDiscount"
    },
    {
      title: "分销折扣区间",
      dataIndex: "beginDiscount",
      renderText: (_: any, entity: any) => `${entity.beginDiscount} ~ ${entity.endDiscount}`
    },
    {
      title: "购买数量控制",
      dataIndex: "isPeopleNumber",
      valueEnum: whetherEnum
    },
    {
      title: "最小起订量",
      dataIndex: "minPeople"
    },
    {
      title: "单次最大预订量",
      dataIndex: "maxPeople"
    },
    {
      title: "出票规则",
      dataIndex: "issueName"
    },
    {
      title: "检票规则",
      dataIndex: "checkName"
    },
    {
      title: "退票规则",
      dataIndex: "retreatName"
    }
  ];

  useEffect(() => {
    productListReq.run({ scenicId, operatorId: [coId] });
    console.log(id);

    if (type === "edit" && id) {
      getGoodsInfoReq.run(id);
    } else {
      setDataSource({});
    }
    return () => {
      formRef.current?.resetFields();
    };
  }, [id, scenicId, type]);

  useEffect(() => {
    if (isDigit === "1") {
      formRef.current?.setFieldsValue({
        isDigit: true
      });
    }
  }, [isDigit]);

  return (
    <div className="relative">
      <ProCard
        title={
          <div
            className="flex align-items-center primary-color pointer"
            onClick={() => jumpPage.push("/ticket/ticket-type")}
          >
            <LeftOutlined style={{ marginRight: 10 }} />
            {type == "edit" ? "编辑商品" : "新增商品"}
          </div>
        }
        className="relative"
        headerBordered
      >
        <BetaSchemaForm
          formRef={formRef}
          layoutType="Form"
          initialValues={{
            overallDiscount: 100,
            beginDiscount: 0,
            endDiscount: 100,
            validityDay: 1
          }}
          scrollToFirstError
          preserve={false}
          columns={newColumns}
          submitter={false}
          onFinishFailed={() => {
            setImmediate(() => {
              document
                .querySelector(".ant-form-item-has-error")
                ?.scrollIntoView({ behavior: "smooth", block: "center" });
            });
          }}
          // 新增/编辑
          onFinish={async (val: any) => {
            const isAdd = type === "add";
            if (val.timeRestrict) val.timeShareVoList = timeList;
            if (!isAdd) {
              val.id = dataSource.id;
            }
            if (val.minPeople > val.maxPeople) {
              message.error("最小起订量不能大于最大预订量");
              return;
            }

            if (issueRuleItem?.type == 0 && checkItem?.adoptType == 1) {
              message.error("一票一人不能与一检多人关联");
              return;
            }
            if (val.beginDiscount > val.endDiscount) {
              message.error("分销折扣区间值不合理");
              return;
            }

            // 格式化数据
            // val.scenicName = scenicName;
            val.holidayPrice = val.holidayPrice * 1;
            val.weekendPrice = val.weekendPrice * 1;
            val.peopleNumber = val.peopleNumber * 1;
            val.times = val.timeShareId ? [val.timeShareId] : [];
            if (val.isPeopleNumber) {
              val.isPeopleNumber = 1;
            } else {
              val.isPeopleNumber = 0;
            }
            if (val.isTravel) {
              val.isTravel = 1;
            } else {
              val.isTravel = 0;
            }
            if (val.isCheck) {
              val.isCheck = 1;
            } else {
              val.isCheck = 0;
            }
            if (val.isRealName) {
              val.isRealName = 1;
            } else {
              val.isRealName = 0;
            }
            val.list = ["0"];
            val.saleChannel = dataSource.saleChannel;
            // 是否走工单流程
            const flag =
              val.beginDiscount !== dataSource.beginDiscount ||
              val.endDiscount !== dataSource.endDiscount ||
              val.overallDiscount !== dataSource.overallDiscount;
            if (flag && !isAdd) {
              try {
                const { data, code } = await getWorkOrderTempByType({
                  scenicId,
                  workOrderType: WorkOrderTypeEnum.商品价格编辑审批,
                  providerId: coId
                });
                if (code === 20000) {
                  setWorkOrderTempData(data);
                  setModifiedValues(omit(val, ["times", "list", "2"]));
                  // 触发工单 中断编辑
                  handleWorkOrder(omit(val, ["times", "list", "2"]), data);
                  return;
                }
              } catch (error) {}
            }

            const msgType = !isAdd ? "编辑" : "新增";
            const hide = message.loading("正在" + msgType);
            // const res = await apiSimpleGoodsAdd({ ...val });
            try {
              await (!isAdd
                ? apiSimpleGoodsUp({ ...val, createUserId: userId, remark: val.remark || " " })
                : apiSimpleGoodsAdd({
                    ...val,
                    createUserId: userId,
                    serviceChargeRate: 3
                  }));

              if (isAdd) {
                // 更新引导
                updateGuideInfo({ tabIndex: 0, status: GuideStepStatus.step0_2 });
                addOperationLogRequest({
                  action: "add",
                  content: `新增【${val.goodsName}】商品`
                });
              } else {
                addOperationLogRequest({
                  action: "edit",
                  changeConfig: {
                    list: logList,
                    beforeData: dataSource,
                    afterData: {
                      ...val,
                      issueName: issueRuleItem?.name,
                      checkName: checkItem?.name,
                      retreatName: retreatRuleItem?.name
                    }
                  },
                  content: `编辑【${val.goodsName}】商品`
                });
              }

              message.success(msgType + "成功");
              jumpPage.push("/ticket/ticket-type");
              actionRef?.current?.reload();
            } catch (error) {
              console.log(error);
            }
            hide();
          }}
          grid
        />

        <WorkOrderModal
          buttonLoading={addWorkOrderReq.loading}
          visible={workOrderModalVisible}
          setVisible={setWorkOrderModalVisible}
          workOrderTempData={workOrderTempData}
          onFinish={values => {
            const approver = Object.entries(values).map((item, index) => item[1]);
            createWorkOrder({
              approver
            });
          }}
        />
        <RuleSelectModal
          modalState={selectModalState}
          productInfo={getProductInfoReq.data || {}}
          onSelect={onRuleSelect}
        />
      </ProCard>

      <div
        className="flex w-100 justify-content-center align-items-center"
        style={{
          position: "sticky",
          height: 72,
          backgroundColor: "white",
          bottom: 0,
          zIndex: 2,
          boxShadow: "0px -2px 9px -1px rgba(208,208,208,0.5)"
        }}
      >
        <Space>
          <Button onClick={() => jumpPage.push("/ticket/ticket-type")} key="1">
            取消
          </Button>
          <Button type="primary" onClick={() => formRef.current?.submit()} key="2">
            确定
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default FCName;
