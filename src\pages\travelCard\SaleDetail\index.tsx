import Export, { columnsSet } from "@/common/components/Export";
import useExport from "@/common/components/Export/useExport";
import Tag from "@/common/components/Tag";
import { tableConfig } from "@/common/utils/config";
import { ticketStatusEnum } from "@/common/utils/enum";
import { downloadBlobFile } from "@/common/utils/tool";
import useModal from "@/hooks/useModal";
import {
  exportTravelCardBill,
  getOrderRightsGoodsDownList,
  getOrderRightsGoodsList,
  getOrderTCardList
} from "@/services/api/travelCard";
import type { ProFormInstance } from "@ant-design/pro-form";
import type { ActionType, ProColumns } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import { Space, Tabs, Typography } from "antd";
import { useForm, useWatch } from "antd/lib/form/Form";
import { trim } from "lodash";
import dayjs from "dayjs";
import type { FC } from "react";
import { useEffect, useRef, useState } from "react";
import { useModel, useRequest } from "@umijs/max";
import RightsGoodsDetail from "./components/RightsGoodsDetail";
import RightsTicketDetail from "./components/RightsTicketDetail";
import SubOrderDetail from "./components/SubOrderDetail";
import TicketInfoDetail from "./components/TicketInfoDetail";
import UsageRecord from "./components/UsageRecord";
import { getEnv } from "@/common/utils/getEnv";

const { Text } = Typography;

interface TCardSaleDetailProps {}

const TCardSaleDetail: FC<TCardSaleDetailProps> = () => {
  const { initialState } = useModel("@@initialState");
  const { scenicId = "" } = initialState?.scenicInfo || {};
  const { coId = "" } = initialState?.currentCompanyInfo || {};
  const actionRef = useRef<ActionType>(null);
  const [form] = useForm<ProFormInstance>();
  const type = useWatch("type", form);

  const [sumPayAmount, setSumPayAmount] = useState("");
  const [activeKey, setActiveKey] = useState<string>("card");

  const [cardItem, setCardItem] = useState<API.OrderTCardListItem>();
  const [ticketItem, setTicketItem] = useState<API.OrderRightsGoodsListItem>();

  const subOrderState = useModal();
  const rightsGoodsState = useModal();
  const usageRecordState = useModal();
  const ticketInfoState = useModal();
  const rightsTicketState = useModal();

  // 查询 form 的下落列表
  const getDownListReq = async (param: 1 | 2 | 3) => {
    const { data = [] } = await getOrderRightsGoodsDownList({
      serviceProviderId: coId,
      scenicId,
      type: param
    });
    return data.map(({ id, name }) => ({
      label: name,
      value: id
    }));
  };

  const cardColumns: ProColumns<API.OrderTCardListItem>[] = [
    {
      title: "订单号",
      dataIndex: "orderId",
      search: {
        transform: val => trim(val)
      }
    },
    {
      title: "权益卡号",
      dataIndex: "cardId",
      search: {
        transform: val => trim(val)
      }
    },
    {
      title: "发卡批次号",
      dataIndex: "sendCardBatchId",
      search: {
        transform: val => trim(val)
      }
    },
    {
      title: "权益卡名称",
      dataIndex: "productSkuName",
      search: false
    },

    {
      title: "有效期",
      dataIndex: "effectiveTime",
      search: false
    },
    {
      title: "使用次数",
      dataIndex: "useFrequency",
      search: false
    },
    {
      title: "姓名",
      dataIndex: "identityName",
      search: false
    },

    {
      title: "支付时间",
      dataIndex: "payTime",
      valueType: "dateRange",
      hideInTable: true,
      search: {
        transform: values => ({
          payTime: dayjs(values[0]).startOf("day").format("YYYY-MM-DD HH:mm:ss"),
          payEndTime: dayjs(values[1]).endOf("day").format("YYYY-MM-DD HH:mm:ss")
        })
      }
    },
    {
      title: "支付时间",
      dataIndex: "payTime",
      search: false,
      renderText: text => dayjs(text).format("YYYY-MM-DD HH:mm:ss")
    },

    {
      title: "权益卡名称",
      dataIndex: "productSkuId",
      valueType: "select",
      search: {
        transform: val => trim(val)
      },
      hideInTable: true,
      request: () => getDownListReq(1),
      debounceTime: 300,
      fieldProps: {
        showSearch: true
      }
    },
    {
      title: "支付金额（元）",
      dataIndex: "payAmount",
      search: false,
      align: "right",
      render: (dom, record) => (record.payAmount * 1).toFixed(2)
    },
    {
      title: "操作",
      valueType: "option",
      fixed: "right",
      render: (_: any, entity: any) => (
        <Space>
          <a
            onClick={() => {
              setCardItem(entity);
              subOrderState.setVisible(true);
            }}
          >
            查看
          </a>
          <a
            onClick={() => {
              setCardItem(entity);
              rightsGoodsState.setVisible(true);
            }}
          >
            权益卡
          </a>
          <a
            onClick={() => {
              setCardItem(entity);
              usageRecordState.setVisible(true);
            }}
          >
            使用记录
          </a>
        </Space>
      )
    }
  ];
  const ticketColumns: ProColumns<API.OrderRightsGoodsListItem>[] = [
    {
      title: "订单号",
      dataIndex: "orderId"
    },
    {
      title: "票号",
      dataIndex: "ticketNumber"
    },
    {
      title: "权益票",
      dataIndex: "rightsTicketId",
      valueType: "select",
      request: () => getDownListReq(2),
      debounceTime: 300,
      fieldProps: {
        showSearch: true
      },
      renderText: (_, record) => (record.rightsTicketName ? record.rightsTicketName : "-")
    },

    {
      title: "所属景区",
      dataIndex: "ticketScenicId",
      search: false,
      valueType: "select",
      request: () => getDownListReq(3),
      debounceTime: 300,
      fieldProps: {
        showSearch: true
      },
      render: (_, { scenicName }) => scenicName || "-",
    },
    {
      title: "所属权益卡",
      dataIndex: "rightsCardId",
      valueType: "select",
      request: () => getDownListReq(1),
      debounceTime: 300,
      fieldProps: {
        showSearch: true
      },
      renderText: (_, record) => (record.rightsCardName ? record.rightsCardName : "-")
    },
    {
      title: "入园日期",
      dataIndex: "enterTime",
      valueType: "date",
      search: false
    },
    {
      title: "入园日期",
      dataIndex: "enterTime",
      valueType: "dateRange",
      hideInTable: true,
      search: {
        transform: values => ({
          enterBeginTime: values[0],
          enterEndTime: values[1]
        })
      }
    },
    {
      title: "状态",
      dataIndex: "status",
      valueEnum: ticketStatusEnum,
      search: false,
      fixed: "right",
      renderText: (dom: any) => <Tag type="ticketStatus" value={dom} />
    },
    {
      title: "出票日期",
      dataIndex: "createTime",
      valueType: "dateRange",
      hideInTable: true,
      search: {
        transform: values => ({
          beginTime: dayjs(values[0]).startOf("day").format("YYYY-MM-DD HH:mm:ss"),
          endTime: dayjs(values[1]).endOf("day").format("YYYY-MM-DD HH:mm:ss")
        })
      }
    },
    {
      title: "出票时间",
      dataIndex: "createTime",
      search: false,
      renderText: text => dayjs(text).format("YYYY-MM-DD HH:mm:ss")
    },
    {
      title: "支付金额（元）",
      dataIndex: "payAmount",
      search: false,
      align: "right",
      render: (dom, record) => (record.payAmount * 1).toFixed(2)
    },
    {
      title: "类型",
      dataIndex: "type",
      valueEnum: {
        1: "当前景区的权益票",
        2: "权益卡所关联的权益票"
      },
      initialValue: "1",
      hideInTable: true
    },
    {
      title: "操作",
      valueType: "option",
      fixed: "right",
      render: (_: any, entity: any) => (
        <Space>
          <a
            onClick={() => {
              setTicketItem(entity);
              ticketInfoState.setVisible(true);
            }}
          >
            查看
          </a>
          <a
            onClick={() => {
              setTicketItem(entity);
              rightsTicketState.setVisible(true);
            }}
          >
            权益票
          </a>
          <a
            onClick={() => {
              setTicketItem(entity);
              rightsGoodsState.setVisible(true);
            }}
          >
            所属权益卡
          </a>
        </Space>
      )
    }
  ];
  const [exportParams, setExportParams] = useState();

  const exportBillReq = useRequest(exportTravelCardBill, {
    manual: true,
    formatResult(res) {
      downloadBlobFile(res, `销售明细权益卡订单.xlsx`, "vnd.ms-excel");
    }
  });

  const mapObj = {
    card: {
      columns: cardColumns,
      async request(params: any) {
        try {
          const { data } = await getOrderTCardList(params);
          setExportParams(params);
          setSumPayAmount(data.sumPayAmount);
          return data;
        } catch (error) {
          return {
            data: []
          };
        }
      }
    },
    ticket: {
      columns: ticketColumns,
      async request(params: any) {
        try {
          console.log("getOrderRightsGoodsList", params);

          const { data } = await getOrderRightsGoodsList({ type: "1", ...params });
          setSumPayAmount(data.sumPayAmount);
          return data;
        } catch (error) {
          return {
            data: []
          };
        }
      }
    }
  };
  const exportState = useExport({
    columns: mapObj.card.columns,
    modulePath: "Backend_TravelCard",
    params: { serviceProviderId: coId, scenicId }
  });

  useEffect(() => {
    if (actionRef.current?.reset) {
      actionRef.current?.reset();
    }
  }, [activeKey]);

  return (
    <>
      <Tabs
        tabBarStyle={{ padding: "0 24px", margin: "0", background: "#fff" }}
        onChange={setActiveKey}
        activeKey={activeKey}
        items={[
          {
            label: "权益卡",
            key: "card"
          },
          {
            label: "权益票",
            key: "ticket"
          }
        ]}
      />
      <ProTable
        {...tableConfig}
        // form={form}
        actionRef={actionRef}
        columns={mapObj[activeKey].columns}
        pagination={{ defaultPageSize: 10 }}
        params={{ serviceProviderId: coId, scenicId }}
        request={mapObj[activeKey].request}
        footer={() => {
          return (
            <div style={{ textAlign: "right" }}>
              <Text strong>总计：{sumPayAmount} 元 </Text>
            </div>
          );
        }}
        formRef={exportState.formRef}
        columnsState={columnsSet(exportState)}
        toolBarRender={
          activeKey === "card"
            ? () => [
                <Export key="export" {...exportState} />
                // <Button
                //   key="export"
                //   type="primary"
                //   onClick={() => {
                //     location.href = `${
                //       getEnv().API_HOST
                //     }/order/export/orderTravelCardList?=${qs.stringify(exportParams)}`;
                //     // exportBillReq.run(exportParams);
                //   }}
                // >
                //   导出报表
                // </Button>,
              ]
            : false
        }
      />
      {/* 订单详情 */}
      <SubOrderDetail {...subOrderState} orderId={cardItem?.orderId} />

      {/* 权益卡详情 */}
      <RightsGoodsDetail
        {...rightsGoodsState}
        isTravelCard={activeKey === "card"}
        dataItem={ticketItem}
        productSkuId={activeKey === "card" ? cardItem?.productSkuId : ticketItem?.rightsCardId}
      />

      {/* 使用记录 */}
      <UsageRecord {...usageRecordState} cardId={cardItem?.cardId} orderId={cardItem?.orderId} />

      {/* 门票详情 */}
      <TicketInfoDetail {...ticketInfoState} ticketId={ticketItem?.ticketNumber} />

      {/* 权益票详情 */}
      <RightsTicketDetail {...rightsTicketState} id={ticketItem?.rightsTicketId} />
    </>
  );
};

export default TCardSaleDetail;
