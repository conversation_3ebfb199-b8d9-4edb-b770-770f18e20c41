/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-03-16 17:54:18
 * @LastEditTime: 2023-03-20 15:22:59
 * @LastEditors: zhangfengfei
 */
import url from "@/assets/imgs/bg.png";
import { goToLogin } from "@/common/utils/tool";
import Footer from "@/components/Footer";
import { apiRenewScenic } from "@/services/api/erp";
import { Modal } from "antd";
import dayjs from "dayjs";
import type { FC } from "react";
import { useEffect } from "react";
import { useModel } from "@umijs/max";

const cancelButtonProps = {
  style: {
    display: "none"
  }
};

interface HomeProps {}

const Home: FC<HomeProps> = () => {
  const { initialState }: any = useModel("@@initialState");

  const { scenicId, appId, endDate } = initialState?.scenicInfo;
  //  过期
  const isExpired = dayjs().isAfter(endDate, "day");

  useEffect(() => {
    if (isExpired) {
      Modal.confirm({
        icon: null,
        content: (
          <div style={{ textAlign: "center" }}>
            <span>您已超过七天体验试用期</span>
          </div>
        ),
        cancelButtonProps,
        onOk: async (...args) => {
          // 续期
          const res = await apiRenewScenic(scenicId);
          Modal.confirm({
            icon: null,
            content: (
              <div style={{ textAlign: "center" }}>
                <span>续费成功！</span>
              </div>
            ),
            okText: "登录",
            cancelButtonProps,
            onOk: (...args) => {
              goToLogin({ scenicId });
            }
          });
          return res;
        },
        okText: "续一下"
      });
    }
  }, [isExpired, scenicId]);

  return (
    <div style={{ position: "relative" }}>
      <img src={url} width={"100%"} alt="图片" />
      <div
        style={{
          position: "absolute",
          width: "100%",
          left: 0,
          bottom: 0
        }}
      >
        <Footer color="white" />
      </div>
    </div>
  );
};

export default Home;
