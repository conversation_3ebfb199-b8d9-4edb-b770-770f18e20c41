import PrefixTitle from '@/common/components/PrefixTitle';
import Province from '@/common/components/Province';
import { GuideStepStatus, ScenicServiceType, ServiceGrade } from '@/common/utils/enum';
import { formWidth } from '@/common/utils/gConfig';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { removeStateFromUrl } from '@/common/utils/tool';
import { letterOrNumberValidator, mobileValidator } from '@/common/utils/validate';
import ImageUpload from '@/components/ImageUpload';
import MDEditor from "@/components/MDEditor";
import { useGuide } from "@/hooks/useGuide";
import type { ModalState } from "@/hooks/useModal";
import { editScenicInfo } from "@/services/api/erp";
import { LeftOutlined } from "@ant-design/icons";
import { BetaSchemaForm, ProCard, ProForm } from "@ant-design/pro-components";
import type { ProFormColumnsType } from "@ant-design/pro-form/lib/components/SchemaForm";
import { Button, Space, message } from "antd";
import { useForm } from "antd/lib/form/Form";
import { cloneDeep, merge } from "lodash";
import type { FC } from "react";
import Map from "./Map";

const logList = [
  {
    title: "旅游服务名称",
    dataIndex: "name"
  },
  {
    title: "旅游服务类型",
    dataIndex: "type",
    valueEnum: ScenicServiceType
  },
  {
    title: "景区等级",
    dataIndex: "grade",
    valueEnum: ServiceGrade
  },
  {
    title: "营业时间",
    dataIndex: "businessTime"
  },
  {
    title: "联系人",
    dataIndex: "contracts"
  },
  {
    title: "联系方式",
    dataIndex: "contractsPhone"
  },
  {
    title: "邮箱",
    dataIndex: "email"
  },
  {
    title: "全景链接",
    dataIndex: "panoramaUrl"
  },
  {
    title: "省市区",
    dataIndex: "location"
  },
  {
    title: "详细地址",
    dataIndex: "address"
  },
  {
    title: "纬度",
    dataIndex: "latitude"
  },
  {
    title: "经度",
    dataIndex: "longitude"
  }
];

interface ScenicInfoEditModalProps {
  modalState: ModalState;
  scenicData: Scenic.ScenicData;
  onSuccess?: () => void;
}

const ScenicInfoEditModal: FC<ScenicInfoEditModalProps> = ({
  modalState: { visible, setVisible },
  scenicData,
  onSuccess
}) => {
  const { scenicAttribute, scenicBusiness, scenicAddress, scenic } = scenicData;
  const { updateGuideInfo } = useGuide();

  // 特殊处理的几个字段
  const { provinceName, cityName, areaName } = scenicAddress || {};
  const [form] = useForm();
  const columns: ProFormColumnsType<any>[] = [
    {
      title: "基础信息",
      valueType: "group",

      columns: [
        {
          title: "旅游服务名称",
          dataIndex: ["scenic", "name"],

          formItemProps: { rules: [{ required: true, max: 30 }] }
        },
        {
          title: "景区别名",
          dataIndex: ["scenic", "alias"]
        },
        {
          title: "旅游服务类型",
          dataIndex: ["scenic", "type"],

          valueType: "select",
          valueEnum: ScenicServiceType,
          formItemProps: { rules: [{ required: true }] }
        },
        {
          title: "景区等级",
          dataIndex: ["scenic", "grade"],

          valueType: "select",
          formItemProps: { rules: [{ required: true }] },
          valueEnum: ServiceGrade
        },
        {
          width: "100%",
          title: "营业时间",
          dataIndex: ["scenicBusiness", "businessTime"],
          valueType: "timeRange",

          initialValue: [scenicBusiness?.businessStartTime || null, scenicBusiness?.businessEndTime || null],
          fieldProps: {
            format: "HH:mm"
          },
          transform: (value: any[]) => {
            return {
              scenicBusiness: {
                businessStartTime: value[0],
                businessEndTime: value[1]
              }
            };
          }
        },
        {
          title: "联系人",

          dataIndex: ["scenic", "contracts"],
          formItemProps: { rules: [{ required: true }, { max: 10, message: "最多 10 位字符" }] }
        },
        {
          title: "联系方式",

          dataIndex: ["scenic", "contractsPhone"],
          formItemProps: {
            rules: [{ required: true }, mobileValidator()]
          }
        },
        {
          title: "邮箱",
          dataIndex: ["scenic", "email"]
        },
        {
          title: "全景链接",
          dataIndex: ["scenic", "panoramaUrl"]
        },

        {
          title: "景区 logo",
          dataIndex: ["scenic", "scenicLogo"],
          renderFormItem: () => {
            return <ImageUpload defaultValue={scenic?.scenicLogo || ""} />;
          }
        },
        {
          title: "景区图片",
          dataIndex: ["scenicBusiness", "picture"],

          renderFormItem: () => {
            return <ImageUpload defaultValue={scenicBusiness?.picture || ""} maxCount={6} />;
          }
        },
        {
          title: "景区信息",
          dataIndex: ["scenic", "remark"],
          colProps: {
            span: 24
          },
          renderFormItem: () => (
            <ProForm.Item
              name={["scenic", "remark"]}
              // label="备注信息"
              rules={[
                {
                  type: "string",
                  max: 2000
                }
              ]}
            >
              <MDEditor />
            </ProForm.Item>
          )
        }
      ]
    },
    {
      title: "地理位置",
      valueType: "group",

      columns: [
        {
          title: "省市区",
          dataIndex: ["scenicAddress", "location"],

          initialValue: [provinceName, cityName, areaName],
          formItemProps: { rules: [{ required: true }] },
          transform: (value: any[]) => {
            const { provinceName, cityName, areaName, provinceCode, cityCode, areaCode } =
              scenicData.scenicAddress || {};

            return {
              scenicAddress: {
                provinceCode: value[0].addressId || provinceCode,
                provinceName: value[0].addressName || provinceName,
                cityCode: value[1].addressId || cityCode,
                cityName: value[1].addressName || cityName,
                areaCode: value[2].addressId || areaCode,
                areaName: value[2].addressName || areaName
              }
            };
          },
          renderFormItem: () => {
            return <Province width="100%" />;
          }
        },
        {
          title: "详细地址",
          dataIndex: ["scenicAddress", "address"],

          formItemProps: { rules: [{ required: true }] },
          fieldProps: {
            allowClear: true
          }
        },
        {
          width: "100%",
          title: "纬度",
          dataIndex: ["scenicAddress", "latitude"],
          valueType: "digit",

          fieldProps: {
            disabled: true
          },
          formItemProps: { rules: [{ required: true }] }
        },
        {
          width: "100%",
          title: "经度",
          valueType: "digit",
          dataIndex: ["scenicAddress", "longitude"],

          fieldProps: {
            disabled: true
          },
          formItemProps: { rules: [{ required: true }] }
        },
        {
          title: "景区范围",
          valueType: "textarea",
          dataIndex: ["scenicAddress", "scenicRange"],
          colProps: {
            span: 24
          },
          fieldProps: {
            disabled: true
          },
          formItemProps: { rules: [{ required: true }] }
        },
        {
          title: "地图选点和范围",
          colProps: { span: 24 },
          renderFormItem: (_, __, formRef) => <Map {...{ formRef }} />
        }
      ]
    },
    {
      title: "景区属性",
      valueType: "group",

      columns: [
        {
          title: "唯一标识符",
          dataIndex: ["scenicAttribute", "uniqueIdentity"],

          fieldProps: {
            disabled: true
          },
          formItemProps: {
            rules: [
              { required: true, type: "string", whitespace: true },
              { max: 20, message: "最多 20 位字符" },
              letterOrNumberValidator()
            ],
            tooltip: "用于设置景区独立域名"
          }
        },
        {
          title: <span style={{ width: formWidth.sm }}>是否启用区块链</span>,

          dataIndex: ["scenicAttribute", "isBlockChain"],
          valueType: "switch",
          initialValue: scenicAttribute?.isBlockChain === 1,
          transform: value => {
            return {
              scenicAttribute: {
                isBlockChain: value ? 1 : 0
              }
            };
          },
          fieldProps: {
            disabled: true,
            checkedChildren: "启用",
            unCheckedChildren: "禁用"
          }
        }
      ]
    }
  ];
  // 统一风格
  const newColumns = columns.map((item: any) => {
    const childColumns = (item.columns ?? []).map(i => ({
      width: "100%",
      colProps: { xs: 24, sm: 12, xl: 8, xxl: 6 },
      ...i
    }));

    return {
      rowProps: { gutter: 24 },
      valueType: "group",
      ...item,
      title: item.title && <PrefixTitle>{item.title} </PrefixTitle>,
      columns: childColumns
    };
  });

  const onFinish = async (values: Scenic.ScenicData) => {
    const data = cloneDeep(scenicData);
    const params = merge(data, values);
    await editScenicInfo(params);

    // 更新引导
    updateGuideInfo({ tabIndex: 1, status: GuideStepStatus.step1_3 });
    history.pushState(null, null, removeStateFromUrl("type"));

    const getChangeData = (source: Record<string, any>) => {
      const { scenic, scenicAddress, scenicBusiness } = source;
      return {
        ...scenic,
        ...scenicAddress,
        ...scenicBusiness,
        businessTime: scenicBusiness?.businessStartTime + "-" + scenicBusiness?.businessEndTime,
        location: `${scenicAddress?.provinceName}${scenicAddress?.cityName}${scenicAddress?.areaName}`
      };
    };

    addOperationLogRequest({
      action: "edit",
      changeConfig: {
        list: logList,
        beforeData: getChangeData(scenicData),
        afterData: getChangeData(values)
      },
      content: `编辑【${values?.scenic?.name}】景区`
    });
    setVisible(false);
    message.success("修改成功");
    if (onSuccess) {
      onSuccess();
    }
    return true;
  };
  return (
    <div className="relative">
      <ProCard
        title={
          <div
            className="flex align-items-center primary-color pointer"
            onClick={() => {
              setVisible(false);
              history.pushState(null, null, removeStateFromUrl("type"));
            }}
          >
            <LeftOutlined style={{ marginRight: 10 }} />
            编辑景区信息
          </div>
        }
        className="relative"
        headerBordered
      >
        <BetaSchemaForm
          scrollToFirstError
          form={form}
          initialValues={scenicData}
          onFinish={onFinish}
          columns={newColumns}
          rowProps={{ gutter: 24 }}
          submitter={false}
          preserve={false}
          grid
        />
      </ProCard>
      <div
        className="flex w-100 justify-content-center align-items-center"
        style={{
          position: "sticky",
          height: 72,
          backgroundColor: "white",
          bottom: 0,
          zIndex: 2,
          boxShadow: "0px -2px 9px -1px rgba(208,208,208,0.5)"
        }}
      >
        <Space>
          <Button onClick={() => setVisible(false)} key="1">
            取消
          </Button>
          <Button type="primary" onClick={form.submit} key="2">
            确定
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default ScenicInfoEditModal;
