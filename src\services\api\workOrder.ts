/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-27 10:11:02
 * @LastEditTime: 2023-07-24 09:23:59
 * @LastEditors: zhangfengfei
 */

import type { WorkOrderTempType, WorkOrderTypeEnum } from "@/pages/workOrder/common/data";
import { request } from "@umijs/max";
import { scenicHost } from ".";

/** 查询企业角色列表 */
export function getRoleDownList(params: API.RoleListParams) {
  return request<ResponseData<API.RoleListItem[]>>(`${scenicHost}/role/listAll`, {
    method: "GET",
    params
  });
}

interface UserItem {
  companyId: string;
  companyIds: any[];
  status: number;
  userId: string;
  nickname: string;
  username: string;
}

/** 查询企业用户列表 */
export function getUserList(params: { scenicId: string; companyId: string; status?: number; appId: string }) {
  return request<ResponseData<UserItem[]>>(`${scenicHost}/orgStructure/userCompanyInfoList`, {
    method: "POST",
    data: { ...params, system: 1 }
  });
}

/** 查询工单模板列表 */
export function getWorkOrderTempList(params: API.WorkOrderTempListParams) {
  return request<ResponseListData<API.WorkOrderTempListItem[]>>(`${scenicHost}/workOrder/pageList`, {
    method: "GET",
    params
  });
}
/** 创建工单模板 */
export function addWorkOrderTemp(params: WorkOrderTempType) {
  return request<ResponseData<null>>(`${scenicHost}/workOrder/save`, {
    method: "POST",
    data: params
  });
}
/** 编辑工单模板 */
export function updateWorkOrderTemp(params: WorkOrderTempType) {
  return request<ResponseData<null>>(`${scenicHost}/workOrder/update`, {
    method: "POST",
    data: params
  });
}
/** 启用/禁用工单模板 */
export function enableWorkOrderTemp(
  params: Pick<WorkOrderTempType, "workOrderType" | "providerId" | "scenicId" | "state"> & {
    workOrderId: string;
  }
) {
  return request<ResponseData<null>>(`${scenicHost}/workOrder/disable`, {
    method: "PUT",
    data: params
  });
}
/** 删除工单模板 */
export function deleteWorkOrderTemp(params: { id: string }) {
  return request<ResponseData<null>>(`${scenicHost}/workOrder/info/${params.id}`, {
    method: "DELETE",
    data: params
  });
}

/** 工单模板详情 */
export function getWorkOrderTempInfo(params: { id: string }) {
  return request<ResponseData<WorkOrderTempType>>(`${scenicHost}/workOrder/info/${params.id}`, {
    method: "GET"
    // params,
  });
}

/** 查询工单脚本下拉列表 */
export function getScriptDownList(params: { type: WorkOrderTypeEnum }) {
  return request<ResponseData<API.WorkOrderScriptListItem[]>>(`${scenicHost}/workOrderScript/list/${params.type}`, {
    params,
    method: "GET"
  });
}

// 工单列表
export function getWorkOrderList(params: API.WorkOrderListParams) {
  return request<ResponseListData<API.WorkOrderListItem[]>>(`${scenicHost}/issue/list`, {
    params
  });
}

// 工单详情
export function getWorkOrderInfo(params: API.WorkOrderInfoParams) {
  return request<ResponseData<API.WorkOrderInfoData>>(`${scenicHost}/issue/info`, {
    params
  });
}

// 根据类型查询启用的工单模板
export function getWorkOrderTempByType(params: {
  scenicId: string;
  workOrderType: WorkOrderTypeEnum;
  providerId: string;
}) {
  return request<ResponseData<WorkOrderTempType>>(`${scenicHost}/workOrder/infoByType`, {
    params,
    skipErrorHandler: true
  });
}

// 根据类型查询启用工单并组合成工单格式
export function getWorkOrderNodes(params: {
  approver?: string[];
  group: string;
  platformId: string;
  providerId: string;
  scenicId: string;
  workOrderType: WorkOrderTypeEnum;
}) {
  return request<
    ResponseData<{
      id: string;
      nodes: API.ResNodeItem[];
      workOrderName: string;
    }>
  >(`${scenicHost}/workOrder/nodes`, {
    params: { ...params, system: 1 },
    method: "GET"
  });
}

// 创建工单
export function addWorkOrder(params: API.AddWorkOrderParams) {
  return request<ResponseData<null>>(`${scenicHost}/issue/info`, {
    data: params,
    method: "POST"
  });
}
//  工单审批 - 加签
export function approvalWorkOrder(params: API.ApprovalWorkOrderOrderParams) {
  return request<ResponseData<null>>(`${scenicHost}/issue/approval`, {
    data: params,
    method: "PUT"
  });
}
