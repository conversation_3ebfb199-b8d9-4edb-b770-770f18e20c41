/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-05-08 15:00:19
 * @LastEditTime: 2023-10-30 11:41:34
 * @LastEditors: z<PERSON><PERSON><PERSON>i
 */
/**
 * 全局枚举类型
 **/

// 企业类型
export const companyType = {
  0: '景区运营',
  1: '分销商',
  // 2: '环球数科股份有限公司',
  3: '政府',
};

// 数字资产
export const isChainEnum = {
  0: '否',
  1: '是',
};

// 票种类型
export const ticketTypeEnum = {
  0: '成人票',
  1: '儿童票',
  2: '老人票',
  3: '保险票',
  4: '全价票',
  5: '半价票',
  // 6: '团体票',
  7: '团体票',
};

/** 出票类型 */
export const IssueTypeEnum = {
  0: '一票一人',
  1: '一票多人',
};

/* 检票类型 */
export const CheckTypeEnum = {
  0: '一检一人',
  1: '一检多人',
};

export const RuleTypeEnum = {
  1: '权益票规则',
  2: '权益卡规则',
  3: '普通票规则',
};

// 产品类型（基础票）
export const baseProductTypeEnum = {
  0: '门票',
  1: '船票',
  2: '缆车',
};

// 产品类型
export const productTypeEnum = {
  ...baseProductTypeEnum,
  20: '权益卡',
};

// 身份识别类型
export const identityType = {
  1: '人脸',
  2: '票',
  3: '身份证',
};

// 售票设备类型
export const SaleTicketType = {
  1: '自助售票',
  2: '窗口售票',
};

// 检票点类型
export const checkType = {
  0: '入口',
  1: '码头',
};
// 检票设备类型
export const checkDeviceType = {
  0: '手持机',
  1: '闸机',
  2: 'App',
};
// 检票终端类型
export const equipmentTypeEnum = {
  ...checkDeviceType,
  3: '后台',
};

// 使用方式
export const useTypeEnum = {
  0: '按次数',
  1: '按时长',
};

// 销售渠道
export const saleChannelEnum = {
  0: 'H5', // 分销商
  1: '微商城',
  2: 'PC 商城',
  3: '扫码购',
  4: '自助机',
  5: '售票窗口',
  6: 'App',
};

// 星期
export const weekEnum = {
  0: '星期日',
  1: '星期一',
  2: '星期二',
  3: '星期三',
  4: '星期四',
  5: '星期五',
  6: '星期六',
};

// 订单状态
export const orderTypeEnum = {
  10: '创建订单',
  11: '已取消',
  12: '订单超时失效',
  13: '用户取消订单',
  14: '系统取消订单',
  20: '待付款',
  21: '支付成功',
  22: '支付失败',
  30: '已完成',
  31: '出库成功',
  32: '出库失败',
  33: '出票失败',
  34: '出票成功',
  50: '退款中',
  51: '退款成功',
  52: '退款失败',
  53: '退款取消',
  54: '部分退款',
  55: '退款',
};

// 结算方式
export const payTypeEnum = {
  0: '扫码支付',
  1: '银联卡',
  2: '现金支付',
  3: '微信支付',
  4: '支付宝',
  5: '区块链支付',
  6: '微信公众号支付',
  7: '银联在线',
  8: '银联商务 POS 通',
  9: '银联商务公众号支付',
  10: '银联商务网关支付',
  11: '银联商务 WAP',
  12: '支付宝服务商',
  13: '微信服务商',
  14: '银联商务 B2B',
  15: '银联商务小程序支付',
  16: '区块链分销系统支付',
  17: '银联商务 C 扫 B',
  18: '信用支付',
  19: '其他支付',
  20: '授信支付',
  21: '0 元购',
  22: '银联商务 wap 支付',
  23: '窗口售票 pos 支付',
  24: '预授信支付',
};

// 票状态
export const ticketStatusEnum = {
  0: '未核销',
  1: '部分核销',
  2: '已过期',
  3: '已完成',
  4: '已退票',
};
// 票状态（样式配置）
export const ticketStatusColor = {
  0: 'orange',
  1: 'green',
  2: '',
  3: 'blue',
  4: 'blue',
};

// 结算开户状态
export const accountStatusEnum = {
  0: '待开户',
  1: '审核通过',
  2: '审核拒绝',
  3: '审核中',
};

// 售票类型
export const sourceTypeEnum = {
  0: '单票',
  1: '套票',
};

// 订单票种类型
export const orderTicketTypeEnum = {
  JQMP: '景区门票',
  JQTC: '权益卡',
};

export const ScenicServiceType = {
  1: '景区',
};
// 服务商等级
export const ServiceGrade = {
  0: '未评',
  1: '国家 A 级景区',
  2: '国家 2A 级景区',
  3: '国家 3A 级景区',
  4: '国家 4A 级景区',
  5: '国家 5A 级景区',
};

// 实名方式
export const realEnum = {
  0: '非实名',
  1: '身份证',
};

// 1 是 0 否
export const whetherEnum = {
  0: '否',
  1: '是',
};

// 分时预约检票信息
export const timeShareBookEnum = {
  0: '检票时间不可提前不可延后',
  1: '检票时间可提前但不可延后',
  2: '检票时间不可提前但可延后',
  3: '检票时间可提前可延后',
};

export enum TravelCardApproveStatus {
  待审核,
  审核失败,
  审核通过,
}

export const RightsTicketStatusEnum = {
  0: '非权益票',
  1: '待审核',
  2: '审核通过',
  3: '审核失败',
};

export enum RightsTicketStatus {
  非权益票,
  待审核,
  审核通过,
  审核失败,
}

export enum BooleanEnum {
  否,
  是,
}

// 折扣率枚举
export const discountRate = {
  0: '特殊折扣率',
  8: '特殊折扣率',
  2: '特殊折扣率',
  3: '特殊折扣率',
  4: '网订折扣率',
  5: '特殊折扣率',
  7: '团体折扣率',
};

// 工单状态
export const IssueStatus = {
  1: '待审批',
  2: '已同意',
  3: '已拒绝',
};

// 工单审批
export const IssueApproval = {
  2: '同意',
  3: '拒绝',
  4: '加签',
};

export const ScenicServiceStatus = {
  0: '试用期过期',
  1: '试用期中',
  2: '正式期',
  3: '正式期过期',
};

// 点位类型
export const pointType = {
  1: '景点',
  2: '住宿',
  3: '餐饮',
  4: '购物',
  5: '娱乐',
  6: '卫生间',
  7: '停车场',
  8: '出入口',
  9: '服务点',
  10: '乘车点',
  11: '售票处',
  12: '医务室',
  13: '母婴室',
  14: '其它',
};

// 语音类型
export const voiceType = {
  白术: '男声 1',
  神里绫人: '男声 2',
  凯瑟琳: '女声 1',
  阿贝多2lanlan: '女声 2',
  八重神子2xingxing: '女声 3',
  琴: '女声 4',
  可莉: '童声',
};

// ai 文案类型
export const wordTypeEnum = {
  0: '综合版',
  1: '少儿版',
  2: '英文版',
  3: '文化版',
  4: '春天版',
  5: '夏天版',
  6: '秋天版',
  7: '冬天版',
};

// 库存类型
export const stockType: any = {
  0: '普通库存',
  1: '区块链库存',
};

// 禁启用状态
export const enableEnum = {
  0: '禁用',
  1: '启用',
};
// 文章类型
export const ArticleType = {
  1: '攻略',
  2: '资讯',
  3: '活动',
  4: '游记',
};

// 导览类型
export const ArticleSourceType = {
  1: '内部',
  2: '外链',
};

// 导览类型
export const ArticleEnableType = {
  1: '禁用',
  2: '启用',
};

export enum StoreGoodsTypeEnum {
  单票 = 1,
  组合票,
}

// 票种类型
export enum TicketTypeEnum {
  成人票,
  儿童票,
  老人票,
  保险票,
  全价票,
  半价票,
  团体票 = 7,
}

export enum UnitTypeEnum {
  单票 = 1,
  权益卡,
}

export const DocumentTypeSelect = {
  ID_CARD: '身份证',
  // { label: '护照', value: 'PASSPORT' },
  // { label: '港澳居民来往内地通行证', value: 'HK_MACAU_PERMIT' },
  // { label: '台湾居民来往大陆通行证', value: 'TAIWAN_PERMIT' },
};

//有效期类型，可用值:FIXED,LONG_TERM
export const ExpirationDateTypeEnum = [
  { label: '长期', value: 'LONG_TERM' },
  { label: '固定', value: 'FIXED' },
];

export const DownloadStatus = {
  0: {
    text: '生成中',
    color: 'green',
  },
  1: {
    text: '已生成',
    color: 'blue',
  },
  2: {
    text: '生成失败',
    color: 'red',
  },
};

/**
 * 引导步骤
 */
export const GuideStepStatus = {
  // tabIndex 0
  step0_1: '完成企业认证',
  step0_2: '完成商品创建',
  step0_3: '完成库存创建',
  step0_4: '景区点位管理',
  step0_5: '景区线路管理',
  step0_6: '销售渠道搭建',
  // tabIndex 1
  step1_1: '完成权益卡创建',
  step1_2: '完成权益卡库存创建',
  step1_3: '完成景区地图定位',
  step1_4: '官网商品管理',
  step1_5: '创建宣传文章',
  step1_6: '创建帮助说明',
  step1_7: '装修官网导航',
  step1_8: '装修官网主页',
};

// ai 知识库识别状态
export const AIKnowledgeBaseIdentifyStatus = {
  DISABLE: '禁用',
  IDENTIFY: '识别中',
  IDENTIFY_FAIL: '识别失败',
  IDENTIFY_SUCCESS: '识别成功',
  START: '开始识别',
};
export const AIKnowledgeBaseFileStatus = {
  RECOGNIZING: {
    text: '识别中',
    color: 'green',
  },
  SUCCEED: {
    text: '识别成功',
    color: 'blue',
  },
  FAILED: {
    text: '识别失败',
    color: 'red',
  },
};
