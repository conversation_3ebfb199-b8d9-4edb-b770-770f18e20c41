/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-04-20 10:16:00
 * @LastEditTime: 2023-04-21 16:49:16
 * @LastEditors: zhangfengfei
 */
import Delete from "@/common/components/Delete";
import Disabled from "@/common/components/Disabled";
import useModal from "@/common/components/ProModal/useModal";
import { tableConfig } from "@/common/utils/config";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import { getHashParams } from "@/common/utils/tool";
import { deleteTravelCard, getTravelCardList, switchTravelCard } from "@/services/api/travelCard";
import { PlusOutlined } from "@ant-design/icons";
import type { ActionType, ProColumns } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import { Button, Space } from "antd";
import { isNil, trim } from "lodash";
import type { FC } from "react";
import { useEffect, useRef, useState } from "react";
import { Access, useAccess, useModel } from "@umijs/max";
import EditChargeRatePop from "../../ticket/TicketType/components/EditChargeRatePop";
import TravelCardModal from "./components/TravelCardModal";

type TravelCardProps = Record<string, never>;

const TravelCard: FC<TravelCardProps> = () => {
  const { initialState } = useModel("@@initialState");
  const { scenicId = "", scenicName } = initialState?.scenicInfo || {};
  const { coId = "" } = initialState?.currentCompanyInfo || {};
  const access = useAccess();
  const queryParams = getHashParams();

  const actionRef = useRef<ActionType>();

  const [travelCardItem, setTravelCardItem] = useState<API.TravelCardListItem>();

  // modal 状态
  const travelCardModal = useModal();

  const columns: ProColumns<API.TravelCardListItem>[] = [
    {
      title: "编号",
      dataIndex: "id",
      hideInTable: true,
      search: false
    },
    {
      title: "权益卡名称",
      dataIndex: "travelCardName",
      fixed: "left",
      search: {
        transform: val => trim(val)
      }
    },
    {
      title: "发卡方",
      key: "scenicName",
      search: false,
      renderText: () => scenicName
    },

    {
      title: "商品折扣率（%）",
      width: 150,
      align: "center",
      dataIndex: "overallDiscount",
      search: false
    },
    {
      title: "分销折扣区间（%）",
      width: 160,
      align: "center",
      key: "discount",
      renderText: (_, { beginDiscount, endDiscount }) =>
        !isNil(beginDiscount ?? endDiscount) ? beginDiscount + " ~ " + endDiscount : "-",
      search: false
    },

    {
      title: "有效期",
      dataIndex: "effectiveTimeShow",
      search: false,
      renderText: (_, { effectiveTimeShow, effectiveTimeUnitShow }) =>
        `生效日期起 ${effectiveTimeShow || "-"} ${effectiveTimeUnitShow || "天"}内有效`
    },

    {
      title: "使用次数",
      search: false,
      dataIndex: "useFrequency",
      renderText: (_, { useFrequencyType, useFrequency }) => {
        if (useFrequencyType === 1) {
          return "不限";
        } else if (useFrequencyType === 2) {
          return `${useFrequency} 次`;
        }

        return "-";
      }
    },

    {
      title: "发行服务费（元）",
      dataIndex: "serviceChargeRate",
      fixed: "right",
      search: false,
      width: 150,
      renderText: (text: number = 0, record) => (
        <>
          {`${text}% = ${((record.marketPrice * text) / 100).toFixed(2)} 元 `}
          <EditChargeRatePop
            dataSourcesType={2}
            marketPrice={record.marketPrice}
            currentItem={{
              ...record,
              name: record.travelCardName
            }}
          />
        </>
      )
    },
    {
      title: "市场标准价（元）",
      width: 150,
      dataIndex: "marketPrice",
      search: false,
      align: "right",
      render: (dom, record) => (record.marketPrice * 1).toFixed(2)
    },
    {
      title: "启用状态",
      dataIndex: "isEnable",
      fixed: "right",
      valueEnum: {
        1: "启用",
        0: "禁用"
      },
      render: (_, entity: any) => (
        <Disabled
          access={access.canTravelCard_openClose}
          status={entity.isEnable == 1}
          params={{ id: entity.id, isEnable: entity.isEnable == 1 ? 0 : 1, scenicId }}
          request={async params => {
            const data = await switchTravelCard(params);
            addOperationLogRequest({
              action: "disable",
              content: `${entity.isEnable == 1 ? "禁用" : "启用"}【${entity.travelCardName}】权益卡`
            });
            return data;
          }}
          actionRef={actionRef}
        />
      )
    },
    {
      // width: 'auto',
      title: "操作",
      valueType: "option",
      fixed: "right",
      renderText: (_, record) => (
        <Space
          onClick={() => {
            setTravelCardItem(record);
          }}
        >
          <a onClick={() => travelCardModal.setType("info")}>查看</a>
          <Access accessible={access.canTravelCard_edit}>
            <a onClick={() => travelCardModal.setType("edit")}>编辑</a>
          </Access>
          <Delete
            access={access.canTravelCard_delete}
            status={record.isEnable == 1}
            params={{ id: record.id }}
            request={async params => {
              const data = await deleteTravelCard(params);
              addOperationLogRequest({
                action: "del",
                content: `删除【${record.travelCardName}】权益卡`
              });
              return data;
            }}
            actionRef={actionRef}
          />
        </Space>
      )
    }
  ];

  useEffect(() => {
    if (queryParams?.type == "add") {
      travelCardModal.setType("add");
    }
  }, [queryParams]);

  return (
    <>
      <ProTable<API.TravelCardListItem, API.TravelCardListParams>
        {...tableConfig}
        rowClassName={(row: any) => (row.isEnable == 1 ? "" : "disableRow")}
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        style={travelCardModal.tableStytle}
        toolBarRender={() => [
          <Access key="primary" accessible={access.canTravelCard}>
            <Button
              type="primary"
              onClick={() => {
                setTravelCardItem(undefined);
                travelCardModal.setType("add");
              }}
            >
              <PlusOutlined /> 新增
            </Button>
          </Access>
        ]}
        pagination={{ defaultPageSize: 10 }}
        params={{ scenicId, operatorId: coId }}
        request={async params => {
          try {
            const { data } = await getTravelCardList(params);
            return data;
          } catch (error) {
            return {
              data: [],
              total: 0
            };
          }
        }}
      />
      {/* 权益卡 modal */}
      <TravelCardModal actionRef={actionRef} travelCardItem={travelCardItem} modalState={travelCardModal} />
    </>
  );
};

export default TravelCard;
