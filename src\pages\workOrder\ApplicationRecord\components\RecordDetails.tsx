/**
 * 工单记录详情
 * @param title  标题
 * */

import { ticketTypeEnum } from "@/common/utils/enum";
import { modelWidth } from "@/common/utils/gConfig";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import { toValueEnum } from "@/common/utils/tool";
import type { ProDescriptionsGroup } from "@/components/ModalDescriptions";
import ModalDescriptions from "@/components/ModalDescriptions";
import type { ModalState } from "@/hooks/useModal";
import { approvalWorkOrder, getUserList, getWorkOrderInfo } from "@/services/api/workOrder";
import { CheckCircleOutlined, ClockCircleOutlined, CloseCircleOutlined, UserOutlined } from "@ant-design/icons";
import ProForm, { ProFormRadio, ProFormSelect } from "@ant-design/pro-form";
import type { ActionType } from "@ant-design/pro-table";
import { Space, Timeline, message } from "antd";
import { useForm, useWatch } from "antd/es/form/Form";
import type { FC } from "react";
import { useEffect } from "react";
import { useModel, useRequest } from "@umijs/max";
import { ApprovalStatusEnum, WorkOrderStatusEnum, WorkOrderTypeEnum } from "../../common/data";

interface ExplainItem {
  key: string;
  id: string;
  name: string;
  pre: any;
  now: any;
}

interface RecordDetailsProps {
  modalState: ModalState;
  actionRef: React.MutableRefObject<ActionType | undefined>;
  currentRow: API.WorkOrderListItem | undefined;
  tabKey: string;
}

const dotData = [
  {
    status: ApprovalStatusEnum.待审批,
    icon: <ClockCircleOutlined style={{ color: "#1890ff" }} />
  },
  {
    status: ApprovalStatusEnum.已同意,
    icon: <CheckCircleOutlined style={{ color: "green" }} />
  },
  {
    status: ApprovalStatusEnum.已拒绝,
    icon: <CloseCircleOutlined style={{ color: "red" }} />
  }
];

const RecordDetails: FC<RecordDetailsProps> = ({
  modalState: { visible, setVisible, type },
  actionRef,
  currentRow,
  tabKey
}) => {
  const { initialState } = useModel("@@initialState");
  const { scenicId = "", appId = "" } = initialState?.scenicInfo || {};
  const { coId = "" } = initialState?.currentCompanyInfo || {};
  const { userId = "" } = initialState?.userInfo || {};

  const [form] = useForm<{
    approvalStatus: ApprovalStatusEnum;
    countersign: string;
  }>();

  const approvalStatus = useWatch("approvalStatus", form);
  // 工单详情
  const workOrderInfoReq = useRequest(getWorkOrderInfo, {
    manual: true,
    onSuccess(data, params) {
      if (type === "info") {
        addOperationLogRequest({
          action: "info",
          module: tabKey,
          content: `查看【${currentRow?.id}】工单申请详情`
        });
      }
    }
  });

  const currentNode = workOrderInfoReq.data?.nodeList.find(item => item.nodeStatus === ApprovalStatusEnum.待审批);

  // 公司成员列表
  const userListReq = useRequest(getUserList, {
    manual: true
  });

  // 审批和加签
  const approvalWorkOrderReq = useRequest(approvalWorkOrder, {
    manual: true,
    onSuccess(data, params) {
      if (params[0].approvalStatus === ApprovalStatusEnum.已同意) {
        addOperationLogRequest({
          action: "audit",
          module: tabKey,
          content: `通过【${currentRow?.id}】工单申请`
        });
      }

      if (params[0].approvalStatus === ApprovalStatusEnum.已拒绝) {
        addOperationLogRequest({
          action: "audit",
          module: tabKey,
          content: `拒绝【${currentRow?.id}】工单申请`
        });
      }

      message.success("审批成功");
      setVisible(false);
      actionRef.current?.reload();
    }
  });
  const onSubmit = async () => {
    const values = await form.validateFields();
    if (currentRow) {
      approvalWorkOrderReq.run({
        approvalStatus: values.approvalStatus,
        counter: {
          group: "backend",
          userId: values.countersign,
          platformId: `${scenicId}/${coId}`
        },
        group: "backend",
        issueId: currentRow.id,
        platformId: `${scenicId}/${coId}`,
        // remark: '',
        userId
      });
    }
  };

  const getShowData = () => {
    const data = workOrderInfoReq.data?.explains.map(item => JSON.parse(item || "null") as ExplainItem);
    const findItem = (key: string) => data?.find(item => item.key === key);
    const showData = [
      {
        key: "id",
        name: "商品编号",
        dom: findItem("id")?.pre
      },
      {
        key: "goodsName",
        name: "商品名称",
        dom: findItem("goodsName")?.pre
      },
      {
        key: "times",
        name: "分时预约",
        dom: findItem("timeBeginTime")?.pre ? `${findItem("timeBeginTime")?.pre}~${findItem("timeEndTime")?.pre}` : "-"
      },
      {
        key: "type",
        name: "票种",
        dom: findItem("type")
          ? `${ticketTypeEnum[findItem("type")?.pre]} --> ${ticketTypeEnum[findItem("type")?.now]}`
          : "权益卡"
      },
      {
        key: "overallDiscount",
        name: "折扣率（%）",
        dom: `${findItem("overallDiscount")?.pre} --> ${findItem("overallDiscount")?.now}`
      },
      {
        key: "range",
        name: "分销折扣率（%）",
        dom: `${findItem("beginDiscount")?.pre}~${findItem("endDiscount")?.pre} --> ${findItem("beginDiscount")?.now}~${
          findItem("endDiscount")?.now
        }`
      }
    ];
    return showData;
  };

  const operationColumns: ProDescriptionsGroup<API.WorkOrderInfoData>[] = [
    {
      title: "审批操作",
      columns: [
        {
          title: "",
          dataIndex: "approvalStatus",
          render: () => {
            return (
              <ProForm layout="vertical" form={form} submitter={false}>
                <ProFormRadio.Group
                  label=""
                  width={"md"}
                  name={"approvalStatus"}
                  rules={[{ required: true, message: "请选择审批操作" }]}
                  options={[
                    {
                      label: "同意",
                      value: ApprovalStatusEnum.已同意
                    },
                    {
                      label: "拒绝",
                      value: ApprovalStatusEnum.已拒绝
                    },
                    {
                      label: "加签",
                      value: ApprovalStatusEnum.加签
                    }
                  ].filter(item => currentNode?.handles.includes(item.value))}
                />
                {approvalStatus === ApprovalStatusEnum.加签 && (
                  <ProFormSelect
                    label="加签对象"
                    width={"md"}
                    name={"countersign"}
                    rules={[{ required: true }]}
                    showSearch
                    fieldProps={{
                      loading: userListReq.loading
                    }}
                    options={userListReq.data?.map(item => ({
                      label: `${item.nickname || ""} ${item.username || ""}`,
                      value: item.userId
                    }))}
                  />
                )}
              </ProForm>
            );
          }
        }
      ]
    }
  ];
  const columns: ProDescriptionsGroup<API.WorkOrderInfoData>[] = [
    {
      title: "基本信息",
      columns: [
        {
          title: "工单名称",
          dataIndex: "issueName"
        },
        {
          title: "工单类型",
          dataIndex: "issueType",
          valueEnum: toValueEnum(WorkOrderTypeEnum),
          renderText: text => Number(text)
        },
        {
          title: "审批编号",
          dataIndex: "id"
        },
        {
          title: "提交时间",
          dataIndex: "createTime"
        },
        ...getShowData().map(item => ({
          title: item.name,
          render: () => item.dom,
          key: item.key
        }))
      ]
    },
    {
      title: "审批流程",
      columns: [
        {
          render: (_, record) => (
            <Timeline>
              {(record.nodeList ?? []).map((item, index) => {
                const dot = dotData.find(i => i.status === item.nodeStatus)?.icon;
                return (
                  <Timeline.Item key={item.id} dot={dot}>
                    <p style={{ fontWeight: "bold" }}>
                      审批节点 {index + 1} · {WorkOrderStatusEnum[item.nodeStatus]}
                    </p>
                    {item.approvalList.map(approval => {
                      const currentUser = userListReq.data?.find(user => user.userId === approval.userId);
                      const icon = dotData.find(i => i.status === approval.approvalStatus)?.icon;
                      return (
                        <p key={approval.id}>
                          <Space>
                            <UserOutlined style={{ fontSize: 16 }} />
                            <span
                              style={{
                                transform: "scale(0.6)",
                                position: "absolute",
                                marginLeft: -12,
                                marginTop: -2
                              }}
                            >
                              {icon}
                            </span>

                            {currentUser?.nickname}
                            {approval.approvalTime}
                            {ApprovalStatusEnum[approval.approvalStatus]}
                          </Space>
                        </p>
                      );
                    })}
                  </Timeline.Item>
                );
              })}
            </Timeline>
          )
        }
      ]
    }
  ];

  useEffect(() => {
    if (visible && currentRow) {
      userListReq.run({
        scenicId,
        companyId: coId,
        appId
      });
      workOrderInfoReq.run({
        id: currentRow.id,
        platformId: `${scenicId}/${coId}`,
        group: "backend"
      });

      form.resetFields();
    }
  }, [currentRow, visible]);

  return (
    <ModalDescriptions
      modalProps={{
        visible,
        title: "申请详情",
        onCancel: () => {
          setVisible(false);
        },
        destroyOnClose: true,
        width: modelWidth.md,
        onOk: onSubmit,
        okButtonProps: {
          loading: approvalWorkOrderReq.loading,
          style: {
            display: type === "info" ? "none" : undefined
          }
        },
        footer: type == "update" ? undefined : false
      }}
      list={type === "info" ? columns : columns.concat(operationColumns)}
      dataSource={workOrderInfoReq.data}
      loading={workOrderInfoReq.loading}
    />
  );
};

export default RecordDetails;
