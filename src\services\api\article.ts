/**
 * 店铺导览
 */
import { request } from "@umijs/max";
import { scenicHost } from ".";
const url = (v: string) => scenicHost + v;

/** 新增文章 */
export function addArticle(data: any) {
  if (data.articleSource == 1) {
    //内链时清链接
    data.externalUrl = "";
  } else {
    //外链时清内容
    data.articleContent = "";
  }

  return request(url("/article/add"), {
    method: "POST",
    data: {
      ...data,
      enableState: 1, // 1 禁用，2 启用
      recommendState: 1 //启用默认开，推荐默认关
    }
  });
}
/** 删除文章 */
export function deleteArticle(params: any) {
  return request(url("/article/delete/" + params.id), {
    method: "DELETE"
  });
}
/** 禁用文章 */
export function enableArticle(data: any) {
  return request(url("/article/enable"), {
    method: "PUT",
    data
  });
}
/** 查看文章 */
export function infoArticle(params: any) {
  return request(url("/article/info"), { params });
}
/** 文章分页 */
export async function pageArticle(params: any) {
  const { data, code } = await request(url("/article/page"), {
    params: params,
    skipCommonParams: true
  });
  return {
    data: data.data,
    success: code == 20000,
    total: data.total
  };
}
/** 修改文章 */
export function editArticle(data: any) {
  if (data.articleSource == 1) {
    //内链时清链接
    data.externalUrl = "";
  } else {
    //外链时清内容
    data.articleContent = "";
  }
  return request(url("/article/update"), {
    method: "PUT",
    data
  });
}

/** 获取文章排序列表 */
export async function sortListArticle(params: any) {
  return request(url("/article/sort/list"), { params });
}

/** 修改文章排序 */
export async function editSortArticle(params: any) {
  const { msg, code } = await request(url("/article/batch/sort"), {
    method: "PUT",
    skipCommonParams: true,
    data: params
  });
  return {
    msg: msg,
    success: code == 20000
  };
}

// 店铺列表
export async function apiShopList(params: any) {
  try {
    const { data } = await request(url("/ticketAgent/shopList"), {
      method: "GET",
      params
    });
    return data.map((item: any) => {
      return {
        label: item.name,
        value: item.id
      };
    });
  } catch (error) {
    return {};
  }
}

// 批量导入文章
export async function addBatchArticle(params: any) {
  const { msg, code } = await request(url("/article/addBatch"), {
    method: "POST",
    skipCommonParams: true,
    data: params
  });
  return {
    msg: msg,
    success: code == 20000
  };
}

// 店铺商品列表 (新)
export async function getShopGoodsPageList(params: any) {
  return await request(url("/ticketAgent/storeGoodsPage"), {
    params: params,
    skipCommonParams: true
  });
}
