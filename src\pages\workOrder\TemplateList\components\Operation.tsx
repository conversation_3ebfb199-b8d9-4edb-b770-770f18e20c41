/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-28 14:38:46
 * @LastEditTime: 2022-12-05 10:50:42
 * @LastEditors: zhangfengfei
 */
import { ProFormCheckbox } from '@ant-design/pro-form';
import type { FC } from 'react';
import type { NodeDataItem } from '../../common/data';
import { NodeOperationEnum, ParticipateModeEnum } from '../../common/data';

interface OperationProps {
  value?: any;
  onChange?: (val: any) => void;
  nodeItem?: NodeDataItem;
}

const Operation: FC<OperationProps> = ({ nodeItem }) => {
  return (
    <>
      <ProFormCheckbox.Group
        name="nodeOperationId"
        // label="审批操作"
        rules={[{ required: true }]}
        options={[
          {
            label: '同意',
            value: NodeOperationEnum.同意,
          },
          {
            label: '同意并加签',
            value: NodeOperationEnum.同意并加签,
            disabled: nodeItem?.participateMode === ParticipateModeEnum.多人,
          },
          {
            label: '拒绝',
            value: NodeOperationEnum.拒绝,
          },
        ]}
      />
    </>
  );
};

export default Operation;
