/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-27 14:25:56
 * @LastEditTime: 2022-12-05 10:50:56
 * @LastEditors: zhangfengfei
 */
/**
 * name: 申请记录
 * path: /workOrder/applicationRecord
 */
import { toValueEnum } from "@/common/utils/tool";
import useModal from "@/hooks/useModal";
import { getWorkOrderList } from "@/services/api/workOrder";
import type { ActionType, ProColumns } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import { useRef, useState } from "react";
import { useModel } from "@umijs/max";
import { WorkOrderStatusEnum, WorkOrderTypeEnum } from "../common/data";
import RecordDetails from "./components/RecordDetails";
import { tableConfig } from "@/common/utils/config";

const Table: React.FC = () => {
  const { initialState } = useModel("@@initialState");
  const { scenicId = "" } = initialState?.scenicInfo || {};
  const { coId = "" } = initialState?.currentCompanyInfo || {};

  const modalState = useModal();
  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.WorkOrderListItem>();

  const tableListReq = async (params: API.WorkOrderListParams) => {
    const { data } = await getWorkOrderList(params);
    return data;
  };

  const columns: ProColumns<API.WorkOrderListItem>[] = [
    {
      title: "审批编号",
      dataIndex: "id"
    },
    {
      title: "工单名称",
      dataIndex: "issueName"
    },
    {
      title: "工单类型",
      dataIndex: "issueType",
      valueEnum: toValueEnum(WorkOrderTypeEnum),
      renderText: text => Number(text)
    },
    {
      title: "申请人姓名",
      dataIndex: "username"
    },
    {
      title: "手机号",
      dataIndex: "phone",
      search: false
    },
    {
      title: "申请提交时间",
      dataIndex: "createTime",
      valueType: "dateRange",
      search: {
        transform: values => {
          const startTime = values[0] + " 00:00:00";
          const endTime = values[1] + " 23:59:59";
          return {
            startTime,
            endTime
          };
        }
      },
      render: (text, record) => record.createTime
    },
    {
      title: "审批状态",
      dataIndex: "issueStatus",
      valueEnum: toValueEnum(WorkOrderStatusEnum)
    },
    {
      title: "操作",
      valueType: "option",
      render: (dom, record) => (
        <a
          onClick={() => {
            modalState.setTypeWithVisible("info");
            setCurrentRow(record);
          }}
        >
          查看详情
        </a>
      )
    }
  ];
  return (
    <>
      <ProTable<API.WorkOrderListItem, API.WorkOrderListParams>
        {...tableConfig}
        actionRef={actionRef}
        columns={columns}
        params={{
          issueGroup: "backend",
          issuePlatformId: `${scenicId}/${coId}`
          // issueUserId: userId,
        }}
        request={tableListReq}
      />
      {/* 详情 */}
      <RecordDetails modalState={modalState} actionRef={actionRef} currentRow={currentRow} />
    </>
  );
};

export default Table;
