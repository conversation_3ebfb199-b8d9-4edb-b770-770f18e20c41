/**
 * @name umi 的路由配置
 * @description 只支持 path,component,routes,redirect,wrappers,name,icon 的配置
 * @param id 业务字段 用于操作日志埋点
 * @param path  path 只支持两种占位符配置，第一种是动态参数 :id 的形式，第二种是 * 通配符，通配符只能出现路由字符串的最后。
 * @param component 配置 location 和 path 匹配后用于渲染的 React 组件路径。可以是绝对路径，也可以是相对路径，如果是相对路径，会从 src/pages 开始找起。
 * @param routes 配置子路由，通常在需要为多个路径增加 layout 组件时使用。
 * @param redirect 配置路由跳转
 * @param wrappers 配置路由组件的包装组件，通过包装组件可以为当前的路由组件组合进更多的功能。比如，可以用于路由级别的权限校验
 * @param name 配置路由的标题，默认读取国际化文件 menu.ts 中 menu.xxxx 的值，如配置 name 为 login，则读取 menu.ts 中 menu.login 的取值作为标题
 * @param icon 配置路由的图标，取值参考 https://ant.design/components/icon-cn，注意去除风格后缀和大小写，如想要配置图标为 <StepBackwardOutlined /> 则取值应为 stepBackward 或 StepBackward，如想要配置图标为 <UserOutlined /> 则取值应为 user 或者 User
 * @doc https://umijs.org/docs/guides/routes
 */

const routes = [
  {
    path: "/:scenic",
    id: "xU9qK5pJ",
    name: "全部",
    replacePathField: "path", // 加此字段 path 会被替换成景区标识符
    flatMenu: true, // 将二级菜单提升成一级菜单
    routes: [
      {
        name: "首页",
        path: "welcome",
        icon: "home",
        id: "mHxZ4wPv",
        component: "Welcome",
        access: "canHomePage"
      },
      {
        name: "基础信息配置",
        path: "basic-information",
        id: "J9aLpDcQ",
        icon: "DatabaseOutlined",
        routes: [
          {
            name: "企业信息",
            id: "tYsN8rFq",
            path: "enterprise",
            icon: "SolutionOutlined",
            component: "basicInformation/Enterprise",
            access: "canEnterpriceInfo"
          },
          {
            name: "景区信息",
            id: "zF6sX3gR",
            path: "scenic-information",
            component: "basicInformation/Information",
            access: "canScenicInfo"
          },
          {
            name: "全局配置",
            path: "config",
            id: "gR3zX6uV",
            component: "basicInformation/Config",
            access: "canGlobalSettings"
          },
          {
            path: "tour",
            id: "jT9uP7vW",
            name: "导览配置",
            routes: [
              {
                path: "point",
                id: "bG5pY9sU",
                name: "点位管理",
                component: "basicInformation/tour/Point"
              },
              {
                path: "line",
                id: "dG5qK8wR",
                name: "线路管理",
                component: "basicInformation/tour/Line"
              }
            ]
          },
          {
            name: "官网配置",
            id: "pM2xY4cV",
            path: "visual",
            component: "basicInformation/Visual",
            access: "canOfficialSiteConfig"
          },
          {
            name: "景区节假日管理",
            path: "festivals",
            component: "basicInformation/Festivals",
            access: "canHolidays"
          }
        ]
      },

      /* 【基础设施管理】 */
      {
        name: "基础设施管理",
        path: "infrastructure",
        id: "nK4cF1dJ",
        icon: "ToolOutlined",
        routes: [
          //企业信息
          {
            path: "device",
            name: "检票设备管理",
            id: "qV2rH6zS",
            icon: "tool",
            routes: [
              {
                name: "检票点",
                path: "ticketsys",
                id: "lN8rV1sF",
                component: "device/Ticketsys",
                access: "canCheckPoint"
              },
              {
                name: "检票设备",
                id: "uL8tA3yN",
                path: "ticket",
                component: "device/Ticket",
                access: "canCheckDevice"
              }
            ]
          },
          {
            path: "sale",
            id: "hJ7yA6tD",
            name: "售票设备管理",
            routes: [
              {
                name: "售票点",
                path: "sell-tickets",
                id: "kL4wT9xP",
                component: "device/SellTickets",
                access: "canSalesSite"
              },
              {
                name: "售票设备",
                path: "sell-device",
                id: "rW1zH3vQ",
                component: "device/SellDevice",
                access: "canSalesDevice"
              }
            ]
          },
          {
            name: "硬件设备管理",
            path: "ticketsys",
            routes: [
              {
                name: "视频监控设备",
                path: "video",
                component: "device/VideoSurveillance",
                access: "canHardwareVideo"
              },
              {
                name: "WIFI 点",
                path: "wifi",
                component: "device/Wifi",
                access: "canHardwareWIFI"
              },
              {
                name: "广播",
                path: "broadcast",
                component: "device/Broadcast",
                access: "canHardwareBoardcast"
              },
              {
                name: "SOS 报警点",
                path: "alarm",
                component: "device/AlarmPoint",
                access: "canHardwareSOS"
              }
            ]
          },
          {
            name: "景区项目管理",
            path: "project",
            routes: [
              {
                name: "景区娱乐项目",
                path: "recreation",
                component: "device/Recreation",
                access: "canProjectEntertainment"
              },
              {
                name: "游客服务项目",
                path: "serve",
                component: "device/Serve",
                access: "canProjectService"
              }
            ]
          }
        ]
      },

      /* 【规则管理】 */
      {
        path: "rule-set",
        name: "规则管理",
        id: "sT4uQ7hL",
        icon: "SolutionOutlined",
        routes: [
          {
            name: "出票规则",
            path: "sell-tickets",
            id: "mH8zD3vQ",
            component: "ruleSet/SellTickets",
            access: "canSalesRule"
          },
          {
            name: "退票规则",
            path: "refund-tickets",
            id: "nK7yA6tD",
            component: "ruleSet/RefundTickets",
            access: "canRefundRule"
          },
          {
            name: "检票规则",
            path: "manage",
            id: "xF8wV2yR",
            component: "ruleSet/Manage",
            access: "canCheckRule"
          }
        ]
      },

      /* 【票务管理】 */
      {
        path: "ticket",
        name: "票务管理",
        id: "dP7mW1xR",
        icon: "project",
        routes: [
          {
            name: "门票设置",
            path: "ticket-type/edit",
            id: "cD8vQ6rW",
            component: "ticket/TicketType",
            access: "canTicketSet",
            hideInMenu: true
          },
          {
            name: "门票设置",
            path: "ticket-type",
            id: "cD8vQ6rW",
            component: "ticket/TicketType",
            access: "canTicketSet",
            exact: false
          },
          {
            name: "库存创建",
            path: "stock",
            id: "mZ1qS6wT",
            component: "ticket/Stock",
            access: "canStorageManage"
          },
          {
            name: "门票销售",
            path: "sale",
            id: "vN5dR9hK",
            component: "ticket/Sale",
            access: "canTicketSalesManagement"
          },
          {
            name: "门票核销",
            path: "records",
            id: "cG3xL7jP",
            component: "ticket/Records",
            access: "canVerificationRecord"
          }
        ]
      },

      /* 【权益卡管理】 */
      {
        path: "travel-card",
        name: "权益卡管理",
        id: "zY9tW1qA",
        icon: "CreditCardOutlined",
        routes: [
          {
            name: "权益卡设置",
            path: "setting",
            id: "bX7vF4uH",
            component: "travelCard/Setting",
            access: "canTravelCard"
          },
          {
            name: "库存创建",
            path: "stock",
            id: "pD3qT5rJ",
            component: "travelCard/Stock",
            access: "canTravelCardStorageManage"
          },
          {
            name: "审核管理",
            path: "audit",
            id: "vK2wN8sM",
            component: "travelCard/Audit",
            access: "canCheckList"
          },
          {
            name: "权益卡权益",
            path: "rights",
            id: "gU6rP9sW",
            component: "travelCard/Rights",
            access: "canRightsManage"
          },
          {
            name: "销售明细",
            path: "sale-detail",
            id: "sF1cY3tL",
            component: "travelCard/SaleDetail",
            access: "canRightsManage"
          }
        ]
      },

      /* 【工单管理】 */
      {
        name: "工单管理",
        path: "work-order",
        id: "dM7yW1hX",
        icon: "ContainerOutlined",
        routes: [
          {
            name: "工单模版列表",
            path: "template-list",
            id: "pN9xG6tJ",
            component: "workOrder/TemplateList",
            access: "canWorkOrder_select"
          },
          {
            name: "申请记录",
            id: "hR2cL4qV",
            path: "application-record",
            component: "workOrder/ApplicationRecord",
            access: "canApplyRecord_select"
          },

          {
            name: "我的工单",
            id: "kP8sD1uW",
            path: "my",
            component: "workOrder/MyWorkOrder",
            access: "canMyWorkOrder_select"
          }
        ]
      },

      /* 【用户中心】 */
      {
        name: "用户中心",
        id: "vF6zT3yQ",
        path: "user-center",
        icon: "UserSwitchOutlined",
        routes: [
          {
            name: "用户管理",
            id: "gJ1wK7mS",
            icon: "ClusterOutlined",
            path: "manage",
            component: "userCenter/Manage",
            access: "canOrganizationManage"
          },
          {
            name: "权限管理",
            id: "cX4rV9hW",
            icon: "UsergroupAddOutlined",
            path: "authority",
            component: "userCenter/Authority",
            access: "canRoleManage"
          },

          {
            name: "用户审批",
            id: "bU5mQ2rL",
            icon: "StepBackwardOutlined",
            path: "approval",
            component: "userCenter/Approval",
            access: "canUserApprove"
          }
        ]
      },

      /* 【系统设置】 */
      {
        path: "system",
        name: "系统设置",
        id: "eG6rV4sN",
        icon: "SettingOutlined",
        routes: [
          {
            name: "景区助手",
            path: "guide",
            id: "uX4tQ9rF",
            component: "systemSettings/Guide",
            access: "canHomePage"
          },
          {
            name: "风控管理",
            path: "risk-control",
            id: "wZ9qP3xM",
            component: "systemSettings/RiskControl",
            access: "canRiskManagement"
          },
          {
            name: "操作日志",
            path: "operation-log",
            id: "uS7hF1tL",
            component: "systemSettings/OperationLog",
            access: "canOperationLog"
          },
          {
            name: "下载中心",
            path: "down",
            component: "systemSettings/Down"
          }
        ]
      }
    ]
  },
  {
    name: "接受邀请",
    layout: false,
    path: "/:scenic/invite",
    id: "rV6zD2wL",
    component: "./cas/Invite",
    menuRender: false,
    // access: 'canRead',
    hideInMenu: true
  },
  {
    name: "试用景区过期",
    layout: false,
    path: "/:scenic/expired",
    component: "./Expired",
    menuRender: false,
    hideInMenu: true
  },

  {
    layout: false,
    hideInMenu: true,
    path: "/:scenic/ApplicationApproved",
    component: "./cas/ApplicationApproved"
  },
  // 提示升级版本
  {
    path: "/:scenic/upgradeVersion",
    name: "升级版本",
    layout: false,
    hideInMenu: true,
    component: "./UpgradeVersion"
  },

  {
    layout: false,
    hideInMenu: true,
    path: "/:scenic/result",
    component: "./Result"
  },
  {
    layout: false,
    hideInMenu: true,
    path: "/:scenic/UserApplies",
    component: "./cas/UserApplies"
  },
  {
    layout: false,
    hideInMenu: true,
    path: "/homePage",
    name: "慧景云",
    component: "./sqp/Home"
  },
  {
    path: "/",
    redirect: "/homePage"
  },
  {
    path: "*",
    component: "./404"
  }
];

/**
 * 可在routes.ts中配置 linkInBreadcrumb 字段来控制点击面包屑时是否跳转到当前路由
 * linkInBreadcrumb true 为跳转，false 为不跳转
 * 默认没有routes的路由 需要跳转
 */
const dealRoutes = item => {
  if (!item.routes) {
    item.linkInBreadcrumb = item.hasOwnProperty("linkInBreadcrumb") ? item.linkInBreadcrumb : true;
    return;
  } else {
    item.linkInBreadcrumb = item.hasOwnProperty("linkInBreadcrumb") ? item.linkInBreadcrumb : false;
    item.routes.forEach(child => {
      dealRoutes(child);
    });
  }
};
routes.forEach(item => {
  item.linkInBreadcrumb = false;
  dealRoutes(item);
});

export default routes;
