/*
 * @Author: z<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-09-13 09:43:40
 * @LastEditTime: 2023-10-25 11:07:32
 * @LastEditors: zhangfengfei
 */
import { ticketStatusEnum } from "@/common/utils/enum";
import { Space, Table } from "antd";
import type { ColumnType } from "antd/lib/table";
import { isEmpty, uniqueId } from "lodash";
import type { Dayjs } from "dayjs";
import dayjs from "dayjs";
import type { FC } from "react";
import { useEffect, useState } from "react";
import type { Updater } from "use-immer";
import Picker from "../Picker";

export type DataSourceType = Ticket.RealNameListItem2 & {
  usedCount: number;
};

export type TicketDataItem = DataSourceType & {
  useDate: string;
};

interface RealNameTableProps {
  ticket: Ticket.SaleTicketItem;
  checkInfo: Ticket.AvailableCheckItem;
  setTicketData: Updater<Record<string, TicketDataItem[]>>;
}

const RealNameTable: FC<RealNameTableProps> = ({ ticket, setTicketData, checkInfo }) => {
  const { id, status, playerNum, availableDays, usedCount, enterTime, useType, useCount } = ticket;

  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);

  const [date, setDate] = useState<Dayjs>(dayjs(enterTime));

  const [dataSource, setDataSource] = useState<DataSourceType[]>([]);

  const columns: ColumnType<DataSourceType>[] = [
    {
      title: "排序",
      dataIndex: "id",
      width: "20%",
      render: (value, record, index) => `游客${index + 1}`
    },
    {
      title: "姓名",
      dataIndex: "idCardName",
      width: "20%",
      render: value => value || "-"
    },
    {
      title: "身份证",
      dataIndex: "idCardNumber",
      width: "40%",
      render: (value: string) => {
        if (value.includes("id")) {
          return "-";
        }
        return value;
      }
    },
    {
      title: "核销次数",
      dataIndex: "usedCount",
      render: value => value || "-",
      width: "20%"
    }
  ];

  const isDisabled = (record: DataSourceType) => {
    // 无限次
    if (useCount === 0) {
      return false;
    }
    // useType  0 每天    1  一共
    if (useType == 1) {
      const sum = (checkInfo.useList || []).reduce((pre, next) => pre + next.usedCount, 0);
      return sum >= useCount;
    } else {
      return record.usedCount >= useCount;
    }
  };

  useEffect(() => {
    // 统一数据格式  无核验信息 useCount 为 0
    // 非实名
    if (isEmpty(checkInfo.realNameList)) {
      const formatData = new Array(playerNum).fill(null).map((j, index) => ({
        idCardNumber: uniqueId("id"),
        idCardName: "",
        usedCount: 0
      }));

      setDataSource(formatData);
    } else {
      // 实名
      const formatData = checkInfo.realNameList.map(i => {
        const item = (checkInfo.realNameUseList ?? []).find(
          j =>
            // 同一天  同 idCard
            dayjs(j.useDate).format("YYYY-MM-DD") === date.format("YYYY-MM-DD") && j.idCardNumber === i.idCardNumber
        );
        return {
          ...i,
          usedCount: item?.usedCount ?? 0
        };
      });
      setDataSource(formatData);
    }
  }, [checkInfo.realNameList, checkInfo.realNameUseList, date]);

  useEffect(() => {
    // 默认除禁用的全选上
    const selectedKeys = dataSource
      .filter(i => i.usedCount === 0 || i.usedCount < useCount || useCount === 0)
      .map(i => i.idCardNumber);
    setSelectedRowKeys(selectedKeys);
  }, [dataSource]);

  useEffect(() => {
    // 传递所需数据
    const selectedRows = dataSource.filter(i => selectedRowKeys.includes(i.idCardNumber));
    setTicketData(draft => {
      draft[id] = selectedRows.map(i => ({
        ...i,
        useDate: date.format("YYYY-MM-DD")
      }));
    });
  }, [dataSource, id, selectedRowKeys]);

  return (
    <>
      <Space size={48} style={{ fontWeight: "bold", marginBottom: 8 }}>
        <span>票号：{id}</span>
        <span>门票状态：{ticketStatusEnum[status]}</span>
        <span>人数：{playerNum}</span>
        <span>核销总次数：{usedCount}</span>
      </Space>
      {/* 多日票 */}
      {availableDays > 1 && (
        <div>
          核销时间：
          <Picker date={date} checkInfo={checkInfo} ticket={ticket} setDate={setDate} />
        </div>
      )}

      <Table<DataSourceType>
        size="middle"
        rowKey="idCardNumber"
        columns={columns}
        dataSource={dataSource}
        pagination={false}
        rowSelection={{
          type: "checkbox",
          selectedRowKeys,
          onChange: (rowKeys: string[]) => {
            setSelectedRowKeys(rowKeys);
          },
          getCheckboxProps(record) {
            return {
              disabled: isDisabled(record)
            };
          }
        }}
      />
    </>
  );
};

export default RealNameTable;
