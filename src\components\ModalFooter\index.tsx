/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-10-08 09:25:53
 * @LastEditTime: 2022-10-08 10:09:57
 * @LastEditors: zhangfengfei
 */
import type { ButtonProps } from 'antd';
import { Button, Space } from 'antd';
import type { FC } from 'react';

interface ModalFooterProps {
  enableButton?: ButtonProps & { isEnable: boolean };
  deleteButton?: ButtonProps;
  editButton?: ButtonProps;
  cancelButton?: ButtonProps;
}

const ModalFooter: FC<ModalFooterProps> = ({
  enableButton,
  deleteButton,
  editButton,
  cancelButton,
}) => {
  return (
    <Space>
      {enableButton && (
        <Button danger {...enableButton}>
          {enableButton.isEnable ? '禁用' : '启用'}
        </Button>
      )}
      {deleteButton && (
        <Button type="primary" danger {...deleteButton}>
          删除
        </Button>
      )}
      {editButton && (
        <Button type="primary" {...editButton}>
          编辑
        </Button>
      )}
      {cancelButton && <Button {...cancelButton}>取消</Button>}
    </Space>
  );
};

export default ModalFooter;
