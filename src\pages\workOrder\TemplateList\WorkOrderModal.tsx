/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-28 15:15:44
 * @LastEditTime: 2023-07-27 15:31:12
 * @LastEditors: zhangfeng<PERSON>i
 */

import { modelWidth } from "@/common/utils/gConfig";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import { getUniqueId } from "@/common/utils/tool";
import type { ModalState } from "@/hooks/useModal";
import {
  addWorkOrderTemp,
  getRoleDownList,
  getUserList,
  getWorkOrderTempInfo,
  updateWorkOrderTemp
} from "@/services/api/workOrder";
import { MinusCircleOutlined, PlusOutlined, QuestionCircleOutlined } from "@ant-design/icons";
import ProForm, {
  ProFormCheckbox,
  ProFormDigit,
  ProFormGroup,
  ProFormRadio,
  ProFormSelect,
  ProFormText
} from "@ant-design/pro-form";
import type { ActionType } from "@ant-design/pro-table";
import { Button, Col, Form, Modal, Skeleton, Space, message } from "antd";
import Tooltip from "antd/es/tooltip";
import { useForm, useWatch } from "antd/lib/form/Form";
import { isEmpty, isNil, remove } from "lodash";
import type { FC } from "react";
import { useEffect } from "react";
import { useAccess, useModel, useRequest } from "@umijs/max";
import { useImmer } from "use-immer";
import type { FormValuesType, NodeDataItem, NodeScriptDataItem } from "../common/data";
import {
  ApprovalModeEnum,
  ApproverRuleTypeEnum,
  ApproverTypeEnum,
  NodeApproverTypeEnum,
  NodeOperationEnum,
  ParticipateModeEnum,
  WorkOrderStateEnum,
  WorkOrderTypeEnum
} from "../common/data";
import ScriptModal from "./components/ScriptModal";

const titleMap = {
  add: "创建工单模版",
  info: "查看工单模版",
  update: "配置工单模版"
};

const initNodeItem: Partial<NodeDataItem> = {
  participateMode: ParticipateModeEnum.单人,
  nodeScriptData: [],
  isDelete: 0,
  approvalMode: ApprovalModeEnum.会签,
  approverType: NodeApproverTypeEnum.指定成员或部门
};

const initValues = {
  sponsorCount: 1,
  approverType: ApproverTypeEnum.默认同意,
  nodeApproverType: ApproverRuleTypeEnum.预设人选
};

interface WorkOrderModalProps {
  modalState: ModalState;
  currentRow?: API.WorkOrderTempListItem;
  actionRef: React.MutableRefObject<ActionType | undefined>;
}
// 字段名动态
export const getFormName = (mode?: ParticipateModeEnum, nodeType?: NodeApproverTypeEnum) => {
  if (mode === ParticipateModeEnum.单人) {
    return "singleApproverId";
  } else {
    return nodeType === NodeApproverTypeEnum.指定角色 ? "roleApproverId" : "departmentAppeoverId";
  }
};

// 交互细节太多太复杂 后续有时间优化  ts 尽量写全 方便后续维护
const WorkOrderModal: FC<WorkOrderModalProps> = ({
  modalState: { visible, setVisible, type, setType },
  actionRef,
  currentRow
}) => {
  const { initialState } = useModel("@@initialState");
  const { scenicId = "", appId = "" } = initialState?.scenicInfo || {};
  const { coId = "" } = initialState?.currentCompanyInfo || {};
  const { username, userId, nickname } = initialState?.userInfo || {};

  // 当前渲染的节点
  const [nodeData, updateNodeData] = useImmer<Partial<NodeDataItem>[]>([]);
  // 被删除的节点 (新增后再删除的不算)
  const [deleteNodeData, updateDeleteNodeData] = useImmer<Partial<NodeDataItem>[]>([]);
  const [form] = useForm<FormValuesType>();

  const [nodeForm] = useForm();

  // 监听 form 表单的几个值
  const approverType = useWatch("approverType", form);
  const nodeApproverType = useWatch("nodeApproverType", form);

  // 公司部门或者成员列表
  const userListReq = useRequest(getUserList, {
    manual: true,
    initialData: [],
    formatResult(res) {
      const data = (res.data || []).map(item => ({
        ...item,
        label: `${item.nickname || ""} ${item.username || ""}`,
        value: item.userId
      }));
      // const result = removeEmpty(arrayToTree(data, '0', 'deptId', 'parentDeptId'));
      return data;
    }
  });

  // const departmentMap = useMemo(() => {
  //   const pathMap: Record<string, string> = {};
  //   for (const item of departmentListReq.data ?? []) {
  //     if (item.deptId !== '0') {
  //       pathMap[item.deptId] = item.parentDeptId;
  //     }
  //   }
  //   return pathMap;
  // }, [departmentListReq.data]);

  // 转换成级联选择器格式
  // const getPath = (id?: string, endId = '0') => {
  //   if (id) {
  //     const path: string[] = [];
  //     function getParent(val: string) {
  //       if (val !== endId) {
  //         path.push(val);
  //         getParent(departmentMap[val]);
  //       }
  //     }
  //     getParent(id);
  //     return path.reverse();
  //   }
  //   return undefined;
  // };

  // 公司角色列表
  const roleListReq = useRequest(getRoleDownList, {
    manual: true,
    initialData: [],
    formatResult(res) {
      const data = (res.data || []).map(item => ({
        ...item,
        disabled: item.status === 2,
        label: item.name,
        value: item.roleId
      }));
      return data;
    }
  });

  // 新增编辑工单模板
  const postWorkOrderTempReq = useRequest(type === "add" ? addWorkOrderTemp : updateWorkOrderTemp, {
    manual: true,
    onSuccess(data, params) {
      const isAdd = type === "add";
      addOperationLogRequest({
        action: isAdd ? "add" : "edit",
        content: `${isAdd ? "新增" : "编辑"}【${params[0].workOrderName}】工单模板`
      });

      message.success(isAdd ? "新增成功" : "编辑成功");
      setVisible(false);
      actionRef.current?.reload();
    }
  });

  // 工单模板详情
  const workOrderTempInfoReq = useRequest(getWorkOrderTempInfo, {
    manual: true,
    onSuccess(data, params) {
      const newNodeData = data.nodeData.map(item => {
        return {
          ...item,
          departmentAppeoverId: (item.departmentAppeoverId ?? "").split(",")
        };
      });
      form.setFieldsValue(data);
      updateNodeData(newNodeData);
    }
  });

  const onValuesChange = (changedValues: FormValuesType) => {
    const { sponsorCount, nodeApproverType: nodeApproveType } = changedValues;
    if (nodeApproveType) {
      form.resetFields(["sponsorCount"]);
      updateNodeData([initNodeItem]);
    }
    // nodes 与审批节点个数的联动
    if (sponsorCount && String(sponsorCount).indexOf(".") === -1) {
      if (sponsorCount > nodeData.length) {
        // 新增
        const addValues = new Array(sponsorCount - nodeData.length).fill(initNodeItem);
        updateNodeData([...nodeData, ...addValues]);
      } else {
        // 减少 需区分原后端数据和自己的数据
        for (const item of nodeData.slice(sponsorCount, nodeData.length)) {
          if (item.id) {
            updateDeleteNodeData(arr => {
              arr.push({
                ...item,
                isDelete: 1
              });
            });
          }
        }
        updateNodeData(nodeData.slice(0, sponsorCount));
      }
    }
  };

  /**
   * @description: 改变 nodeData 数据的方法 看起来很奇怪 没想到好办法
   */
  const onNodeValueChange = (name: (keyof NodeDataItem | keyof NodeScriptDataItem | number)[], value: any) => {
    updateNodeData(nodes => {
      const path = `["${name.join('"]["')}"]`;
      eval(`nodes${path}=value`);
    });
    console.log(nodeData);
  };

  const onClose = () => {
    setVisible(false);
  };

  const onSubmit = async () => {
    const values = await form.validateFields();
    // 最后一个节点必须有脚本
    const hasLastNodeScript = nodeData[nodeData.length - 1].nodeScriptData?.some(
      ({ scriptId }) => scriptId && scriptId !== "0"
    );
    if (!hasLastNodeScript) {
      message.warning("最后一个审批节点必须有可执行脚本");
      return;
    }
    if (nodeApproverType === ApproverRuleTypeEnum.预设人选) {
      const hasApproverId = nodeData.every(
        ({ singleApproverId, departmentAppeoverId, roleApproverId }) =>
          singleApproverId || !isEmpty(departmentAppeoverId) || roleApproverId
      );
      if (!hasApproverId) {
        message.warning("每个审批节点必须有审批人");
        return;
      }
    }
    // 处理数据
    const newNodeData = [...deleteNodeData, ...nodeData].map(item => {
      const { departmentAppeoverId = [] } = item as any;
      return {
        ...item,
        departmentAppeoverId: departmentAppeoverId?.join(",")
      };
    });

    if (type === "add") {
      postWorkOrderTempReq.run({
        scenicId,
        providerId: coId,
        ...values,
        nodeData: newNodeData,
        createUser: username,
        createUserName: nickname,
        state: WorkOrderStateEnum.启用
      });
    } else if (type === "update") {
      postWorkOrderTempReq.run({
        id: currentRow?.workOrderId,
        scenicId,
        providerId: coId,
        ...values,
        nodeData: newNodeData,
        updateUser: username,
        state: currentRow?.state
      });
    }

    console.log(nodeData);
  };

  useEffect(() => {
    // nodes 与审批节点个数的联动
    if (nodeData) {
      form.setFieldsValue({
        sponsorCount: nodeData.length
      });
    }
  }, [nodeData.length]);

  useEffect(() => {
    if (visible) {
      updateDeleteNodeData([]);
      userListReq.run({
        scenicId,
        companyId: coId,
        appId
      });
      roleListReq.run({
        relationId: `${scenicId}/${coId}`,
        type: "02"
      });
    }
  }, [visible]);

  //  新增重置 详情赋值
  useEffect(() => {
    if (visible) {
      if (type === "add") {
        form.resetFields();
        updateNodeData([initNodeItem]);
      } else if ((type === "info" || type === "update") && currentRow) {
        workOrderTempInfoReq.run({ id: currentRow.workOrderId });
      }
    }
  }, [type, visible]);

  const getModal = (index: number, option: NodeOperationEnum, nodeScriptData: NodeScriptDataItem[]) => {
    const defaultValue = nodeScriptData?.find(i => i.nodeOperationId === option);
    const scriptIndex = nodeScriptData?.findIndex(i => i.nodeOperationId === option);

    return defaultValue ? (
      <ScriptModal
        defaultValue={defaultValue}
        disabled={type === "info"}
        onFinish={values => {
          if (!isNil(scriptIndex)) {
            onNodeValueChange([index, "nodeScriptData", scriptIndex, "name"], values?.name);
            onNodeValueChange([index, "nodeScriptData", scriptIndex, "scriptId"], values?.scriptId);
          }
        }}
      />
    ) : null;
  };

  const access = useAccess();

  return (
    <Modal
      title={titleMap[type]}
      visible={visible}
      width={modelWidth.md}
      destroyOnClose
      onCancel={onClose}
      onOk={onSubmit}
      footer={
        type === "info" ? (
          false
        ) : (
          // <ModalFooter
          //   enableButton={{
          //     isEnable: workOrderTempInfoReq.data?.state === WorkOrderStateEnum.启用,
          //     onClick: onEnable,
          //   }}
          //   deleteButton={{
          //     onClick: onDelete,
          //   }}
          //   editButton={
          //     access.canWorkOrder_edit
          //       ? {
          //           onClick: (e) => {
          //             setType('update');
          //           },
          //         }
          //       : undefined
          //   }
          //   cancelButton={{ onClick: onClose }}
          // />
          <Space>
            <Button onClick={onClose}>取消</Button>
            <Button type="primary" loading={postWorkOrderTempReq.loading} onClick={onSubmit}>
              确定
            </Button>
          </Space>
        )
      }
    >
      <Skeleton active loading={workOrderTempInfoReq.loading}>
        <ProForm<FormValuesType>
          form={form}
          submitter={false}
          onValuesChange={onValuesChange}
          preserve={false}
          initialValues={initValues}
          grid
        >
          <ProFormGroup
            rowProps={{
              gutter: 24
            }}
            title="基本信息"
          >
            <ProFormText
              colProps={{
                xs: 24,
                sm: 12
              }}
              name="workOrderName"
              label="工单名称"
              disabled={type === "info"}
              rules={[{ required: true, max: 20 }]}
            />
            <ProFormSelect
              colProps={{
                xs: 24,
                sm: 12
              }}
              name="workOrderType"
              label="工单类型"
              disabled={type === "info"}
              options={[
                {
                  label: "商品价格编辑审批",
                  value: WorkOrderTypeEnum.商品价格编辑审批
                }
              ]}
              rules={[{ required: true }]}
            />
          </ProFormGroup>
          {/* 规则设置 */}
          <ProFormGroup
            rowProps={{
              gutter: 24
            }}
            title="规则设置"
          >
            <ProFormGroup
              rowProps={{
                gutter: 24
              }}
            >
              <ProFormRadio.Group
                colProps={{
                  xs: 24,
                  sm: 12
                }}
                name="approverType"
                label="审批人为空时"
                disabled={type === "info"}
                options={[
                  {
                    label: "默认同意",
                    value: ApproverTypeEnum.默认同意
                  },
                  {
                    label: "指定人员处理",
                    value: ApproverTypeEnum.指定人员处理
                  }
                ]}
                rules={[{ required: true }]}
              />

              {approverType === ApproverTypeEnum.指定人员处理 && (
                <ProFormSelect
                  colProps={{
                    xs: 24,
                    sm: 12
                  }}
                  label="请选择指定人员"
                  name="specifyApproverId"
                  disabled={type === "info"}
                  showSearch
                  fieldProps={{ options: userListReq.data }}
                  formItemProps={{
                    style: {
                      margin: 0
                    }
                  }}
                  rules={[{ required: true }]}
                />
              )}
            </ProFormGroup>

            <ProFormRadio.Group
              colProps={{
                xs: 24,
                sm: 12
              }}
              name="nodeApproverType"
              label="各节点审批人为"
              disabled={type !== "add"}
              options={[
                {
                  label: "预设人选",
                  value: ApproverRuleTypeEnum.预设人选
                },
                {
                  label: "由发起人确定",
                  value: ApproverRuleTypeEnum.由发起人确定
                }
              ]}
              rules={[{ required: true }]}
            />
            {nodeApproverType === ApproverRuleTypeEnum.由发起人确定 && (
              <ProFormDigit
                colProps={{
                  xs: 24,
                  sm: 12
                }}
                label="审批节点个数"
                name="sponsorCount"
                disabled={type === "info"}
                min={1}
                max={10}
                rules={[{ required: true }]}
                fieldProps={{
                  precision: 0
                }}
              />
            )}
          </ProFormGroup>
        </ProForm>
        {/* 动态审批节点  */}
        <ProForm form={nodeForm} submitter={false} preserve={false} grid>
          {nodeData.map((nodeItem, index) => {
            const {
              id,
              participateMode,
              approvalMode,
              approverType,
              roleApproverId,
              singleApproverId,
              departmentAppeoverId,
              nodeScriptData = []
            } = nodeItem;
            return (
              <ProFormGroup
                rowProps={{
                  gutter: 24
                }}
                key={getUniqueId()}
                title={
                  <Space>
                    审批节点{index + 1}
                    <Tooltip title="删除节点">
                      {nodeData.length > 1 && type !== "info" && (
                        <MinusCircleOutlined
                          onClick={() => {
                            updateNodeData(nodes => {
                              remove(nodes, (_, i) => i === index);
                            });
                            if (id) {
                              updateDeleteNodeData(nodes => {
                                nodes.push({
                                  ...nodeItem,
                                  isDelete: 1
                                });
                              });
                            }
                          }}
                        />
                      )}
                    </Tooltip>
                  </Space>
                }
              >
                {/* 参与方式 */}
                {nodeApproverType === ApproverRuleTypeEnum.预设人选 && (
                  <ProFormRadio.Group
                    colProps={{
                      xs: 24,
                      sm: 12
                    }}
                    name={index + "participateMode"}
                    label="参与方式"
                    initialValue={ParticipateModeEnum.单人}
                    disabled={type === "info"}
                    fieldProps={{
                      value: participateMode,
                      onChange(e) {
                        onNodeValueChange([index], initNodeItem);
                        onNodeValueChange([index, "participateMode"], e.target.value);
                      }
                    }}
                    options={[
                      {
                        label: "单人",
                        value: ParticipateModeEnum.单人
                      },
                      {
                        label: "多人",
                        value: ParticipateModeEnum.多人
                      }
                    ]}
                    rules={[{ required: true }]}
                  />
                )}

                {/* 审批人 */}
                {nodeApproverType === ApproverRuleTypeEnum.预设人选 &&
                  (approverType === NodeApproverTypeEnum.指定角色 && participateMode === ParticipateModeEnum.多人 ? (
                    <ProFormSelect
                      colProps={{
                        xs: 24,
                        sm: 12
                      }}
                      name={index + "roleApproverId"}
                      label="请选择审批人"
                      disabled={type === "info"}
                      showSearch
                      options={roleListReq.data}
                      rules={[{ required: true }]}
                      fieldProps={{
                        value: roleApproverId,
                        onChange: val => {
                          onNodeValueChange([index, "roleApproverId"], val);
                        }
                      }}
                    />
                  ) : (
                    <ProFormSelect
                      colProps={{
                        xs: 24,
                        sm: 12
                      }}
                      name={
                        index +
                        (participateMode === ParticipateModeEnum.单人 ? "singleApproverId" : "departmentAppeoverId")
                      }
                      mode={participateMode === ParticipateModeEnum.多人 ? "multiple" : "single"}
                      disabled={type === "info"}
                      label="请选择审批人"
                      rules={[{ required: true }]}
                      showSearch
                      fieldProps={{
                        value: (participateMode === ParticipateModeEnum.单人
                          ? singleApproverId
                          : departmentAppeoverId || []) as any,
                        options: userListReq.data,
                        onChange: (value: any) => {
                          const changeValue =
                            participateMode === ParticipateModeEnum.单人 ? "singleApproverId" : "departmentAppeoverId";
                          updateNodeData(nodes => {
                            nodes[index][changeValue] = value;
                          });
                        }
                      }}
                    />
                  ))}

                {/* 审批方式、审批人类型 */}
                {/* 依赖参与方式  依赖值为 nodeData[name].mode 和 approveType */}
                {nodeApproverType === ApproverRuleTypeEnum.预设人选 && participateMode === ParticipateModeEnum.多人 && (
                  <>
                    <ProFormRadio.Group
                      colProps={{
                        xs: 24,
                        sm: 12
                      }}
                      label="审批方式"
                      name={index + "approvalMode"}
                      disabled={type === "info"}
                      fieldProps={{
                        value: approvalMode,

                        onChange(e) {
                          onNodeValueChange([index, "approvalMode"], e.target.value);
                          // onNodeValueChange([index, 'approvalMode'], e.target.value);
                        }
                      }}
                      rules={[{ required: true }]}
                      options={[
                        {
                          label: (
                            <Space>
                              会签
                              <Tooltip title="需全员审批通过，工单才进入下一级审批。其中任意一个审批人驳回，则审批不通过">
                                <QuestionCircleOutlined />
                              </Tooltip>
                            </Space>
                          ),
                          value: ApprovalModeEnum.会签
                        },
                        {
                          label: (
                            <Space>
                              或签
                              <Tooltip title="仅其中一人审批通过即可，审批通过后，其它同级审批人在“我的审批”页面中也能查看此工单。任意一个人驳回，则审批不通过">
                                <QuestionCircleOutlined />
                              </Tooltip>
                            </Space>
                          ),
                          value: ApprovalModeEnum.或签
                        }
                      ]}
                    />
                    <ProFormRadio.Group
                      colProps={{
                        xs: 24,
                        sm: 12
                      }}
                      label="审批人类型"
                      name={index + "approverType"}
                      disabled={type === "info"}
                      fieldProps={{
                        value: approverType,
                        onChange(e) {
                          onNodeValueChange([index, "approverType"], e.target.value);
                          onNodeValueChange([index, "roleApproverId"], undefined);
                          onNodeValueChange([index, "departmentAppeoverId"], undefined);
                        }
                      }}
                      options={[
                        {
                          label: "指定角色",
                          value: NodeApproverTypeEnum.指定角色
                        },
                        {
                          label: "指定成员或部门",
                          value: NodeApproverTypeEnum.指定成员或部门
                        }
                      ]}
                      rules={[{ required: true }]}
                    />
                  </>
                )}

                {/* 审批操作 */}
                <ProFormCheckbox.Group
                  colProps={{ span: 24 }}
                  name={index + "nodeScriptData"}
                  label="审批操作"
                  rules={[{ required: true }]}
                  disabled={type === "info"}
                  layout="vertical"
                  fieldProps={{
                    value: nodeScriptData?.map(i => i.nodeOperationId),
                    onChange(value) {
                      onNodeValueChange(
                        [index, "nodeScriptData"],
                        value.map(i => {
                          const current = nodeScriptData.find(item => item.nodeOperationId === i) || {};
                          return {
                            ...current,
                            nodeOperationId: i
                          };
                        })
                      );
                    }
                  }}
                  options={[
                    {
                      label: (
                        <Space>
                          同意
                          {getModal(index, NodeOperationEnum.同意, nodeScriptData)}
                        </Space>
                      ),
                      value: NodeOperationEnum.同意
                    },
                    {
                      label: (
                        <Space>
                          同意并加签
                          {getModal(index, NodeOperationEnum.同意并加签, nodeScriptData)}
                        </Space>
                      ),
                      value: NodeOperationEnum.同意并加签,
                      disabled: participateMode === ParticipateModeEnum.多人
                    },
                    {
                      label: (
                        <Space>
                          拒绝
                          {getModal(index, NodeOperationEnum.拒绝, nodeScriptData)}
                        </Space>
                      ),
                      value: NodeOperationEnum.拒绝
                    }
                  ]}
                />
              </ProFormGroup>
            );
          })}

          {type !== "info" && nodeApproverType === ApproverRuleTypeEnum.预设人选 && nodeData.length < 10 && (
            <Col span={24}>
              <Form.Item>
                <Button
                  type="dashed"
                  onClick={() => {
                    updateNodeData(nodes => {
                      nodes.push(initNodeItem);
                    });
                  }}
                  block
                  icon={<PlusOutlined />}
                >
                  新增审批节点
                </Button>
              </Form.Item>
            </Col>
          )}
        </ProForm>
      </Skeleton>
    </Modal>
  );
};

export default WorkOrderModal;
