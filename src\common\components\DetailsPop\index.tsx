/**
 * 详情弹窗组件
 * @param title  标题
 * @param visible/setVisible   开关
 * @param isLoading  骨架屏
 * @param columnsInitial  模板
 * @param dataSource  数据 {isEnable:禁启用}
 * @param onEnable  禁启用按钮 (成功交互函数 (),失败交互函数 (提示)) => {}
 * @param onDelete  删除按钮
 * @param onUpdate  编辑按钮
 * */
import { modelWidth } from "@/common/utils/gConfig";
import { getUniqueId } from "@/common/utils/tool";
import { InfoCircleTwoTone } from "@ant-design/icons";
import ProDescriptions from "@ant-design/pro-descriptions";
import { Button, Divider, message, Modal } from "antd";
import React from "react";
import { Access, useAccess } from "@umijs/max";
import PrefixTitle from "../PrefixTitle";

const DetailsPop = ({
  title,
  visible,
  isLoading,
  setVisible,
  columnsInitial,
  dataSource,
  onEnable,
  onDelete,
  onUpdate,
  limit, //是否具有权限
  width,
  enableTitle
}: {
  title: string | React.ReactNode;
  visible: boolean;
  isLoading: boolean;
  setVisible: any;
  columnsInitial: any[];
  dataSource: any;
  onEnable?: any;
  onDelete?: any;
  onUpdate?: any;
  enableTitle?: string;
  limit?: {
    openClose?: string;
    delete?: string;
    edit?: string;
  };
  width?: any; // 弹窗宽
}) => {
  const access = useAccess();
  const [columnsList, setColumnsList] = React.useState<any>([]);
  const isEnable = dataSource?.isEnable * 1;
  // 初始化函数
  React.useEffect(() => {
    // 自动优化新增 key
    columnsInitial.map((item, index) => {
      item.columns.map((itemChild: any, indexChild: any) => {
        columnsInitial[index].columns[indexChild].key = itemChild.dataIndex;
      });
      // if (item.hideInList) columnsInitial.splice(index, 1)
    });
    setColumnsList(columnsInitial);
  }, [columnsInitial]);
  // 监听函数
  React.useEffect(() => {
    // 打开弹窗
    if (visible) {
    }
  }, [visible]);
  return (
    <Modal
      width={width || modelWidth.md}
      title={title}
      open={visible}
      onCancel={() => {
        setVisible(false);
      }}
      footer={
        onEnable || onDelete || onUpdate
          ? [
              onEnable ? (
                <Access key="startAccess" accessible={!limit?.openClose || access[limit.openClose]}>
                  <Button
                    key="start"
                    type="primary"
                    danger={!!isEnable}
                    ghost
                    onClick={async () => {
                      Modal.confirm({
                        title: `${[enableTitle || "确认启用吗？", "确认禁用吗？"][isEnable]}`,
                        icon: <InfoCircleTwoTone />,
                        okText: "确认",
                        cancelText: "取消",
                        onOk: () => {
                          onEnable(
                            () => {
                              message.success(`已${["启用", "禁用"][isEnable]}`);
                              setVisible(false);
                            },
                            (msg: any) => {
                              message.error(msg);
                            }
                          );
                        }
                      });
                    }}
                  >
                    {["启用", "禁用"][isEnable]}
                  </Button>
                </Access>
              ) : (
                ""
              ),

              onDelete ? (
                <Access key="delAccess" accessible={!limit?.delete || access[limit.delete]}>
                  <Button
                    key="del"
                    type="primary"
                    danger
                    onClick={async () => {
                      if (isEnable) {
                        Modal.warning({
                          title: "不可删除",
                          content: "请先禁用后删除"
                        });
                        return;
                      }
                      Modal.confirm({
                        title: "确认删除吗？",
                        icon: <InfoCircleTwoTone />,
                        content: "删除后不可恢复",
                        okText: "确认",
                        cancelText: "取消",
                        onOk: () => {
                          const hide = message.loading("正在删除");
                          onDelete(
                            () => {
                              hide();
                              message.success("删除成功");
                              setVisible(false);
                            },
                            (msg: any) => {
                              hide();
                              message.error(msg);
                            }
                          );
                        }
                      });
                    }}
                  >
                    删除
                  </Button>
                </Access>
              ) : (
                ""
              ),

              onUpdate ? (
                <Access key="onSubmitAccess" accessible={!limit?.edit || access[limit.edit]}>
                  <Button
                    key="onSubmit"
                    type="primary"
                    onClick={() => {
                      setVisible(false);
                      onUpdate();
                    }}
                  >
                    编辑
                  </Button>
                </Access>
              ) : (
                ""
              ),

              <Button
                key="back"
                onClick={() => {
                  setVisible(false);
                }}
              >
                取消
              </Button>
            ]
          : false
      }
    >
      <div style={{ minHeight: "337px" }}>
        {columnsList.map((item: any, index: any) => (
          <>
            <ProDescriptions
              column={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 2, xxl: 2 }}
              {...item}
              loading={index === 0 && isLoading}
              title={item.title && <PrefixTitle>{item.title} </PrefixTitle>}
              dataSource={dataSource}
              columns={item.columns}
              key={getUniqueId()}
              style={{ display: isLoading ? "none" : "block" }}
              contentStyle={{ display: "block" }}
            />
            {index < columnsList.length - 1 && !isLoading && <Divider />}
          </>
        ))}
      </div>
    </Modal>
  );
};

export default DetailsPop;
