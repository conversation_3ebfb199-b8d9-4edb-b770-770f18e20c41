/*
 * @Author: z<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-10-08 10:27:45
 * @LastEditTime: 2022-10-14 14:48:38
 * @LastEditors: zhangfengfei
 */
import { modelWidth } from '@/common/utils/gConfig';
import { getScriptDownList } from '@/services/api/workOrder';
import { PlusOutlined } from '@ant-design/icons';
import { ModalForm, ProFormSelect } from '@ant-design/pro-form';
import { Button } from 'antd';
import type { FC } from 'react';
import { useState } from 'react';
import { WorkOrderTypeEnum } from '../../common/data';

interface ScriptModalProps {
  onFinish: (values?: { name: string; scriptId: string }) => void;
  defaultValue?: { name: string; scriptId: string };
  disabled: boolean;
}

const ScriptModal: FC<ScriptModalProps> = ({ onFinish, defaultValue, disabled }) => {
  const [current, setCurrent] = useState<{ name: string; scriptId: string }>();
  console.log(defaultValue);

  return (
    <ModalForm
      title="选择脚本"
      width={modelWidth.sm}
      onVisibleChange={(visible) => {
        if (visible) {
          if (defaultValue?.scriptId !== '0') {
            setCurrent(defaultValue);
          } else {
            setCurrent(undefined);
          }
        }
      }}
      trigger={
        defaultValue?.scriptId && defaultValue?.scriptId !== '0' ? (
          <Button type="link" disabled={disabled}>
            {defaultValue.name}
          </Button>
        ) : (
          <Button type="text" icon={<PlusOutlined />}>
            新增脚本
          </Button>
        )
      }
      modalProps={{
        destroyOnClose: true,
        onCancel: () => console.log('run'),
      }}
      onFinish={async () => {
        onFinish(current);
        return true;
      }}
      layout="horizontal"
    >
      <ProFormSelect
        request={async () => {
          const { data } = await getScriptDownList({
            type: WorkOrderTypeEnum.商品价格编辑审批,
          });
          return (data ?? []).map((item) => ({
            label: item.name,
            value: item.id,
          }));
        }}
        fieldProps={{
          value: current?.scriptId,
          onChange(value, option: any) {
            setCurrent({
              name: option?.label,
              scriptId: value,
            });
          },
        }}
        width="md"
        name="scriptId"
        label="脚本模版"
      />
    </ModalForm>
  );
};

export default ScriptModal;
