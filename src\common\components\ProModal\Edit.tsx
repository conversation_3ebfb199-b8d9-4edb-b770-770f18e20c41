/**
 * 新增 | 编辑 弹窗组件
 **/
import { modelWidth } from "@/common/utils/gConfig";
import { LeftOutlined } from "@ant-design/icons";
import { BetaSchemaForm, FooterToolbar, ModalForm, ProCard, ProForm } from "@ant-design/pro-components";
import type { FormInstance } from "antd";
import { Button, Space, Spin, message } from "antd";
import { useEffect } from "react";
import { useRequest } from "@umijs/max";
import PrefixTitle from "../PrefixTitle";
import styles from "./index.less";

type AttributeTypeEdit = {
  page?: any;
  layout?: any;
  title: string;
  fullTitle?: string;
  type: any;
  setType: any;
  columns: any;
  params: any;
  dataSource?: any;
  infoRequest?: any;
  onFinish?: any;
  actionRef?: any;
  addRequest?: any;
  editRequest?: any;
  formInstance?: FormInstance<any>;
  onValuesChange?: any;
  onOpenChange?: any;
  onCancel?: any;
};

export default ({
  page,
  layout,
  title,
  fullTitle,
  type,
  setType,
  columns,
  params = {},
  dataSource,
  infoRequest,
  onFinish,
  actionRef,
  addRequest,
  editRequest,
  formInstance,
  onValuesChange,
  onOpenChange,
  onCancel
}: AttributeTypeEdit) => {
  // 自动优化默认属性
  const newColumns = columns.map((item: any) => {
    if (item.valueType == "dependency") return item;
    const childColumns = (item.columns ?? []).map(i => ({
      width: "100%",
      colProps: page ? { xs: 24, sm: 12, xl: 8, xxl: 6 } : { xs: 24, sm: 12 },
      ...i
    }));

    return {
      rowProps: { gutter: 24 },
      valueType: "group",
      ...item,
      title: item.title && <PrefixTitle>{item.title} </PrefixTitle>,
      columns: childColumns
    };
  });
  // 表单实例
  const [form] = ProForm.useForm(formInstance);
  // 加载数据
  const req = useRequest(dataSource ? () => new Promise(r => r({ data: dataSource })) : infoRequest, {
    manual: true,
    onSuccess(data) {
      form.setFieldsValue(data);
    }
  });
  // 保存数据
  const finish = async (val: any) => {
    if (type == "add") {
      const isPass = await addRequest({ ...val, ...params });
      if (!isPass) return;
      message.success("新增成功");
      actionRef?.current?.reload();
      setType(null);
    } else {
      const isPass = await editRequest({ ...val, ...params });
      if (!isPass) return;
      message.success("编辑成功");
      actionRef?.current?.reload();
      setType(null);
    }
  };
  // 初始化
  useEffect(() => {
    if (type == "edit") req.run(params);
  }, [type]);

  return page ? (
    <div className="relative">
      <ProCard
        title={
          <div
            className="flex align-items-center primary-color pointer"
            onClick={() => {
              setType(null);
              onCancel?.();
            }}
          >
            <LeftOutlined style={{ marginRight: 10 }} />
            {(type == "add" ? "新增" : "编辑") + title}
          </div>
        }
        className="relative"
        headerBordered
      >
        <ProForm
          form={form}
          title={(type == "add" ? "新增" : "编辑") + title}
          preserve={false}
          onFinish={onFinish || finish}
          onFinishFailed={() => {
            // 表单提交失败滚动定位
            setImmediate(() => {
              document
                .querySelector(".ant-form-item-has-error")
                ?.scrollIntoView({ behavior: "smooth", block: "center" });
            });
          }}
          rowProps={{
            gutter: 24
          }}
          grid
          clearOnDestroy
          submitter={false}
          onValuesChange={(...e) => {
            if (onValuesChange) onValuesChange(form, ...e);
          }}
        >
          <Spin spinning={req.loading} wrapperClassName={styles.spin}>
            <BetaSchemaForm layoutType="Embed" columns={newColumns} />
          </Spin>
        </ProForm>
      </ProCard>
      <div
        className="flex w-100 justify-content-center align-items-center"
        style={{
          position: "sticky",
          height: 72,
          backgroundColor: "white",
          bottom: 0,
          zIndex: 2,
          boxShadow: "0px -2px 9px -1px rgba(208,208,208,0.5)"
        }}
      >
        <Space>
          <Button
            onClick={() => {
              setType(null);
              onCancel?.();
            }}
            key="1"
          >
            取消
          </Button>
          <Button type="primary" onClick={form.submit} key="2">
            确定
          </Button>
        </Space>
      </div>
    </div>
  ) : (
    <ModalForm
      layout={layout || "vertical"}
      form={form}
      width={modelWidth.md}
      title={fullTitle || (type == "add" ? "新增" : "编辑") + title}
      open={!!type}
      onOpenChange={e => {
        if (!e) {
          setType(null);
        }
        onOpenChange?.(e);
      }}
      modalProps={{
        maskClosable: false,
        destroyOnClose: true
      }}
      preserve={false}
      onFinish={onFinish || finish}
      onFinishFailed={() => {
        // 表单提交失败滚动定位
        setImmediate(() => {
          document.querySelector(".ant-form-item-has-error")?.scrollIntoView({ behavior: "smooth", block: "center" });
        });
      }}
      rowProps={{
        gutter: 24
      }}
      grid
      submitter={
        page && {
          searchConfig: {
            submitText: "确定"
          },
          render: (_: any, dom: any) => (
            <FooterToolbar>{[<Button onClick={() => setType(null)}>取消</Button>, dom[1]]}</FooterToolbar>
          )
        }
      }
      onValuesChange={e => {
        if (onValuesChange) onValuesChange(form, e);
      }}
    >
      <Spin spinning={req.loading} wrapperClassName={styles.spin}>
        <BetaSchemaForm layoutType="Embed" columns={newColumns} />
      </Spin>
    </ModalForm>
  );
};
