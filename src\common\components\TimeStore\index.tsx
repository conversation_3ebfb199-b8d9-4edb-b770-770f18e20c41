import { getUniqueId } from '@/common/utils/tool';
import type { CalendarProps } from 'antd';
import { Calendar, DatePicker, Flex } from 'antd';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { useMemo } from 'react';
import styles from './index.less';

export default ({ type, dateRange, lawsColumns, lawsList, value, onChange, set, get }: any) => {
  console.log(dateRange);

  const valueObj = useMemo(() => {
    const obj1: any = {};
    value?.forEach((item1: any) => {
      const obj2: any = {};
      item1.timeShareDateList.forEach((item2: any) => {
        obj2[item2.timeShareId] = item2;
      });
      obj1[item1.timeShareData] = obj2;
    });
    return obj1;
  }, [value]);

  const headerRender = ({ value, type, onChange }: any) => {
    return (
      <div className="month">
        <DatePicker value={value} onChange={onChange} picker="month" variant="borderless" />
      </div>
    );
  };
  const cellRender: CalendarProps<Dayjs>['cellRender'] = (current) => {
    const timeShareDate = current.format('YYYY-MM-DD');
    return (
      <>
        <Flex>
          {current.day() == 1 && <div className="d1 week" />}
          <div className="d2 day">{current.format('DD')}</div>
        </Flex>
        {lawsList?.map((item: any) => (
          <Flex key={getUniqueId()}>
            {current.day() == 1 && (
              <div className="d1 left">
                <Flex vertical>
                  {lawsColumns?.map((columns: any) => (
                    <div key={getUniqueId()}>{columns.title(item)}</div>
                  ))}
                </Flex>
              </div>
            )}
            <div
              className={[
                'd2 content',
                type == 'add' &&
                  !(
                    dayjs(timeShareDate).isBefore(dateRange[0]) ||
                    dayjs(timeShareDate).isAfter(dateRange[1])
                  ) &&
                  set &&
                  'set',
                type == 'add' &&
                  !(
                    dayjs(timeShareDate).isBefore(dateRange[0]) ||
                    dayjs(timeShareDate).isAfter(dateRange[1])
                  ) &&
                  get &&
                  'get',
              ]
                .filter((i) => i)
                .join(' ')}
              style={{ width: '100%', textAlign: 'right' }}
              onClick={() => (set ? set(item.id, timeShareDate) : get(item, timeShareDate))}
            >
              <Flex vertical>
                {lawsColumns?.map((columns: any) => (
                  <div key={getUniqueId()}>
                    {!(
                      dayjs(timeShareDate).isBefore(dateRange[0]) ||
                      dayjs(timeShareDate).isAfter(dateRange[1])
                    ) &&
                      (type == 'add' || valueObj?.[timeShareDate]?.[item.id]) &&
                      columns.render(valueObj?.[timeShareDate]?.[item.id] || {})}
                  </div>
                ))}
              </Flex>
            </div>
          </Flex>
        ))}
      </>
    );
  };

  return (
    <div className={styles.timeStore}>
      <div className="time-share-head" />
      <Calendar
        className="time-share-content"
        headerRender={headerRender}
        fullCellRender={cellRender}
      />
    </div>
  );
};
