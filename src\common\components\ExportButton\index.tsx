import { scenicHost } from '@/services/api';
import { apiExportHeaderQuery } from '@/services/api/config';
import { DownloadOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import qs from 'qs';
import { useEffect } from 'react';

export default ({ exportState }: any) => {
  useEffect(() => {
    console.log(exportState.moduleFlag);

    apiExportHeaderQuery({
      modulePath:
        exportState.uri +
        (exportState.params.moduleFlag ? `/export_${exportState.params.moduleFlag}` : '/export'),
    }).then((res) => {
      if (res.data?.headerInfos?.length) {
        const obj = {};
        for (const v of res.data.headerInfos) {
          obj[v.headerField] = {
            show: !!v.state,
            order: v.sort,
            fixed: v.fixed,
          };
        }
        exportState.setColumnsValue(obj);
      }
    });
  }, []);
  return (
    <Button
      type="primary"
      icon={<DownloadOutlined />}
      onClick={() => {
        location.href = `${scenicHost}${exportState.uri}/export?${qs.stringify({
          ...exportState.formRef.current?.getFieldsFormatValue(),
          ...exportState.params,
        })}`;
      }}
    >
      导出
    </Button>
  );
};
