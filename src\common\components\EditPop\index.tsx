/**
 * 新增/编辑 弹窗组件
 **/
import { modelWidth } from '@/common/utils/gConfig';
import { BetaSchemaForm } from '@ant-design/pro-components';
import { Form } from 'antd';
import { useEffect } from 'react';
import PrefixTitle from '../PrefixTitle';

const EditPop = ({
  title,
  visible,
  setVisible,
  columns,
  dataSource,
  onFinish,
  onValuesChange,
  width,
  loading = false,
  disable = false,
}: {
  title: string; // 标题
  visible: boolean; // 弹窗状态
  setVisible: any; // 设置弹窗状态
  columns: any[]; // 详情配置数据
  dataSource: any; // 默认数据
  onFinish?: any; // 确定回调
  onValuesChange?: any; // 数据变化回调
  width?: any; // 弹窗宽
  loading?: boolean; // 加载状态
  disable?: boolean; // 是否全禁用
}) => {
  // 自动优化默认属性
  const newColumns = columns.map((item: any) => {
    if (item.valueType !== 'dependency') {
      const childColumns = (item.columns ?? []).map((i) => ({
        width: '100%',
        colProps: { xs: 24, sm: 12 },
        ...i,
      }));

      return {
        rowProps: { gutter: 24 },
        valueType: 'group',
        ...item,
        title: item.title && <PrefixTitle>{item.title} </PrefixTitle>,
        columns: childColumns,
      };
    }
    return item;
  });
  // 表单对象
  const [form] = Form.useForm<{ name: string; company: string }>();
  // 初始化
  useEffect(() => {
    console.log('dataSource?.id', dataSource?.id);
    if (visible && dataSource?.id) {
      form.setFieldsValue(dataSource);
    }
  }, [visible, dataSource]);

  return (
    <BetaSchemaForm
      layoutType="ModalForm"
      width={width || modelWidth.md}
      // formRef={formRef}
      form={form}
      open={visible}
      onOpenChange={setVisible}
      title={(dataSource?.id ? '编辑' : '新增') + title}
      modalProps={{
        maskClosable: false,
        destroyOnClose: true,
      }}
      preserve={false}
      onFinish={async (values) => {
        await onFinish(values, { current: form });
      }}
      onFinishFailed={() => {
        setImmediate(() => {
          document
            .querySelector('.ant-form-item-has-error')
            ?.scrollIntoView({ behavior: 'smooth', block: 'center' });
        });
      }}
      onValuesChange={(e) => {
        if (onValuesChange) onValuesChange({ current: form }, e);
      }}
      columns={newColumns}
      rowProps={{
        gutter: 24,
      }}
      grid
      disabled={disable}
    />
  );
};

export default EditPop;
