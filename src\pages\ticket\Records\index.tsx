/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-11-01 16:38:34
 * @LastEditTime: 2023-10-23 11:08:14
 * @LastEditors: zhangfengfei
 */
import Export, { columnsSet } from "@/common/components/Export";
import useExport from "@/common/components/Export/useExport";
import { tableConfig } from "@/common/utils/config";
import { equipmentTypeEnum, identityType, productTypeEnum, ticketTypeEnum, whetherEnum } from "@/common/utils/enum";
import { apiWriteOffList, getDistributorList } from "@/services/api/ticket";
import type { ProColumns } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import { trim } from "lodash";
import dayjs from "dayjs";
import qs from "qs";
import React, { useEffect, useState } from "react";
import { useModel } from "@umijs/max";
import { getEnv } from "@/common/utils/getEnv";

const TableList: React.FC = () => {
  // 【景区】信息
  const { initialState } = useModel("@@initialState");
  const { scenicId, isBlockChain } = initialState?.scenicInfo || {};
  const { currentCompanyInfo }: any = initialState || {};
  const [totalCheckCount, setTotalCheckCount] = useState(0);
  const [distributorList, setDistributorList] = useState({});

  // 【表格】数据绑定
  const columns: ProColumns[] = [
    {
      title: "所属企业",
      dataIndex: "sellerId",
      hideInTable: true,
      valueEnum: distributorList,
      search: {
        transform: val => trim(val)
      },
      fieldProps: {
        showSearch: true
      }
    },
    {
      title: "所属企业",
      dataIndex: "sellerName",
      search: false
    },
    {
      title: "票名称",
      dataIndex: "goodsName",
      fixed: "left",
      search: {
        transform: val => trim(val)
      }
    },
    {
      title: "产品名称",
      dataIndex: "ticketName",
      search: {
        transform: val => trim(val)
      }
    },
    {
      title: "产品类型",
      dataIndex: "ticketType",
      valueEnum: productTypeEnum
    },
    {
      title: "票种",
      dataIndex: "goodsType",
      valueEnum: ticketTypeEnum
    },
    {
      title: "数字资产",
      dataIndex: "isChainTicket",
      hideInSearch: isBlockChain == 0,
      hideInTable: isBlockChain == 0,
      valueEnum: whetherEnum
    },
    {
      title: "票号",
      dataIndex: "ticketNumber",
      fixed: "left",
      search: {
        transform: val => trim(val)
      }
    },
    {
      title: "订单号",
      dataIndex: "orderId",
      search: {
        transform: val => trim(val)
      }
    },
    {
      title: "核销次数",
      dataIndex: "checkCount",
      valueType: "digit",
      fixed: "right",
      search: false
    },
    {
      title: "核销终端类型",
      dataIndex: "equipmentType",
      valueEnum: equipmentTypeEnum
    },
    {
      title: "核销设备名称",
      dataIndex: "equipmentName",
      search: {
        transform: val => trim(val)
      },
      renderText: (text, { checkType, remark }) => {
        if (checkType == 2) {
          return remark || "-";
        }
        return text;
      }
    },
    {
      title: "身份识别类型",
      dataIndex: "enterWay",
      valueEnum: identityType
    },
    {
      title: "核销时间",
      dataIndex: "time",
      valueType: "dateRange",
      hideInTable: true,
      search: {
        transform: value => {
          return {
            beginTime: value[0],
            endTime: value[1]
          };
        }
      },
      initialValue: [dayjs().startOf("month").format("YYYY-MM-DD"), dayjs().endOf("month").format("YYYY-MM-DD")]
    },
    {
      title: "核销时间",
      dataIndex: "checkTime",
      valueType: "dateTime",
      fixed: "left",
      search: false
    },
    {
      title: "核销哈希",
      dataIndex: "txId",
      hideInTable: isBlockChain == 0,
      search: false,
      renderText: text =>
        text ? (
          <a
            title={text}
            target="_blank"
            href={`${getEnv().CHAIN_URL}/#/tx_list?${qs.stringify({
              tx: text
            })}`}
            rel="noreferrer"
          >
            {text.length > 20 ? text.slice(0, 10) + "..." + text.slice(-10) : text}
          </a>
        ) : (
          "-"
        )
    }
  ];
  const exportState = useExport({
    columns,
    modulePath: "Backend_VerificationRecord",
    params: { scenicId, operatorId: currentCompanyInfo.coId }
  });

  useEffect(() => {
    getDistributorList({ distributorId: currentCompanyInfo.coId }).then(res => {
      const obj = {
        [currentCompanyInfo.coId]: currentCompanyInfo?.coName
      };
      res.data.map((item: any) => {
        obj[item.coId] = item.coName;
      });
      setDistributorList(obj);
    });
  }, []);

  return (
    <>
      <ProTable
        headerTitle={"核销总次数：" + totalCheckCount}
        {...tableConfig}
        params={{ scenicId, operatorId: currentCompanyInfo.coId }}
        request={async params => {
          const { data } = await apiWriteOffList(params);
          setTotalCheckCount(data.totalCheckCount);
          return { data: data.data, total: data.page.total };
        }}
        columns={columns}
        formRef={exportState.formRef}
        columnsState={columnsSet(exportState)}
        toolBarRender={() => [<Export key="export" {...exportState} />]}
      />
    </>
  );
};

export default TableList;
