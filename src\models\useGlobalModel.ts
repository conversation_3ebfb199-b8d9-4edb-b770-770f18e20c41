/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-07-07 14:46:50
 * @LastEditTime: 2022-07-08 09:59:59
 * @LastEditors: zhangfengfei
 */
import { getParamsConfig } from "@/services/api/settings";
import { apiUserCoInfo } from "@/services/api/ticket";
import { useEffect } from "react";
import { useModel, useRequest } from "@umijs/max";

// export interface GlobalModelData {
//   run: () => Promise<API.ParamsConfigType>;
//   data: API.ParamsConfigType | undefined;
//   loading: boolean;
// }

/**
 * @description: 放全局数据  初始化数据initialState之后的请求数据 避免阻塞
 */
export default function useGlobalModel() {
  const { initialState, loading } = useModel("@@initialState");
  const { scenicId = "" } = initialState?.scenicInfo || {};
  const { userId } = initialState?.userInfo || {};
  const getParamsConfigReq = useRequest(getParamsConfig, {
    manual: true
  });

  useEffect(() => {
    if (!loading && scenicId && userId) {
      getParamsConfigReq.run({
        id: scenicId
      });
    }
  }, [loading, scenicId, userId]);

  return {
    /** 景区参数设置 */
    paramsConfig: getParamsConfigReq
  };
}
