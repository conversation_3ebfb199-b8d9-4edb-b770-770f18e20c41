// @ts-ignore
/* eslint-disable */

type ResponseData<T = any> = {
  code: number;
  data: T;
  msg: string;
};
type ResponseListData<T, S = {}> = {
  code: number;
  data: {
    current: number;
    pageSize: number;
    data: T;
    total: number;
  } & S;
  msg: string;
};

declare namespace API {
  type CurrentUser = {
    name?: string;
    avatar?: string;
    userid?: string;
    email?: string;
    signature?: string;
    title?: string;
    group?: string;
    tags?: { key?: string; label?: string }[];
    notifyCount?: number;
    unreadCount?: number;
    country?: string;
    access?: string;
    geographic?: {
      province?: { label?: string; key?: string };
      city?: { label?: string; key?: string };
    };
    address?: string;
    phone?: string;
  };

  type LoginResult = {
    status?: string;
    type?: string;
    currentAuthority?: string;
  };

  type PageParams = {
    current?: number;
    pageSize?: number;
    total?: number;
    scenicId?: string;
  };
  // type PageParams = any;

  type RuleListItem = {
    data: any;
    key?: number;
    disabled?: boolean;
    href?: string;
    avatar?: string;
    name?: string;
    owner?: string;
    desc?: string;
    callNo?: number;
    status?: number;
    updatedAt?: string;
    createdAt?: string;
    progress?: number;
  };

  type RuleList = {
    data?: RuleListItem[];
    /** 列表的内容总数 */
    total?: number;
    success?: boolean;
  };

  type FakeCaptcha = {
    code?: number;
    status?: string;
  };

  type LoginParams = {
    credential?: string;
    secret?: string;
    autoRegister?: boolean;
    loginTypeCode?: string;
  };

  type ErrorResponse = {
    /** 业务约定的错误码 */
    errorCode: string;
    /** 业务上的错误信息 */
    errorMessage?: string;
    /** 业务上的请求是否成功 */
    success?: boolean;
  };

  type NoticeIconList = {
    data?: NoticeIconItem[];
    /** 列表的内容总数 */
    total?: number;
    success?: boolean;
  };

  type NoticeIconItemType = 'notification' | 'message' | 'event';

  type NoticeIconItem = {
    id?: string;
    extra?: string;
    key?: string;
    read?: boolean;
    avatar?: string;
    title?: string;
    status?: string;
    datetime?: string;
    description?: string;
    type?: NoticeIconItemType;
  };

  interface MenuPermissionsParams {
    appId: string;
    groups: string;
    type: string;
    companyId: string;
    scenicId?: string;
    users?: string;
  }

  type Permission = {
    group: string;
    code: string;
    action: string;
    user: string;
  };
  //tip: 2022 年 1 月 17 日：智旅云角色管理的权限参数 pr01，用户管理 p01
  type GetPermissionListParam = {
    roleId?: any;
    type?: string;
    relationId?: string;
    companyId?: string;
    scenicId?: string;
  };

  // feat 权益卡 2022/4/26
  // 权益卡
  type TravelCardListParams = {
    scenicId: string;
    operatorId: string;
  };

  type TravelCardListItem = {
    beginDiscount: number;
    effectiveTimeShow: string;
    effectiveTimeUnitShow: string;
    endDiscount: number;
    id: string;
    isEnable: number;
    marketPrice: number;
    overallDiscount: number;
    scenicId: string;
    serviceChargeRate: number;
    travelCardName: string;
    useFrequency: number;
    useFrequencyType: number;
  };

  interface AddRightsCardParams {
    beginDiscount: number;
    effectiveTime: string;
    effectiveTimeUnit: string;
    endDiscount: number;
    htmlName: string;
    issueId: string;
    marketPrice: number;
    notices: string;
    operatorId: string;
    overallDiscount: number;
    renewEndTime: number;
    renewEndTimeUnit: string;
    renewTipAfterTime: number;
    renewTipFrontTime: number;
    rightsId: string;
    scenicId: string;
    travelCardName: string;
    useFrequency: number;
    useFrequencyType: number;
  }

  interface AddRightsParams {
    rightsName: string;
    scenicId: string;
    scenicType: number;
  }

  type TravelCardDownListItem = {
    travelCardId: string;
    travelCardName: string;
    operatorId: string;
    isEnable: 0 | 1;
  };
  // 权益卡商品
  type TravelCardGoodsListParams = {
    travelCardId: string;
    scenicId: string;
  };

  interface TravelGoodsTicketItem {
    id: string;
    goodsName: string;
  }
  type TravelCardGoodsListItem = {
    overallDiscount: number;
    beginDiscount: number;
    endDiscount: number;
    marketPrice: number;
    dailyPrice: number;
    goodsName: string;
    holidayPrice: number;
    id: string;
    isRight: boolean;
    effectiveTime: string;
    isEnable: number;
    isFirst: number;
    issueId: string;
    notices: string;
    rightsId: string;
    rightsName: string;
    scenicId: string;
    serviceChargeRate: number;
    travelCardId: string;
    travelCardName: string;
    marketPrice: string;
    travelGoodsTicket: TravelGoodsTicketItem[];
  };

  //权益管理
  type RightsDownListItem = { rightsId: string; rightsName: string };

  type RightsListParams = {
    scenicId: string;
  };
  type RightsListItem = {
    id: string;
    isEnable: number;
    rightsName: string;
    scenicType: number;
    travelCardNames: string[];
  };
  type PrivilegeUserListParams = {
    scenicId: string;
    rightsId: string;
  };
  type PrivilegeUserListItem = {
    buyerCompanyName: string;
    isEnable: number;
    phone: string;
    sendCardBatchId: string;
    travelCardGoodsTransactionId: string;
    userIdCard: string;
    userName: string;
    validityBeginTime: string;
    validityEndTime: string;
  };
  type PrivilegeGoodsListParams = {
    rightsId: string;
  };

  type PrivilegeGoodsListItem = {
    beginTime: string;
    endTime: string;
    goodsId: string;
    goodsName: string;
    goodsType: string;
    holidayPrice: string;
    productName: string;
    timeRestrict: number;
    weekendPrice: string;
  };
  type RenewalLogListParams = {
    id: string;
  };
  type RenewalLogListItem = {
    id: string;
    modifyTime: string;
    validityBeginTime: string;
    validityEndTime: string;
  };

  // 库存管理
  type StockListParams = {
    isEnable?: number;
    operatorId: any[];
    scenicId: string;
    ticketId?: string;
    /**0 票 1 权益卡  */
    selectType?: 0 | 1;
    stockAmount: number;
    enterEndTime: string;
  };

  type StockInfoParams = { productType: 0 | 1; id: string };

  type StockListItem = {
    enterBeginTime: string;
    enterEndTime: string;
    id: string;
    isEnable: number;
    operatorId: string;
    purchaseBeginTime: string;
    purchaseEndTime: string;
    remark: string;
    scenicId: string;
    ticketId: string;
    ticketName: string;
    timeLaws: TimeLaw[];
    totalStock: number;
    type: number;
  };

  export interface AddStockParams {
    sourceType?: number;
    ticket?: Ticket;
    travelCard?: TravelCard;
  }
  export interface AddTicketStockParams {
    accountId: string;
    creditCode: string;
    enterBeginTime: string;
    enterEndTime: string;
    meta: string;
    nums: number;
    operatorId: string;
    productType: number;
    purchaseBeginTime: string;
    purchaseEndTime: string;
    remark: string;
    restrictWeekList: any[];
    scenicId: string;
    scenicName: string;
    ticketId: string;
    ticketName: string;
    timeLaws: TimeLawItem[];
    totalStock: number;
    type: number;
  }

  export interface TimeLawItem {
    stockAmount: number;
    timeShareBeginTime: string;
    timeShareEndTime: string;
    timeShareId: string;
  }

  type AddTravelCardStockParams = {
    operatorId: string;
    purchaseBeginTime?: string;
    purchaseEndTime?: string;
    remark?: string;
    scenicId: string;
    scenicName?: string;
    ticketId: string;
    ticketName: string;
    totalStock?: number;
    type?: number;
  };

  // 审核管理
  type AuditListParams = {
    scenicId: string;
  };

  type TicketRightsListParams = {
    beginTime?: string;
    endTime?: string;
    productName?: string;
    rightsStatus?: string;
    scenicId: string;
  };
  type TicketRightsListItem = {
    approveId: string;
    createUserId: string;
    approveTime: string;
    goodsName: string;
    id: string;
    name: string;
    rightsStatus: number;
    scenicId: string;
    type: string;
  };

  interface SupplierInfoItem {
    supplierId: string;
    supplierName: string;
  }

  interface TicketRightsInfo {
    approveId: string;
    beginDiscount: number;
    checkId: string;
    checkName: string;
    createUserId: string;
    endDiscount: number;
    goodsName: string;
    id: string;
    isEnable: string;
    isPeopleNumber: string;
    issueId: string;
    issueName: string;
    marketPrice: number;
    maxPeople: string;
    minPeople: string;
    notice: string;
    overallDiscount: number;
    peopleNumber: string;
    product: productType;
    reason: string;
    remark: string;
    retreatId: string;
    retreatName: string;
    rightsStatus: number;
    ticketId: string;
    ticketName: string;
    type: string;
    validityDay: string;
  }

  interface productType {
    id: string;
    isEnable: number;
    isUpdateTimeLaws: boolean;
    issueType: number;
    marketPrice: number;
    mode: number;
    name: string;
    notice: string;
    operatorId: string;
    operatorName: string;
    parkStatistic: number;
    pcName: string;
    pictureUrl: string;
    proType: number;
    remark: string;
    restrictType: number;
    restrictWeekList: any[];
    scenicId: string;
    scenicName: string;
    sorts: number;
    timeRestrict: number;
    timeShare: TimeShareItem[];
  }

  interface TimeShareItem {
    beginTime: string;
    endTime: string;
    id: string;
  }
  interface UpdateRightsStatusParams {
    reason?: string;
    id: string;
    name: string;
    phone: string;
    rightsStatus: RightsTicketStatus;
    userId: string;
  }

  type AuditListItem = {
    enclosure: string;
    id: string;
    idCard: string;
    isEnable: number;
    notice: string;
    phone: string;
    picture: string;
    status: number;
    travelCardId: string;
    userName: string;
    productId: string;
    /**1:门票 2：权益卡 */
    typeProduct: 1 | 2;
    ticketName: string;
    goodsName: string;
    travelCardName: string;
    travelGoodsName: string;
  };
  interface ParamsConfigType {
    paramId: string;
    printCode: string;
    scenicId: string;
    totalSalesNum: number;
    totalServiceNum: number;
    certificate: string;
    checkEquipmentKey: string;
  }

  interface CoListItem {
    coCode: string;
    coId: string;
    coName: string;
    coPhone: string;
    contactName: string;
    contactPhone: string;
    settlementStatus: number;
  }
  interface SimpleGoodsInfo {
    action: string;
    beginDiscount: number;
    checkId: string;
    checkName: string;
    endDiscount: number;
    goodsName: string;
    group: string;
    id: string;
    isCheck: number;
    isEnable: number;
    isPeopleNumber: number;
    isRealName: number;
    isTravel: number;
    issueId: string;
    issueName: string;
    marketPrice: number;
    maxPeople: number;
    minPeople: number;
    notice: string;
    overallDiscount: number;
    peopleNumber: string;
    proType: string;
    remark: string;
    retreatId: string;
    retreatName: string;
    roleCode: string;
    roleName: string;
    saleChannel: string;
    ticketId: string;
    ticketName: string;
    timeBeginTime: string;
    timeEndTime: string;
    type: string;
    useType: number;
    validityDay: number;
    workState: string;
  }

  interface RoleListItem {
    code: string;
    name: string;
    roleId: string;
    status: number;
  }

  interface RoleListParams extends PageParams {
    code?: string;
    name?: string;
    status?: number;
    relationId: string;
    type: 'p01' | '02' | '03';
  }

  interface RoleListData {
    page: API.PageParams;
    roles: API.RoleListItem[];
  }

  interface WorkOrderTempListParams {
    scenicId: string;
    providerId: string;
  }
  interface WorkOrderTempListItem {
    createTime: string;
    createUserName: string;
    state: number;
    workOrderId: string;
    workOrderName: string;
    workOrderType: number;
  }

  interface WorkOrderListParams {
    approvalGroup?: string;
    approvalPlatformId?: string;
    approvalStatus?: ApprovalStatusEnum;
    approvalUserId?: string;
    endTime?: string;
    issueGroup?: string;
    issueId?: string;
    issueName?: string;
    issuePlatformId?: string;
    issueStatus?: string;
    issueType?: string;
    issueUserId?: string;
    startTime?: string;
  }
  interface WorkOrderListItem {
    createTime: string;
    group: string;
    id: string;
    issueName: string;
    issueStatus: WorkOrderStatus;
    issueType: string;
    phone: string;
    platformId: string;
    userId: string;
    username: string;
  }

  interface WorkOrderInfoParams {
    group?: string;
    id: string;
    platformId: string;
  }

  interface WorkOrderInfoData {
    createTime: string;
    explains: string[];
    id: string;
    issueName: string;
    issueType: string;
    nodeList: NodeListItem[];
  }

  interface NodeListItem {
    approvalList: ApprovalListItem[];
    id: string;
    issueId: string;
    nodeStatus: number;
    nodeType: number;
    sort: string;
    handles: number[];
  }

  interface WorkOrderScriptListItem {
    id: string;
    name: string;
  }

  interface ApprovalListItem {
    approvalStatus: number;
    approvalTime: string;
    counterId: string;
    group: string;
    id: string;
    nodeId: string;
    platformId: string;
    remark: string;
    userId: string;
  }

  interface ApprovalUserListItem {
    group: string;
    platformId: string;
    userId: string;
  }
  interface ResNodeItem {
    approvalUserList: ApprovalUserListItem[];
    nodeType: 1 | 2;
  }

  interface AddWorkOrderParams {
    explains: string[];
    issueName: string;
    issueType: string;
    nodes: ResNodeItem[];
    group: string;
    explainId: string;
    platformId: string;
    providerId: string;
    scenicId: string;
    userId: string;
  }

  interface Counter {
    group: string;
    platformId: string;
    userId: string;
  }
  interface ApprovalWorkOrderOrderParams {
    approvalStatus: number;
    counter: Counter;
    group: string;
    issueId: string;
    platformId: string;
    remark?: string;
    userId: string;
  }
  interface OrderTCardListParams {
    scenicId: string;
    serviceProviderId: string;
  }
  interface OrderTCardListItem {
    effectiveTime: number;
    identityName: string;
    orderId: string;
    payAmount: string;
    payTime: string;
    productSkuId: string;
    productSkuName: string;
    marketPrice: string;
    travelCardName: string;
    cardId: string;
  }
  interface OrderRightsGoodsListItem {
    createTime: string;
    orderId: string;
    payAmount: string;
    payTime: number;
    rightsTicketName: string;
    rightsCardId: string;
    rightsCardName: string;
    rightsTicketId: string;
    scenicId: string;
    scenicName: string;
    status: number;
    ticketNumber: string;
  }

  interface OrderRightsGoodsDownListItem {
    id: string;
    name: string;
  }

  interface OrderInfoType {
    order: OrderInfo;
    orderRefund: OrderRefundItem[];
    orderStatus: OrderStatusType;
    orderTravelCard: OrderTravelCardItem[];
    ticketInfoList: TicketInfoList;
  }

  interface OrderInfo {
    agentId: string;
    agentName: string;
    commissionAmount: number;
    createTime: string;
    distributorId: string;
    distributorName: string;
    modifyTime: string;
    orderId: string;
    orderStatus: string;
    orderType: string;
    payAmount: number;
    payTime: string;
    payType: number;
    pilotIdentity: string;
    pilotName: string;
    pilotPhone: string;
    serviceProviderId: string;
    serviceProviderName: string;
    sourceType: number;
    ticketNum: string;
    totalAmount: number;
    tradeNo: string;
    userId: string;
    username: string;
  }

  interface OrderStatusType {
    checkStatus: string;
    checkTime: string;
    createTime: string;
    orderStatus: string;
    orderTime: string;
    payStatus: string;
    payTime: string;
    refundStatus: string;
    refundTime: string;
    ticketStatus: string;
    ticketTime: string;
  }

  interface TicketInfoList {
    current: number;
    data: TicketInfoListItem[];
    pageSize: number;
    total: number;
  }

  interface OrderRefundItem {
    certId: string;
    createTime: string;
    failMessage: string;
    orderId: string;
    orderProductId: string;
    productId: string;
    productName: string;
    productSkuId: string;
    productSkuName: string;
    productType: number;
    refundAmount: number;
    refundFee: number;
    refundId: string;
    refundStatus: number;
    refundTime: string;
    refundType: number;
    remark: string;
    ticketInfo: TicketInfoItem[];
    ticketNumber: string;
    ticketType: number;
    tradeNo: string;
  }

  interface OrderTravelCardItem {
    actualAmount: number;
    cardId: string;
    checkId: string;
    commissionAmount: number;
    commissionRate: number;
    commissionType: number;
    effectiveTime: string;
    examineIdCard: string;
    identity: string;
    identityName: string;
    orderId: string;
    orderProductId: string;
    phone: string;
    productId: string;
    productName: string;
    productPrice: number;
    productSkuId: string;
    productSkuName: string;
    productType: number;
    realName: number;
    scenicId: string;
    scenicName: string;
    serviceCharge: number;
  }

  interface TicketInfoListItem {
    actualAmount: number;
    buyer: string;
    buyerId: string;
    commissionAmount: number;
    commissionRate: number;
    commissionType: string;
    createTime: string;
    enterBeginTime: string;
    enterEndTime: string;
    enterTime: string;
    goodsName: string;
    id: string;
    orderId: string;
    proName: string;
    proType: number;
    productPrice: number;
    providerId: string;
    refundTime: string;
    rightsId: string;
    rightsName: string;
    saleChannel: number;
    scenicId: string;
    scenicName: string;
    serviceCharge: number;
    status: string;
    type: number;
  }

  interface TicketInfoItem {
    day: string;
    identity: string;
    name: string;
    productName: string;
    productType: string;
    refundAmount: number;
    ticketNumber: string;
    ticketType: string;
  }

  interface OrderTicketInfo {
    buyerId: string;
    buyerName: string;
    checkId: string;
    createTime: string;
    enterBeginTime: string;
    enterDate: string;
    enterEndTime: string;
    enterWay: string;
    goodsName: string;
    goodsType: number;
    id: string;
    issueId: string;
    orderId: string;
    printStr: string;
    proName: string;
    proType: number;
    providerId: string;
    providerName: string;
    realPrice: string;
    retreatId: string;
    saleChannel: number;
    scenicId: string;
    scenicName: string;
    status: number;
    validityDay: string;
  }

  interface UserPageListItem {
    applyName: string;
    companyId: string;
    nickname: string;
    phone: string;
    status: number;
    userId: string;
    username: string;
  }

  interface DepartmentItem {
    companyId: string;
    deptId: string;
    name: string;
    parentDeptId: string;
  }

  interface RoleInfoListItem {
    roleId: string;
    roleName: string;
  }
  interface GuideInfo {
    stepList: any;
    guidanceContent: string;
    guideUpdateFlag: any;
  }
}

declare namespace Scenic {
  interface ScenicData {
    appInfo?: AppInfo;
    scenic?: ScenicInfo;
    scenicAddress?: ScenicAddress;
    scenicAttribute?: ScenicAttribute;
    scenicBusiness?: ScenicBusiness;
    scenicDate?: ScenicDate;
    scenicDesc?: ScenicDesc;
  }

  interface AppInfo {
    appBrief: string;
    applicationName: string;
    enterpriseId: string;
    imgUrl: string;
    loginType: number;
    logoutType: number;
    type: number;
    url: string;
  }

  interface ScenicInfo {
    contracts: string;
    contractsPhone: string;
    createTime: string;
    creatorId: string;
    email: string;
    grade: string;
    id: string;
    isEnable: string;
    modifyId: string;
    modifyTime: string;
    name: string;
    remark: string;
    scenicLogo: string;
    screenId: string;
    serviceStatus: number;
    settlement: string;
    type: string;
  }

  interface ScenicAddress {
    address: string;
    areaCode: string;
    areaName: string;
    cityCode: string;
    cityName: string;
    latitude: string;
    longitude: string;
    provinceCode: string;
    provinceName: string;
    scenicId: string;
    trafficLocationDes: string;
  }

  interface ScenicAttribute {
    appId: string;
    blockChainOrgStatus: number;
    entertainmentService: string;
    inherentService: string;
    isBlockChain: number;
    isOrderModify: string;
    isPticketRefund: string;
    modifyNum: string;
    scenicId: string;
    teamNum: string;
    uniqueIdentity: string;
  }

  interface ScenicBusiness {
    advanceBookDay: string;
    bookEndTime: string;
    bookStartTime: string;
    businessEndTime: string;
    businessStartTime: string;
    isForceRefund: string;
    isPlayRefund: string;
    isReceipt: string;
    picture: string;
    scenicId: string;
    sortPriority: string;
  }

  interface ScenicDate {
    endDate: string;
    startDate: string;
  }

  interface ScenicDesc {
    bookDesc: string;
    briefDesc: string;
    costDesc: string;
    detailDesc: string;
    playDesc: string;
    refundDesc: string;
    scenicId: string;
  }

  interface ControlListItem {
    controlConfId: string;
    isEnable: number;
    operateContent: string;
    operateMenu: string;
    operateType: string;
  }

  interface OperationLogItem {
    id: string;
    app: string;
    project: string;
    module: string | null;
    function: string;
    content: string;
    createTime: string;
    creator: string;
    nickname: string;
    deviceIp: string;
    contentDetails: string;
  }
}

declare namespace Ticket {
  interface SaleTicketItem {
    isChain: number;
    actualAmount: number;
    buyer: string;
    buyerId: string;
    checkType: string;
    checkedNum: number;
    commissionAmount: number;
    commissionRate: number;
    commissionType: string;
    createTime: string;
    enterBeginTime: string;
    enterEndTime: string;
    enterTime: string;
    goodsName: string;
    id: string;
    orderId: string;
    playerNum: number;
    proName: string;
    proType: number;
    productPrice: number;
    providerId: string;
    refundTime: string;
    rightsId: string;
    rightsName: string;
    saleChannel: number;
    scenicId: string;
    scenicName: string;
    serviceCharge: number;
    status: string;
    type: number;
    validityDay: number;
    isActivate: number;
    isRetreat: number;
    availableDays: number;
    useType: number;
    useCount: number;
    usedCount: number;
    isRetreatReason: string;
  }
  interface TicketInfo {
    buyerId: string;
    buyerName: string;
    checkId: string;
    checkedNum: number;
    createTime: string;
    enterBeginTime: string;
    enterDate: string;
    enterEndTime: string;
    enterWay: string;
    goodsName: string;
    goodsType: number;
    id: string;
    issueId: string;
    orderId: string;
    playerNum: number;
    printStr: string;
    proName: string;
    proType: number;
    providerId: string;
    providerName: string;
    realNameList: RealNameListItem[];
    realPrice: string;
    retreatId: string;
    saleChannel: number;
    scenicId: string;
    scenicName: string;
    status: number;
    validityDay: string;
  }

  interface RealNameListItem {
    idCardName: string;
    idCardNumber: string;
    isCheck: number;
    usedCount: number;
  }

  interface MultipleTicketInfoType {
    checkRetreatInfo: CheckRetreatInfo;
    id: string;
    realNameList: RealNameListItem[];
    status: number;
    usedCount: number;
  }

  interface RealNameListItem2 {
    idCardName: string;
    idCardNumber: string;
    ticketNumber?: string;
  }

  export interface AvailableCheckItem {
    realNameUseList: RealNameUseListItem[];
    ticketNumber: string;
    useList: UseListItem[];
    realNameList: RealNameListItem2[];
  }

  export interface RealNameUseListItem {
    idCardNumber: string;
    ticketNumber: string;
    useDate: string;
    usedCount: number;
  }

  export interface UseListItem {
    ticketNumber: string;
    useDate: string;
    usedCount: number;
  }

  interface CheckRetreatInfo {
    amount: number;
    isRetreat: number;
    orderId: string;
    rate: number;
    refundAmount: number;
    refundFee: number;
    rule: RuleItem[];
    ruleBook: RuleBookItem[];
    ticketId: string;
  }

  interface RuleItem {
    dayBeginHour: string;
    dayBeginTime: string;
    dayEndHour: string;
    dayEndTime: string;
    flag: number;
    id: string;
    rate: string;
  }

  interface RuleBookItem {
    dayBeginHour: string;
    dayBeginTime: string;
    dayEndHour: string;
    dayEndTime: string;
    id: string;
    rate: string;
  }
}

declare namespace Co {
  interface ServiceProviderItem {
    account: string;
    coCode: string;
    coId: string;
    coName: string;
    coPhone: string;
    coTypeList: any[];
    coUrl: string;
    contactName: string;
    contactPhone: string;
    isEnable: string;
    modifyTime: string;
    org: string;
    registerStatus: number;
    settlementName: string;
    settlementStatus: string;
  }
}
