/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-04-22 14:32:42
 * @LastEditTime: 2023-06-30 14:50:33
 * @LastEditors: zhangfengfei
 */

import { tableConfig } from "@/common/utils/config";
import { getHashParams } from "@/common/utils/tool";
import useModal from "@/hooks/useModal";
import { getTravelCardDownList } from "@/services/api/travelCard";
import { getStockList } from "@/services/api/travelCardStock";
import { PlusOutlined } from "@ant-design/icons";
import type { ActionType, ProColumns } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import { Button } from "antd";
import { trim } from "lodash";
import type { FC } from "react";
import { useEffect, useRef, useState } from "react";
import { Access, useAccess, useModel } from "@umijs/max";
import StockModal from "./StockModal";

type TravelCardStockProps = Record<string, never>;

const TravelCardStock: FC<TravelCardStockProps> = () => {
  const { initialState } = useModel("@@initialState");

  const { scenicId } = initialState!.scenicInfo!;
  const { coId = "" } = initialState?.currentCompanyInfo || {};

  const actionRef = useRef<ActionType>();
  const [stockItem, setStockItem] = useState<API.StockListItem>();

  const access = useAccess();
  const queryParams = getHashParams();

  // model 状态
  const stockModal = useModal();

  const columns: ProColumns<API.StockListItem>[] = [
    {
      title: "库存批次号",
      dataIndex: "id",
      search: {
        transform: val => trim(val)
      }
    },

    {
      title: "权益卡名称",
      dataIndex: "ticketName",
      search: {
        transform: value => ({
          ticketId: trim(value)
        })
      },
      fieldProps: {
        showSearch: true
      },
      request: async () => {
        const { data = [] } = await getTravelCardDownList({
          scenicId,
          operatorIds: [coId]
        });

        return data.map(({ travelCardId, travelCardName }) => ({
          label: travelCardName,
          value: travelCardId
        }));
      }
    },
    {
      title: "购买有效时间",
      valueType: "dateRange",
      dataIndex: "purchaseEndTime",
      search: {
        transform: v => ({
          purchaseBeginTime: v[0],
          purchaseEndTime: v[1]
        })
      },
      hideInTable: true
    },
    {
      title: "购买有效时间",
      search: false,
      render: (_, { purchaseBeginTime, purchaseEndTime }) => [purchaseBeginTime, purchaseEndTime].join(" 至 ")
    },

    {
      title: "总库存量",
      dataIndex: "totalStock",
      search: false,
      align: "right",
      valueType: "digit"
    },
    {
      title: "剩余库存量",
      dataIndex: "residualInventory",
      align: "right",
      valueType: "digit",
      search: {
        transform: val => trim(val)
      }
    },
    {
      width: "auto",
      title: "操作",
      valueType: "option",
      fixed: "right",
      render: (_: any, entity: any) => (
        <a
          onClick={() => {
            stockModal.setTypeWithVisible("info");
            setStockItem(entity);
          }}
        >
          查看
        </a>
      )
    }
  ];

  const onClickAdd = () => {
    stockModal.setTypeWithVisible("add");
    setStockItem(undefined);
  };

  useEffect(() => {
    if (queryParams?.type == "add") {
      onClickAdd();
    }
  }, [queryParams]);

  return (
    <>
      <ProTable<API.StockListItem, API.StockListParams>
        {...tableConfig}
        actionRef={actionRef}
        rowKey="id"
        toolBarRender={() => [
          <Access key="primary" accessible={access.canTravelCardStorageManage_insert}>
            <Button
              type="primary"
              onClick={() => {
                onClickAdd();
              }}
            >
              <PlusOutlined /> 新增
            </Button>
          </Access>
        ]}
        pagination={{ defaultPageSize: 10 }}
        params={{ scenicId, operatorId: [coId], selectType: 1 }}
        request={async params => {
          try {
            const { data } = await getStockList(params);
            return data;
          } catch (error) {
            return {
              data: [],
              total: 0
            };
          }
        }}
        columns={columns}
      />

      {/* 库存 modal */}
      <StockModal stockItem={stockItem} actionRef={actionRef} {...stockModal} />
    </>
  );
};

export default TravelCardStock;
