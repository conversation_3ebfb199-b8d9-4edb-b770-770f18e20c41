import styles from "./CompanyInfoModal.less";

import DetailsPop from "@/common/components/DetailsPop";
import { DocumentTypeSelect, ExpirationDateTypeEnum } from "@/common/utils/enum";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import { getEnv } from "@/common/utils/getEnv";
import { coSyncAuth, getCompanyInfo } from "@/services/api/erp";
import { Alert, Image, Modal, Space, message } from "antd";
import type { FC } from "react";
import { useEffect, useState } from "react";
import { useRequest } from "@umijs/max";

interface CompanyInfoModalProps {
  visible: boolean;
  setVisible: (visible: boolean) => void;
  currentRow?: any;
}

const CompanyInfoModal: FC<CompanyInfoModalProps> = ({ visible, setVisible, currentRow }) => {
  const [associationMV, setAssociationMV] = useState(false); //关联并更新的弹窗

  // 企业信息
  const getCompanyInfoReq = useRequest(getCompanyInfo, {
    manual: true,
    onSuccess(data, params) {
      addOperationLogRequest({
        action: "info",
        content: `查看企业【${currentRow?.coName}】详情`
      });
    }
  });

  const columns: any[] = [
    {
      title: "",
      columns: [
        {
          title: "",
          dataIndex: "settlementStatus",
          render: (text, entity: any) => {
            /**
             *  0: '未认证',1: '已认证',2: '已拒绝',3: '认证中',4 : '认证失败'
             */
            let message: React.ReactNode = "";
            if (text == 0) {
              message = "完善信息后可提交企业认证审核！";
            } else if (text == 3) {
              message = "企业认证审核中！";
            } else if (text == 1) {
              message = "企业认证审核通过！";
              //企业在“已认证”状态下可进行企业信息修改的方案
              const updateStatue = entity?.companyUpdateStatus;
              if (updateStatue == "WAITING") {
                message = "企业信息修改审核中！";
              } else if (updateStatue == "REFUSE") {
                const updateComment = entity?.updateComment;
                message = `企业信息修改审核不通过${
                  updateComment ? `，具体原因：${updateComment}` : ""
                }，请修改后重新提交！`;
              } else if (updateStatue == "PASS") {
                message = "企业信息修改审核通过！";
              }
            } else if (text == 2) {
              const authComment = entity?.authComment;
              message = `企业认证审核不通过${authComment ? `，具体原因：${authComment}` : ""}，请修改后重新提交！`;
            } else if (text == 4) {
              message = (
                <>
                  认证失败，已存在同一认证企业请审核相关信息后进行结算账户关联！{" "}
                  <a
                    onClick={() => {
                      setAssociationMV(true);
                    }}
                  >
                    关联并更新
                  </a>
                </>
              );
            }
            return <Alert message={message} type="success" />;
          }
        }
      ]
    },
    {
      title: "基础信息",
      columns: [
        {
          title: "企业名称",
          dataIndex: "coName"
        },
        {
          title: "统一社会信用代码",
          dataIndex: "coCode"
        },
        {
          title: "企业所在地（省市区）",
          dataIndex: "province",
          render(_, { coAreaName, coProvinceName, coCityName }) {
            return `${coProvinceName}${coCityName}${coAreaName}` || "-";
          }
        },
        {
          title: "详细地址",
          dataIndex: "coAddressInfo"
        },
        {
          title: "营业执照有效期",
          render(_, { licenseExpirationDateType, businessLicenseBeginDate, businessLicenseEndDate }) {
            if (licenseExpirationDateType == ExpirationDateTypeEnum[0].value) {
              return ExpirationDateTypeEnum[0].label;
            } else {
              return `${businessLicenseBeginDate} 至 ${businessLicenseEndDate}`;
            }
          }
        },
        {
          title: "营业执照上传",
          dataIndex: "businessLicenseImageUrl",
          render: dom => <Image width={100} src={`${getEnv().IMG_HOST}${dom}`} />
        }
      ]
    },
    {
      title: "法人信息",
      columns: [
        {
          title: "法人名称",
          dataIndex: "legalRepresentativeName"
        },
        {
          title: "证件类型",
          dataIndex: "documentType",
          valueEnum: DocumentTypeSelect
        },
        {
          title: "证件号码",
          dataIndex: "documentNumber"
        },
        {
          title: "证件有效期",
          render(_, { documentExpirationDateType, documentBeginDate, documentEndDate }) {
            if (documentExpirationDateType == ExpirationDateTypeEnum[0].value) {
              return ExpirationDateTypeEnum[0].label;
            } else {
              return `${documentBeginDate} 至 ${documentEndDate}`;
            }
          }
        },
        {
          title: "证件正反面上传",
          dataIndex: "documentImageUrl",
          render: (dom: any) => (
            <Space>
              {dom.split(",").map((s: string) => (
                <Image width={100} src={`${getEnv().IMG_HOST}${s}`} />
              ))}
            </Space>
          )
        }
      ]
    },
    {
      title: "收款信息",
      columns: [
        {
          title: "商户账号",
          dataIndex: "settlementName"
        },
        {
          title: "开户名称",
          dataIndex: ["coBankAccount", "accountName"]
        },
        {
          title: "开户银行",
          dataIndex: ["coBankAccount", "bankName"]
        },
        {
          title: "银行账号",
          dataIndex: ["coBankAccount", "account"]
        }
      ]
    },
    {
      title: "区块链账号信息",
      columns: [
        {
          title: "区块链组织信息",
          dataIndex: "org"
        },
        {
          title: "区块链钱包地址",
          dataIndex: "account"
        }
      ]
    },
    {
      title: "联系信息",
      columns: [
        {
          title: "联系人",
          dataIndex: "contactName"
        },
        {
          title: "联系手机",
          dataIndex: "contactPhone"
        }
      ]
    }
  ];

  useEffect(() => {
    if (currentRow && visible) {
      getCompanyInfoReq.run(currentRow.coId);
    }

    return () => {
      getCompanyInfoReq.cancel();
    };
  }, [visible, currentRow]);

  return (
    <>
      <DetailsPop
        title="企业详情"
        visible={visible}
        className={styles.form}
        setVisible={setVisible}
        columnsInitial={columns}
        dataSource={getCompanyInfoReq.data}
        isLoading={getCompanyInfoReq.loading}
      />
      <Modal
        title="确认进行结算账户关联吗?"
        open={associationMV}
        onOk={() => {
          coSyncAuth(currentRow?.coCode ?? ""); //不用理会结果,直接关闭弹窗
          setAssociationMV(false);
          setVisible(false);
          message.info("企业关联成功！");
        }}
        onCancel={() => {
          setAssociationMV(false);
        }}
        zIndex={1001}
      >
        <p>请进行企业信息审核后慎重操作</p>
      </Modal>
    </>
  );
};

export default CompanyInfoModal;
