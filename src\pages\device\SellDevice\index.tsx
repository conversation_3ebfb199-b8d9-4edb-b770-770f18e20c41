import { InfoCircleTwoTone, PlusOutlined } from "@ant-design/icons";
import type { ActionType, ProColumns } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import { Button, Col, Modal, Row, Space, Switch, Tooltip, message } from "antd";
import React, { useEffect, useRef, useState } from "react";
// api
import {
  AddTicketEquipment,
  addEquipmentCarousel,
  addEquipmentPublicize,
  delTicketEquipment,
  getEquipmentCarousel,
  getEquipmentPublicize,
  getTicketEquipmentList,
  getTicketEquipments,
  getTicketOfficeList,
  setTicketEquipmentStatus
} from "@/services/api/device";
// 组件
import DetailsPop from "@/common/components/DetailsPop";
import EditPop from "@/common/components/EditPop";
import { tableConfig } from "@/common/utils/config";
import { enableEnum, SaleTicketType } from "@/common/utils/enum";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import { getUniqueId } from "@/common/utils/tool";
import UploadFile from "@/components/UploadFile";
import type { ProFormColumnsType } from "@ant-design/pro-components";
import { ProFormDigit, ProFormGroup, ProFormItem, ProFormList, ProFormText } from "@ant-design/pro-components";
import { toString } from "lodash";
import { Access, useAccess, useModel, useRequest } from "@umijs/max";
import styles from "./index.less";

const TableList: React.FC = () => {
  const access = useAccess();
  // 【景区】信息
  const { initialState } = useModel("@@initialState");
  const { scenicId, scenicName }: any = initialState?.scenicInfo;

  // 【表格】数据绑定
  const actionRef = useRef<ActionType>();
  // 【列表】数据绑定
  const [detailsVisible, setDetailsVisible] = useState<boolean>(false);
  const [isLoading, setLoading] = useState<boolean>(true);
  const [dataSource, setDataSource] = useState<any>({ id: "", isEnable: 0 });
  const columns: ProColumns[] = [
    {
      title: "售票设备编号",
      dataIndex: "id",
      hideInSearch: true,
      fixed: "left"
    },
    {
      title: "售票设备名称",
      fixed: "left",
      dataIndex: "name"
    },
    {
      title: "所属售票点",
      dataIndex: "officeName",
      hideInSearch: true
    },
    {
      title: "售票设备类型",
      dataIndex: "type",
      valueEnum: SaleTicketType
    },
    {
      title: "MAC 地址",
      dataIndex: "mac",
      hideInSearch: true
    },
    {
      title: "IP 地址",
      dataIndex: "ip",
      hideInSearch: true
    },
    {
      title: "启用状态",
      dataIndex: "isEnable",
      fixed: "right",
      valueEnum: enableEnum,
      renderText: (dom: any, entity: any) => (
        <Switch
          disabled={!access.canSalesDevice_openClose}
          checkedChildren="启用"
          unCheckedChildren="禁用"
          checked={!!dom}
          onChange={() => {
            setTicketEquipmentStatus({
              equipmentId: entity.id,
              isEnable: 1 - entity.isEnable,
              scenicId
            })
              .then(() => {
                // 添加日志
                addOperationLogRequest({
                  action: "disable",
                  content: `${dom == 1 ? "禁用" : "启用"}【${entity.name}】售票设备`
                });

                message.success(dom ? "已禁用" : "已启用");
                actionRef.current?.reload();
              })
              .catch(() => {});
          }}
        />
      )
    },
    {
      title: "操作",
      valueType: "option",
      fixed: "right",
      render: (_: any, record: any) => (
        <Space size="large">
          <a
            onClick={async () => {
              setLoading(true);
              const [device, carousel, publicize] = await Promise.all([
                getTicketEquipments({ id: record.id }),
                getEquipmentCarousel({
                  ticketEquipmentId: record.id
                }),
                getEquipmentPublicize({
                  ticketEquipmentId: record.id
                })
              ]);

              // 添加日志
              addOperationLogRequest({
                action: "info",
                content: `查看【${record.name}】售票设备详情`
              });

              setDataSource({
                ...device,
                carousel: carousel?.data?.data,
                publicizeUrl: (publicize?.data?.data || []).map(i => i?.publicizeUrl).join(",")
              });
              setDetailsVisible(true);

              setLoading(false);
            }}
          >
            查看
          </a>
          <Access accessible={access.canSalesDevice_edit}>
            <a
              onClick={async () => {
                const [device, carousel, publicize] = await Promise.all([
                  getTicketEquipments({ id: record.id }),
                  getEquipmentCarousel({
                    ticketEquipmentId: record.id
                  }),
                  getEquipmentPublicize({
                    ticketEquipmentId: record.id
                  })
                ]);

                setDataSource({
                  ...device,
                  carousel: carousel?.data?.data,
                  publicizeUrl: (publicize?.data?.data || []).map(i => i?.publicizeUrl).join(",")
                });
                setEditVisible(true);
              }}
            >
              编辑
            </a>
          </Access>
          <Access accessible={access.canSalesDevice_edit}>
            <a
              style={{ color: "red" }}
              onClick={async () => {
                if (record.isEnable) {
                  Modal.warning({
                    title: "不可删除",
                    content: "请先禁用后删除"
                  });
                  return;
                }
                Modal.confirm({
                  title: "确认删除吗？",
                  icon: <InfoCircleTwoTone />,
                  content: "删除后不可恢复",
                  okText: "确认",
                  cancelText: "取消",
                  onOk: () => {
                    delTicketEquipment(record.id)
                      .then(() => {
                        message.success("删除成功");
                        // 添加日志
                        addOperationLogRequest({
                          action: "del",
                          content: `删除【${record.name}】售票设备`
                        });
                        actionRef.current?.reload();
                      })
                      .catch(() => {});
                  }
                });
              }}
            >
              删除
            </a>
          </Access>
        </Space>
      )
    }
  ];

  // 【表单】数据绑定
  const [editVisible, setEditVisible] = useState<boolean>(false);

  const {
    data: ticketOfficeEnum,
    run,
    loading
  } = useRequest(getTicketOfficeList, {
    manual: true,
    initialData: {},
    formatResult(res) {
      console.log(res);
      const valueEnum = {};
      res?.data?.forEach(item => (valueEnum[item.id] = item.name));
      return valueEnum;
    }
  });

  const editColumns: ProFormColumnsType<any>[] = [
    {
      title: "基础信息",
      columns: [
        {
          title: "所属景区",
          dataIndex: "scenicId",
          valueType: "select",
          valueEnum: { [scenicId]: scenicName },
          initialValue: scenicId,
          fieldProps: { disabled: true }
        },
        {
          title: "IP 地址",
          dataIndex: "ip",
          formItemProps: { rules: [{ max: 32 }] }
        },
        {
          title: "所属售票点",
          dataIndex: "officeId",
          valueType: "select",
          valueEnum: ticketOfficeEnum,
          fieldProps: {
            allowClear: false,
            showSearch: true,
            loading
          },
          formItemProps: { rules: [{ required: true }] }
        },
        {
          title: "MAC 地址",
          dataIndex: "mac",
          formItemProps: { rules: [{ max: 48 }] }
        },
        {
          title: "售票设备类型",
          dataIndex: "type",
          valueType: "select",
          convertValue: val => toString(val),
          initialValue: "2",
          valueEnum: SaleTicketType,
          formItemProps: { rules: [{ required: true }] }
        },
        {
          title: "售票设备名称",
          dataIndex: "name",
          formItemProps: { rules: [{ required: true, max: 30 }] }
        }
      ]
    },
    {
      valueType: "dependency",
      fieldProps: {
        name: ["type"]
      },
      columns({ type }) {
        return type == "1"
          ? [
              {
                title: "自助售票宣传信息",
                valueType: "group",
                columns: [
                  {
                    title: "顶部轮播",
                    colProps: {
                      span: 24
                    },

                    renderFormItem(schema, config, form, action) {
                      return (
                        <ProFormList
                          name="carousel"
                          className={styles.formList}
                          colProps={{ span: 20 }}
                          min={1}
                          max={10}
                          initialValue={[
                            {
                              skipUrl: undefined,
                              imgUrl: undefined
                            }
                          ]}
                          copyIconProps={false}
                          creatorButtonProps={{
                            position: "bottom",
                            creatorButtonText: "添加"
                          }}
                        >
                          {(meta, index, action, count) => {
                            const currentRowData = action.getCurrentRowData();

                            return (
                              <ProFormGroup key={currentRowData.id || meta.key} style={{ alignItems: "center" }}>
                                <ProFormDigit
                                  width={80}
                                  colProps={{ xs: 7 }}
                                  addonBefore="序号"
                                  addonAfter="--"
                                  name="sort"
                                  rules={[{ required: true }]}
                                />
                                <ProFormText
                                  addonBefore="链接"
                                  colProps={{ xs: 13 }}
                                  width="sm"
                                  name="skipUrl"
                                  placeholder={"请输入跳转链接"}
                                  rules={[{ required: true }]}
                                />
                                <ProFormItem name="imgUrl">
                                  <UploadFile
                                    defaultValue={currentRowData.imgUrl || ""}
                                    listType="picture-card"
                                    maxCount={1}
                                    size={5}
                                    accept=".png,.jpg"
                                  />
                                </ProFormItem>
                              </ProFormGroup>
                            );
                          }}
                        </ProFormList>
                      );
                    }
                  },
                  {
                    title: "底部宣传",
                    colProps: {
                      span: 24
                    },
                    key: "publicizeUrl",
                    renderFormItem(schema, config, form, action) {
                      return (
                        <div>
                          <ProFormItem name={"publicizeUrl"}>
                            <UploadFile
                              accept=".mp4,.jpg,.png"
                              listType="picture"
                              defaultValue={dataSource.publicizeUrl || ""}
                              maxCount={1}
                              size={50}
                            />
                          </ProFormItem>
                          <div>请上传视频或图片，支持扩展名：.mp4 .jpg .png ...</div>
                        </div>
                      );
                    }
                  }
                ]
              }
            ]
          : [];
      }
    },

    {
      title: "说明信息",
      columns: [
        {
          title: "售票设备描述",
          dataIndex: "remark",
          valueType: "textarea",
          colProps: { span: 24 },
          fieldProps: {
            maxLength: 1000,
            showCount: true
          },
          formItemProps: { rules: [{ max: 1000 }] }
        }
      ]
    }
  ];

  const columnsInitial = [
    {
      title: "基础信息",
      columns: [
        {
          title: "所属景区",
          dataIndex: "scenicName"
        },
        {
          title: "IP 地址",
          dataIndex: "ip"
        },
        {
          title: "所属售票点",
          dataIndex: "officeName"
        },
        {
          title: "MAC 地址",
          dataIndex: "mac"
        },
        {
          title: "售票设备名称",
          dataIndex: "name"
        }
      ]
    },
    {
      title: "自助售票宣传信息",
      column: 1,

      columns: [
        {
          title: "顶部轮播",
          dataIndex: "carousel",
          column: 1,

          renderText: (text: any[] = []) => {
            return (
              <Row wrap className={styles.formList}>
                {text.map(i => {
                  return (
                    <Col key={i.id} span={24}>
                      <Space align="center" size={12}>
                        <div style={{ marginTop: -6 }}>序号 {i.sort}</div>
                        <Tooltip title={i.skipUrl}>
                          <span className="text-overflow" style={{ width: 400 }}>
                            链接 {i.skipUrl}
                          </span>
                        </Tooltip>
                        <UploadFile defaultValue={i.imgUrl} listType="picture-card" readonly />
                      </Space>
                    </Col>
                  );
                })}
              </Row>
            );
          }
        },
        {
          title: "底部宣传",
          dataIndex: "publicizeUrl",
          column: 1,

          renderText: val => {
            return <UploadFile listType="picture" defaultValue={val || ""} readonly />;
          }
        }
      ]
    },
    {
      title: "说明信息",
      columns: [
        {
          title: "备注",
          dataIndex: "remark"
        }
      ]
    }
  ];

  const logList = [...editColumns[0].columns];

  useEffect(() => {
    run({
      scenicId
    });
  }, [scenicId]);

  return (
    <>
      <ProTable<API.RuleListItem, API.PageParams>
        {...tableConfig}
        rowClassName={(row: any) => (row.isEnable == 1 ? "" : "disableRow")}
        actionRef={actionRef}
        toolBarRender={() => [
          <Access key={getUniqueId()} accessible={access.canSalesDevice_insert}>
            <Button
              type="primary"
              key="primary"
              onClick={() => {
                setDataSource({ id: "", isEnable: 0 });
                setEditVisible(true);
              }}
            >
              <PlusOutlined /> 新增
            </Button>
          </Access>
        ]}
        params={{ scenicId }}
        request={getTicketEquipmentList}
        columns={columns}
      />

      {/* 新增编辑 */}
      <EditPop
        title="售票设备"
        visible={editVisible}
        setVisible={setEditVisible}
        columns={editColumns}
        dataSource={dataSource}
        // 新增/编辑
        onFinish={async (val: any) => {
          console.log(val);
          if (dataSource.id) val.id = dataSource.id;
          val.scenicName = scenicName;
          const msgType = val.id ? "编辑" : "新增";
          const hide = message.loading("正在" + msgType);
          try {
            // 网络请求
            const { data } = await AddTicketEquipment({ ...val, remark: val.remark || " " });

            if (val.id) {
              addOperationLogRequest({
                action: "edit",
                changeConfig: {
                  list: logList,
                  beforeData: dataSource,
                  afterData: val
                },
                content: `编辑【${val.name}】售票设备`
              });
            } else {
              addOperationLogRequest({
                action: "add",
                content: `新增【${val.name}】售票设备`
              });
            }

            const { carousel = [], publicizeUrl = "" } = val;

            // 售票设备轮播图
            await addEquipmentCarousel({
              ticketEquipmentId: data,
              data: carousel
            });
            //  新增售票设备宣传信息
            await addEquipmentPublicize({
              ticketEquipmentId: data,
              data: [
                {
                  publicizeUrl
                }
              ]
            });

            message.success(msgType + "成功");
            // 关闭弹窗并刷新列表
            setEditVisible(false);
            actionRef?.current?.reload();
          } catch (error) {}
          hide();
        }}
      />

      {/* 详情 */}
      <DetailsPop
        title="售票设备详情"
        visible={detailsVisible}
        isLoading={isLoading}
        setVisible={setDetailsVisible}
        columnsInitial={columnsInitial}
        dataSource={dataSource}
      />
    </>
  );
};

export default TableList;
