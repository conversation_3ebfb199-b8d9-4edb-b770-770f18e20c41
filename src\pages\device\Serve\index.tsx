import Province from "@/common/components/Province";
import { tableConfig } from "@/common/utils/config";
import { getUniqueId } from "@/common/utils/tool";
import {
  apiTouristsAddUpdate,
  apiTouristsDelete,
  apiTouristsDetails,
  apiTouristsEnable,
  apiTouristsPageList
} from "@/services/api/device";
import { PlusOutlined } from "@ant-design/icons";
import ProForm, { ModalForm, ProFormSelect, ProFormText } from "@ant-design/pro-form";
import type { ActionType, ProColumns } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import { Button, Modal, Tag, message } from "antd";
import { useEffect, useRef, useState } from "react";
import { Access, useAccess, useModel } from "@umijs/max";
import Details from "./Details";

const { confirm } = Modal;

//声明百度地图实例
const BMap = window.BMap;
let map: any;
let oldMarker: any;
let points: any;
let myGeo: any;

const Serve = (props: any) => {
  // const formRef = useRef();
  const access = useAccess();
  const formObj = useRef();
  const [formObj2] = ProForm.useForm();
  const actionRef = useRef<ActionType>();
  const [modalVisit, setModalVisit] = useState(false);
  // console.log(selectedOptions)
  //获取当前 ID
  const [Id, setId] = useState(undefined);
  // 【表单】数据绑定
  const [editVisible, setEditVisible] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<any>({ id: "", isEnable: 0 });
  // 获取景区 ID
  const { initialState }: any = useModel("@@initialState");
  const scenicId = initialState?.scenicInfo?.scenicId;
  console.log(scenicId, initialState);
  const [flag, setflag] = useState(false);

  //储存省市区的 id
  const [areaId, setAreaId] = useState("");
  const [cityId, setCityId] = useState("");
  const [provinceId, setProvinceId] = useState("");
  //获取景区名称
  const scenicName = initialState?.scenicInfo?.scenicName;
  // let provinces;
  const ProvincesArray = [];
  //收集省市区 id
  const [provincesId, setProvincesId] = useState([]);
  const [provincesArray, setProvincesArray] = useState([]);

  const [initialValues, setInitialValues] = useState({});

  const typeEnum = {
    "0": "咨询服务点",
    "1": "游客休息区",
    "2": "特殊人群服务设施点",
    "3": "便民服务点"
  };

  // 详情
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [detailData, setDetailsData] = useState([]);
  // //获取当前 id
  // const [isId,setIsId]=useState()

  const showModal = async (val: any) => {
    const id = val.id;
    const result: any = await apiTouristsDetails(id);
    const { data } = result;
    console.log("yyds", data, val);
    //回选
    // formObj?.current?.setFieldsValue(data);
    setDetailsData(data);
    setInitialValues(data);
    // setDataSource(data);
    setId(val.id);
    // setIsEnableStatus(data.isEnable);
    // setModalVisit(true);
    setIsModalVisible(true);
  };

  const handleOk = () => {
    setIsModalVisible(false);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  //编辑
  const updateMethod = async (val: any) => {
    console.log("aaaaaaaaaaaaaaaaaaa", val);
    const id = val.id;
    const reslut: any = await apiTouristsDetails(id);
    console.log(reslut.data);
    const { data } = reslut;
    if (data.type == 0) {
      data.type = "咨询服务点";
    } else if (data.type == 1) {
      data.type = "游客休息区";
    } else if (data.type == 2) {
      data.type = "特殊人群服务设施点";
    } else if (data.type == 3) {
      data.type = "便民服务点";
    }
    data.province = [data.provinceName, data.cityName, data.areaName];

    // formObj2.setFieldsValue(data);
    // setProvincesArray([data.provinceName, data.cityName, data.areaName]);
    // setProvincesId([provinceId, cityId, cityId]);'
    // formObj?.current?.setFieldsValue(data);
    console.log("yyyyyyyyyyyyyyy", data);
    // setInitialValues(data);
    formObj2.setFieldsValue(data);
    setAreaId(data.areaId);
    setCityId(data.cityId);
    setProvinceId(data.provinceId);
    setId(val.id);
    setIsModalVisible(false);
    setModalVisit(true);
  };
  // //选择省市区的方法
  // const provincesMeothd = (val: any) => {
  //   setProvincesArray(val);
  // };
  //启用/禁用
  const onStatus = async (val: any) => {
    console.log(val.isEnable);
    confirm({
      title: `您确定${val.isEnable == 1 ? "禁用" : "启用"}吗？`,
      // content: 'Some descriptions',
      onOk: async () => {
        try {
          const result = await apiTouristsEnable(val.id);
          message.success(val.isEnable == 0 ? "启用成功" : "禁用成功");
          console.log(val);
          // getNoticeList()
          // 刷新
          actionRef?.current?.reload();
          setIsModalVisible(false);
        } catch (e) {
          console.error(e);
        }
      },
      onCancel() {
        console.log("Cancel");
      }
    });
  };
  //删除
  // const onDelete = () => { }
  const onDelete = (id: any) => {
    confirm({
      title: `您确定删除吗？`,
      // content: 'Some descriptions',
      onOk: async () => {
        try {
          const result = await apiTouristsDelete(id);
          // console.log(result);
          message.success("删除成功");
          // 刷新
          actionRef?.current?.reload();
          setIsModalVisible(false);
        } catch (e) {
          console.log(e);
        }
      },
      onCancel() {
        console.log("Cancel");
      }
    });
  };

  //获取列表
  const getTouristsPageList = async (params: any) => {
    const { data } = await apiTouristsPageList(params);
    return data;
  };

  // 保存数据
  const submitData = async (val: any) => {
    console.log("123131321321231", val);
    if (val.id) {
      console.log("jjjjjjjjjjjjjjjjjjjjjjjjjjjj");
      if (typeof val.province[0] !== "string") {
        //省
        val.provinceName = val.province[0].addressName;
        val.provinceId = val.province[0].addressId;
        //市
        val.cityName = val.province[1].addressName;
        val.cityId = val.province[1].addressId;
        //区
        val.areaName = val.province[2].addressName;
        val.areaId = val.province[2].addressId;
      } else {
        console.log("123132132hujiajiajiaiajiajaijaiajai");
        //省
        val.provinceName = val.province[0];
        val.provinceId = provinceId;
        //市
        val.cityName = val.province[1];
        val.cityId = cityId;
        //区
        val.areaName = val.province[2];
        val.areaId = areaId;
      }
    } else {
      val.provinceName = val.province[0].addressName;
      val.provinceId = val.province[0].addressId;
      //市
      val.cityName = val.province[1].addressName;
      val.cityId = val.province[1].addressId;
      //区
      val.areaName = val.province[2].addressName;
      val.areaId = val.province[2].addressId;
    }
    const pras = {
      ...val,
      scenicId
      // isEnable: 1,
    };
    console.log("=========edit===============");

    console.log(pras.type, pras);

    if (pras.type == "咨询服务点") {
      pras.type = "0";
    } else if (pras.type == "游客休息区") {
      pras.type = "1";
    } else if (pras.type == "特殊人群服务设施点") {
      pras.type = "2";
    } else if (pras.type == "便民服务点") {
      pras.type = "3";
    }

    try {
      const result = await apiTouristsAddUpdate(pras);
      // message.success('保存成功');
      if (val.hasOwnProperty("id")) {
        formObj2.resetFields();
      }
      message.success(val.hasOwnProperty("id") ? "编辑成功" : "新增成功");

      actionRef?.current?.reload();
      setEditVisible(false);
      setModalVisit(false);
    } catch (e) {
      console.error(e);
    }
  };

  useEffect(() => {
    map = new BMap.Map("l-map");
    myGeo = new BMap.Geocoder();
    // map.centerAndZoom(new BMap.Point(116.404, 39.915), 11);
    // map = new BMap.Map('map-container');
    // points = new BMap.Point(initialValues?.longitude, initialValues?.latitude);
    // let marker = new BMap.Marker(points);   // 创建标注
    // map.centerAndZoom(points, 18); // 初始化地图
    // map.enableScrollWheelZoom(true); // 开启鼠标滚轮缩放
    // map.addOverlay(marker);                     // 将标注新增到地图中
  }, []);

  // 将地址解析结果显示在地图上，并调整地图视野

  const [acceptorData, setAcceptorData] = useState({});

  const geoProvince = ({ province, address }: any) => {
    // map.centerAndZoom(new BMapGL.Point(116.331398,39.897445), 13);

    myGeo.getPoint(
      address,
      (point: any) => {
        // setAccetorData(point)
        if (point) {
          console.log("wwww", point);
          getPoint(point);
        } else {
          message.warn("您选择的地址没有解析到结果，请手动填写经纬度");
          // getPoint(point)
          console.log("iiiiiiiiiiiiiiiiiii");
        }
      },
      province[0].addressName
    );
  };

  //计算经纬度
  const setPoint = e => {
    geoProvince({
      province: formObj2.getFieldValue("province"),
      address: formObj2.getFieldValue("address")
    });
  };
  /*
   *省市区回调
   * */
  const getPoint = ({ lat, lng }) => {
    console.log("yyyyyyyyyyyyyyyyy", lat, lng);
    //设置经纬度
    formObj2.setFieldsValue({
      latitude: lat,
      longitude: lng
    });
  };

  const columns: ProColumns<any>[] = [
    {
      title: "所属景区",
      dataIndex: "scenicName",
      hideInSearch: true
    },
    {
      title: "游客服务项目名称",
      dataIndex: "touristsName",

      render: (dom, record) => {
        return <a onClick={() => showModal(record)}>{dom}</a>;
      }
    },

    {
      title: "所属类型",
      dataIndex: "type",
      valueEnum: {
        "0": "咨询服务点",
        "1": "游客休息区",
        "2": "特殊人群服务设施点",
        "3": "便民服务点"
      },

      valueType: "select"
    },
    {
      title: "地址",
      dataIndex: "address",
      hideInSearch: true,
      ellipsis: true
    },
    {
      title: "管理单位",
      dataIndex: "managementUnit",
      hideInSearch: true
    },
    {
      title: "联系人",
      dataIndex: "contacts",
      hideInSearch: true
    },
    {
      title: "联系方式",
      dataIndex: "contactMobile",
      hideInSearch: true
    },
    {
      title: "状态",
      dataIndex: "isEnable",
      hideInSearch: true,
      valueEnum: {
        0: "禁用",
        1: "启用"
      },
      render: (dom: any, record: any) => {
        return <Tag color={record.isEnable == 1 ? "blue" : "red"}>{record.isEnable == 1 ? "已启用" : "已禁用"}</Tag>;
      }
    }
    // {
    //   title: '操作',
    //   dataIndex: 'option',
    //   width: '10%',
    //   hideInSearch: true,
    //   render: (dom, record) => {
    //     return (
    //       <Space>
    //         <a onClick={() => updateMethod(record)}>编辑</a>
    //       </Space>
    //     );
    //   },
    // },
  ];
  const editColumns = [
    {
      columns: [
        {
          title: "所属景区",
          dataIndex: "scenicName",
          name: "scenicName",
          initialValue: `${scenicName}`,
          key: "scenicName",
          // valueEnum: {
          //   '0': '公告',
          // },
          formItemProps: {
            // rules: [{ required: true }],
            // disable: false
          },
          fieldProps: {
            disabled: true
          }
        },
        {
          dataIndex: "managementUnit",
          title: "管理单位",
          name: "managementUnit",
          key: "managementUnit",
          fieldProps: {},
          formItemProps: {
            rules: [{ required: true }, { max: 100, message: "最多可输入 100 个字符" }]
          }
        },
        {
          dataIndex: "touristsName",
          title: "游戏服务项目名称",
          ellipsis: true,
          name: "touristsName",
          key: "touristsName",
          fieldProps: {},
          formItemProps: {
            rules: [{ required: true }, { max: 100, message: "最多可输入 100 个字符" }]
          }
        },
        {
          dataIndex: "type",
          title: "类型",
          name: "type",
          valueType: "select",
          valueEnum: {
            "0": "咨询服务点",
            "1": "游客休息区",
            "2": "特殊人群服务设施点",
            "3": "便民服务点"
          },

          key: "type",
          fieldProps: {},
          formItemProps: {
            rules: [{ required: true }]
          }
        },
        {
          dataIndex: "contacts",
          title: "联系人",
          name: "contacts",
          key: "contacts",
          fieldProps: {},
          formItemProps: {
            rules: [{ required: true }, { max: 100, message: "最多可输入 100 个字符" }]
          }
        },
        {
          dataIndex: "contactMobile",
          title: "联系人电话",
          name: "contactMobile",
          key: "contactMobile",
          fieldProps: {},
          formItemProps: {
            rules: [
              { required: true },
              {
                pattern: /^1(3\d|4[5-9]|5[0-35-9]|6[567]|7[0-8]|8\d|9[0-35-9])\d{8}$/,
                message: "请输入格式不正确"
              }
            ]
          }
        },
        {
          title: "省市区",
          dataIndex: "province",
          name: "province",
          key: "province",
          renderFormItem: () => {
            return <Province width={328} />;
          },
          formItemProps: {
            rules: [{ required: true }]
          },
          valueType: "select",
          fieldProps: {
            // mode: 'multiple',
            // options: positionValue2 == 1 ? scenicData2 : companyData2,
            // onChange: (value, option) => {
            //   console.log(value);
            //   setAcceptorData(value);
            // },
            // showArrow: true,
            // disabled: flag ? false : show ? false : true,
          }
        },
        {
          dataIndex: "address",
          title: "详细地址",
          name: "address",
          key: "address",
          fieldProps: {},
          formItemProps: {
            rules: [{ required: true }, { max: 100, message: "最多可输入 100 个字符" }]
          }
        },

        {
          dataIndex: "longitude",
          title: "经度",
          tooltip: '"-" 为西经，"+" 为东经',
          name: "longitude",
          key: "longitude",
          fieldProps: {},
          formItemProps: {
            rules: [
              {
                pattern:
                  /^(\-|\+)?(((\d|[1-9]\d|1[0-7]\d|0{1,3})\.\d{0,6})|(\d|[1-9]\d|1[0-7]\d|0{1,3})|180\.0{0,6}|180)$/,
                message: '(范围："-180°~ +180°",保留 6 位小数)'
              }
            ]
          }
        },
        {
          dataIndex: "latitude",
          title: "纬度",
          tooltip: '"+"为北纬，"-"为南纬',
          name: "latitude",
          key: "latitude",
          fieldProps: {},
          formItemProps: {
            rules: [
              {
                pattern: /^(\-|\+)?([0-8]?\d{1}\.\d{0,6}|90\.0{0,6}|[0-8]?\d{1}|90)$/,
                message: '( 范围："-90°~+90°",保留 6 位小数)'
              }
            ]
          }
        }
      ]
    }
  ];

  return (
    <>
      {/* 详情 */}
      <Modal
        title="游客服务项目详情"
        open={isModalVisible}
        width={800}
        onOk={handleOk}
        onCancel={handleCancel}
        footer={[
          <Access key={getUniqueId()} accessible={access.canProjectService_openClose}>
            <Button
              type="primary"
              ghost
              danger={detailData.isEnable == 1 ? true : false}
              key="isEnable"
              onClick={() => onStatus(detailData)}
            >
              {detailData.isEnable == 1 ? "禁用" : "启用"}
            </Button>
          </Access>,
          <Access key={getUniqueId()} accessible={access.canProjectService_delete}>
            <Button key="del" type="primary" danger onClick={() => onDelete(Id)}>
              删除
            </Button>
          </Access>,
          <Access key={getUniqueId()} accessible={access.canProjectService_edit}>
            <Button key="update" type="primary" onClick={() => updateMethod(detailData)}>
              编辑
            </Button>
          </Access>,
          <Button key="cel" onClick={handleCancel}>
            取消
          </Button>
        ]}
      >
        <Details detailData={detailData} />
      </Modal>
      {/* 编辑 */}

      <ModalForm
        title="游客项目服务"
        open={modalVisit}
        // initialValues={initialValues}
        form={formObj2}
        formRef={formObj}
        onFinish={async val => {
          if (Id) {
            val.id = Id;
          }
          // val.isEnable = isEnableStatus
          console.log("编辑", val);
          submitData(val);
        }}
        onVisibleChange={val => {
          setModalVisit(val);
          if (!val) {
            setInitialValues({});
          }
        }}
      >
        <ProForm.Group>
          <ProFormText width="md" name="scenicName" label="所属景区" initialValue={scenicName} disabled={true} />
          <ProFormText
            width="md"
            name="managementUnit"
            label="管理单位"
            placeholder="请输入管理单位"
            rules={[{ required: true, max: 40 }]}
          />
        </ProForm.Group>

        <ProForm.Group>
          <ProFormText
            width="md"
            name="touristsName"
            label="游客服务项目名称"
            placeholder="请输入项目名称"
            rules={[{ required: true, max: 30 }]}
          />
          <ProFormSelect
            width="md"
            name="type"
            label="类型"
            valueEnum={typeEnum}
            placeholder="请输入类型"
            rules={[{ required: true, message: "请输入类型" }]}
          />
        </ProForm.Group>
        <ProForm.Group>
          <ProFormText
            width="md"
            name="contacts"
            label="联系人"
            placeholder="请输入联系人"
            rules={[{ required: true, max: 20 }]}
          />

          <ProFormText
            width="md"
            name="contactMobile"
            label="联系方式"
            placeholder="请输入联系方式"
            rules={[
              { required: true, message: "请输入联系方式" },
              {
                pattern: /^\d+(-\d+)*$/,
                message: "不合法的手机号"
              }
            ]}
          />
        </ProForm.Group>

        <ProForm.Group>
          {/* <ProFormText
            width="md"
            name="provinceName"
            label="省市区"
            placeholder="请输入点位名称"
            rules={[{ required: true, message: '请输入点位名称' }]}
          /> */}
          <ProForm.Item label="省市区" name="province" rules={[{ required: true, message: "请输入省市区" }]}>
            <Province width={328} />
          </ProForm.Item>

          <ProFormText
            width="md"
            name="address"
            label="详细地址"
            placeholder="请输入详细地址"
            fieldProps={{ onChange: e => setPoint(e) }}
            rules={[{ required: true, max: 200 }]}
          />
        </ProForm.Group>
        <ProForm.Group>
          <ProFormText
            width="md"
            name="longitude"
            label="经度"
            tooltip='"-" 为西经,"+" 为东经'
            placeholder="请输入经度"
            rules={[
              {
                pattern:
                  /^(\-|\+)?(((\d|[1-9]\d|1[0-7]\d|0{1,3})\.\d{0,6})|(\d|[1-9]\d|1[0-7]\d|0{1,3})|180\.0{0,6}|180)$/,
                message: '(范围："-180°~ +180°",保留6位小数)'
              }
            ]}
          />
          <ProFormText
            width="md"
            name="latitude"
            label="纬度"
            tooltip='"+"为北纬，"-"为南纬'
            placeholder="请输入纬度"
            rules={[
              {
                pattern: /^(\-|\+)?([0-8]?\d{1}\.\d{0,6}|90\.0{0,6}|[0-8]?\d{1}|90)$/,
                message: '( 范围："-90°~+90°",保留6位小数)'
              }
            ]}
          />
        </ProForm.Group>
      </ModalForm>

      {/* 新增编辑 */}
      {/* <EditPop
        title="游客服务项目"
        visible={editVisible}
        setVisible={setEditVisible}
        columns={editColumns}
        dataSource={dataSource}
        // 新增/编辑
        onFinish={(val: any) => {
          submitData(val);
          console.log(val);
        }}
      /> */}
      <>
        <ProTable
          // headerTitle={'查询表格'}
          {...tableConfig}
          actionRef={actionRef}
          rowKey="id"
          toolBarRender={() => [
            <Access key={getUniqueId()} accessible={access.canProjectService_insert}>
              <Button
                type="primary"
                key="primary"
                onClick={() => {
                  // handleOptionId('');
                  // handleModalVisible(true);
                  // setEditVisible(true);
                  // modalVisit
                  // setId(undefined)
                  formObj2.resetFields();
                  setModalVisit(true);
                }}
              >
                <PlusOutlined /> 新增
              </Button>
            </Access>
          ]}
          request={getTouristsPageList}
          columns={columns}
          params={{ scenicId }}
        />
      </>
    </>
  );
};
export default Serve;
