import { apiExportHeaderSet } from '@/services/api/config';
import type { ProTableProps } from '@ant-design/pro-table';
import { getUniqueId } from './tool';

export const tableConfig: ProTableProps<any, any> = {
  tableLayout: 'auto',
  search: { labelWidth: 'auto' },
  scroll: { x: 'auto' },
  columnEmptyText: '--',
  rowKey: () => getUniqueId(),
};

export function columnsState(exportState: any) {
  return {
    value: exportState.columnsValue,
    onChange: (v: any) => {
      let sort = 0;
      const headerInfos: any = [];
      for (const value of exportState.columns) {
        if (!value.hideInTable) {
          const obj = {
            ...{
              show: true,
              order: sort++,
              fixed: value.fixed,
            },
            ...v[value.dataIndex],
          };
          headerInfos.push({
            fieldDescribe: value.title,
            headerField: value.dataIndex,
            sort: obj.order,
            state: obj.show ? 1 : 0,
            fixed: obj.fixed,
          });
        }
      }
      apiExportHeaderSet({
        headerInfos,
        modulePath:
          exportState.uri +
          (exportState.params.moduleFlag ? `/export_${exportState.params.moduleFlag}` : '/export'),
      });
      exportState.setColumnsValue(v);
    },
  };
}
