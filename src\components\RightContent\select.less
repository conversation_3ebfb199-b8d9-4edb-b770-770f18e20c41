#select_box {
  position: absolute;
  top: 0;
  right: 220px;
  display: flex;
  align-items: center;
  height: 48px;

  .select_label {
    color: #fff;
  }

  .select_content {
    min-width: 140px;

    .ant-select-selector {
      color: #fff;
      background-color: #001529;
      border: none;
    }

    .loading {
      pointer-events: none;
    }

    .ant-select-suffix {
      color: #fff !important;
      // color: rgba(0, 0, 0, 0.25) !important;
    }

    .anticon-close-circle {
      color: #fff !important;
      // color: rgba(0, 0, 0, 0.25) !important;
    }

    /* 自定义滚动条 */
    ::-webkit-scrollbar {
      width: 8px;
    }

    // ::-webkit-scrollbar-track {
    //   border-radius: 4px;
    //   background: #f1f1f1;
    // }

    ::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
      border-radius: 4px;
    }
  }
}
