/**
 * 设置 token
 * */
function setToken(token: string) {
  if (token) {
    localStorage.setItem('erq_token', token);
  } else {
    console.error('token设置失败', token);
  }
}
/**
 * 获取 token
 * @return 可用于请求的token值
 * */
function getToken(): string {
  const token = localStorage.getItem('erq_token');
  return token ? 'bearer ' + token : '';
}
/**
 * 移除 token
 * */
function removeToken() {
  if (getToken()) {
    localStorage.removeItem('erq_token');
  }
}

export { getToken, setToken, removeToken };
