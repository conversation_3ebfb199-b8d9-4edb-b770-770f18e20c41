import { jumpPage } from '@/common/utils/tool';
import { apiCustomDownload, apiCustomHeaderGet, apiCustomHeaderSet } from '@/services/api/config';
import { DownloadOutlined } from '@ant-design/icons';
import { Button, Flex, Modal } from 'antd';
import { useEffect, useState } from 'react';

const getHeaderInfos = (columns: any, v: any = {}) => {
  let sort = 0;
  const headerInfos: any = [];
  for (const value of columns) {
    if (!value.hideInTable) {
      const obj = {
        ...{
          show: true,
          order: sort++,
          fixed: value.fixed,
        },
        ...v[value.dataIndex],
      };
      headerInfos.push({
        fieldDescribe: value.title,
        headerField: value.dataIndex,
        sort: obj.order,
        state: obj.show ? 1 : 0,
        fixed: obj.fixed,
      });
    }
  }
  return headerInfos;
};

export default (props: any) => {
  const [orderOpen, setOrderOpen] = useState(false);
  const [downOpen, setDownOpen] = useState(false);
  const downOrder = (param = {}) => {
    apiCustomDownload({
      code: props.modulePath,
      param: { ...param, ...props.params },
    }).then(() => {
      setDownOpen(true);
    });
  };

  useEffect(() => {
    apiCustomHeaderGet({
      modulePath: props.modulePath,
    }).then((res) => {
      let modify = false;
      const headerInfos = getHeaderInfos(props.columns);
      const headerInfosLatest = res?.data?.headerInfos || [];
      for (const k in headerInfosLatest) {
        if (
          headerInfosLatest[k].fieldDescribe != headerInfos[k].fieldDescribe ||
          headerInfosLatest[k].headerField != headerInfos[k].headerField
        ) {
          modify = true;
          break;
        }
      }
      if (res?.data?.headerInfos?.length && !modify) {
        const obj = {};
        for (const v of headerInfosLatest) {
          obj[v.headerField] = {
            show: !!v.state,
            order: v.sort,
            fixed: v.fixed,
          };
        }
        props.setColumnsValue(obj);
      } else {
        apiCustomHeaderSet({
          headerInfos,
          modulePath: props.modulePath,
        });
      }
    });
  }, []);
  return (
    <>
      <Button type="primary" icon={<DownloadOutlined />} onClick={() => setOrderOpen(true)}>
        导出
      </Button>
      <Modal
        title="订单导出"
        open={orderOpen}
        okText="导出当前订单列表"
        cancelText="导出全部订单列表"
        onOk={() => {
          setOrderOpen(false);
          downOrder(props.formRef.current?.getFieldsFormatValue());
        }}
        onCancel={(e) => {
          setOrderOpen(false);
          if (e.target.nodeName == 'SPAN') {
            downOrder();
          }
        }}
      >
        <Flex vertical>
          <b>为了给您提供更好的查询性能及体验，我们对导出功能进行了改造：</b>
          <span>
            1、若需要根据订单筛选条件导出当前订单列表数据，可点击【导出当前订单列表】；若需要导出全部订单数据，可点击【导出全部订单】；
          </span>
          <span>2、为了保证您的导出性能，导出数据需要在【下载中心】获取；</span>
          <span>3、为了保证您的导出性能，导出数据量多时请保证导出时间在5分钟以上。</span>
        </Flex>
      </Modal>
      <Modal
        title="操作成功"
        open={downOpen}
        okText="下载中心"
        cancelText="关闭"
        onOk={() => {
          jumpPage.push('/system/down');
        }}
        onCancel={(e) => {
          setDownOpen(false);
        }}
      >
        <Flex vertical>
          <b>导出操作成功，请到【下载中心】中查看导出数据。</b>
        </Flex>
      </Modal>
    </>
  );
};

export function columnsSet(props: any) {
  return {
    value: props.columnsValue,
    onChange: (v: any) => {
      apiCustomHeaderSet({
        headerInfos: getHeaderInfos(props.columns, v),
        modulePath: props.modulePath,
      });
      props.setColumnsValue(v);
    },
  };
}
