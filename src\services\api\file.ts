/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-07-05 17:53:53
 * @LastEditTime: 2023-04-21 15:50:42
 * @LastEditors: zhangfeng<PERSON>i
 */

import { request } from "@umijs/max";
import { scenicHost } from ".";
import { getEnv } from "@/common/utils/getEnv";

interface FileDataItem {
  size: number;
  path: string;
  name: string;
  type: string;
  mtime: string;
}
// 上传图片或者文件 支持多选
export async function uploadFile(files: File[]) {
  const { UPLOAD_HOST } = getEnv();
  const formData = new FormData();
  for (const file of files) {
    formData.append("file", file);
    console.log(file);
  }

  return request<FileDataItem[]>(`${UPLOAD_HOST}`, {
    method: "POST",
    requestType: "form",
    data: formData,
    skipErrorHandler: true
  });
}
// 上传文件
export async function uploadFileNew(file: File) {
  const formData = new FormData();
  formData.append("file", file);
  return request<ResponseData<FileDataItem>>(scenicHost + "/aws/uploadFile", {
    method: "POST",
    requestType: "form",
    data: formData
  });
}

/**
 * @description: 导出权益商品票的销售账单
 * @return {*}
 */
export function downloadRightsGoodsSaleBill(id: string) {
  return request<Blob>(`${scenicHost}/bill/export/billRightsGoodsTicketSale/${id}`, {
    skipErrorHandler: true,
    responseType: "blob",
    method: "GET"
  });
}
