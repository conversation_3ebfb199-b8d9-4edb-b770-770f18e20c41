import { GuideStepStatus } from '@/common/utils/enum';
import { getGuideInfo, saveGuideInfo } from '@/common/utils/storage';
import { getScenicIdentifier } from '@/common/utils/tool';
import { useGuide } from '@/hooks/useGuide';
import { getShopGoodsPageList } from '@/services/api/article';
import { CheckOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { Button, Card, Divider } from 'antd';
import { useEffect, useState } from 'react';
import styles from '../index.module.scss';
import { STEP_ITEMS, UrlEnum } from './datas';

const NewbieTask = () => {
  const { initialState } = useModel('@@initialState');
  const { fetchGuideInfo, updateGuideInfo } = useGuide();
  const [finishStep, setFinishStep] = useState(0);
  const [StepItems, setStepsItems] = useState(STEP_ITEMS);
  const [btnLoading, setBtnLoading] = useState<any>(null);

  // 跳过当前步骤
  const onSkipStep = (item: any) => {
    setBtnLoading(`${item?.guidanceContent}Skip`);
    updateGuideInfo({ tabIndex: 1, status: item?.guidanceContent }).finally(() => {
      setBtnLoading(null);
    });
  };

  // 跳转到对应的步骤
  const onLink = (item: any) => {
    const guideSteps = getGuideInfo();
    const _guideInfo = { ...guideSteps, tabIndex: 1 };
    saveGuideInfo(_guideInfo);
    const urlItem = UrlEnum[item.key];
    if (urlItem?.type === 'full') {
      window.open(urlItem?.url, '_blank');
    } else {
      window.open(
        `${location.origin}${window.location.pathname}#/${getScenicIdentifier()}${urlItem?.url}`,
        '_blank',
      );
    }
  };

  // 获取操作栏内容
  const getOperate = (item: any) => {
    const { key, isUsed } = item;
    if (isUsed) {
      return (
        <Button type="link" onClick={() => onLink(item)}>
          去优化
        </Button>
      );
    }
    switch (key) {
      case 1:
        return (
          <>
            <Button
              type="link"
              className={styles.skipBtn}
              loading={btnLoading == `${item?.guidanceContent}Skip`}
              onClick={() => onSkipStep(item)}
            >
              暂不需要
            </Button>
            <Divider type="vertical" />
            <Button type="link" onClick={() => onLink(item)}>
              立即创建
            </Button>
          </>
        );
      case 2:
        return (
          <>
            <Button
              type="link"
              className={styles.skipBtn}
              loading={btnLoading == `${item?.guidanceContent}Skip`}
              onClick={() => onSkipStep(item)}
            >
              暂不需要
            </Button>
            <Divider type="vertical" />
            <Button type="link" onClick={() => onLink(item)}>
              立即创建
            </Button>
          </>
        );
      case 3:
        return (
          <>
            <Button type="link" onClick={() => onLink(item)}>
              立即配置
            </Button>
          </>
        );
      case 4:
        return (
          <>
            <Button type="link" onClick={() => onLink(item)}>
              立即配置
            </Button>
          </>
        );
      case 5:
        return (
          <>
            <Button type="link" onClick={() => onLink(item)}>
              立即创建
            </Button>
          </>
        );
      case 6:
        return (
          <>
            <Button type="link" onClick={() => onLink(item)}>
              立即创建
            </Button>
          </>
        );
      default:
        return (
          <Button type="link" onClick={() => onLink(item)}>
            去优化
          </Button>
        );
    }
  };

  // 获取对应状态步骤的icon
  const getStepIcon = (item: any, index) => {
    if (item.isUsed) {
      return <CheckOutlined />;
    } else {
      return <span className={styles.wait}>{index + 1}</span>;
    }
  };

  // 4:官网商品管理
  const getGoodsFn = async () => {
    getShopGoodsPageList({
      current: 1,
      pageSize: 10,
      scenicId: initialState?.scenicInfo?.scenicId,
      officialRecommend: 2,
    }).then((res) => {
      if (res?.data?.data?.length) {
        onSkipStep({ guidanceContent: GuideStepStatus.step1_4 });
      }
    });
  };

  useEffect(() => {
    const stepList = getGuideInfo()?.stepList || [];
    if (Array.isArray(stepList)) {
      const _StepItems = STEP_ITEMS.map((item) => {
        const _steps = stepList.find((e) => e.guidanceContent === item.label);
        const _newItem = {
          ...item,
          ..._steps,
        };
        UrlEnum[_newItem.key].isUsed = _newItem.isUsed;
        return _newItem;
      });
      setStepsItems(_StepItems);
      const _finishStep = (_StepItems || []).filter((e) => e?.isUsed)?.length;
      setFinishStep(_finishStep);
      /****************** 判断节点是否已完成 *******************/
      if (stepList?.length) {
        // 4:官网商品管理
        if (!UrlEnum[4]?.isUsed) {
          getGoodsFn();
        }
      }
    }
  }, [initialState?.guideUpdateFlag]);

  useEffect(() => {
    fetchGuideInfo();
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        fetchGuideInfo();
      }
    };
    document.addEventListener('visibilitychange', handleVisibilityChange);
    // 清理函数，移除事件监听器
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  return (
    <div>
      <p>
        已完成 {finishStep} 项任务，共 8
        项。全部完成后即可解锁强大的票务管理、数据分析和营销推广的能力
      </p>

      {StepItems?.map((item, index) => {
        return (
          <Card key={item.key} className={styles.card}>
            <div className={styles.container}>
              <span className={styles.stepIcon}>{getStepIcon(item, index)}</span>
              <div className={styles.content}>
                <h1>{item.label}</h1>
                <div>{item.desc}</div>
              </div>
              <div className={styles.operate}>{getOperate(item)}</div>
            </div>
          </Card>
        );
      })}
    </div>
  );
};

export default NewbieTask;
