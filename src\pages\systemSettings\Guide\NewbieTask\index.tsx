import { GuideStepStatus } from '@/common/utils/enum';
import { getCompanyInfoStore, getGuideInfo, saveGuideInfo } from '@/common/utils/storage';
import { getScenicIdentifier, getUniqueId } from '@/common/utils/tool';
import { useGuide } from '@/hooks/useGuide';
import { apiCoList } from '@/services/api/erp';
import { CheckOutlined, CloseOutlined, SketchOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { Button, Card, Collapse, Divider } from 'antd';
import { useEffect, useState } from 'react';
import styles from '../index.module.scss';
import { STEP_ITEMS, UrlEnum } from './datas';

interface Props {
  type?: 'default' | 'collapse';
  onClose?: () => void;
}

const NewbieTask = ({ type = 'default', onClose }: Props) => {
  const { initialState, setInitialState } = useModel('@@initialState');
  const { fetchGuideInfo, updateGuideInfo } = useGuide();
  const [activeKey, setActiveKey] = useState(0);
  const [finishStep, setFinishStep] = useState(0);
  const [StepItems, setStepsItems] = useState(STEP_ITEMS);
  const [collapseItems, setCollapseItems] = useState(STEP_ITEMS);
  const [btnLoading, setBtnLoading] = useState<any>(null);

  // 跳过当前步骤
  const onSkipStep = (item: any) => {
    setBtnLoading(`${item?.guidanceContent}Skip`);
    updateGuideInfo({ tabIndex: 0, status: item?.guidanceContent }).finally(() => {
      setBtnLoading(null);
    });
  };

  // 跳转到对应的步骤
  const onLink = (item: any) => {
    const guideSteps = getGuideInfo();
    const _guideInfo = { ...guideSteps, tabIndex: 0 };
    saveGuideInfo(_guideInfo);
    const urlItem = UrlEnum[item.key];
    if (urlItem?.type === 'full') {
      window.open(urlItem?.url, '_blank');
    } else {
      window.open(
        `${location.origin}${window.location.pathname}#/${getScenicIdentifier()}${urlItem?.url}`,
        '_blank',
      );
    }
  };

  // 获取操作栏内容
  const getOperate = (item: any, btnType = null) => {
    const { key, isUsed, guidanceContent } = item;
    if (isUsed && guidanceContent != GuideStepStatus.step0_6) {
      return (
        <Button type={btnType || 'link'} onClick={() => onLink(item)}>
          去优化
        </Button>
      );
    }
    switch (key) {
      case 1:
        return (
          <Button type={btnType || 'link'} onClick={() => onLink(item)}>
            立即认证
          </Button>
        );
      case 2:
        return (
          <>
            <Button type={btnType || 'link'} onClick={() => onLink(item)}>
              立即创建
            </Button>
          </>
        );
      case 3:
        return (
          <Button type={btnType || 'link'} onClick={() => onLink(item)}>
            立即创建
          </Button>
        );
      case 4:
        return (
          <>
            <Button
              type={'link'}
              className={styles.skipBtn}
              loading={btnLoading == `${item?.guidanceContent}Skip`}
              onClick={() => onSkipStep(item)}
            >
              暂不需要
            </Button>
            <Divider type="vertical" />
            <Button type={btnType || 'link'} onClick={() => onLink(item)}>
              立即创建
            </Button>
          </>
        );
      case 5:
        return (
          <>
            <Button
              type={'link'}
              className={styles.skipBtn}
              loading={btnLoading == `${item?.guidanceContent}Skip`}
              onClick={() => onSkipStep(item)}
            >
              暂不需要
            </Button>
            <Divider type="vertical" />
            <Button type={btnType || 'link'} onClick={() => onLink(item)}>
              立即创建
            </Button>
          </>
        );
      case 6:
        return (
          <Button type={btnType || 'link'} onClick={() => onLink(item)}>
            前往易旅通
          </Button>
        );
      default:
        return (
          <Button type={btnType || 'link'} onClick={() => onLink(item)}>
            去优化
          </Button>
        );
    }
  };

  // 获取对应状态步骤的icon
  const getStepIcon = (item: any) => {
    if (item.key === 6) {
      return (
        <SketchOutlined style={{ fontSize: 18 }} className={!item.isUsed ? styles.wait : ''} />
      );
    }
    if (item.isUsed) {
      return <CheckOutlined />;
    } else {
      return <span className={styles.wait}>{item.key}</span>;
    }
  };

  const onActiveKeyChange = (key) => {
    setActiveKey(key);
  };

  // 1:完成企业认证
  const getCompanyFn = async () => {
    const _currentCompany = getCompanyInfoStore();
    apiCoList({ current: 1, pageSize: 10, coName: _currentCompany?.coName }).then((res) => {
      if (
        res?.data?.length &&
        ['1', '2', '3', '4'].includes(String(res?.data?.[0]?.settlementStatus))
      ) {
        onSkipStep({ guidanceContent: GuideStepStatus.step0_1 });
      }
    });
  };

  useEffect(() => {
    const stepList = getGuideInfo()?.stepList || [];
    if (Array.isArray(stepList)) {
      const _StepItems = [];
      const _collapseItems = STEP_ITEMS.map((item) => {
        const _steps = stepList?.filter((e) => e?.guidanceContent === item.label)?.[0] || {};
        const _newItem = {
          ...item,
          ..._steps,
        };
        _StepItems.push(_newItem);
        UrlEnum[_newItem.key].isUsed = _newItem.isUsed;

        return {
          ..._newItem,
          label: (
            <span className={styles.stepTitle}>
              {getStepIcon(_newItem)}
              {_newItem.label}
            </span>
          ),
          children: (
            <div>
              <p>{_newItem.desc}</p>
              <div className={styles.operate}>{getOperate(_newItem, 'primary')}</div>
            </div>
          ),
        };
      });
      setStepsItems(_StepItems);
      setCollapseItems(_collapseItems);
      const _finishStep = (_StepItems || []).filter((e) => e?.isUsed)?.length;
      setFinishStep(_finishStep);

      /****************** 判断节点是否已完成 *******************/
      if (stepList?.length) {
        // 1:完成企业认证
        if (!UrlEnum[1]?.isUsed) {
          getCompanyFn();
        }
        // 6:销售渠道搭建
        const _isAllFinish =
          _StepItems.filter((e) => e?.isUsed && e?.guidanceContent != GuideStepStatus.step0_6)
            ?.length == 5;
        const step0_6 = stepList.filter((e) => e.guidanceContent === GuideStepStatus.step0_6)?.[0];
        if (_isAllFinish && !step0_6?.isUsed) {
          onSkipStep({ guidanceContent: GuideStepStatus.step0_6 });
        }
      }
    }
  }, [initialState?.guideUpdateFlag]);

  useEffect(() => {
    fetchGuideInfo();
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        fetchGuideInfo();
      }
    };
    document.addEventListener('visibilitychange', handleVisibilityChange);
    // 清理函数，移除事件监听器
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  return (
    <>
      {type === 'default' ? (
        <div>
          <p>
            已完成 {finishStep} 项任务，共 6
            项。全部完成后即可解锁强大的票务管理、数据分析和营销推广的能力
          </p>

          {StepItems?.map((item) => {
            return (
              <Card key={item.key} className={styles.card}>
                <div className={styles.container}>
                  <span className={styles.stepIcon}>{getStepIcon(item)}</span>
                  <div className={styles.content}>
                    <h1>{item.label}</h1>
                    <div>{item.desc}</div>
                  </div>
                  <div className={styles.operate}>{getOperate(item)}</div>
                </div>
              </Card>
            );
          })}
        </div>
      ) : (
        <div className={styles.stepsContent}>
          <CloseOutlined
            className={styles.close}
            onClick={() => {
              onClose?.();
              setInitialState((s) => ({ ...s, guideUpdateFlag: getUniqueId() }));
            }}
          />
          <span className={styles.desc1}>已完成{finishStep}项任务，共6项</span>
          <h1>新手指南</h1>
          <span className={styles.desc2}>
            全部完成后即可解锁强大的票务管理、数据分析和营销推广的能力
          </span>
          <Collapse
            activeKey={activeKey}
            onChange={onActiveKeyChange}
            accordion
            expandIconPosition={'end'}
            items={collapseItems}
            className={styles.collapse}
          />
        </div>
      )}
    </>
  );
};

export default NewbieTask;
