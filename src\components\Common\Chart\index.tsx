import React, { useRef, useEffect } from 'react';
import { getInstanceByDom, init } from 'echarts';

export interface IChart {
  options: any;
  loading: boolean;
}

const Chart: React.FC<IChart> = ({ options, loading }) => {
  const chartRef = useRef(null);
  let chartInstance: any = null;
  const renderChart = () => {
    try {
      const renderedInstance = getInstanceByDom(chartRef.current!);
      if (renderedInstance) {
        chartInstance = renderedInstance;
        chartInstance.clear();
      } else {
        chartInstance = init(chartRef.current!);
      }
      // 自执行方法给空集合赋最小刻度
      (() => {
        if (!options?.dataset?.source.length) return;
        for (const element of options.dataset.source) {
          for (const element2 of Object.values(element)) {
            if (typeof element2 == 'number' && element2 > 0) return;
          }
        }
        options.yAxis.min = 5;
      })();

      chartInstance.setOption(options);
      resizeHandler();
    } catch (err) {
      console.error(err);
    }
  };

  function resizeHandler() {
    chartInstance.resize();
  }

  // 页面初始化时，开始渲染图表
  useEffect(() => {
    if (!loading) renderChart();
  }, [loading]);

  // 监听窗口大小改变
  useEffect(() => {
    const dom: any = document.getElementById('chartBox');
    const observer = new ResizeObserver(() => resizeHandler());
    observer.observe(dom);
    return () => {
      observer.unobserve(dom);
      if (chartInstance) chartInstance.dispose();
    };
  }, []);
  return <div ref={chartRef} style={{ width: '100%', height: '100%' }} />;
};

export default Chart;
