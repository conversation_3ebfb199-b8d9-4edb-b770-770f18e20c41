/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-08-11 10:43:26
 * @LastEditTime: 2023-03-10 16:47:38
 * @LastEditors: zhang<PERSON><PERSON>i
 */

import DetailsPop from "@/common/components/DetailsPop";
import { BooleanEnum, productTypeEnum, RightsTicketStatus, ticketTypeEnum, useTypeEnum } from "@/common/utils/enum";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import ImageUpload from "@/components/ImageUpload";
import type { ProDescriptionsGroup } from "@/components/ModalDescriptions";
import type { ModalState } from "@/hooks/useModal";
import { getTicketRightsInfo, updateRightsStatus } from "@/services/api/auditManage";
import type { ActionType } from "@ant-design/pro-table";
import type { ModalFuncProps } from "antd";
import { Input, message, Space } from "antd";
import { isNil } from "lodash";
import type { FC } from "react";
import { useEffect, useRef } from "react";
import { useAccess, useModel, useRequest } from "@umijs/max";
import CheckRuleDetails from "../../../ruleSet/Manage/components/CheckRuleDetails";
import RetreatRuleDetails from "../../../ruleSet/RefundTickets/components/RetreatRuleDetails";
import IssueRuleDetails from "../../../ruleSet/SellTickets/components/IssueRuleDetails";

type AuditModalProps = ModalState & {
  dataItem: API.TicketRightsListItem | undefined;
  actionRef: React.MutableRefObject<ActionType | undefined>;
  tabKey: React.Key;
};

const AuditModal: FC<AuditModalProps> = ({ visible, type, dataItem: rightsItem, tabKey, actionRef, setVisible }) => {
  const { initialState } = useModel("@@initialState");
  const { userId, username, phone } = initialState!.userInfo!;
  const access = useAccess();
  const reasonRef = useRef<string>("");

  // 审批驳回请求
  const updateRightsStatusReq = useRequest(updateRightsStatus, {
    manual: true,
    onSuccess(data, params) {
      message.success("操作成功");
      setVisible(false);
      actionRef.current?.reload();
    }
  });

  // 审核详情请求
  const {
    data: info,
    loading,
    run
  } = useRequest(getTicketRightsInfo, {
    manual: true,
    onSuccess(data, params) {
      addOperationLogRequest({
        action: "info",
        module: tabKey,
        content: `查看【${rightsItem?.goodsName}】权益票审核详情`
      });
    }
  });

  // 通过/驳回
  const handleApproval = async (action: "pass" | "reject") => {
    if (info?.approveId) {
      try {
        updateRightsStatusReq
          .run({
            reason: reasonRef.current,
            id: info.approveId,
            name: username,
            phone: phone,
            userId: info.createUserId,
            rightsStatus: action === "pass" ? RightsTicketStatus.审核通过 : RightsTicketStatus.审核失败
          })
          .then(() => {
            addOperationLogRequest({
              action: "audit",
              content: `${action === "pass" ? "通过" : "驳回"}【${info.goodsName}】审核`
            });
          });
      } catch (error) {}
      reasonRef.current = "";
    }
  };

  // 详情信息
  const infoList: ProDescriptionsGroup<API.TicketRightsInfo>[] = [
    {
      title: "基础信息",
      columns: [
        {
          title: "景区名称",
          dataIndex: ["product", "scenicName"]
        },
        {
          title: "产品类型",
          dataIndex: ["product", "proType"],
          valueEnum: productTypeEnum
        },
        {
          title: "所属服务商",
          dataIndex: ["product", "operatorName"]
        },
        {
          title: "所属产品名称",
          dataIndex: ["product", "name"]
        },
        {
          title: "C 端显示名称",
          dataIndex: ["product", "pcName"]
        },
        {
          title: "商品名称",
          dataIndex: "goodsName"
        },
        {
          title: "使用方式",
          dataIndex: "useType",
          valueEnum: useTypeEnum
        },
        {
          title: "票种",
          dataIndex: "type",
          valueEnum: ticketTypeEnum
        }
      ]
    },
    {
      title: "价格信息",
      columns: [
        {
          title: "特殊折扣率",
          dataIndex: "overallDiscount",
          renderText: (text: any, record) => {
            const { overallDiscount, marketPrice } = record;
            const discountPrice = marketPrice * (overallDiscount / 100);
            return (
              <Space direction="vertical">
                <span>{!isNil(overallDiscount) ? `${overallDiscount}%` : "-"}</span>
                <span>{`${marketPrice}*${overallDiscount}%=${discountPrice.toFixed(2)}`}</span>
              </Space>
            );
          }
        },
        {
          title: "分销折扣区间（%）",
          dataIndex: "beginDiscount",
          render: (text: any, record) => {
            const { beginDiscount, endDiscount, overallDiscount, marketPrice } = record;
            const discountPrice = marketPrice * (overallDiscount / 100);
            return (
              <div>
                <div>{!isNil(beginDiscount) ? `${beginDiscount}% ~ ${endDiscount}%` : "-"}</div>
                <div>
                  {`${((beginDiscount / 100) * discountPrice).toFixed(2)}
                     ~
                     ${((endDiscount / 100) * discountPrice).toFixed(2)}`}
                </div>
              </div>
            );
          }
        },
        {
          title: "市场标准价（元）",
          dataIndex: "marketPrice"
        }
      ]
    },
    {
      title: "票务属性信息",
      column: 1,
      columns: [
        {
          title: "是否按星期控制（设置该星期下，不可售该票）",
          dataIndex: ["product", "restrictType"],
          valueEnum: BooleanEnum
        },
        {
          title: "分时预约（入园开始时间（含）至 入园截止时间（不含））",
          dataIndex: ["product", "timeRestrict"],
          valueEnum: BooleanEnum
        },
        {
          title: "参与入园统计",
          dataIndex: ["product", "parkStatistic"],
          valueEnum: BooleanEnum
        }
      ]
    },
    {
      title: "其他信息",
      columns: [
        {
          title: "是否允许购买数量控制",
          dataIndex: "isPeopleNumber",
          valueType: "select",
          valueEnum: BooleanEnum
        },
        {
          title: "最小起订量",
          dataIndex: "minPeople"
        },
        {
          title: "单次最大预订量",
          dataIndex: "maxPeople"
        },
        {
          title: "使用有效期",
          dataIndex: "validityDay",
          renderText: text => <span>游玩日期起 {text ?? "-"} 天内有效</span>
        }
      ]
    },
    {
      title: "关联规则",
      columns: [
        {
          title: "出票规则",
          dataIndex: "issueName",
          renderText: (dom, entity) => (
            <a
              onClick={() => {
                IssueRuleDetails.show(entity.issueId);
              }}
            >
              {dom}
            </a>
          )
        },
        {
          title: "检票规则",
          dataIndex: "checkName",
          renderText: (dom, entity) => (
            <a
              onClick={() => {
                CheckRuleDetails.show(entity.checkId);
              }}
            >
              {dom}
            </a>
          )
        },
        {
          title: "退票规则",
          dataIndex: "retreatName",
          renderText: (dom, entity) => (
            <a
              onClick={() => {
                RetreatRuleDetails.show(entity.retreatId);
              }}
            >
              {dom}
            </a>
          )
        }
      ]
    },
    {
      title: "审核状态",
      columns: [
        {
          title: "",
          dataIndex: "rightsStatus",
          valueEnum: RightsTicketStatus
        }
      ]
    },
    {
      title: "说明信息",
      columns: [
        {
          title: "景区图片",
          dataIndex: ["product", "scenicPicUrl"],
          renderText: text => {
            return text ? <ImageUpload defaultValue={text} readonly /> : "-";
          }
        },
        {
          title: "入园须知",
          dataIndex: ["product", "notice"],
          renderText: text => (
            <div
              style={{
                maxHeight: 300,
                overflowY: "scroll"
              }}
            >
              {text}
            </div>
          )
        },
        {
          title: "备注",
          dataIndex: "remark"
        }
      ]
    }
  ];

  const passConfig = {
    width: 500,
    title: "点击确定，将同意该门票关联到该权益",
    content: <span style={{ color: "red" }}>该操作不可撤回，请谨慎填写</span>,
    onOk: () => {
      handleApproval("pass");
    },
    okButtonProps: {
      loading: updateRightsStatusReq.loading
    }
  };

  const rejectConfig: ModalFuncProps = {
    width: 500,
    title: "点击确定，将拒绝该门票关联到该权益",
    content: (
      <Space direction="vertical">
        <div style={{ color: "red" }}>该操作不可撤回，请谨慎填写</div>
        <div>请填写驳回原因：</div>
        <Input.TextArea
          style={{ height: 100, width: 350 }}
          showCount
          maxLength={1000}
          onChange={({ target: { value } }) => {
            /**
             * @description 有坑 不要用 setState 去保存值，modal.confirm 是函数实现
             * @see https://github.com/ant-design/ant-design/issues/29291#issuecomment-775869367
             */
            reasonRef.current = value;
          }}
        />
      </Space>
    ),
    onOk: () => {
      handleApproval("reject");
    },
    okButtonProps: {
      loading: updateRightsStatusReq.loading
    }
  };

  useEffect(() => {
    if (visible && type !== "add" && rightsItem) {
      run({ id: rightsItem.id });
    }
  }, [type, rightsItem?.id, visible]);

  return (
    <DetailsPop
      title="审核详情"
      visible={visible}
      isLoading={loading}
      setVisible={setVisible}
      dataSource={info}
      columnsInitial={infoList}
    />
  );
};

export default AuditModal;
