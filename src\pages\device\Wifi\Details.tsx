import React from "react";

import ProDescriptions from "@ant-design/pro-descriptions";
import { Access, useAccess, useModel } from "@umijs/max";
export default function Details(props) {
  const { detailData } = props;
  // 获取景区ID
  const { initialState }: any = useModel("@@initialState");
  const scenicId = initialState?.scenicInfo?.scenicId;
  console.log(scenicId, initialState);
  //获取景区名称
  const scenicName = initialState?.scenicInfo?.scenicName;
  console.log("景区名称", scenicName);
  const columns = [
    {
      title: "所属景区",
      dataIndex: "scenicName",
      name: "scenicName",
      initialValue: scenicName,
      key: "scenicName",
      // fieldProps: {
      //     disabled: true,
      // },
      render: () => {
        return scenicName;
      }
    },
    {
      title: "点位名称",
      dataIndex: "wifiName",
      name: "wifiName",
      // width: '5%',
      // ellipsis: true,
      // copyable: true,
      key: "wifiName"
      // formItemProps: {
      //   rules: [{ required: true }],
      // },
    },
    {
      title: "账号",
      dataIndex: "loginName",
      name: "loginName",
      key: "loginName",
      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      title: "密码",
      dataIndex: "loginPassword",
      name: "loginPassword",
      key: "loginPassword",
      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      title: "经度",
      dataIndex: "longitude",
      name: "longitude",
      key: "longitude",
      render: (dom, record) => {
        const str = record.longitude.charAt(0);
        if (str == "+" || str == "-") {
          if (str == "+") {
            return <span>{record.longitude.slice(1)}° E </span>;
          } else if (str == "-") {
            return <span>{record.longitude.slice(1)}° W</span>;
          }
        } else {
          return <span>{record.longitude}° E</span>;
        }
      },
      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      title: "纬度",
      dataIndex: "latitude",
      name: "latitude",
      key: "latitude",
      render: (dom, record) => {
        const str = record.latitude.charAt(0);
        if (str == "+" || str == "-") {
          if (str == "+") {
            return <span>{record.latitude.slice(1)}° N</span>;
          } else if (str == "-") {
            return <span>{record.latitude.slice(1)}° S</span>;
          }
        } else {
          return <span>{record.latitude}° N</span>;
        }
      },
      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      title: "品牌",
      dataIndex: "brand",
      name: "brand",
      // valueEnum: {
      //     '0': '1',
      //     "1": '2'
      // },
      key: "brand",
      formItemProps: {
        rules: [{ required: true }]
      },
      // valueType: 'select',
      fieldProps: {
        // mode: 'multiple',
        // options: positionValue2 == 1 ? scenicData2 : companyData2,
        // onChange: (value, option) => {
        //   console.log(value);
        //   setAcceptorData(value);
        // },
        // showArrow: true,
        // disabled: flag ? false : show ? false : true,
      }
    },
    {
      title: "链接地址",
      dataIndex: "linkAddress",
      name: "linkAddress",
      key: "linkAddress",
      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      title: "管理端口",
      dataIndex: "point",
      name: "point",
      key: "point"
    },
    {
      title: "通道号",
      dataIndex: "channel",
      name: "channel",
      key: "channel"
    }
  ];
  const data: any = [];
  return (
    <ProDescriptions
      title="基础信息"
      // title="高级定义列表request columns"
      //   request={async () => {}}
      dataSource={detailData}
      columns={columns}
      column={2}
    >
      {/* <ProDescriptions.Item dataIndex="percent" label="百分比" valueType="percent">
                100
            </ProDescriptions.Item> */}
    </ProDescriptions>
  );
}
