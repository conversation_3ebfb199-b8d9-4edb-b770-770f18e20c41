import { getCheckInPointDownList } from "@/services/api/device";
import {
  addTicket<PERSON>heck,
  delTicketCheck,
  getTicketCheckRulePageList,
  getTicketCheckStatus,
  TicketCheckXq
} from "@/services/api/ticket";
import { InfoCircleTwoTone, PlusOutlined } from "@ant-design/icons";
import type { ActionType, ProColumns } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import { Button, message, Modal, Space, Switch } from "antd";
import React, { useEffect, useRef, useState } from "react";

import DetailsPop from "@/common/components/DetailsPop";
import EditPop from "@/common/components/EditPop";
import { tableConfig } from "@/common/utils/config";
import { baseProductTypeEnum, CheckTypeEnum, enableEnum } from "@/common/utils/enum";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import { getAllPath, getUniqueId } from "@/common/utils/tool";
import { apiScenicConfig } from "@/services/api/settings";
import type { ProFormColumnsType } from "@ant-design/pro-components";
import { useRequest } from "@umijs/max";
import { trim } from "lodash";
import { Access, useAccess, useModel } from "@umijs/max";

const TableList: React.FC = () => {
  // 获取检票点
  const getCheckInPointReq = useRequest(getCheckInPointDownList, {
    manual: true,
    initialData: [],
    formatResult: res => {
      const _list: any = {};
      (res?.data || []).forEach(item => {
        _list[item.id] = item.checkName;
      });
      return _list;
    }
  });

  useEffect(() => {
    getIsFace();
  }, []);
  const access = useAccess();
  // 【景区】信息
  const { initialState } = useModel("@@initialState");
  const { scenicId, scenicName }: any = initialState?.scenicInfo;

  // 【表格】数据绑定
  const actionRef = useRef<ActionType>();
  const columns: ProColumns[] = [
    {
      title: "检票规则名称",
      dataIndex: "name",
      search: {
        transform: val => trim(val)
      }
    },
    {
      title: "产品类型",
      dataIndex: "proType",
      valueEnum: baseProductTypeEnum
    },
    {
      title: "检票控制方式",
      dataIndex: "controlType",
      hideInSearch: true,
      render: (dom: any) => "平台检票"
    },
    {
      title: "检票通行方式",
      dataIndex: "adoptType",
      valueEnum: { 0: "一检一人", 1: "一检多人" },
      renderText: (dom: any) => ["一检一人", "一检多人"][dom]
    },
    {
      title: "身份识别类型",
      dataIndex: "identityType",
      hideInSearch: true,
      render: (dom: any) => dom.join(" + ")
    },
    {
      title: "启用状态",
      dataIndex: "isEnable",
      fixed: "right",
      valueEnum: enableEnum,
      renderText: (dom: any, entity: any) => (
        <Switch
          disabled={!access.canCheckRule_openClose}
          checkedChildren="启用"
          unCheckedChildren="禁用"
          checked={!!dom}
          onChange={() => {
            getTicketCheckStatus({
              id: entity.id,
              isEnable: 1 - entity.isEnable,
              scenicId
            })
              .then(() => {
                message.success(dom ? "已禁用" : "已启用");
                addOperationLogRequest({
                  action: "disable",
                  content: `${dom ? "禁用" : "启用"}【${entity.name}】检票规则`
                });
                actionRef.current?.reload();
              })
              .catch(() => {});
          }}
        />
      )
    },
    {
      title: "操作",
      valueType: "option",
      fixed: "right",
      render: (_: any, record: any) => (
        <Space size="large">
          <a
            onClick={async () => {
              setLoading(true);
              setDetailsVisible(true);
              const { data } = await TicketCheckXq({ id: record.id });
              data.adoptType *= 1;
              data.controlType *= 1;
              data.proType += "";
              setDataSource(data);
              setLoading(false);
              addOperationLogRequest({
                action: "info",
                content: `查看【${record.name}】检票规则详情`
              });
            }}
          >
            查看
          </a>
          <Access accessible={access.canCheckRule_edit && record?.isDigit != "1"}>
            <a
              onClick={async () => {
                const { data } = await TicketCheckXq({ id: record.id });
                data.adoptType *= 1;
                data.controlType *= 1;
                data.proType += "";
                getCheckInPointReq.run({ scenicId, proType: data.proType });
                setDataSource(data);
                setEditVisible(true);
              }}
            >
              编辑
            </a>
          </Access>
          <Access accessible={access.canCheckRule_delete && record?.isDigit != "1"}>
            <a
              style={{ color: "red" }}
              onClick={async () => {
                if (record.isEnable) {
                  Modal.warning({
                    title: "不可删除",
                    content: "请先禁用后删除"
                  });
                  return;
                }
                Modal.confirm({
                  title: "确认删除吗？",
                  icon: <InfoCircleTwoTone />,
                  content: "删除后不可恢复",
                  okText: "确认",
                  cancelText: "取消",
                  onOk: () => {
                    delTicketCheck(record.id)
                      .then(() => {
                        message.success("删除成功");
                        addOperationLogRequest({
                          action: "del",
                          content: `删除【${record.name}】检票规则`
                        });
                        actionRef.current?.reload();
                      })
                      .catch(() => {});
                  }
                });
              }}
            >
              删除
            </a>
          </Access>
        </Space>
      )
    }
  ];

  // 【表单】数据绑定
  const [isFace, setIsFace] = useState<any>([]);
  const [editVisible, setEditVisible] = useState<boolean>(false);

  const editColumns: ProFormColumnsType<unknown, "text">[] = [
    {
      title: "基础信息",
      columns: [
        {
          title: "所属景区",
          dataIndex: "scenicId",
          valueType: "select",
          valueEnum: { [scenicId]: scenicName },
          initialValue: scenicId,
          fieldProps: { disabled: true }
        },
        {
          title: "身份识别类型",
          dataIndex: "identityTypeList",
          valueType: "checkbox",
          fieldProps: {
            options: isFace
          },
          initialValue: "票",
          formItemProps: { rules: [{ required: true }] }
        },
        {
          title: "检票规则名称",
          dataIndex: "name",
          // width: 'xl',
          formItemProps: { rules: [{ required: true, max: 30 }] }
        },
        {
          title: "产品类型",
          dataIndex: "proType",
          valueType: "select",
          valueEnum: baseProductTypeEnum,
          initialValue: "0",
          fieldProps: {
            allowClear: false
          },
          formItemProps: { rules: [{ required: true }] }
        },
        {
          valueType: "dependency",
          name: ["proType"],
          // fieldProps: {
          //   name: ['proType'],
          // },
          columns: ({ proType }: { proType: any }) => {
            console.log(proType);
            return [
              {
                title: (
                  <div className="flex justify-content-between w-100">
                    检票点名称
                    <a
                      onClick={() => {
                        localStorage.setItem("pageOperateType", "add");
                        window.open(`${getAllPath()}/infrastructure/device/ticketsys`, "_blank");
                      }}
                    >
                      新增
                    </a>
                  </div>
                ),
                width: "md",
                colProps: { span: 12 },
                dataIndex: "pointList",
                valueType: "select",
                // params: { scenicId, proType },
                valueEnum: getCheckInPointReq.data,
                fieldProps: {
                  mode: "multiple",
                  loading: getCheckInPointReq.loading,
                  onDropdownVisibleChange: e => {
                    if (e) {
                      getCheckInPointReq.run({ scenicId, proType });
                    }
                  }
                },
                formItemProps: { rules: [{ required: true, message: "请输入检票点名称" }] }
              }
            ];
          }
        }
      ]
    },
    {
      title: "检票信息",
      columns: [
        {
          title: "检票控制方式",
          dataIndex: "controlType",
          valueType: "select",
          // valueEnum: { '0': '平台检票' },
          initialValue: 0,
          fieldProps: {
            options: [
              {
                value: 0,
                label: "平台检票"
              }
            ],
            disabled: true
          }
        },
        {
          title: "检票间隔时间（秒）",
          dataIndex: "intervalTime",
          valueType: "digit",
          initialValue: 1,
          formItemProps: { rules: [{ required: true }] }
        },
        {
          title: "检票通行方式",
          dataIndex: "adoptType",
          valueType: "select",
          fieldProps: {
            options: [
              {
                value: 0,
                label: "一检一人"
              },
              {
                value: 1,
                label: "一检多人"
              }
            ]
          },
          initialValue: 0
        }
      ]
    },
    {
      title: "其他信息",
      columns: [
        {
          title: "分时预约检票设置",
          dataIndex: "timeShareBook",
          valueType: "select",
          valueEnum: {
            0: "检票时间不可提前不可延后",
            1: "检票时间可提前但不可延后",
            2: "检票时间不可提前但可延后",
            3: "检票时间可提前可延后"
          },
          initialValue: "0"
        }
      ]
    }
  ];
  const getIsFace = async () => {
    console.log(columnsInitial);
    try {
      const data = await apiScenicConfig({ id: scenicId });
      const sum = [];
      if (data.isFace) sum.push("人脸");
      sum.push("票");
      if (data.isRealName) sum.push("身份证");
      setIsFace(sum);
    } catch (error) {}
  };

  // 【列表】数据绑定
  const [detailsVisible, setDetailsVisible] = useState<boolean>(false);
  const [isLoading, setLoading] = useState<boolean>(true);
  const [dataSource, setDataSource] = useState<any>({ id: "", isEnable: 0 });
  // const [proType, setProType] = useState(0);
  const columnsInitial = [
    {
      title: "基础信息",
      columns: [
        {
          title: "所属景区",
          dataIndex: "scenicName",
          renderText: () => scenicName
        },
        {
          title: "身份识别类型",
          dataIndex: "identityTypeList",
          renderText: (val: any[]) => val.join("+")
        },
        {
          title: "检票规则名称",
          dataIndex: "name"
        },
        {
          title: "产品类型",
          dataIndex: "proType",
          valueEnum: baseProductTypeEnum
        },
        {
          title: "检票点名称",
          dataIndex: "pointList",
          valueType: "select",
          renderText: (text: any, entity: any) =>
            entity.checkInPointList
              .filter(i => text.includes(i.id))
              .map((item: any) => item.checkName)
              .join("、")
        }
      ]
    },
    {
      title: "检票信息",
      columns: [
        {
          title: "检票控制方式",
          dataIndex: "controlType",
          renderText: (dom: any) => "平台检票"
        },
        {
          title: "检票间隔时间 (秒)",
          dataIndex: "intervalTime"
        },
        {
          title: "检票通行方式",
          dataIndex: "adoptType",
          valueType: "select",
          valueEnum: CheckTypeEnum
        }
      ]
    },
    {
      title: "其他信息",
      columns: [
        {
          title: "分时预约检票设置",
          dataIndex: "timeShareBook",
          valueEnum: {
            0: "检票时间不可提前不可延后",
            1: "检票时间可提前但不可延后",
            2: "检票时间不可提前但可延后",
            3: "检票时间可提前可延后"
          }
        }
      ]
    }
  ];

  const logList = [...columnsInitial[0].columns, ...columnsInitial[1].columns, ...columnsInitial[2].columns];

  // 展示新增弹窗
  const onAdd = () => {
    setDataSource({ id: "", isEnable: 0 });
    setEditVisible(true);
  };

  useEffect(() => {
    if (editVisible && dataSource?.proType) {
      getCheckInPointReq.run({ scenicId, proType: dataSource?.proType });
    }
  }, [dataSource?.proType, editVisible, scenicId]);

  useEffect(() => {
    const pageOperateType = localStorage.getItem("pageOperateType");
    if (pageOperateType === "add") {
      onAdd();
    }
  }, []);

  return (
    <>
      <ProTable<API.RuleListItem, API.PageParams>
        {...tableConfig}
        rowClassName={(row: any) => (row.isEnable == 1 ? "" : "disableRow")}
        actionRef={actionRef}
        toolBarRender={() => [
          <Access key={getUniqueId()} accessible={access.canCheckRule_insert}>
            <Button
              type="primary"
              key="primary"
              onClick={() => {
                onAdd();
              }}
            >
              <PlusOutlined /> 新增
            </Button>
          </Access>
        ]}
        params={{ scenicId }}
        request={async params => {
          const { data } = await getTicketCheckRulePageList(params);
          return data;
        }}
        columns={columns}
      />

      {/* 新增编辑 */}
      <EditPop
        title="检票规则"
        visible={editVisible}
        setVisible={(v: boolean) => {
          setEditVisible(v);
          localStorage.removeItem("pageOperateType");
        }}
        columns={editColumns}
        dataSource={dataSource}
        onValuesChange={(formRef: any, e: any) => {
          if (e.proType) {
            console.log(formRef?.current);

            formRef?.current.setFieldsValue({ pointList: [] });
          }
        }}
        // 新增/编辑
        onFinish={async (val: any) => {
          if (dataSource.id) val.id = dataSource.id;
          val.scenicName = scenicName;
          const msgType = val.id ? "编辑" : "新增";
          const hide = message.loading("正在" + msgType);
          if (val.identityTypeList.length == 1) val.identityTypeList = [val.identityTypeList[0]];

          try {
            await addTicketCheck({ ...val });
            if (val.id) {
              addOperationLogRequest({
                action: "edit",
                changeConfig: {
                  list: logList,
                  beforeData: dataSource,
                  afterData: {
                    ...val,
                    checkInPointList: [getCheckInPointReq.data]
                  }
                },
                content: `编辑【${val.name}】检票规则`
              });
            } else {
              addOperationLogRequest({
                action: "add",
                content: `新增【${val.name}】检票规则`
              });
            }
            message.success(msgType + "成功");
            // 关闭弹窗并刷新列表
            setEditVisible(false);
            actionRef?.current?.reload();
          } catch (error) {
            console.log(error);
          }
          hide();
        }}
      />

      {/* 详情 */}
      <DetailsPop
        title="检票规则详情"
        visible={detailsVisible}
        isLoading={isLoading}
        setVisible={setDetailsVisible}
        columnsInitial={columnsInitial}
        dataSource={dataSource}
      />
    </>
  );
};

export default TableList;
