import styles from "./index.less";

import routesAccess from "@/../config/routes";
import Disabled from "@/common/components/Disabled";
import SearchTree from "@/common/components/SeachTree";
import { tableConfig } from "@/common/utils/config";
import { modelWidth } from "@/common/utils/gConfig";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import { getUniqueId } from "@/common/utils/tool";
import {
  addRole,
  checkDefaultRole,
  deleteRole,
  getPermissionList,
  getPermissionListNew,
  getRoleList,
  operationRequest,
  postCreateDefaultRole,
  setRole
} from "@/services/api/cas";
import { PlusOutlined } from "@ant-design/icons";
import type { ProFormInstance } from "@ant-design/pro-form";
import ProForm, { ModalForm, ProFormText } from "@ant-design/pro-form";
import type { ActionType, ProColumns } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import { But<PERSON>, Modal, Popconfirm, Tree, message } from "antd";
import _, { isEmpty } from "lodash";
import type { DefaultValueType } from "rc-select/lib/interface/generator";
import type { DataNode, Key } from "rc-tree/lib/interface";
import React, { useEffect, useRef, useState } from "react";
import { Access, useAccess, useModel } from "@umijs/max";

const { DirectoryTree } = Tree;

let permissionList: DefaultValueType[] = []; //全局权限列表数据
const permissionTree: DataNode[] = []; //全局权限树状结构
//取权限列表
async function handlePermissionList({
  scenicId,
  roleId,
  companyId
}: {
  scenicId?: string;
  roleId?: string;
  companyId?: string;
}) {
  if (roleId) {
    const d = await getPermissionList({ relationId: `${scenicId}/${companyId}`, roleId: roleId }); //skip
    return dealPermissionList(d, false); //有加载单独角色时，会把此角色的权限叠加到默认的权限列表上
  } else {
    getPermissionListNew({ scenicId, companyId }).then(r => (permissionList = dealPermissionList(r, true)));
  }
  return [];
}

/**
 * 处理服务器取到的数据为本地组件所使用的格式
 * @param data 服务器返回的数据
 * @param clear 是否需要清除之前全局的权限列表，清除叠加态脏数据权限列表
 * @returns 叠加后的权限列表
 */
function dealPermissionList(data: any, clear: boolean): DefaultValueType[] {
  if (clear) {
    permissionTree.length = 0; //清空数组
  }
  const findTreeKeyIndex = (tree: DataNode[], key: string): number => {
    return tree.findIndex((e: DataNode) => {
      return e.key === key;
    });
  };

  const result: DefaultValueType[] = [];
  if (data.code !== 20000) return [];
  data.data.forEach((e: any) => {
    const gn = e.groupName.replaceAll("|", " / ");
    const cn = e.codeName.replaceAll("/", " / ").replaceAll("-", " / ");
    const a = e.action.replace("禁用/启用", "开关").replace("启用/禁用", "开关").replace("上架/下架", "上下架");
    const name = `${gn} / ${cn} / ${a}`;
    const value = `${e.groupCode}|${e.code}|${e.actionCode}|${e.id}`;
    result.push({ label: name, value: value });
    //树结构构建，以下树结构应为已有时不再新加
    const treeInfo = [
      [gn, e.groupCode],
      [cn, `${e.groupCode}|${e.code}`],
      [a, value]
    ]; //[name,value]
    let treeNode = permissionTree;
    for (let i = 0; i < treeInfo.length; i++) {
      const index = findTreeKeyIndex(treeNode, treeInfo[i][1]);
      if (index != -1) {
        treeNode = treeNode[index].children!;
        continue;
      }
      const tempNode: DataNode = {
        title: treeInfo[i][0],
        children: [],
        key: treeInfo[i][1]
      };
      treeNode.push(tempNode);
      treeNode = tempNode.children!;
    }
  });
  return result;
}
/**
 * 递归选出勾选的权限
 * @param keys 选中的key
 */
function dealPermissionCheck(keys: Key[], parent: DataNode[]): DataNode[] {
  const node: DataNode[] = [];
  for (let i = 0; i < parent.length; i++) {
    const element = parent[i];
    const children = element.children;
    if (children && children.length > 0) {
      const c = dealPermissionCheck(keys, children);
      if (c && c.length > 0) {
        //有子集才新增
        node.push({ title: element.title, key: element.key, children: c });
      }
    } else {
      //最后一层，判定是否新增
      if (keys.findIndex((e: Key) => e == element.key) != -1) {
        node.push({ title: element.title, key: element.key });
      }
    }
  }
  const s = JSON.stringify(routesAccess);
  const i = (v: any) => {
    const r = s.indexOf("can" + v.key.split("|").at(-1));
    return r == -1 ? Infinity : r;
  };
  node[0]?.children?.sort((a, b) => i(a) - i(b));
  return node;
}

const TableList: React.FC = () => {
  useEffect(() => {
    handlePermissionList({ scenicId: scenicId, companyId: currentCompanyInfo.coId });
  }, []);
  /**
   * @en-US Pop-up window of new window
   * @zh-CN 新增窗口的弹窗
   *  */
  const [createModalVisible, handleModalVisible] = useState<boolean>(false);
  const [permissionsMV, setPermissionsMV] = useState<boolean>(false); //注:MV=ModalVisible
  const [isCreate, handleIsCreate] = useState<boolean>(false);
  /**
   * @en-US The pop-up window of the distribution update window
   * @zh-CN 分布更新窗口的弹窗
   * */
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const [currentRow, setCurrentRow] = useState<any>({});
  const [checkPermissions, setCheckPermissions] = useState<Key[]>([]);
  const [checkPermissionsTemp, setCheckPermissionsTemp] = useState<Key[]>([]);
  //默认角色弹窗
  const [createRoleVisible, setCreateRoleVisible] = useState(false);
  /**
   * 查询详情的 id 值
   * */
  const access = useAccess();
  const { initialState } = useModel("@@initialState"); //用于取景区 ID
  const scenicId = initialState?.scenicInfo?.scenicId;

  const { currentCompanyInfo } = useModel("@@initialState").initialState || {};

  const columns: ProColumns<API.RuleListItem>[] = [
    {
      title: "编号",
      dataIndex: "roleId"
    },
    {
      title: "角色名称",
      dataIndex: "name"
    },
    {
      title: "启用状态",
      dataIndex: "status",
      fixed: "right",
      valueEnum: {
        0: "全部",
        1: "已启用",
        2: "已禁用"
      },
      render: (_, entity: any) => (
        <Disabled
          access={access.canRoleManage_openClose}
          status={entity.status == 1}
          params={{
            relationId: `${scenicId}/${currentCompanyInfo?.coId}`,
            roleId: entity.roleId,
            status: entity.status == 1 ? 2 : 1
          }}
          request={async (params: any) => {
            const data = await setRole(params);
            addOperationLogRequest({
              action: "disable",
              content: `${entity.status == 1 ? "禁用" : "启用"}【${entity.name}】角色`
            });

            return data;
          }}
          actionRef={actionRef}
        />
      )
    },
    {
      width: "auto",
      title: "操作",
      valueType: "option",
      hideInTable: !(access.canRoleManage_edit || access.canRoleManage_delete), //当有任意一项时才显示此栏
      fixed: "right",
      render: (_, record: Record<string, any>) => [
        //TODO: href 需要根据环境动态配置
        <Access key={getUniqueId()} accessible={access.canRoleManage_edit}>
          <a
            key="edit"
            onClick={async () => {
              // record.activate = record.status === 1;
              setCurrentRow(record);
              handleIsCreate(false);
              handleModalVisible(true);
            }}
          >
            编辑
          </a>
        </Access>,
        <Access key={getUniqueId()} accessible={access.canRoleManage_delete}>
          <Popconfirm
            title="你确认要删除吗？"
            okText="确认"
            cancelText="取消"
            okType="danger"
            onConfirm={() => {
              handleDelete(record.roleId, record.name);
            }}
          >
            <a key="delete" style={{ color: "red" }}>
              删除
            </a>
          </Popconfirm>
        </Access>
      ]
    }
  ];

  //增加或编辑角色
  const handleNewOrEditRole = async (value: any) => {
    value.permissions = checkPermissions.filter((e: Key) => {
      //过滤掉第一层和第二层的选择，只留第三层的选择，保留有三个 ｜ 符号的，代表第三层最终权限
      const title = e as string;
      const symbol = "|";
      let index = title.indexOf(symbol);
      let num = 0;
      while (index !== -1) {
        num++;
        index = title.indexOf(symbol, index + 1);
      }
      return num == 3;
    });
    //权限数据格式化
    const permissionCodeFormat = (data: string[]): object[] => {
      const pc: object[] = [];
      data.forEach((e: string) => {
        const info = e.split("|");
        pc.push({
          groupCode: info[0],
          code: info[1],
          actionCode: info[2],
          id: info[3]
        });
      });
      return pc;
    };

    value.relationId = `${scenicId}/${currentCompanyInfo.coId}`;
    if (!currentRow) {
      if (value.permissions) {
        value.permissions = permissionCodeFormat(value.permissions);
      }
      //新增
      operationRequest(
        addRole({
          ...value
        }),
        () => {
          addOperationLogRequest({
            action: "add",
            content: `新增【${value.name}】角色`
          });

          handleModalVisible(false);
          actionRef.current?.reload();
        }
      );
    } else {
      //编辑
      //处理权限的问题
      const oldPower = currentRow.permissions; //之前的数据
      let addPermissions: any = []; //新增的权限
      let deletePermissions: any = []; //删除的权限
      value.permissions.forEach((e: string) => {
        //新数据在旧数据中找不到，为新增
        if (oldPower.indexOf(e) == -1) {
          addPermissions.push(e);
        }
      });
      oldPower.forEach((e: string) => {
        //旧数据在新数据中找不到，为删除
        if (value.permissions.indexOf(e) == -1) {
          deletePermissions.push(e);
        }
      });
      addPermissions = permissionCodeFormat(addPermissions);
      deletePermissions = permissionCodeFormat(deletePermissions);
      if (addPermissions.length != 0) {
        value.addPermissions = addPermissions;
      }
      if (deletePermissions.length != 0) {
        value.deletePermissions = deletePermissions;
      }
      delete value.permissions;

      // value.status = value.activate ? 1 : 2; //  1 为启动 2 为禁用
      value.roleId = currentRow.roleId;
      // delete value.activate;
      // const p = value.permission;
      // //string to int
      // for (var i = 0; i < p.length; i++) {
      //   p[i] = parseInt(p[i]);
      // }
      operationRequest(
        setRole({
          ...value
        }),
        () => {
          addOperationLogRequest({
            action: "edit",
            changeConfig: {
              list: [
                {
                  title: "角色名称",
                  dataIndex: "name"
                }
              ],
              beforeData: currentRow,
              afterData: value
            },
            content: `编辑【${currentRow.name}】角色`
          });
          handleModalVisible(false);
          actionRef.current?.reload();
        }
      );
    }
  };

  const handleDelete = async (roleId: string, name: string) => {
    operationRequest(
      deleteRole(roleId),
      () => {
        addOperationLogRequest({
          action: "del",
          content: `删除【${name}】角色`
        });
        handleModalVisible(false);
        actionRef.current?.reload();
      },
      "正在删除",
      "删除成功",
      "删除失败"
    );
  };

  const roleListReq = async params => {
    const { roleId, ...rest } = params;
    const { data } = await getRoleList({
      code: roleId,
      ...rest
    });
    return data;
  };

  const powerSelect = {};
  for (let i = 0; i < 7; i++) {
    powerSelect[`${1 << i}`] = `权限${i}`;
  }

  const addDefaultRole = async (roles?: string[]) => {
    try {
      await postCreateDefaultRole({
        relationId: `${scenicId}/${currentCompanyInfo?.coId}`,
        type: "02"
      });
      addOperationLogRequest({
        action: "add",
        content: `新增【${roles?.join("，")}】默认角色`
      });

      message.success("操作成功");
      setCreateRoleVisible(false);
      actionRef.current?.reload();
    } catch (error) {}
  };

  return (
    <>
      <ProTable
        {...tableConfig}
        rowClassName={(row: any) => (row.status == 1 ? "" : "disableRow")}
        headerTitle={<div style={{ color: "#ff4d4f" }}>请慎重编辑</div>}
        actionRef={actionRef}
        rowKey="roleId"
        params={{ type: "02", relationId: `${scenicId}/${currentCompanyInfo?.coId}` }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              setCreateRoleVisible(true);
            }}
          >
            <PlusOutlined />
            默认角色
          </Button>,
          <Access key={getUniqueId()} accessible={access.canRoleManage_insert}>
            <Button
              type="primary"
              key="primary"
              onClick={() => {
                setCurrentRow(null);
                handleIsCreate(true);
                handleModalVisible(true);
              }}
            >
              <PlusOutlined />
              新增
            </Button>
          </Access>
        ]}
        request={roleListReq}
        columns={columns}
      />
      <ModalForm
        title={`${currentRow ? "编辑" : "新增"}角色`}
        width={modelWidth.md}
        className={styles.form}
        labelCol={{ style: { width: 80 } }}
        layout="horizontal"
        formRef={formRef}
        visible={createModalVisible}
        onVisibleChange={(v: boolean) => {
          if (v) {
            new Promise(async function (resolve, reject) {
              if (currentRow) {
                //当有角色数据时，为编辑。编辑需要加载角色的权限
                const d = await handlePermissionList({
                  //取当前角色的权限
                  scenicId: scenicId,
                  companyId: currentCompanyInfo.coId,
                  roleId: currentRow.roleId
                });
                const r: any = [];
                d?.forEach((e: any) => {
                  //只取 value
                  r.push(e.value);
                });
                setCheckPermissions(_.cloneDeep(r)); //设置当前选中权限
                const newRow = _.cloneDeep(currentRow);
                newRow.permissions = r; //保存起始值用于编辑时比较原数据
                setCurrentRow(newRow); //延时性
                formRef.current?.setFieldsValue(newRow);
              } else {
                //无数据时则为新增，新增需要重新加载一次默认权限，防脏数据权限列表叠加
                handlePermissionList({ scenicId: scenicId, companyId: currentCompanyInfo.coId });
              }
            }).then();
          } else {
            setCheckPermissions([]);
            setCheckPermissionsTemp([]);
          }
          handleModalVisible(v);
        }}
        modalProps={{
          maskClosable: false, //点击蒙层不可关闭
          destroyOnClose: true //关闭弹窗销毁子元素
        }}
        onFinish={handleNewOrEditRole}
      >
        <ProFormText
          name="name"
          label="角色名称："
          fieldProps={{
            maxLength: 10,
            showCount: true
          }}
          rules={[{ required: true, message: "请输入角色名称" }]}
        />
        {/* {!isCreate ? (
          <ProFormSwitch
            name="activate"
            label="角色状态："
            checkedChildren="开"
            unCheckedChildren="关"
            initialValue={true}
          />
        ) : (
          ''
        )} */}
        <ProForm.Item
          label={
            <a
              onClick={() => {
                setCheckPermissionsTemp(checkPermissions); //初始化临时权限的值，解决不编辑直接点确定权限被清除问题
                setPermissionsMV(true);
              }}
            >
              菜单权限
            </a>
          }
        >
          <DirectoryTree
            virtual={false}
            height={modelWidth.sm}
            selectable={false}
            checkable={false}
            showLine={false}
            showIcon={false}
            treeData={dealPermissionCheck(checkPermissions, permissionTree)}
          />
        </ProForm.Item>
      </ModalForm>

      <ModalForm
        title={currentRow ? "编辑" : "新增"}
        width={modelWidth.md}
        labelCol={{ style: { width: 70 } }}
        layout="horizontal"
        visible={permissionsMV}
        modalProps={{
          maskClosable: false, //点击蒙层不可关闭
          destroyOnClose: true //关闭弹窗销毁子元素
        }}
        onVisibleChange={setPermissionsMV}
        onFinish={async (__: any) => {
          setPermissionsMV(false);
          setCheckPermissions(checkPermissionsTemp);
        }}
      >
        <SearchTree
          searchPlaceholder="搜索"
          treeData={permissionTree}
          defaultCheckedKeys={checkPermissions}
          onCheck={(k, e) => {
            setCheckPermissionsTemp(k as Key[]);
          }}
        />
      </ModalForm>
      <Modal
        title="默认角色"
        visible={createRoleVisible}
        onOk={async () => {
          try {
            const { data = [] } = await checkDefaultRole({
              relationId: `${scenicId}/${currentCompanyInfo.coId}`,
              type: "02"
            });
            if (!isEmpty(data)) {
              Modal.confirm({
                title: "提示",
                content: <div>{[...new Set(data)].join("、")}角色重复，是否还需继续创建默认角色？</div>,
                onOk(...args) {
                  addDefaultRole([...new Set(data)]);
                }
              });
              return;
            }
            addDefaultRole();
          } catch (error) {}
        }}
        onCancel={() => setCreateRoleVisible(false)}
      >
        <p>为您提供了便于操作的默认角色，确认将默认角色新增到权限管理中？</p>
      </Modal>
    </>
  );
};

export default TableList;
