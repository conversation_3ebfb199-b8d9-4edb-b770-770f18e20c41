import EditPop from "@/common/components/EditPop";
import { tableConfig } from "@/common/utils/config";
import { apiHolidayAddUpdate, apiHolidayDelete, apiHolidayDetails, apiHolidayPageList } from "@/services/api/device";
import { PlusOutlined, QuestionCircleOutlined } from "@ant-design/icons";
import ProForm, { ModalForm, ProFormDatePicker, ProFormSelect, ProFormText } from "@ant-design/pro-form";
import type { ActionType } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import { Button, Input, Popconfirm, Space, message } from "antd";
import React, { useRef, useState } from "react";
import { useModel } from "@umijs/max";

const Festivals: React.FC = () => {
  const actionRef = useRef<ActionType>();
  // 【表单】数据绑定
  const [editVisible, setEditVisible] = useState<boolean>(false);
  const [dataSource] = useState<any>({ id: "", isEnable: 0 });
  const [modalVisit, setModalVisit] = useState(false);
  //获取当前ID
  const [Id, setId] = useState(undefined);
  // 获取景区ID
  const { initialState }: any = useModel("@@initialState");
  const scenicId = initialState?.scenicInfo?.scenicId;
  console.log(scenicId, initialState);
  //初始视化数据
  const [initialValues, setInitialValues] = useState({});
  //编辑
  const updateMethod = async (val: any) => {
    const id = val.id;
    const result: any = await apiHolidayDetails(id);
    const { data } = result;
    val.type = val.type.toString();
    setInitialValues(data);
    setId(val.id);
    setModalVisit(true);
  };
  const confirm = async (id: any) => {
    console.log(id);
    try {
      await apiHolidayDelete(id);
      message.success("删除成功");
      // 刷新
      actionRef?.current?.reload();
    } catch (e) {
      console.log(e);
    }
  };

  const cancel = (e: any) => {
    console.log(e);
  };

  // 保存数据
  const submitData = async (val: any) => {
    const endTime = new Date(val.endDate);
    const startTime = new Date(val.beginDate);
    console.log(endTime, startTime);
    const date = endTime - startTime;
    const duration = date / (24 * 60 * 60 * 1000) + 1;
    const pras = {
      ...val,
      scenicId,
      duration
    };
    console.log(duration);
    if (date < 0) {
      message.error("结束日期应大于开始日期");
      setEditVisible(true);
    } else {
      try {
        await apiHolidayAddUpdate(pras);
        console.log(1315646516516512313);
        // message.success('保存成功');
        message.success(val.hasOwnProperty("id") ? "编辑成功" : "新增成功");
        actionRef?.current?.reload();
        setEditVisible(false);
      } catch (e) {
        console.error(e);
      }
    }
  };

  const getFestivalsPageList = async (params: any) => {
    const pars = { ...params, scenicId };
    try {
      const result = await apiHolidayPageList(pars);
      const { data } = result.data;
      // 刷新
      return {
        data,
        success: true,
        total: result.data.total
      };
    } catch (e) {
      console.error(e);
    }
  };

  const columns: any = [
    {
      title: "节假日名称",
      dataIndex: "name",
      hideInSearch: true
    },
    {
      title: "开始时间",
      dataIndex: "beginDate",
      hideInSearch: true
    },
    {
      title: "结束时间",
      dataIndex: "endDate",
      hideInSearch: true
    },

    {
      title: "时长/天",
      dataIndex: "duration",
      hideInSearch: true
    },
    {
      title: "节假日类型",
      dataIndex: "type",
      hideInSearch: true,
      // ellipsis: true,
      valueType: "select",
      valueEnum: {
        0: "国家法定节假日",
        1: "特殊节日"
      }
    },
    {
      title: "年份",
      dataIndex: "beginYear",
      hideInTable: true,
      valueType: "dateYear"
    },
    {
      title: "操作",
      dataIndex: "option",
      width: "10%",
      hideInSearch: true,
      fixed: "right",
      render: (dom, record) => {
        return (
          <Space size="large">
            <a onClick={() => updateMethod(record)}>编辑</a>
            <Popconfirm
              title="您确定删除吗?"
              onConfirm={() => confirm(record.id)}
              icon={<QuestionCircleOutlined style={{ color: "red" }} />}
              onCancel={cancel}
              okText="确认"
              cancelText="取消"
            >
              <a href="#" style={{ color: "red" }}>
                删除
              </a>
            </Popconfirm>
          </Space>
        );
      }
    }
  ];

  const editColumns = [
    {
      columns: [
        {
          title: "节日名称",
          dataIndex: "name",
          name: "name",
          key: "name",
          formItemProps: {
            rules: [{ required: true }, { max: 100, message: "最多可输入100个字符" }]
          },
          fieldProps: {},
          renderFormItem: () => {
            return <Input placeholder="请输入节日名称" />;
          }
        },
        {
          title: "开始时间",
          dataIndex: "beginDate",
          name: "beginDate",
          valueType: "date",
          fieldProps: {
            onchange: e => {}
          },
          formItemProps: {
            rules: [{ required: true }]
          }
        },
        {
          title: "结束时间",
          dataIndex: "endDate",
          name: "endDate",
          valueType: "date",
          fieldProps: {
            onchange: e => {}
          },
          formItemProps: {
            rules: [{ required: true }]
          }
        },
        {
          dataIndex: "type",
          title: "节假日类型",
          name: "type",
          key: "type",
          valueEnum: new Map([
            [0, "国家法定节假日"],
            [1, "特殊节日"]
          ]),
          fieldProps: {},
          formItemProps: {
            rules: [{ required: true }]
          }
        }
      ]
    }
  ];

  return (
    <>
      {JSON.stringify(initialValues) !== "{}" ? (
        <ModalForm
          title="编辑节假日"
          visible={modalVisit}
          // formRef={formObj}
          initialValues={initialValues}
          onFinish={async val => {
            val.id = Id;
            // val.isEnable = isEnableStatus

            submitData(val);
            return true;
          }}
          onVisibleChange={val => {
            setModalVisit(val);
            if (!val) {
              setInitialValues({});
            }
          }}
        >
          <ProForm.Group>
            <ProFormText
              width="md"
              name="name"
              label="节假日名称"
              rules={[{ required: true }, { max: 30, message: "最多可输入30个字符" }]}
              // initialValue={scenicName}
              // disabled={true}
            />
            <ProFormDatePicker
              width="md"
              name="beginDate"
              label="开始时间"
              placeholder="请选择开始时间"
              rules={[{ required: true, message: "请选择开始时间" }]}
            />
            <ProFormDatePicker
              width="md"
              name="endDate"
              label="结束时间"
              placeholder="请选择结束时间"
              rules={[{ required: true, message: "请选择结束时间" }]}
            />
            <ProFormSelect
              width="md"
              name="type"
              label="节假日类型"
              valueEnum={() =>
                new Map([
                  [0, "国家法定节假日"],
                  [1, "特殊节日"]
                ])
              }
              placeholder="请选择节假日类型"
              rules={[{ required: true, message: "请选择节假日类型" }]}
            />
          </ProForm.Group>
        </ModalForm>
      ) : (
        ""
      )}

      <EditPop
        title="节假日"
        visible={editVisible}
        setVisible={setEditVisible}
        columns={editColumns}
        dataSource={dataSource}
        // 新增/编辑
        onFinish={(val: any) => {
          submitData(val);

          console.log(val);
        }}
      />
      <ProTable<API.RuleListItem, API.PageParams>
        {...tableConfig}
        actionRef={actionRef}
        // rowKey="id"
        // search={{
        //   labelWidth: 'auto',
        //   collapseRender: false,
        //   collapsed: false,
        // }}
        toolBarRender={() => [
          // <Access accessible={access.canDeviceTicketsysAdd}>
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              setEditVisible(true);
            }}
          >
            <PlusOutlined /> 新增
          </Button>
          // </Access>,
        ]}
        request={getFestivalsPageList}
        columns={columns}
        // scroll={{ x: 1000 }}
      />
    </>
  );
};

export default Festivals;
