import { useEffect, useState } from "react";
import { useModel } from "@umijs/max";

export default (props: any) => {
  const {
    initialState: {
      scenicInfo: {
        scenicAddress: { latitude, longitude }
      }
    }
  }: any = useModel("@@initialState");
  console.log(latitude, longitude);
  const [TMap] = useState(window.TMap);
  const [map, setMap] = useState<any>(null);
  const [markerLayer, setMarkerLayer] = useState<any>();
  const [circle, setCircle] = useState<any>();

  useEffect(() => {
    const map = new TMap.Map(document.getElementById("container"), {
      center: new TMap.LatLng(latitude || 22.530935, longitude || 113.951875), // 中心点坐标 TODO：未设置景区坐标使用其行政区坐标
      zoom: 16, // 缩放级别 [3, 20]
      viewMode: "2D", // 视图模式
      baseMap: {
        type: "vector",
        features: ["base", "building2d", "point"] // 仅渲染：道路及底面(base) + 2d建筑物(building2d) + poi文字
      }
    });
    const marker = new TMap.MultiMarker({ map });
    map.removeControl(TMap.constants.DEFAULT_CONTROL_ID.ROTATION);
    map.on("click", (v: any) => {
      marker.updateGeometries([{ id: "pointKey", position: v.latLng }]);
      props.formRef.setFieldsValue({ longitude: v.latLng.lng, latitude: v.latLng.lat });
    });
    setMarkerLayer(marker);
    setCircle(
      new TMap.MultiCircle({
        map,
        styles: {
          // 设置圆形样式
          default: new TMap.CircleStyle({
            color: "rgba(153, 202, 255, .67)",
            showBorder: true,
            borderColor: "rgba(153, 202, 255, 1)",
            borderWidth: 2
          })
        }
      })
    );
    setMap(map);
    return () => map && map.destroy();
  }, []);

  useEffect(() => {
    if (props.longitude && props.latitude) {
      map?.panTo(new TMap.LatLng(props.latitude, props.longitude));
      markerLayer.updateGeometries([
        {
          id: "pointKey",
          position: new TMap.LatLng(props.latitude, props.longitude)
        }
      ]);
      if (props.aiExplainTriggerRange) {
        circle.updateGeometries([
          {
            id: "circleKey",
            center: new TMap.LatLng(props.latitude, props.longitude),
            radius: props.aiExplainTriggerRange
          }
        ]);
      }
    }
  }, [props.longitude, props.latitude]);

  useEffect(() => {
    if (props.longitude && props.latitude && props.aiExplainTriggerRange) {
      circle.updateGeometries([
        {
          id: "circleKey",
          center: new TMap.LatLng(props.latitude, props.longitude),
          radius: props.aiExplainTriggerRange
        }
      ]);
    }
  }, [props.aiExplainTriggerRange]);

  return (
    <div
      style={{
        width: "100%",
        height: 675,
        // paddingBottom: '56.25%',
        position: "relative",
        zIndex: 0
      }}
    >
      <div
        id="container"
        style={{
          width: "100%",
          height: "100%",
          position: "absolute",
          zIndex: 0,
          border: "1px solid #d9d9d9",
          borderRadius: "2px",
          overflow: "hidden"
        }}
      />
    </div>
  );
};
