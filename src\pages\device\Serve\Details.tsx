import ProDescriptions from "@ant-design/pro-descriptions";
import { useModel } from "@umijs/max";

export default function Details(props) {
  const { detailData } = props;
  // 获取景区 ID
  const { initialState }: any = useModel("@@initialState");
  const scenicId = initialState?.scenicInfo?.scenicId;
  console.log(scenicId, initialState);
  //获取景区名称
  const scenicName = initialState?.scenicInfo?.scenicName;
  console.log("景区名称", scenicName);
  const columns = [
    {
      title: "所属景区",
      dataIndex: "scenicName",
      name: "scenicName",
      initialValue: `${scenicName}`,
      key: "scenicName",
      render: () => {
        return scenicName;
      },
      // valueEnum: {
      //   '0': '公告',
      // },
      formItemProps: {
        // rules: [{ required: true }],
        // disable: false
      },
      fieldProps: {
        disabled: true
      }
    },
    {
      dataIndex: "managementUnit",
      title: "管理单位",
      name: "managementUnit",
      key: "managementUnit",
      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      dataIndex: "touristsName",
      title: "游戏服务项目名称",
      name: "touristsName",
      key: "touristsName",
      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      dataIndex: "type",
      title: "类型",
      name: "type",
      valueType: "select",
      valueEnum: {
        "0": "咨询服务点",
        "1": "游客休息区",
        "2": "特殊人群服务设施点",
        "3": "便民服务点"
      },

      key: "type",
      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      dataIndex: "contacts",
      title: "联系人",
      name: "contacts",
      key: "contacts",
      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      dataIndex: "contactMobile",
      title: "联系人电话",
      name: "contactMobile",
      key: "contactMobile",
      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      title: "省市区",
      dataIndex: "province",
      name: "province",
      key: "province",
      // renderFormItem: () => {
      //     return <Province width={328} />;
      // },
      render: () => {
        return detailData.provinceName + detailData.cityName + detailData.areaName;
      },
      formItemProps: {
        rules: [{ required: true }]
      },
      valueType: "select",
      fieldProps: {
        // mode: 'multiple',
        // options: positionValue2 == 1 ? scenicData2 : companyData2,
        // onChange: (value, option) => {
        //   console.log(value);
        //   setAcceptorData(value);
        // },
        // showArrow: true,
        // disabled: flag ? false : show ? false : true,
      }
    },
    {
      dataIndex: "address",
      title: "详细地址",
      name: "address",
      key: "address",
      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },

    {
      dataIndex: "longitude",
      title: "经度",
      name: "longitude",
      key: "longitude",
      fieldProps: {},
      render: (dom, record) => {
        const str = record.longitude.charAt(0);
        if (str == "+" || str == "-") {
          if (str == "+") {
            return <span>{record.longitude.slice(1)}° E </span>;
          } else if (str == "-") {
            return <span>{record.longitude.slice(1)}° W</span>;
          }
        } else {
          return <span>{record.longitude}° E</span>;
        }
      }
      // formItemProps: {
      //     rules: [{ required: true }],
      // },
    },
    {
      dataIndex: "latitude",
      title: "纬度",
      name: "latitude",
      key: "latitude",
      fieldProps: {},
      render: (dom, record) => {
        const str = record.latitude.charAt(0);
        if (str == "+" || str == "-") {
          if (str == "+") {
            return <span>{record.latitude.slice(1)}° N</span>;
          } else if (str == "-") {
            return <span>{record.latitude.slice(1)}° S</span>;
          }
        } else {
          return <span>{record.latitude}° N</span>;
        }
      }
      // formItemProps: {
      //     rules: [{ required: true }],
      // },
    }
  ];
  return (
    <ProDescriptions
      title="基础信息"
      //   request={async () => {}}
      dataSource={detailData}
      columns={columns}
      column={2}
    >
      {/* <ProDescriptions.Item dataIndex="percent" label="百分比" valueType="percent">
                100
            </ProDescriptions.Item> */}
    </ProDescriptions>
  );
}
