import { useEffect, useState } from 'react';
import styles from './Map.less';

export default (props: any) => {
  const [activeKey, setActiveKey] = useState<any>(null);
  const [editor, setEditor] = useState<any>(null);
  const [TMap] = useState(window.TMap);

  const controlClick = (key: string) => {
    editor.setActiveOverlay(key);
    // 清空上次编辑数据
    const list = editor.getActiveOverlay().overlay.geometries;
    if (list.length) {
      // 开启编辑状态
      editor.setActionMode(TMap.tools.constants.EDITOR_ACTION.INTERACT);
      editor.select([list[0].id]);
      editor.delete();
      // 关闭编辑状态
      editor.setActionMode(TMap.tools.constants.EDITOR_ACTION.DRAW);
    }
    setActiveKey(key);
  };

  useEffect(() => {
    const { latitude, longitude, scenicRange } =
      props.formRef?.getFieldValue('scenicAddress') || props;
    const map = new TMap.Map(document.getElementById('container'), {
      center:
        latitude && longitude
          ? new TMap.LatLng(latitude, longitude)
          : new TMap.LatLng(22.530935, 113.951875), // 中心点坐标，不存在使用地区坐标
      scrollable: !!props.formRef, // 只读状态取消鼠标滚轮缩放地图
      zoom: 16, // 缩放级别 [3, 20]
      viewMode: '2D', // 视图模式
      baseMap: {
        type: 'vector',
        features: ['base', 'building2d', 'point'], // 仅渲染：道路及底面(base) + 2d建筑物(building2d) + poi文字
      },
    });
    map.removeControl(TMap.constants.DEFAULT_CONTROL_ID.ROTATION);
    const paths = scenicRange
      ? JSON.parse(scenicRange).map((item: any) => new TMap.LatLng(item[0], item[1]))
      : [];
    // 设置范围
    if (scenicRange) {
      const bounds = new TMap.LatLngBounds();
      // 扩大 bounds 范围
      paths.forEach((position: any) => {
        bounds.extend(position);
      });
      // 设置地图可视范围
      map.fitBounds(bounds, { padding: 50 });
    }
    // 初始化编辑器
    const editor = new TMap.tools.GeometryEditor({
      map, // 编辑器绑定的地图对象
      overlayList: [
        {
          overlay: new TMap.MultiMarker(
            latitude && longitude
              ? {
                  map,
                  geometries: [
                    {
                      position: new TMap.LatLng(latitude, longitude),
                    },
                  ],
                }
              : { map },
          ),
          id: 'marker',
        },
        {
          overlay: new TMap.MultiPolygon(
            scenicRange
              ? {
                  map,
                  geometries: [{ paths }],
                }
              : { map },
          ),
          id: 'polygon',
        },
      ],
      actionMode: TMap.tools.constants.EDITOR_ACTION.DRAW, // 编辑器的工作模式
      snappable: true, // 开启吸附
    });
    // 激活图层
    editor.setActiveOverlay(null);
    // 监听绘制结束事件，获取绘制几何图形
    editor.on('draw_complete', (geometry: any) => {
      if (geometry.position) {
        props.formRef.setFieldsValue({
          scenicAddress: {
            latitude: geometry.position.lat,
            longitude: geometry.position.lng,
          },
        });
      } else {
        props.formRef.setFieldsValue({
          scenicAddress: {
            scenicRange: JSON.stringify(geometry.paths.map((item: any) => [item.lat, item.lng])),
          },
        });
      }
      editor.setActiveOverlay(null);
      setActiveKey(null);
    });
    setEditor(editor);
    return () => map && map.destroy();
  }, []);

  return (
    <div
      style={{
        width: '100%',
        height: 675,
        // paddingBottom: '56.25%',
        position: 'relative',
        zIndex: 0,
      }}
    >
      <div
        id="container"
        style={{
          width: '100%',
          height: '100%',
          position: 'absolute',
          border: '1px solid #d9d9d9',
          borderRadius: '2px',
          overflow: 'hidden',
        }}
      />
      {/* 只读状态不显示控件 */}
      {props.formRef && (
        <div className={styles.control}>
          <div
            className={styles.marker + (activeKey == 'marker' ? ' ' + styles.active : '')}
            onClick={() => controlClick('marker')}
          />
          <div
            className={styles.polygon + (activeKey == 'polygon' ? ' ' + styles.active : '')}
            onClick={() => controlClick('polygon')}
          />
        </div>
      )}
    </div>
  );
};
