/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-05-24 15:31:18
 * @LastEditTime: 2022-12-05 10:51:39
 * @LastEditors: zhangfengfei
 */
import { tableConfig } from "@/common/utils/config";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import type { ModalState } from "@/hooks/useModal";
import useModal from "@/hooks/useModal";
import { getPrivilegeUserList, switchRights } from "@/services/api/rightsManage";
import type { ActionType, ProColumns } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import { Modal, Popconfirm, Space, message } from "antd";
import type { FC } from "react";
import { useRef, useState } from "react";
import { useModel } from "@umijs/max";
import LogModal from "./LogModal";

type EquityDetailProps = ModalState & {
  rightsItem?: API.RightsListItem;
  actionRef?: React.MutableRefObject<ActionType | undefined>;
};

/**
 * @description: 权益明细
 */
const EquityDetail: FC<EquityDetailProps> = ({ visible, setVisible, rightsItem }) => {
  const { initialState } = useModel("@@initialState");

  const { scenicId } = initialState!.scenicInfo!;
  // 特权用户 Item
  const [privilegeUserItem, setPrivilegeUserItem] = useState<API.PrivilegeUserListItem>();

  const actionRef = useRef<ActionType>();

  const logModal = useModal();

  // 禁用/启用
  const onConfirm = async () => {
    if (privilegeUserItem) {
      try {
        await switchRights({
          id: privilegeUserItem.travelCardGoodsTransactionId,
          isEnable: privilegeUserItem.isEnable ^ 1
        });
        message.success("操作成功");
        actionRef.current?.reload();
      } catch (error) {}
    }
  };

  const columns: ProColumns<API.PrivilegeUserListItem>[] = [
    {
      title: "卡号",
      dataIndex: "travelCardGoodsTransactionId",
      search: false
    },

    {
      title: "发卡批次号",
      dataIndex: "sendCardBatchId"
    },
    {
      title: "用户名",
      search: false,
      dataIndex: "userName"
    },
    {
      title: "所属企业",
      dataIndex: "buyerCompanyName"
    },

    {
      title: "身份证号",
      dataIndex: "userIdCard",
      search: false
    },
    {
      title: "生效时间",
      dataIndex: "validityBeginTime",
      search: false
    },
    {
      title: "失效时间",
      dataIndex: "validityEndTime",
      search: false
    },
    {
      title: "日期区间",
      key: "dateRange",
      valueType: "dateRange",
      hideInTable: true,
      search: {
        transform: value => {
          return {
            validityBeginTime: value[0],
            validityEndTime: value[1]
          };
        }
      }
    },
    {
      title: "权益状态",
      dataIndex: "isEnable",
      valueEnum: {
        0: "已失效",
        1: "已生效"
      },
      valueType: "select",
      search: false
    },
    {
      title: "操作",
      key: "option",
      valueType: "option",
      // fixed: 'right',
      renderText: (_, record) => (
        <Space
          size="middle"
          onClick={() => {
            setPrivilegeUserItem(record);
          }}
        >
          <Popconfirm
            title={
              <p>
                <div>确定要{record.isEnable === 1 ? "禁用" : "启用"}该权益吗</div>
              </p>
            }
            onConfirm={onConfirm}
          >
            <a style={record.isEnable == 1 ? { color: "red" } : {}}>{record.isEnable === 1 ? "禁用" : "启用"}</a>
          </Popconfirm>
          <a
            onClick={() => {
              logModal.setVisible(true);
            }}
          >
            续期日志
          </a>
        </Space>
      )
    }
  ];

  return (
    <Modal
      width={1200}
      title="权益明细"
      open={visible}
      footer={false}
      onCancel={() => {
        setVisible(false);
      }}
      destroyOnClose
    >
      <ProTable<API.PrivilegeUserListItem, API.PrivilegeUserListParams>
        {...tableConfig}
        actionRef={actionRef}
        rowKey="travelCardGoodsTransactionId"
        headerTitle={
          <Space size="large">
            <span>权益 ID：{rightsItem?.id}</span>
            <span>权益名称：{rightsItem?.rightsName || "-"}</span>
          </Space>
        }
        pagination={{ defaultPageSize: 10 }}
        params={{ scenicId, rightsId: rightsItem?.id || "" }}
        request={async params => {
          try {
            const { data } = await getPrivilegeUserList(params);

            addOperationLogRequest({
              action: "info",
              content: `查看【${rightsItem?.rightsName}】权益明细`
            });
            return data;
          } catch (error) {
            return {
              data: [],
              total: 0
            };
          }
        }}
        columns={columns}
      />
      {rightsItem && <LogModal privilegeUserItem={privilegeUserItem} rightsItem={rightsItem} {...logModal} />}
    </Modal>
  );
};

export default EquityDetail;
