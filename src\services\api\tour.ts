/**
 * 导览配置
 */
import { request } from "@umijs/max";
import { scenicHost } from ".";
import { getEnv } from "@/common/utils/getEnv";
const { AI_HOST } = getEnv();

const url = (v: string) => scenicHost + v;

/** 新增点位 */
export function addPoint(data: any) {
  return request(url("/navigation/point/add"), {
    method: "POST",
    data
  });
}
/** 修改点位 */
export function editPoint(data: any) {
  return request(url("/navigation/point/update"), {
    method: "PUT",
    data
  });
}
/** 查看点位 */
export function infoPoint(params: any) {
  return request(url("/navigation/point/info"), { params });
}
/** 禁用点位 */
export function enablePoint(data: any) {
  return request(url("/navigation/point/enable"), {
    method: "PUT",
    data
  });
}
/** 删除点位 */
export function deletePoint(params: any) {
  return request(url("/navigation/point/delete/" + params.id), {
    method: "DELETE"
  });
}

// 音频重试
export function reloadAudio(id: string) {
  return request(url(`/navigation/point/retry/audio/${id}`));
}
/** 点位分页 */
export async function pagePoint(params: any) {
  const { data, code } = await request(url("/navigation/point/page"), { params });
  return {
    data: data.data,
    success: code == 20000,
    total: data.total
  };
}
/** 点位列表 */
export async function listPoint(params: any) {
  return await request(url("/navigation/point/list"), { params });
}

/** 新增线路 */
export function addLine(data: any) {
  return request(url("/navigation/line/add"), {
    method: "POST",
    data
  });
}
/** 修改线路 */
export function editLine(data: any) {
  return request(url("/navigation/line/update"), {
    method: "PUT",
    data
  });
}
/** 查看线路 */
export function infoLine(params: any) {
  return request(url("/navigation/line/info/"), { params });
}
/** 禁用线路 */
export function enableLine(data: any) {
  return request(url("/navigation/line/enable"), {
    method: "PUT",
    data
  });
}
/** 删除线路 */
export function deleteLine(params: any) {
  return request(url("/navigation/line/delete/" + params.id), {
    method: "DELETE"
  });
}
/** 线路列表 */
export async function pageLine(params: any) {
  const { data, code } = await request(url("/navigation/line/page"), { params });
  return {
    data: data.data,
    success: code == 20000,
    total: data.total
  };
}
/** 智能生成导游词 */
export async function pineattractionIntroduce(params: any) {
  const { data, code } = await request(AI_HOST + "/algorithm/total-product-microservices/api/navigation/line/page", {
    params
  });
  return {
    data: data.data,
    success: code == 20000,
    total: data.total
  };
}
