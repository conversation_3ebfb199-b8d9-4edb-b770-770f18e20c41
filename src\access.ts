/**
 * 权限配置
 *
 * 命名规范：can[Code]_[action]
 * action = select 时为菜单权限，请省略 _[action]
 * action = open/close 等含有特殊字符时，请省略 / 并采用驼峰命名
 *
 * 备注规范：Ⅰ一级菜单 Ⅱ二级菜单 Ⅲ三级菜单
 * insert：新增
 * delete：删除
 * edit：编辑
 * select： 查看
 * open/close：启用/禁用
 * 其它权限需单独注释
 */
export default function access(initialState: { permissionList?: API.Permission[] }) {
  const { permissionList } = initialState || {};
  function has(code: string, action: string) {
    let boolean = false;
    for (const item of permissionList || []) {
      if (code == item.code && action == item.action) {
        boolean = true;
        break;
      }
    }
    return boolean;
  }
  return {
    // Ⅰ导航栏
    // Ⅱ帮助中心
    canHelp: has('Help', 'select'),

    // Ⅰ首页
    canHomePage: has('HomePage', 'select'),

    // Ⅰ系统设置
    // Ⅱ风控管理
    canRiskManagement: has('RiskManagement', 'select'),
    canRiskManagement_accountant: has('RiskManagement', 'accountant'), // 复核员信息
    canRiskManagement_openClose: has('RiskManagement', 'open/close'),
    // Ⅱ操作日志
    canOperationLog: has('OperationLog', 'select'),

    // Ⅰ用户中心
    // Ⅱ用户管理
    canOrganizationManage: has('OrganizationManage', 'select'),
    canOrganizationManage_insert: has('OrganizationManage', 'insert'),
    canOrganizationManage_insertDepartment: has('OrganizationManage', 'insertDepartment'), // 新增部门
    canOrganizationManage_revisePassword: has('OrganizationManage', 'revisePassword'), // 重置密码
    canOrganizationManage_deleteDepartment: has('OrganizationManage', 'deleteDepartment'), // 删除部门
    canOrganizationManage_edit: has('OrganizationManage', 'edit'),
    canOrganizationManage_openClose: has('OrganizationManage', 'open/close'),
    canOrganizationManage_invite: has('OrganizationManage', 'invite'), // 邀请
    canOrganizationManage_authorityCopy: has('OrganizationManage', 'authorityCopy'), // 权限拷贝
    // Ⅱ权限管理
    canRoleManage: has('RoleManage', 'select'),
    canRoleManage_insert: has('RoleManage', 'insert'),
    canRoleManage_delete: has('RoleManage', 'delete'),
    canRoleManage_edit: has('RoleManage', 'edit'),
    canRoleManage_openClose: has('RoleManage', 'open/close'),
    // Ⅱ用户审批
    canUserApprove: has('UserApprove', 'select'),
    canUserApprove_refuseAgree: has('UserApprove', 'refuse/agree'), // 审批

    // Ⅰ基础信息配置
    // Ⅱ企业信息
    canEnterpriceInfo: has('EnterpriceInfo', 'select'),
    canEnterpriceInfo_edit: has('EnterpriceInfo', 'edit'),
    // Ⅱ景区信息
    canScenicInfo: has('ScenicInfo', 'select'),
    canScenicInfo_edit: has('ScenicInfo', 'edit'),
    // Ⅱ全局配置
    canGlobalSettings: has('GlobalSettings', 'select'),
    canGlobalSettings_realNameEdit: has('GlobalSettings', 'realnameedit'), // 实名信息编辑
    canGlobalSettings_paramEdit: has('GlobalSettings', 'paramedit'), // 参数配置
    // Ⅱ官网配置
    canOfficialSiteConfig: has('OfficialSiteConfig', 'select'),
    canOfficialSiteConfig_edit: has('OfficialSiteConfig', 'edit'),
    // Ⅱ景区节假日管理
    canHolidays: has('Holidays', 'select'),

    // Ⅰ基础设施管理
    // Ⅱ检票设备管理
    // Ⅲ检票点
    canCheckPoint: has('CheckPoint', 'select'),
    canCheckPoint_insert: has('CheckPoint', 'insert'),
    canCheckPoint_delete: has('CheckPoint', 'delete'),
    canCheckPoint_edit: has('CheckPoint', 'edit'),
    canCheckPoint_openClose: has('CheckPoint', 'open/close'),
    // Ⅲ检票设备
    canCheckDevice: has('CheckDevice', 'select'),
    canCheckDevice_insert: has('CheckDevice', 'insert'),
    canCheckDevice_delete: has('CheckDevice', 'delete'),
    canCheckDevice_edit: has('CheckDevice', 'edit'),
    canCheckDevice_openClose: has('CheckDevice', 'open/close'),
    // Ⅱ售票设备管理
    // Ⅲ售票点
    canSalesSite: has('SalesSite', 'select'),
    canSalesSite_insert: has('SalesSite', 'insert'),
    canSalesSite_delete: has('SalesSite', 'delete'),
    canSalesSite_edit: has('SalesSite', 'edit'),
    canSalesSite_openClose: has('SalesSite', 'open/close'),
    // Ⅲ售票设备
    canSalesDevice: has('SalesDevice', 'select'),
    canSalesDevice_insert: has('SalesDevice', 'insert'),
    canSalesDevice_delete: has('SalesDevice', 'delete'),
    canSalesDevice_edit: has('SalesDevice', 'edit'),
    canSalesDevice_openClose: has('SalesDevice', 'open/close'),
    // Ⅱ硬件设备管理
    // Ⅲ视频监控设备
    canHardwareVideo: has('HardwareVideo', 'select'),
    canHardwareVideo_insert: has('HardwareVideo', 'insert'),
    canHardwareVideo_delete: has('HardwareVideo', 'delete'),
    canHardwareVideo_edit: has('HardwareVideo', 'edit'),
    canHardwareVideo_openClose: has('HardwareVideo', 'open/close'),
    // Ⅲwifi点
    canHardwareWIFI: has('HardwareWIFI', 'select'),
    canHardwareWIFI_insert: has('HardwareWIFI', 'insert'),
    canHardwareWIFI_delete: has('HardwareWIFI', 'delete'),
    canHardwareWIFI_edit: has('HardwareWIFI', 'edit'),
    canHardwareWIFI_openClose: has('HardwareWIFI', 'open/close'),
    // Ⅲ广播
    canHardwareBoardcast: has('HardwareBoardcast', 'select'),
    canHardwareBoardcast_insert: has('HardwareBoardcast', 'insert'),
    canHardwareBoardcast_delete: has('HardwareBoardcast', 'delete'),
    canHardwareBoardcast_edit: has('HardwareBoardcast', 'edit'),
    canHardwareBoardcast_openClose: has('HardwareBoardcast', 'open/close'),
    // ⅢSOS报警点
    canHardwareSOS: has('HardwareSOS', 'select'),
    canHardwareSOS_insert: has('HardwareSOS', 'insert'),
    canHardwareSOS_delete: has('HardwareSOS', 'delete'),
    canHardwareSOS_edit: has('HardwareSOS', 'edit'),
    canHardwareSOS_openClose: has('HardwareSOS', 'open/close'),
    // Ⅱ景区项目管理
    // Ⅲ景区娱乐项目
    canProjectEntertainment: has('ProjectEntertainment', 'select'),
    canProjectEntertainment_insert: has('ProjectEntertainment', 'insert'),
    canProjectEntertainment_delete: has('ProjectEntertainment', 'delete'),
    canProjectEntertainment_edit: has('ProjectEntertainment', 'edit'),
    canProjectEntertainment_openClose: has('ProjectEntertainment', 'open/close'),
    // Ⅲ游客服务项目
    canProjectService: has('ProjectService', 'select'),
    canProjectService_insert: has('ProjectService', 'insert'),
    canProjectService_delete: has('ProjectService', 'delete'),
    canProjectService_edit: has('ProjectService', 'edit'),
    canProjectService_openClose: has('ProjectService', 'open/close'),

    // Ⅰ规则管理
    // Ⅱ出票规则
    canSalesRule: has('SalesRule', 'select'),
    canSalesRule_insert: has('SalesRule', 'insert'),
    canSalesRule_delete: has('SalesRule', 'delete'),
    canSalesRule_edit: has('SalesRule', 'edit'),
    canSalesRule_openClose: has('SalesRule', 'open/close'),
    // Ⅱ退票规则
    canRefundRule: has('RefundRule', 'select'),
    canRefundRule_insert: has('RefundRule', 'insert'),
    canRefundRule_delete: has('RefundRule', 'delete'),
    canRefundRule_edit: has('RefundRule', 'edit'),
    canRefundRule_openClose: has('RefundRule', 'open/close'),
    // Ⅱ检票规则
    canCheckRule: has('CheckRule', 'select'),
    canCheckRule_insert: has('CheckRule', 'insert'),
    canCheckRule_delete: has('CheckRule', 'delete'),
    canCheckRule_edit: has('CheckRule', 'edit'),
    canCheckRule_openClose: has('CheckRule', 'open/close'),

    // Ⅰ票务管理
    // Ⅱ门票设置
    canTicketSet: has('TicketSet', 'select'),
    canTicketSet_insert: has('TicketSet', 'insert'),
    canTicketSet_delete: has('TicketSet', 'delete'),
    canTicketSet_edit: has('TicketSet', 'edit'),
    canTicketSet_openClose: has('TicketSet', 'open/close'),
    canTicketSet_goodsSelect: has('TicketSet', 'goodsSelect'), // 商品管理
    // Ⅱ库存创建
    canStorageManage: has('StorageManage', 'select'),
    canStorageManage_insert: has('StorageManage', 'insert'),
    canStorageManage_delete: has('StorageManage', 'delete'),
    canStorageManage_edit: has('StorageManage', 'edit'),
    canStorageManage_openClose: has('StorageManage', 'open/close'),
    // Ⅱ门票销售
    canTicketSalesManagement: has('TicketSalesManagement', 'select'),
    canTicketSalesManagement_delete: has('TicketSalesManagement', 'delete'),
    // Ⅱ门票核销
    canVerificationRecord: has('VerificationRecord', 'select'),

    // Ⅰ权益卡管理
    // Ⅱ权益卡设置
    canTravelCard: has('TravelCard', 'select'),
    canTravelCard_insert: has('TravelCard', 'insert'),
    canTravelCard_delete: has('TravelCard', 'delete'),
    canTravelCard_edit: has('TravelCard', 'edit'),
    canTravelCard_openClose: has('TravelCard', 'open/close'),
    canTravelCard_goodsSelect: has('TravelCard', 'goodsSelect'), // 商品管理
    // Ⅱ库存创建
    canTravelCardStorageManage: has('TravelCardStorageManage', 'select'),
    canTravelCardStorageManage_insert: has('TravelCardStorageManage', 'insert'),
    canTravelCardStorageManage_delete: has('TravelCardStorageManage', 'delete'),
    canTravelCardStorageManage_edit: has('TravelCardStorageManage', 'edit'),
    canTravelCardStorageManage_openClose: has('TravelCardStorageManage', 'open/close'),
    // Ⅱ权益卡审核
    canCheckList: has('CheckList', 'select'),
    canCheckList_passReject: has('CheckList', 'pass/reject'), // 通过/驳回
    canCheckList_selectDetail: has('CheckList', 'selectDetail'), // 查看详情
    // Ⅱ权益卡权益
    canRightsManage: has('RightsManage', 'select'),
    canRightsManage_equityDetail: has('RightsManage', 'equityDetail'), // 权益明细
    canRightsManage_selectPrivilege: has('RightsManage', 'selectPrivilege'), // 查看所有特权
    // 工单管理
    canWorkOrder_select: has('WorkOrder', 'select'),
    canWorkOrder_add: has('WorkOrder', 'add'),
    canWorkOrder_edit: has('WorkOrder', 'edit'),
    canApplyRecord_select: has('ApplyRecord', 'select'),
    canMyWorkOrder_select: has('MyWorkOrder', 'select'),
  };
}
