// import { request } from './api';
import { message } from "antd";
import { request } from "@umijs/max";
import { scenicHost } from ".";
import { getEnv } from "@/common/utils/getEnv";

/** 获取景区列表 GET /scenic/pageList */
export async function getScenicPageList(
  params: {
    // query
    /** 当前的页码 */
    current?: number;
    /** 页面的容量 */
    pageSize?: number;
  },
  options?: Record<string, any>
) {
  //处理省市区
  const paramsObj = {
    ...params,
    provinceCode: params?.provice?.[0]?.addressId,
    cityCode: params?.provice?.[1]?.addressId,
    areaCode: params?.provice?.[2]?.addressId
  };
  delete paramsObj?.provice; //删除无用参数

  const { code, data } = await request(`${scenicHost}/scenic/pageList`, {
    method: "GET",
    params: {
      ...paramsObj
    },
    ...(options || {})
  });

  data?.data?.map((e: any, i: any) => {
    // 新增 id
    e.id = i;
    // 处理启用状态的样式
    e.isEnable = {
      color: e.isEnable === "1" ? "blue" : "red",
      text: e.isEnable === "1" ? "启用" : "禁用"
    };
  });
  return {
    data: data?.data,
    // success 请返回 true，
    // 不然 table 会停止解析数据，即使有数据
    success: code === 20000,
    // 不传会使用 data 的长度，如果是分页一定要传
    total: data?.total //TODO:后端接口报错时页面会报错
  };
}

/** 编辑景区详情 post  */
export function editScenicInfo(params: any, options?: Record<string, any>) {
  return request<API.RuleList>(`${scenicHost}/scenic/update`, {
    method: "POST",
    data: params,
    ...(options || {})
  });
}

/** 获取企业信息列表 GET /Co/pageList */
export async function getCoPageList(
  params: {
    // query
    /** 当前的页码 */
    pageNum?: number;
    /** 页面的容量 */
    pageSize?: number;
  },
  options?: Record<string, any>
) {
  //处理省市区
  const paramsObj: any = {
    ...params,
    coProvinceCode: params?.provice?.[0]?.addressId,
    coCityCode: params?.provice?.[1]?.addressId,
    coAreaCode: params?.provice?.[2]?.addressId
  };
  delete paramsObj?.provice; //删除无用参数

  const { code, data } = await request(`${scenicHost}/co/pageList`, {
    method: "GET",
    params: {
      ...paramsObj
    },
    ...(options || {})
  });

  // 处理启用状态的样式
  data?.data?.map((e: any) => {
    e.isEnable = {
      color: e.isEnable === "1" ? "blue" : "red",
      text: e.isEnable === "1" ? "启用" : "禁用"
    };
  });

  return {
    data: data.data,
    // success 请返回 true，
    // 不然 table 会停止解析数据，即使有数据
    success: code === 20000,
    // 不传会使用 data 的长度，如果是分页一定要传
    total: data.total
  };
}
// 企业管理列表
export async function apiCoList(params: any) {
  try {
    const { data } = await request(`${scenicHost}/co/pageList`, {
      method: "GET",
      params
    });
    return {
      success: true,
      data: data.data,
      total: data.total
    };
  } catch (error) {
    return {};
  }
}

/** 获取景区企业信息列表 GET /Co/pageList */
export async function getCo(scenicId: string, params?: {}, options?: Record<string, any>) {
  const { code, data } = await request(`${scenicHost}/scenic/getCoInfo/${scenicId}`, {
    method: "GET",
    params: {
      appId: JSON.parse(sessionStorage.getItem("scenicInfo"))?.appId,
      ...params
    },
    ...(options || {})
  });
  return {
    data: data,
    // success 请返回 true，
    // 不然 table 会停止解析数据，即使有数据
    success: code === 20000
    // 不传会使用 data 的长度，如果是分页一定要传
  };
}

/** 获取省市区 GET /address/info */
export async function getProvince(
  params: {
    // query
    /** 父级 id */
    id?: number;
  },
  options?: Record<string, any>
) {
  const { code, data, msg }: any = await request<API.RuleList>(`${scenicHost}/address/info`, {
    method: "GET",
    params: {
      ...params
    },
    ...(options || {})
  });
  if (code === 20000) {
    return data;
  } else {
    message.error(msg);
    return [];
  }
}

/** 获取企业详情 */
export async function getCoInfo(params: any) {
  const { data }: any = await request<API.RuleList>(`${scenicHost}/co/info/${params.id}`);
  // 处理省市区数据结构
  data.province = [];
  if (data.coProvinceName) {
    data.province[0] = data.coProvinceName;
  }
  if (data.coCityName) {
    data.province[1] = data.coCityName;
  }
  if (data.coAreaName) {
    data.province[2] = data.coAreaName;
  }
  return data;
}

// 获取景区列表
export function apiScenicList(params: any, options?: Record<string, any>) {
  return request<ResponseListData<any[]>>(`${scenicHost}/scenic/pageList`, {
    method: "GET",
    params: params,
    ...(options || {})
  });
}

// 获取银行列表
export function apiBankList(params: any) {
  return request<any>(`${scenicHost}/bank/list`, {
    method: "GET",
    params: params
  });
}

/** 获取景区详情 GET /scenic/info */
export async function getScenicInfo(id?: string, options?: Record<string, any>) {
  const { IMG_HOST } = getEnv();
  if (!id) return {}; //如果 id 为空，不发送请求 TODO:初始化会发送一次无用请求，暂时这样处理，可能有更优雅的写法？
  const {
    code,
    msg,
    data: { scenic, scenicAddress, scenicAttribute, scenicBusiness, scenicDesc }
  } = await request(`${scenicHost}/scenic/info/` + id, {
    method: "GET",
    ...(options || {})
  });
  if (code !== 20000) {
    message.error(msg);
    return {};
  }
  //处理图片数据结构用于展示
  if (scenicBusiness.picture) {
    scenicBusiness.picture = scenicBusiness.picture.split(",").map((e: any) => {
      return {
        url: IMG_HOST + e, //展示用的路径
        pathUrl: e //返回给后端的参数
      };
    });
  } else {
    scenicBusiness.picture = [];
  }
  if (scenic.scenicLogo) {
    scenic.scenicLogo = scenic.scenicLogo.split(",").map((e: any) => {
      return {
        url: IMG_HOST + e, //展示用的路径
        pathUrl: e //返回给后端的参数
      };
    });
  } else {
    scenic.scenicLogo = [];
  }

  //处理省市区数据结构
  scenicAddress.province = [];
  if (scenicAddress.provinceName) {
    scenicAddress.province[0] = scenicAddress.provinceName;
  }
  if (scenicAddress.cityName) {
    scenicAddress.province[1] = scenicAddress.cityName;
  }
  if (scenicAddress.areaName) {
    scenicAddress.province[2] = scenicAddress.areaName;
  }
  return {
    ...scenic,
    ...scenicAddress,
    ...scenicAttribute,
    ...scenicBusiness,
    ...scenicDesc
  };
}

/**
 * @description: 景区信息查询 (根据景区 ID)
 */
export function getScenicInfoById(params: { id: string }) {
  return request<ResponseData<Scenic.ScenicData>>(`${scenicHost}/scenic/info/${params.id}`, {
    method: "GET",
    params
  });
}

/** 新增或修改景区状态 */
export async function postScenicStatus(params: any, options?: Record<string, any>) {
  return await request<API.RuleList>(`${scenicHost}/scenic/status`, {
    method: "POST",
    data: params,
    ...(options || {})
  });
}

/** 新增或编辑企业状态 */
export async function postCoStatus(params: any, options?: Record<string, any>) {
  return await request<API.RuleList>(`${scenicHost}/co/status`, {
    method: "POST",
    data: params,
    ...(options || {})
  });
}

/** 新增企业详情 */
export async function postCoInfo(params: any, options?: Record<string, any>) {
  return await request<API.RuleList>(`${scenicHost}/co/info`, {
    method: "POST",
    data: params,
    ...(options || {})
  });
}

export function getCompanyInfo(coId: string) {
  return request(`${scenicHost}/co/info/${coId}`);
}

// 同步支付系统企业信息
export function coSyncAuth(coCode: string) {
  return request<any>(`${scenicHost}/co/sync/auth/info`, {
    method: "PUT",
    params: { coCode }
  });
}
// 落地页，查询信用号是否占用
export function apiCheckCoCode(id: string) {
  return request<any>(`${scenicHost}/co/coCode/auth?coCode=${id}`, {
    method: "GET"
  });
}

/** 编辑企业详情  */
export function updateCoInfo(params: any, options?: Record<string, any>) {
  return request<API.RuleList>(`${scenicHost}/co/info`, {
    method: "PUT",
    data: params,
    ...(options || {})
  });
}

/** 删除企业 delete /Co/del/{id} */
export async function deleteCo(id: string, options?: Record<string, any>) {
  return await request<API.RuleList>(`${scenicHost}/co/del/` + id, {
    method: "DELETE",
    ...(options || {})
  });
}

/** 删除景区 delete /scenic/del/{id} */
export async function deleteScenic(id: string, options?: Record<string, any>) {
  return await request<API.RuleList>(`${scenicHost}/scenic/del/` + id, {
    method: "DELETE",
    ...(options || {})
  });
}

/** 消息列表 */
export async function apiMessageList(params?: Record<string, any>) {
  return await request<API.RuleList>(`${scenicHost}/message/pageList`, {
    method: "GET",
    params: {
      ...params
    }
  });
}

/** 已读消息 */
export async function apiMessageRead(id: string | number) {
  return await request<API.RuleList>(`${scenicHost}/message/messageInfo/${id}`, {
    method: "PUT"
  });
}

/** 已办列表 */
export async function apiProcessedFlowBizList(params?: Record<string, any>) {
  return await request<API.RuleList>(`${scenicHost}/flowBiz/processedFlowBizPageList`, {
    method: "GET",
    params: {
      ...params
    }
  });
}

/** 待办列表 */
export async function apiProcessingFlowBizList(params?: Record<string, any>) {
  return await request<API.RuleList>(`${scenicHost}/flowBiz/processingFlowBizPageList`, {
    method: "GET",
    params: {
      ...params
    }
  });
}

/** 审批流程详情 */
export async function apiFlowBizDetail(params?: Record<string, any>) {
  return await request<API.RuleList>(`${scenicHost}/flowBiz/flowBizList`, {
    method: "GET",
    params: {
      ...params
    }
  });
}

/** 审核待办 */
export async function apiFlowBizApprove(params?: Record<string, any>) {
  return await request<API.RuleList>(`${scenicHost}/flowBiz/approve`, {
    method: "POST",
    data: params
  });
}

/** 获取消息未读数量 */
export async function apiUnReadMessageCount(params: any) {
  return await request<API.RuleList>(`${scenicHost}/message/noAuth/getUnReadMessageCount`, {
    method: "GET",
    params: {
      ...params
    }
  });
}

/** 获取审批人代办数量 */
export async function apiProcessingFlowBizCount(params: any) {
  return await request<API.RuleList>(`${scenicHost}/flowBiz/noAuth/getProcessingFlowBizCount`, {
    method: "GET",
    params: {
      ...params
    }
  });
}

/** 获取业务审批流程列表 */
export async function apiFlowBizPageList(params: any) {
  return await request<API.RuleList>(`${scenicHost}/flowBiz/FlowBizPageList`, {
    method: "GET",
    params: {
      ...params
    }
  });
}

/** 获取业务规则列表 */
export async function apiFlowRulePageList(params: any) {
  return await request<API.RuleList>(`${scenicHost}/flowRule/pageList`, {
    method: "GET",
    params: {
      ...params
    }
  });
}

/** 更新业务规则 */
export async function apiUpdateFlowRule(params: any) {
  return await request<API.RuleList>(`${scenicHost}/flowRule/rule`, {
    method: "PUT",
    data: params
  });
}

/** 获取规则管理详情 */
export async function apiFlowRuleManage(params: any) {
  return await request<API.RuleList>(`${scenicHost}/flowRule/ruleManage`, {
    method: "GET",
    params: {
      ...params
    }
  });
}

/** 新增规则管理 */
export async function apiPostFlowRuleManage(params: any) {
  return await request<API.RuleList>(`${scenicHost}/flowRule/ruleManage`, {
    method: "POST",
    data: params
  });
}

/** 景区信息查询 (根据唯一标识) */
export async function apiScenicUniqueIdentity(uniqueIdentity: string) {
  return await request<any>(`${scenicHost}/scenic/noAuth/uniqueIdentity/info/${uniqueIdentity}`, {
    method: "GET"
  });
}

// 上传截图存证
export async function apiUploadEvidence(params: any) {
  return await request<any>(`${scenicHost}/scenic/expireScenicImg`, {
    method: "POST",
    data: params
  });
}

//授权基础默认权限
export async function roleBasePermission(params: any) {
  return request(`${scenicHost}/role/basePermission`, {
    method: "PUT",
    data: params
  });
}

/** 订单管理列表 */
export async function apiOderPageList(params: any) {
  // const { distributorId, orderId } = params
  // console.log('params111', params)
  return await request<API.RuleList>(`${scenicHost}/order/pageList`, {
    method: "GET",
    //   data: (params),
    params: {
      // distributorId: params.distributorId,
      // orderId: params.orderId
      ...params
    }
  });
}

//订单详情列表
export async function apiOderPageParticularsList(params: any) {
  return await request<API.RuleList>(`${scenicHost}/order/info/${params}`, {
    method: "GET"
    //   data: (params),
    // data: (params),
    // params: {
    //   // distributorId: params.distributorId,
    //   // orderId: params.orderId
    //   ...params
    // },
  });
}
//官网可视化列表
export async function apiWebsiteList(params: any) {
  return await request<API.RuleList>(`${scenicHost}/websiteControl/officialList`, {
    method: "GET",
    params
  });
}

//新增
export async function apiWebsiteInfo(params: any) {
  console.log(params);
  return await request<API.RuleList>(`${scenicHost}/websiteControl/info`, {
    method: "POST",
    data: params
    // params: {
    //   // distributorId: params.distributorId,
    //   // orderId: params.orderId
    //   ...params
    // },
  });
}
//更新
export async function apiWebsiteInfoId(params: any) {
  console.log(params);
  return await request<API.RuleList>(`${scenicHost}/websiteControl/info/${params}`, {
    method: "GET"

    // data: (params),
    // params: {
    //   // distributorId: params.distributorId,
    //   // orderId: params.orderId
    //   ...params,
    // },
  });
}
//官网模板查询所有列表
export async function apiTicketList(params: any) {
  console.log(params);
  return await request<API.RuleList>(`${scenicHost}/websiteControl/ticketList`, {
    method: "POST",
    data: params
  });
}

//官网模板查询所有列表
export async function apiTemplateGoodsList(params: any) {
  console.log(params);
  return await request<API.RuleList>(`${scenicHost}/simpleGoods/templateGoodsList`, {
    method: "GET",
    params: {
      ...params
    }
  });
}

export async function apiWebsiteControl(params: any) {
  console.log(params);
  return await request<API.RuleList>(`${scenicHost}/websiteControl/releaseTemplate`, {
    method: "POST",
    data: params
  });
}

export interface Root {
  current: number;
  pageSize: number;
  records: NoticeItem[];
  total: number;
}

interface NoticeItem {
  isClose: number;
  isEnable: number;
  noticeContent: string;
  noticeDisplayBeginTime: string;
  noticeDisplayEndTime: string;
  noticeId: string;
  noticePosition: number;
  noticeType: number;
  noticeUrl: string;
  receiveNameList: ReceiveNameListItem[];
}

interface ReceiveNameListItem {
  id: string;
  name: string;
}

//公告列表
export async function getNoticeList(params: any) {
  return request<
    ResponseData<{
      current: number;
      pageSize: number;
      records: NoticeItem[];
      total: number;
    }>
  >(`${scenicHost}/notice/list`, {
    method: "GET",
    params: {
      ...params
    }
  });
}

// 景区列表
export async function apiScenicListByRegistration({ registrationNumber }) {
  return await request<API.RuleList>(`${scenicHost}/bourse/readsByRNum/${registrationNumber}`, {
    method: "GET"
  });
}

// 退订单列表
export async function apiOrderCustomList(params: any) {
  return await request<API.RuleList>(`${scenicHost}/order/customList`, {
    method: "GET",
    params: {
      ...params
    }
  });
}
//退订单详情
export async function apiOrderCustomDetail(params: any) {
  return await request<API.RuleList>(`${scenicHost}/order/info/${params}`, {
    method: "GET"
  });
}

//景区官网模板
export async function apiWebsiteControlTemp(params: any) {
  return await request<API.RuleList>(`${scenicHost}/websiteControlTemp/templateInfo/${params}`, {
    method: "GET"
  });
}

//景区官网编辑商品购买照片
export async function apiBuyImgUrl(params: any) {
  return await request<API.RuleList>(`${scenicHost}/simpleGoods/buyUrl`, {
    method: "PUT",
    data: params
  });
}

// 查询区块链组织信息
export async function apiScenicOrgInfo(params: any) {
  return await request<API.RuleList>(`${scenicHost}/scenic/orgInfo/${params}`, {
    method: "GET"
  });
}

//查看申请日志
export async function apiServiceLogList(params: any) {
  return await request<API.RuleList>(`${scenicHost}/scenic/serviceLogList/${params}`, {
    method: "GET"
  });
}

// 景区数据大屏 ID
export async function apiGenerateScenicScreen(id: string) {
  return await request(`${scenicHost}/scenic/generateScenicScreen/${id}`);
}

//景区登录注册
export async function apiAppScenicExperienceInfo(params) {
  return await request<API.RuleList>(`${scenicHost}/appScenic/experienceInfo`, {
    method: "POST",
    data: params
  });
}

//发送短信
export async function apiUserOtp(params) {
  return await request<API.RuleList>(`${scenicHost}/user/otp`, {
    method: "GET",
    params: {
      ...params
    }
  });
}

//用户关联公司
export async function apiAssociateCompany(params: any) {
  return await request<API.RuleList>(`${scenicHost}/orgStructure/associateCompany`, {
    method: "POST",
    data: params
  });
}

/** 登录接口 POST /user/no_auth/login */
export async function apiUserNoAuthLogin(params) {
  const { CAS_API_HOST } = getEnv();
  return request<API.LoginResult>(`${CAS_API_HOST}/user/login`, {
    method: "POST",
    data: params
    // headers: {
    //   'Content-Type': 'application/json',
    // },
    // data: { autoRegister: true, ...body },
    // ...(options || {}),
  });
}
// 查询景区所属企业
export async function apiCoInfo(params: any) {
  const id = params.scenicId;
  delete params.scenicId;
  return await request<API.RuleList>(`${scenicHost}/scenic/getCoInfo/${id}`, {
    params
  });
}

/** 获取结算开户信息 */
export async function apiSettlementInfo(creditCode: string) {
  return await request<API.RuleList>(`${scenicHost}/co/settlementInfo`, {
    method: "GET",
    params: {
      id: creditCode
    }
  });
}
/** 试用期续期 */
export function apiRenewScenic(params) {
  return request<any>(`${scenicHost}/scenic/noAuth/renewScenic/${params}`, {
    method: "PUT"
    //  body:JSON.stringify(params)
  });
}

//获取景区风控管理信息
export async function apiScenicControlList(id) {
  return request<ResponseData<Scenic.ControlListItem[]>>(`${scenicHost}/controlConf/scenicControlList/${id}`, {
    method: "GET"
  });
}
//获取企业风控管理信息
export async function apiCoControlList(params) {
  return await request<API.RuleList>(`${scenicHost}/controlConf/coControlList/${params}`, {
    method: "GET"
  });
}
// 查看景区复核员信息
export async function apiScenicPerson(params) {
  return await request<API.RuleList>(`${scenicHost}/controlConf/scenicPerson/${params}`, {
    method: "GET"
  });
}

// 更新或编辑复景区核员
export async function apiPostScenicPerson(params) {
  return await request<API.RuleList>(`${scenicHost}/controlConf/scenicPerson`, {
    method: "POST",
    data: params
  });
}
// 编辑操作配置
export async function apiControlConf(params) {
  return await request<API.RuleList>(`${scenicHost}/controlConf/controlConf`, {
    method: "PUT",
    data: params
  });
}
// 给复核员发送短信
export async function apiControlConfMessage(params) {
  return await request<API.RuleList>(`${scenicHost}/controlConf/message`, {
    method: "PUT",
    data: params
  });
}
// 校验是否有权限
export async function apiAuthority(params) {
  return await request<API.RuleList>(`${scenicHost}/controlConf/authority`, {
    method: "PUT",
    data: params
  });
}

// 删除订单
export async function apiDeleteTicket(params) {
  return await request<API.RuleList>(`${scenicHost}/order/ticket`, {
    method: "DELETE",
    data: params
  });
}
// 赋予相关微服务权限
export function setServicePermission(params) {
  return request(`${scenicHost}/role/servicePermission`, {
    method: "POST",
    data: params
  });
}

// 根据一组 ID 获取企业信息

export async function apiMyCoInfoList(params) {
  return await request<API.CoListItem>(`${scenicHost}/co/coInfoList`, {
    method: "GET",
    params: {
      ...params
    }
  });
}
//操作指南列表
export async function apiWikiList(params: any) {
  return await request<API.RuleList>(`${scenicHost}/wiki/list`, {
    method: "GET",
    params: {
      ...params
    }
  });
}

/**
 * @description: 根据景区 id 获取企业列表
 */
export function getCoListByScenicId(params: { scenicId: string }) {
  return request<ResponseData<API.CoListItem[]>>(`${scenicHost}/scenic/getCoInfo/${params.scenicId}`);
}

interface CheckInviteToAdminData {
  companyId: string;
  phone: string;
  uniqueIdentity: string;
  userId: string;
  companyName: string;
  scenicId: string;
  scenicName: string;
  url: string;
}
/**
 * @description: 给企业联系人发送成为景区管理员
 */
export function checkInviteToAdmin(params: { code: string }) {
  return request<ResponseData<CheckInviteToAdminData>>(`${scenicHost}/user/scenicManager/${params.code}`, {
    method: "PUT",
    data: params
  });
}

/**
 * @description: 邀请用户成为管理员授权接口
 * @see https://test.shukeyun.com/scenic/api-v2/doc.html#/%E6%99%AF%E5%8C%BA%E6%9D%83%E9%99%90%E6%A8%A1%E5%9D%97/%E8%A7%92%E8%89%B2%E6%A8%A1%E5%9D%97/getServicePermissionUsingPOST
 */
export function inviteToAdmin(params: {
  companyId: string;
  userId: string;
  scenicId?: string;
  type: string;
  appId: string;
}) {
  return request<ResponseData<null>>(`${scenicHost}/role/servicePermission`, {
    method: "POST",
    data: params
  });
}
/**
 * @description: 查询用户所在部门
 * @see https://dev.shukeyun.com/scenic/api-v2/doc.html#/%E6%99%AF%E5%8C%BA%E6%9D%83%E9%99%90%E6%A8%A1%E5%9D%97/%E7%BB%84%E7%BB%87%E6%9E%B6%E6%9E%84%E6%A8%A1%E5%9D%97/queryUserDeptUsingGET
 */
export function getUserDept(params: { companyId: string; userId: string }) {
  return request<ResponseData<API.DepartmentItem[]>>(`${scenicHost}/orgStructure/queryUserDept`, {
    params
  });
}

//================ 官网配置 ================
//================ 帮助中心 ================

// 目录新增
export function categoryAdd(data: any) {
  return request(`${scenicHost}/official/help/article/category/add`, {
    method: "POST",
    data
  });
}
// 目录编辑
export function categoryEdit(data: any) {
  return request(`${scenicHost}/official/help/article/category/update`, {
    method: "PUT",
    data
  });
}
// 目录文章列表
export function categoryTree(params: any) {
  return request(`${scenicHost}/official/help/article/category/tree`, {
    params
  });
}
// 目录删除
export function categoryDel(id: any) {
  return request(`${scenicHost}/official/help/article/category/delete/${id}`, {
    method: "DELETE"
  });
}
// 文章新增
export function articleAdd(data: any) {
  return request(`${scenicHost}/official/help/article/add`, {
    method: "POST",
    data
  });
}
// 文章编辑
export function articleEdit(data: any) {
  return request(`${scenicHost}/official/help/article/update`, {
    method: "PUT",
    data
  });
}
// 文章禁启用
export function articleEnable(data: any) {
  return request(`${scenicHost}/official/help/article/enable`, {
    method: "PUT",
    data
  });
}
// 文章删除
export function articleDel(id: any) {
  return request(`${scenicHost}/official/help/article/delete/${id}`, {
    method: "DELETE"
  });
}
// 文章列表
export function articlePage(params: any) {
  return request(`${scenicHost}/official/help/article/page`, {
    params
  });
}
// 文章目录排序
export function articleCategorySort(data: any) {
  return request(`${scenicHost}/official/help/article/category/updateSort`, {
    method: "PUT",
    data
  });
}

//================ 帮助中心 end ================
//================ 官网配置 end ================

//================ 官网导航 start ================
// 获取导航列表数据
export function apiGetNavigationList(params: any) {
  return request(`${scenicHost}/official/navigation/list`, {
    method: "GET",
    params
  });
}

// 保存导航列表数据
export function apiSaveNavigationList(params: any) {
  return request(`${scenicHost}/official/navigation/add`, {
    method: "POST",
    data: params
  });
}

// 链接名称下的 modal 数据
// 景区详情页
export async function apiGetScenicDetailList(params: any) {
  const { data, code } = await request(`${scenicHost}/ticketAgent/official/recommend/scenic`, {
    method: "GET",
    params
  });
  return {
    data: data.records,
    success: code == 20000,
    total: data.total
  };
}
//================ 官网导航 end ================

//================ 官网装修 start ================
/** 删除页面 */
export function apiDelRenovate(id: string) {
  return request(`${scenicHost}/official/design/delete/${id}`, {
    method: "DELETE"
  });
}
/** 批量删除页面 */
export function apiBatchDelRenovate(ids: string) {
  return request(`${scenicHost}/official/design/batch/delete/${ids}`, {
    method: "DELETE"
  });
}
/** 页面分页 */
export async function apiGetRenovateList(params: any) {
  const { data, code } = await request(`${scenicHost}/official/design/page`, { params });
  return {
    data: data.data,
    success: code == 20000,
    total: data.total
  };
}
/** 修改状态 */
export function apiEditRenovateState(data: any) {
  return request(`${scenicHost}/official/design/state`, {
    method: "PUT",
    data
  });
}
//================ 官网装修 end ================

/**************************** 动态引导 ****************************/
// 查询接口
export async function getGuideListReq(params = {}) {
  const data = {
    companyId: localStorage.getItem("currentCompanyId"),
    systemId: "HJY",
    scenicId: JSON.parse(sessionStorage.getItem("scenicInfo") || "{}")?.scenicId,
    ...params
  };
  const guideRes = await request(`${scenicHost}/admissionFlow/getAdmissionFlow`, {
    method: "GET",
    params: data
  });
  return guideRes;
}
// 更新
export async function updateGuideListReq(params = {}) {
  const data = {
    companyId: localStorage.getItem("currentCompanyId"),
    systemId: "HJY",
    scenicId: JSON.parse(sessionStorage.getItem("scenicInfo") || "{}")?.scenicId,
    ...params
  };
  const guideRes = await request(`${scenicHost}/admissionFlow/updateAdmissionFlow`, {
    method: "POST",
    data
  });
  return guideRes;
}
