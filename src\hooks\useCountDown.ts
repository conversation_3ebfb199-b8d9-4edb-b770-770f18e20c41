/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-07-13 17:20:48
 * @LastEditTime: 2022-07-18 21:26:15
 * @LastEditors: zhangfengfei
 */
import { useEffect, useMemo, useRef, useState } from 'react';

// 倒计时
export default function useCountDown(initCount = 60, callBack = () => {}) {
  const timeId = useRef<{ id: number }>({ id: -1 });
  const [count, setCount] = useState(initCount);

  const start = () => {
    clearInterval(timeId.current.id);
    setCount(initCount);
    // 开始
    timeId.current.id = window.setInterval(() => {
      setCount((count) => count - 1);
    }, 1000);
  };
  useEffect(() => {
    return () => {
      clearInterval(timeId.current.id);
    };
  }, []);

  useEffect(
    () => {
      // 当倒计时为0时 清除定时器
      if (count === 0) {
        clearInterval(timeId.current.id);
        setCount(initCount);
        callBack();
      }
    },
    [count], // 监听 count
  );

  return useMemo(() => ({ count, start, isOver: count === 60 }), [count]);
}
