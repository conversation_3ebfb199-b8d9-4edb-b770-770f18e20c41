# 开发文档

## 技术选型

- [React](https://react.docschina.org/docs/getting-started.html) （框架）
- [Ant Design Pro](https://pro.ant.design/zh-CN/docs/getting-started/) (业务框架)
- [ProComponents](https://procomponents.ant.design/components/) （组件）
- [ Antd Design 4.x](https://4x.ant.design/components/overview-cn/) （组件）
- [TypeScript](https://www.tslang.cn/index.html) （语言风格）

## 开发环境

### 安装

项目使用 [node](http://nodejs.org/) 和 [yarn](https://yarnpkg.com/)，请确保你本地安装了它们。

安装 yarn

```
npm install yarn -g
```

安装依赖

```bash
yarn
```

### 启动

分 dev 开发、test 测试、prod 生产环境，使用不同后缀可接入对应的后台环境接口，如：

```bash
开发环境: yarn start:dev
```

**注意：慧景云启动后需要手动添加对应景区的标志符**，如 wei 景区：

```
http://localhost:8000/#/wei/welcome
```

## 登录

### CAS 系统

[CAS](https://baike.baidu.com/item/CAS/1329561) 是 Central Authentication Service 的缩写，中央认证服务，一种独立开放指令协议，旨在为 Web 应用系统提供一种可靠的[单点登录](https://baike.baidu.com/item/单点登录/4940767?fromModule=lemma_inlink)方法。

公司所有账号统一由 CAS 系统管理，对接 CAS 登录成功后返回当前系统。

### 账号密码

dev 环境 账号：wangdan 密码：12345678

test 环境 账号：xiaoh 密码：123456

生产环境 账号：public 密码：i0651n6r（请勿更改数据）

如果密码错误，请询问测试或开发小伙伴。

## 目录结构

```bash
config
  └── config.xxx.ts     // xxx 环境配置
  └── routes.ts         // 路由文件
  └── proxy.ts          // 代理配置文件
mock					// mock 数据文件
public					// 公共资源
src
  └── common         	// 公共组件
  └── components        // 公共组件
  └── hooks             // 自定义 hooks
  └── pages             // 页面组件
  └── services          // 接口 API
  └── access.ts         // 权限配置文件
  └── app.tsx           // 主入口文件
  └── *                 // 其它
```

参考[Ant Design Pro 目录结构](https://pro.ant.design/zh-CN/docs/folder)

## 部署

上传到 gitlab 会自动构建流水线打包并部署

## 项目规范

### JavaScript 规范

详情阅读[《JavaScript 风格指南》](https://git.shukeyun.com/scenic/common/-/blob/master/docs/JavaScript.md)

### TypeScript 规范

详情阅读[《TypeScript 风格指南》](https://git.shukeyun.com/scenic/common/-/blob/master/docs/typeScript.md)

### 文件夹命名

```
src
├── components
└── pages
    ├── Welcome        // 路由组件下不应该再包含其他路由组件，基于这个约定就能清楚的区分路由组件和非路由组件了
    |   ├── components // 对于复杂的页面可以再自己做更深层次的组织，但建议不要超过三层
    |   ├── Form.tsx
    |   ├── index.tsx  // 页面组件的代码
    |   └── index.less // 页面样式
    ├── Order          // 路由组件下不应该再包含其他路由组件，基于这个约定就能清楚的区分路由组件和非路由组件了
    |   ├── index.tsx
    |   └── index.less
    ├── User
    |   ├── components // group 下公用的组件集合
    |   ├── Login      // group 下的页面 Login
    |   ├── Register   // group 下的页面 Register
    |   └── util.ts    // 这里可以有一些共用方法之类，不做推荐和约束，看业务场景自行做组织
    └── *              // 其它页面组件代码
```

### 接口规范

**所有的接口都以 Promise 形式返回**

```js
interface RightsListParams {
    scenicId: string;
  }

interface RightsListItem {
    id: string;
    isEnable: number;
    rightsName: string;
    scenicType: number;
    travelCardNames: string[];
  };

/** 查询权益列表 */
export function getRightsList(
  params: API.RightsListParams,
  options: Record<string, any> = {},
) {
  return request<ResponseListData<API.RightsListItem[]>>(
    `${getEnv().API_HOST}/rightsService/rights/pageList`,
    {
      method: 'GET',
      params,
      ...options,
    },
  );
}
```

请求参数和返回参数使用 TypeScript 做类型限制

返回结果使用 `src\services\api\typings.d.ts` 中的 `ResponseData` 类型包裹，开发只关注返回中 data 的内容

接口文档地址：[Swagger 接口文档](https://dev.shukeyun.com/scenic/api-v2/doc.html#/%E7%A5%A8%E5%8A%A1%E6%A8%A1%E5%9D%97/%E5%95%86%E5%93%81%E6%A8%A1%E5%9D%97/batchAddGoodsUsingPOST)

### 提交规范

**commit 提交格式**

- 💥 `feat: 添加功能`
- 🐛 `fix: 修复 Bug`
- 📝 `docs: 更新文档`
- 🌷 `UI: 修改样式`
- 🏰 `chore: 更改脚手架`
- 🌐 `locale: 国际化贡献` 上传到 gitlab 主分支（`dev` `test` `canary` `master`）会自动构建流水线打包并部署，

## 统一错误处理

**所有非 20000（业务码）的响应都会被返回拦截器拦截**

```js
// 传送门：统一请求处理
export const request: RequestConfig = {
  requestInterceptors: [authHeaderInterceptor], //请求拦截器
  responseInterceptors: [responseInterceptor], //响应拦截器
  errorConfig: errorConfig //统一错误处理
};

// 统一错误处理
const errorConfig = {
  adaptor: (resData: any) => {
    //此配置只用于错误处理，不影响最终传递给页面的数据格式
    return {
      ...resData,
      success: resData.code === 20000,
      showType: 1, // error display type： 0 silent; 1 message.warn; 2 message.error; 4 notification; 9 page
      errorMessage: resData.msg
    };
  }
};

//响应拦截器
const responseInterceptor: ResponseInterceptor = async (
  response: Response
  // options: RequestOptionsInit,
) => {
  const data = await response.clone().json();
  if (data.code === 40001) {
    //退到首页
    const scenicCode = getScenicIdentifier();

    const { query } = history?.location;
    goToLogin({
      ...query,
      scenicCode
    });
  } else if (data.code === 40003) {
    // 无权限重刷
    // if (history.location.pathname.indexOf('/welcome') !== -1) {
    //   history.push('/welcome');
    //   location.reload();
    // }
  }

  return response;
};
```
