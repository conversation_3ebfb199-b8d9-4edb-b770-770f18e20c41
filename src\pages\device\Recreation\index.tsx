import Province from "@/common/components/Province";
import { getUniqueId } from "@/common/utils/tool";
import {
  apiRecreationAddUpdate,
  apiRecreationDelete,
  apiRecreationDetails,
  apiRecreationEnable,
  apiRecreationPageList
} from "@/services/api/device";
import { PlusOutlined } from "@ant-design/icons";
import ProForm, { ModalForm, ProFormSelect, ProFormText } from "@ant-design/pro-form";
import ProTable from "@ant-design/pro-table";
import { Button, Modal, Tag, message } from "antd";
import { useEffect, useRef, useState } from "react";
import { Access, useAccess, useModel } from "@umijs/max";
import Details from "./Details";

const { confirm } = Modal;

//声明百度地图实例
const BMap = window.BMap;
let map: any;
let myGeo: any;
const Recreation = () => {
  const access = useAccess();
  const formObj = useRef();
  const [formObj2] = ProForm.useForm();
  const actionRef = useRef();
  const [modalVisit, setModalVisit] = useState(false);
  // 【表单】数据绑定
  const [editVisible, setEditVisible] = useState(false);
  //获取当前 ID
  const [Id, setId] = useState(undefined);
  //收集省市区 id
  const [provincesArray, setProvincesArray] = useState([]);
  // 获取景区 ID
  const { initialState }: any = useModel("@@initialState");
  const scenicId = initialState?.scenicInfo?.scenicId;
  console.log(scenicId, initialState);
  //获取景区名称
  const scenicName = initialState?.scenicInfo?.scenicName;

  //储存省市区的 id
  const [areaId, setAreaId] = useState("");
  const [cityId, setCityId] = useState("");
  const [provinceId, setProvinceId] = useState("");
  // let provinces;

  const [initialValues, setInitialValues] = useState({});

  const typeEnum = {
    "0": "休闲场所",
    "1": "KTV",
    "2": "游乐场",
    "3": "茶楼"
  };

  const childRef = useRef();
  useEffect(() => {
    map = new BMap.Map("l-map");
    myGeo = new BMap.Geocoder();
  }, []);

  // 将地址解析结果显示在地图上，并调整地图视野

  const [acceptorData, setAcceptorData] = useState({});

  const geoProvince = ({ province, address }: any) => {
    // map.centerAndZoom(new BMapGL.Point(116.331398,39.897445), 13);

    myGeo.getPoint(
      address,
      (point: any) => {
        // setAccetorData(point)
        if (point) {
          console.log("wwww", point);
          getPoint(point);
        } else {
          message.warn("您选择的地址没有解析到结果，请手动填写经纬度");
          // getPoint(point)
          console.log("iiiiiiiiiiiiiiiiiii");
        }
      },
      province[0].addressName
    );
  };
  // const geoProvince = (address) => {

  //   //创建地址解析器实例
  //   myGeo = new BMap.Geocoder();
  //   myGeo.getPoint(
  //     address.address,
  //     function (point) {
  //       console.log('1111111111111111111', point)
  //       if (point) {
  //         // map.centerAndZoom(point, 18);
  //         // map.removeOverlay(oldMarker);
  //         const marker = new BMap.Marker(point, { title: address.address, enableDragging: true });
  //         console.log('2222222222222', marker)
  //         oldMarker = marker;
  //         // map.addOverlay(marker); // 将标注新增到地图中

  //         // marker.addEventListener('dragend', function () {
  //         //   const nowPoint = marker.getPosition(); // 拖拽完成之后坐标的位置
  //         //   getPoint(nowPoint);
  //         // });
  //         getPoint(point); //送回父级
  //       } else {
  //         // message.warn('您选择的地址没有解析到结果，请手动填写经纬度');
  //       }

  //     },
  //     address.province,
  //   );
  // };

  // // 此处注意 useImperativeHandle 方法的的第一个参数是目标元素的 ref 引用
  // useImperativeHandle(cRef, () => ({
  //   // changeVal 就是暴露给父组件的方法
  //   changeVal: (address) => {
  //     if (address.province) {
  //       // message.info('已自动计算经纬度')
  //       geoProvince(address);
  //     } else {
  //       message.warn('请选择省市区');
  //     }
  //   },
  // }));

  //计算经纬度
  const setPoint = e => {
    geoProvince({
      province: formObj2.getFieldValue("province"),
      address: formObj2.getFieldValue("address")
    });

    // geoProvince(formRef?.current?.getFieldValue('address'))
    // // 传地理位置获取坐标
    // childRef?.current?.changeVal({
    //   province: formRef?.current?.getFieldValue('province'),
    //   address: formRef?.current?.getFieldValue('address'),
    // });
  };
  /*
   *省市区回调
   * */
  const getPoint = ({ lat, lng }) => {
    console.log("yyyyyyyyyyyyyyyyy", lat, lng);
    //设置经纬度
    formObj2.setFieldsValue({
      latitude: lat,
      longitude: lng
    });
  };

  //设置经纬度
  // formRef?.current?.setFieldsValue({
  //   // latitude: acceptorData.Lat,
  //   // longitude: acceptorData.Lng,
  // });

  // 详情
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [detailData, setDetailsData] = useState([]);
  // //获取当前 id
  // const [isId,setIsId]=useState()

  const showModal = async (val: any) => {
    const id = val.id;
    const result: any = await apiRecreationDetails(id);
    const { data } = result;
    console.log("yyds", data, val);
    //回选
    // formObj?.current?.setFieldsValue(data);
    setDetailsData(data);
    setInitialValues(data);
    // setDataSource(data);
    setId(val.id);
    // setIsEnableStatus(data.isEnable);
    // setModalVisit(true);
    setIsModalVisible(true);
  };

  const handleOk = () => {
    setIsModalVisible(false);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  //编辑
  const updateMethod = async (val: any) => {
    const id = val.id;
    const { data } = await apiRecreationDetails(id);

    // const typeMap = {
    //   0: '休闲场所',
    //   1: 'KTV',
    //   2: '游乐场',
    //   3: '茶楼',
    // };
    // data.type = typeMap[data.type];

    formObj2.setFieldsValue({
      ...data,
      type: `${data.type}`,
      province: [data.provinceName, data.cityName, data.areaName]
    });

    setAreaId(data.areaId);
    setCityId(data.cityId);
    setProvinceId(data.provinceId);
    setId(val.id);
    setIsModalVisible(false);
    setModalVisit(true);
  };

  //启用/禁用
  const onStatus = async (val: any) => {
    confirm({
      title: `您确定${val.isEnable == 1 ? "禁用" : "启用"}吗？`,
      onOk: async () => {
        try {
          const result = await apiRecreationEnable(val.id);
          message.success(val.isEnable == 0 ? "启用成功" : "禁用成功");
          console.log(val);
          // getNoticeList()
          // 刷新
          actionRef?.current?.reload();
          setIsModalVisible(false);
        } catch (e) {
          console.error(e);
        }
      },
      onCancel() {
        console.log("Cancel");
      }
    });
  };
  //删除
  // const onDelete = () => { }
  const onDelete = (id: any) => {
    confirm({
      title: `您确定删除吗？`,
      // content: 'Some descriptions',
      onOk: async () => {
        try {
          const result = await apiRecreationDelete(id);
          // console.log(result);
          message.success("删除成功");
          // 刷新
          actionRef?.current?.reload();
          setIsModalVisible(false);
        } catch (e) {
          console.log(e);
        }
      },
      onCancel() {
        console.log("Cancel");
      }
    });
  };

  //选择省市区的方法
  // const provincesMeothd = (val: any) => {
  //   formObj2.setFieldsValue({ province: val });
  //   setProvincesArray(val);
  // };
  //娱乐项目列表详情
  const getRecreationPageList = async params => {
    const pars = { ...params, scenicId };
    try {
      const result = await apiRecreationPageList(pars);
      const { data } = result.data;
      console.log("hujiajia", result);
      return {
        data,
        success: true,
        total: data.total
      };
    } catch (e) {
      console.error(e);
    }
  };

  // 保存数据
  // const submitData = async (val: any) => {
  //   console.log('123131321321231', val);
  //   //省
  //   const provinceName = val.provinceName[0].addressName;
  //   const provinceId = val.provinceName[0].addressId;
  //   //市
  //   const cityName = val.provinceName[1].addressName;
  //   const cityId = val.provinceName[1].addressId;
  //   //区
  //   const areaName = val.provinceName[2].addressName;
  //   const areaId = val.provinceName[2].addressId;
  //   const pras = {
  //     ...val,
  //     scenicId,
  //     provinceName,
  //     provinceId,
  //     cityName,
  //     cityId,
  //     areaName,
  //     areaId,
  //     // isEnable: 1,
  //   };
  //   try {
  //     const result = await apiRecreationAddUpdate(pras);
  //     // message.success('保存成功');
  //     message.success(val.hasOwnProperty('id') ? '编辑成功' : '新增成功');
  //     actionRef?.current?.reload();
  //     setEditVisible(false);
  //   } catch (e) {
  //     console.error(e);
  //   }
  // };

  // 保存数据
  const submitData = async (val: any) => {
    console.log("123131321321231", val, provincesArray);
    if (val.id) {
      if (typeof val.province[0] !== "string") {
        //省
        val.provinceName = val.province[0].addressName;
        val.provinceId = val.province[0].addressId;
        //市
        val.cityName = val.province[1].addressName;
        val.cityId = val.province[1].addressId;
        //区
        val.areaName = val.province[2].addressName;
        val.areaId = val.province[2].addressId;
      } else {
        console.log("123132132hujiajiajiaiajiajaijaiajai");
        //省
        val.provinceName = val.province[0];
        val.provinceId = provinceId;
        //市
        val.cityName = val.province[1];
        val.cityId = cityId;
        //区
        val.areaName = val.province[2];
        val.areaId = areaId;
      }
    } else {
      val.provinceName = val.province[0].addressName;
      val.provinceId = val.province[0].addressId;
      //市
      val.cityName = val.province[1].addressName;
      val.cityId = val.province[1].addressId;
      //区
      val.areaName = val.province[2].addressName;
      val.areaId = val.province[2].addressId;
    }
    const pras = {
      ...val,
      scenicId
      // isEnable: 1,
    };
    console.log("=========edit===============");

    console.log(pras.type, pras);

    // '0': '休闲场所',
    //       '1': 'KTV',
    //       '2': '游乐场',
    //       '3': '茶楼',
    if (pras.type == "休闲场所") {
      pras.type = "0";
    } else if (pras.type == "KTV") {
      pras.type = "1";
    } else if (pras.type == "游乐场") {
      pras.type = "2";
    } else if (pras.tyep == "茶楼") {
      pras.type = "3";
    }

    try {
      const result = await apiRecreationAddUpdate(pras);
      // message.success('保存成功');
      message.success(val.hasOwnProperty("id") ? "编辑成功" : "新增成功");
      actionRef?.current?.reload();
      setEditVisible(false);
      setModalVisit(false);
    } catch (e) {
      console.error(e);
    }
  };

  const columns: any = [
    {
      title: "所属景区",
      dataIndex: "scenicName",
      hideInSearch: true
    },
    {
      title: "景区娱乐项目",
      dataIndex: "enterName",
      fileProps: {
        placeholder: "请输入景区娱乐项目"
      },
      render: (dom, record) => {
        return <a onClick={() => showModal(record)}>{record.enterName}</a>;
      }
    },

    {
      title: "所属类型",
      dataIndex: "type",
      valueType: "select",
      // hideInSearch: true,
      valueEnum: {
        "0": "休闲场所",
        "1": "KTV",
        "2": "游乐场",
        "3": "茶楼"
      },
      render: (dom, record) => {
        if (record.type == 0) {
          return <span>休闲场所</span>;
        } else if (record.type == 1) {
          return <span>KTV</span>;
        } else if (record.type == 2) {
          return <span>游乐场</span>;
        } else if (record.type == 3) {
          return <span>茶楼</span>;
        }
      }
    },
    {
      title: "地址",
      dataIndex: "address",
      hideInSearch: true,
      ellipsis: true,
      width: "10%"
    },
    {
      title: "管理单位",
      dataIndex: "managementUnit",
      // valueType: 'select',
      hideInSearch: true
      // valueEnum: {
      //     '0': {
      //         text: '禁用',
      //     },
      //     '1': {
      //         text: '启用',
      //     },
      // },
      // render: (dom: any, record: any) => {
      //     return (
      //         <Tag color={record.isEnable == 1 ? 'blue' : 'red'}>
      //             {record.isEnable == 1 ? '已启用' : '已禁用'}
      //         </Tag>
      //     );
      // },
      // render: (_, record) => <Tag color={record.isEnable.color}>{record.isEnable.text}</Tag>,
    },
    {
      title: "联系人",
      dataIndex: "contacts",
      hideInSearch: true
    },
    {
      title: "联系方式",
      dataIndex: "contactMobile",
      hideInSearch: true
    },
    {
      title: "状态",
      dataIndex: "isEnable",
      hideInSearch: true,
      valueEnum: {
        "0": {
          text: "禁用"
        },
        "1": {
          text: "启用"
        }
      },
      render: (dom: any, record: any) => {
        return <Tag color={record.isEnable == 1 ? "blue" : "red"}>{record.isEnable == 1 ? "已启用" : "已禁用"}</Tag>;
      }
      // render: (_, record) => <Tag color={record.isEnable.color}>{record.isEnable.text}</Tag>,
    }
    // {
    //   title: '操作',
    //   dataIndex: 'option',
    //   width: '10%',
    //   hideInSearch: true,
    //   render: (dom, record) => {
    //     return (
    //       <Space>
    //         <a onClick={() => updateMethod(record)}>编辑</a>

    //         <Popconfirm
    //           title={`您确定${record.isEnable == 1 ? '禁用' : '启用'}吗？`}
    //           onConfirm={() => onStatus(record)}
    //           icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
    //           onCancel={cancel}
    //           okText="确认"
    //           cancelText="取消"
    //         >
    //           <a href="#"> {record.isEnable == 1 ? '禁用' : '启用'}</a>
    //         </Popconfirm>

    //         <Popconfirm
    //           title="您确定删除吗？"
    //           onConfirm={() => confirm(record.id)}
    //           icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
    //           onCancel={cancel}
    //           okText="确认"
    //           cancelText="取消"
    //         >
    //           <a href="#">删除</a>
    //         </Popconfirm>
    //       </Space>
    //     );
    //   },
    // },
  ];
  const editColumns = [
    {
      columns: [
        {
          title: "所属景区",
          dataIndex: "scenicName",
          name: "scenicName",
          initialValue: scenicName,
          key: "scenicName",
          render: () => {
            return scenicName;
          },
          // valueEnum: {
          //   '0': '公告',
          // },
          formItemProps: {
            // rules: [{ required: true }],
            // disable: false
          },
          fieldProps: {
            disabled: true
          }
        },

        {
          dataIndex: "managementUnit",
          title: "管理单位",
          name: "managementUnit",
          key: "managementUnit",
          fieldProps: {},
          formItemProps: {
            rules: [{ required: true }, { max: 100, message: "最多可输入 100 个字符" }]
          }
        },
        {
          dataIndex: "enterName",
          title: "景区娱乐项目名称",
          ellipsis: true,
          name: "enterName",
          key: "enterName",
          fieldProps: {},
          formItemProps: {
            rules: [{ required: true }, { max: 100, message: "最多可输入 100 个字符" }]
          }
          // render: (dom, record) => {
          //   return record.enterName
          // }
        },
        {
          dataIndex: "type",
          title: "类型",
          name: "type",
          valueType: "select",
          valueEnum: {
            "0": "休闲场所",
            "1": "KTV",
            "2": "游乐场",
            "3": "茶楼"
          },
          key: "type",
          fieldProps: {},
          formItemProps: {
            rules: [{ required: true }]
          }
        },
        {
          dataIndex: "contacts",
          title: "联系人",
          name: "contacts",
          key: "contacts",
          fieldProps: {},
          formItemProps: {
            rules: [{ required: true }, { max: 100, message: "最多可输入 100 个字符" }]
          }
        },
        {
          dataIndex: "contactMobile",
          title: "联系人电话",
          name: "contactMobile",
          valueType: "number",
          key: "contactMobile",
          fieldProps: {},
          formItemProps: {
            rules: [
              { required: true }
              // {
              //   pattern: /^1(3\d|4[5-9]|5[0-35-9]|6[567]|7[0-8]|8\d|9[0-35-9])\d{8}$/,
              //   message: '请输入格式不正确',
              // },
            ]
          }
        },
        {
          title: "省市区",
          dataIndex: "province",
          name: "province",
          key: "province",
          formItemProps: {
            rules: [{ required: true }]
          },
          renderFormItem: () => {
            return <Province width={328} />;
          },

          valueType: "select"
          // fieldProps: {
          //     // mode: 'multiple',
          //     options:

          // onChange: (value, option) => {
          //   console.log(value);
          //   setAcceptorData(value);
          // },
          //     // showArrow: true,
          //     // disabled: flag ? false : show ? false : true,
          // },
        },
        {
          dataIndex: "address",
          title: "详细地址",
          name: "address",
          key: "address",
          fieldProps: {
            onChange: e => {
              // setPoint(e);
            }
          },
          formItemProps: {
            rules: [{ required: true }, { max: 100, message: "最多可输入 100 个字符" }]
          }
        },

        {
          dataIndex: "longitude",
          title: "经度",
          tooltip: '"-" 为西经，"+" 为东经',
          name: "longitude",
          key: "longitude",
          fieldProps: {},
          formItemProps: {
            rules: [
              {
                pattern:
                  /^(\-|\+)?(((\d|[1-9]\d|1[0-7]\d|0{1,3})\.\d{0,6})|(\d|[1-9]\d|1[0-7]\d|0{1,3})|180\.0{0,6}|180)$/,
                message: '(范围："-180°~ +180°",保留 6 位小数)'
              }
            ]
          }
        },
        {
          dataIndex: "latitude",
          title: "纬度",
          tooltip: '"+"为北纬，"-"为南纬',
          name: "latitude",
          key: "latitude",
          fieldProps: {},
          formItemProps: {
            rules: [
              {
                pattern: /^(\-|\+)?([0-8]?\d{1}\.\d{0,6}|90\.0{0,6}|[0-8]?\d{1}|90)$/,
                message: '( 范围："-90°~+90°",保留 6 位小数)'
              }
            ]
          }
        }
        // {
        //   title: 'dsasdad',
        //   dataIndex: 'latitsddssaude',
        //   renderFormItem: () => {
        //     return <BaiduMap
        //       width="600px"
        //       height="300px"
        //       lng={initialValues.longitude}
        //       lat={initialValues.latitude}
        //       cRef={childRef}
        //       getPoint={getPoint}
        //     />
        //   }
        // }
      ]
    }
  ];

  return (
    <>
      {/* 详情 */}
      <Modal
        title="景区娱乐项目详情"
        visible={isModalVisible}
        width={800}
        onOk={handleOk}
        onCancel={handleCancel}
        footer={[
          <Access key={getUniqueId()} accessible={access.canProjectEntertainment_openClose}>
            <Button
              type="primary"
              ghost
              danger={detailData.isEnable == 1 ? true : false}
              key="isEnable"
              onClick={() => onStatus(detailData)}
            >
              {detailData.isEnable == 1 ? "禁用" : "启用"}
            </Button>
          </Access>,
          <Access key={getUniqueId()} accessible={access.canProjectEntertainment_delete}>
            <Button type="primary" danger onClick={() => onDelete(Id)}>
              删除
            </Button>
          </Access>,
          <Access key={getUniqueId()} accessible={access.canProjectEntertainment_edit}>
            <Button type="primary" onClick={() => updateMethod(detailData)}>
              编辑
            </Button>
          </Access>,
          <Button key={getUniqueId()} onClick={handleCancel}>
            取消
          </Button>
        ]}
      >
        <Details detailData={detailData} />
      </Modal>
      {/* 编辑 */}

      {
        <ModalForm
          title="景区娱乐项目"
          visible={modalVisit}
          // formRef={formObj}
          // form={formRef}
          form={formObj2}
          // initialValues={initialValues}
          // submitter={false}
          onFinish={async val => {
            // 编辑
            if (Id) {
              val.id = Id;
            }
            // val.isEnable = isEnableStatus
            console.log("编辑", val);
            submitData(val);
          }}
          onVisibleChange={val => {
            setModalVisit(val);
            if (!val) {
              setInitialValues({});
            }
          }}
        >
          <ProForm.Group>
            <ProFormText width="md" name="scenicName" label="所属景区" initialValue={scenicName} disabled={true} />
            <ProFormText
              width="md"
              name="managementUnit"
              label="管理单位"
              placeholder="请输入管理单位"
              rules={[{ required: true, max: 40 }]}
            />
          </ProForm.Group>

          <ProForm.Group>
            <ProFormText
              width="md"
              name="enterName"
              label="景区娱乐服务项目名称"
              placeholder="请输入项目名称"
              rules={[{ required: true, max: 30 }]}
            />
            <ProFormSelect
              width="md"
              name="type"
              label="类型"
              valueEnum={typeEnum}
              placeholder="请输入类型"
              rules={[{ required: true, message: "请输入类型" }]}
            />
          </ProForm.Group>
          <ProForm.Group>
            <ProFormText
              width="md"
              name="contacts"
              label="联系人"
              placeholder="请输入联系人"
              rules={[{ required: true, max: 20 }]}
            />
            <ProFormText
              width="md"
              name="contactMobile"
              label="联系方式"
              placeholder="请输入联系方式"
              rules={[
                { required: true, message: "请输入联系方式" },
                {
                  // pattern: /^(((\d{3,4}-)?[0-9]{5,8})|(1(3|4|5|6|7|8|9)\d{9}))$/,
                  pattern: /^\d+(-\d+)*$/,
                  message: "不合法的手机号"
                }
              ]}
            />
          </ProForm.Group>

          <ProForm.Group>
            {/* <ProFormText
            width="md"
            name="provinceName"
            label="省市区"
            placeholder="请输入点位名称"
            rules={[{ required: true, message: '请输入点位名称' }]}
          /> */}
            <ProForm.Item label="省市区" name="province" rules={[{ required: true, message: "请输入省市区" }]}>
              <Province width={328} />
            </ProForm.Item>

            <ProFormText
              width="md"
              name="address"
              label="详细地址"
              placeholder="请输详细地址"
              fieldProps={{ onChange: e => setPoint(e) }}
              rules={[{ required: true, max: 200 }]}
            />
          </ProForm.Group>
          <ProForm.Group>
            <ProFormText
              width="md"
              name="longitude"
              label="经度"
              tooltip='"-" 为西经,"+" 为东经'
              placeholder="请输入经度"
              rules={[
                {
                  pattern:
                    /^(\-|\+)?(((\d|[1-9]\d|1[0-7]\d|0{1,3})\.\d{0,6})|(\d|[1-9]\d|1[0-7]\d|0{1,3})|180\.0{0,6}|180)$/,
                  message: '(范围："-180°~ +180°",保留6位小数)'
                }
              ]}
            />
            <ProFormText
              width="md"
              name="latitude"
              label="纬度"
              tooltip='"+"为北纬，"-"为南纬'
              placeholder="请输入纬度"
              rules={[
                {
                  pattern: /^(\-|\+)?([0-8]?\d{1}\.\d{0,6}|90\.0{0,6}|[0-8]?\d{1}|90)$/,
                  message: '( 范围："-90°~+90°",保留6位小数)'
                }
              ]}
            />
          </ProForm.Group>

          {/* <Form.Item>
          <BaiduMap
            width="100%"
            height="300px"
            lng={initialValues.longitude}
            lat={initialValues.latitude}
            cRef={childRef}
            getPoint={getPoint}
          />
        </Form.Item> */}
        </ModalForm>
      }

      {/*
      <EditPop
        title="景区娱乐项目"
        visible={editVisible}
        setVisible={setEditVisible}
        columns={editColumns}
        dataSource={dataSource}
        // 新增/编辑
        onFinish={(val: any) => {
          console.log(val);
          submitData(val);
          console.log(val);
        }}
      /> */}
      <>
        <ProTable
          // headerTitle={'查询表格'}
          actionRef={actionRef}
          rowKey="id"
          options={false}
          search={{
            labelWidth: 120,
            collapseRender: false,
            collapsed: false
          }}
          toolBarRender={() => [
            <Access key={getUniqueId()} accessible={access.canProjectEntertainment_insert}>
              <Button
                type="primary"
                key="primary"
                onClick={() => {
                  // handleOptionId('');
                  // handleModalVisible(true);
                  // setEditVisible(true);
                  // setInitialValues({});
                  formObj2.resetFields();
                  setModalVisit(true);
                  setId(undefined);
                }}
              >
                <PlusOutlined /> 新增
              </Button>
            </Access>
          ]}
          // dataSource={data}
          // params={{ scenicId }}
          request={getRecreationPageList}
          columns={columns}
        />
        {/* 新增编辑 */}
      </>
    </>
  );
};

export default Recreation;
