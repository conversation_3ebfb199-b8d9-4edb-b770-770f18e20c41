import { Button, Result } from 'antd';
import React from 'react';
import { getScenicIdentifier, goToLogin, jumpPage } from '@/common/utils/tool';

const NoFoundPage: React.FC = () => (
  <Result
    status="403"
    title="403"
    subTitle="抱歉，你无权访问该页面"
    extra={
      <Button
        type="primary"
        onClick={() => {
          const scenicCode = getScenicIdentifier();
          goToLogin({
            scenicCode,
          });
        }}
      >
        返回登录页
      </Button>
    }
  />
);

export default NoFoundPage;
