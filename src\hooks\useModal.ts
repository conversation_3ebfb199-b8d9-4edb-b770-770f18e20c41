/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-04-20 13:48:54
 * @LastEditTime: 2022-04-22 11:36:52
 * @LastEditors: zhangfengfei
 */
import { useState, useMemo } from 'react';

export type ModalType = 'add' | 'update' | 'info';

interface useModalConfig {
  defaultVisible?: boolean;
  defaultType?: ModalType;
}

export interface ModalState {
  type: ModalType;
  setType: React.Dispatch<React.SetStateAction<ModalType>>;
  visible: boolean;
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
  toggle: () => void;
  setTypeWithVisible: (val: ModalType, show?: boolean) => void;
}

export default function useModal(config: useModalConfig = {}): ModalState {
  const { defaultType = 'add', defaultVisible = false } = config;
  const [type, setType] = useState<ModalType>(defaultType);
  const [visible, setVisible] = useState<boolean>(defaultVisible);

  function toggle() {
    setVisible(!visible);
  }

  function setTypeWithVisible(val: ModalType) {
    setType(val);
    setVisible(true);
  }

  return useMemo(() => {
    return {
      type,
      setType,
      visible,
      setVisible,
      toggle,
      setTypeWithVisible,
    };
  }, [type, visible]);
}
