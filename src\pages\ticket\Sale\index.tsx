/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-09-04 14:14:27
 * @LastEditTime: 2023-10-20 18:25:13
 * @LastEditors: zhangfengfei
 */
import Export, { columnsSet } from "@/common/components/Export";
import useExport from "@/common/components/Export/useExport";
import { tableConfig } from "@/common/utils/config";
import { productTypeEnum, ticketStatusColor, ticketStatusEnum, ticketTypeEnum, whetherEnum } from "@/common/utils/enum";
import useModal from "@/hooks/useModal";
import type { CheckedMultipleTicketParams } from "@/services/api/ticket";
import { getTicketSalePageList, getTraceRecord } from "@/services/api/ticket";
import type { ActionType, ProColumns } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import { Space, Tag, message } from "antd";
import { trim } from "lodash";
import type { FC } from "react";
import { useRef, useState } from "react";
import { useModel } from "@umijs/max";
import TicketDetail from "./TicketDetail";
import ChainRecordModal from "./components/ChainRecordModal";
import CheckModal from "./components/CheckModal";
import RefundModal from "./components/RefundModal";

const TicketSale: FC = () => {
  const { initialState } = useModel("@@initialState");
  const { scenicId, isBlockChain } = initialState?.scenicInfo || {};
  const { coId } = initialState?.currentCompanyInfo || {};

  const actionRef = useRef<ActionType>();

  const tableReq = async (params: any) => {
    const { data } = await getTicketSalePageList(params);
    return data;
  };

  const detailModalState = useModal();
  const [currentRow, setCurrentRow] = useState<Ticket.SaleTicketItem>();

  const checkModalState = useModal();
  const refundModalState = useModal();
  const [selectedRows, setSelectedRows] = useState<Ticket.SaleTicketItem[]>([]);

  const columns: ProColumns<Ticket.SaleTicketItem>[] = [
    {
      title: "票号",
      dataIndex: "id",
      fixed: "left",
      width: 300,
      search: {
        transform: val => trim(val)
      }
    },
    {
      title: "产品名称",
      dataIndex: "proName",
      search: {
        transform: val => trim(val)
      }
    },
    {
      title: "商品名称",
      dataIndex: "goodsName",
      search: {
        transform: val => trim(val)
      }
    },
    {
      title: "产品类型",
      dataIndex: "proType",
      valueEnum: productTypeEnum
    },
    {
      title: "人数",
      dataIndex: "playerNum",
      search: false
    },
    {
      title: "票种",
      dataIndex: "type",
      valueEnum: ticketTypeEnum
    },
    {
      title: "数字资产",
      dataIndex: "isChain",
      hideInSearch: isBlockChain == 0,
      hideInTable: isBlockChain == 0,
      valueEnum: whetherEnum
    },

    {
      title: "出票时间",
      dataIndex: "createTime",
      valueType: "dateTimeRange",
      hideInTable: true,
      search: {
        transform: value => {
          return {
            beginTime: value?.[0],
            endTime: value?.[1]
          };
        }
      }
    },
    {
      title: "出票时间",
      dataIndex: "createTime",
      search: false
    },

    {
      title: "预计入园时间",
      dataIndex: "enterTime",
      valueType: "dateRange",
      hideInTable: true,
      search: {
        transform: value => {
          return {
            enterBeginTime: value?.[0],
            enterEndTime: value?.[1]
          };
        }
      }
    },
    {
      title: "预计入园时间",
      dataIndex: "enterTime",
      search: false
    },
    {
      title: "有效时长天数",
      dataIndex: "validityDay",
      search: false
    },
    {
      title: "可入园天数",
      dataIndex: "availableDays",
      search: false
    },
    {
      title: "使用次数",
      dataIndex: "useCount",
      search: false,
      renderText: (_, { useType = 0, useCount = 0 }) => {
        const useTypeArr = ["每天", "一共"];
        return `${useTypeArr[useType]}${useCount || "无限"}次`;
      }
    },
    {
      title: "核销总次数",
      dataIndex: "usedCount",
      valueType: "digit",
      search: false
    },
    {
      title: "订单号",
      dataIndex: "orderId",
      search: {
        transform: value => {
          return {
            orderIds: value ? [trim(value)] : undefined
          };
        }
      }
    },
    {
      title: "状态",
      fixed: "right",
      dataIndex: "status",
      valueType: "select",
      fieldProps: {
        options: Object.entries(ticketStatusEnum).map(item => ({
          label: item[1],
          value: item[0]
        }))
      },

      renderText: (dom: any) => <Tag color={ticketStatusColor[dom]}>{ticketStatusEnum[dom]}</Tag>
    },
    {
      width: 100,
      title: "操作",
      dataIndex: "_option",
      valueType: "option",
      fixed: "right",
      render: (_, record) => (
        <Space>
          <a
            onClick={() => {
              setCurrentRow(record);
              detailModalState.setVisible(true);
            }}
          >
            查看
          </a>
          {/* {record.isChain === 1 && <ChainModal ticketNumber={record.id} />} */}
          {record.isChain === 1 && (
            <ChainRecordModal getTraceRequest={getTraceRecord} params={{ ticketId: record.id }} />
          )}
        </Space>
      )
    }
  ];

  const onFinish = (rows: CheckedMultipleTicketParams["data"]) => {
    console.log(rows);
  };
  const exportState = useExport({
    columns,
    modulePath: "Backend_TicketSalesManagement",
    params: { scenicId, operatorId: coId }
  });

  return (
    <>
      <ProTable<Ticket.SaleTicketItem>
        {...tableConfig}
        actionRef={actionRef}
        rowKey="id"
        params={{ scenicId, operatorId: coId }}
        request={tableReq}
        columns={columns}
        formRef={exportState.formRef}
        columnsState={columnsSet(exportState)}
        toolBarRender={() => [<Export key="export" {...exportState} />]}
        rowSelection={{
          preserveSelectedRowKeys: false,
          getCheckboxProps({ status }) {
            return {
              disabled: !(status == "0" || status == "1" || status == "2")
            };
          }
        }}
        tableAlertRender={({ selectedRowKeys, onCleanSelected }) => {
          return (
            <span>
              已选 {selectedRowKeys.length} 项
              <a style={{ marginLeft: 8 }} onClick={onCleanSelected}>
                取消选择
              </a>
            </span>
          );
        }}
        tableAlertOptionRender={params => {
          return (
            <Space size={"middle"}>
              <a
                onClick={() => {
                  setSelectedRows(params.selectedRows);
                  const unRetreatTicket = params.selectedRows.find(i => i.isRetreat === 0);
                  if (unRetreatTicket) {
                    message.warning(`${unRetreatTicket.isRetreatReason} 票号:${unRetreatTicket.id}`);
                    return;
                  }
                  refundModalState.setVisible(true);
                }}
              >
                批量退票
              </a>
              <a
                onClick={() => {
                  setSelectedRows(params.selectedRows);
                  checkModalState.setVisible(true);
                }}
              >
                批量核销
              </a>
            </Space>
          );
        }}
      />
      <TicketDetail modalState={detailModalState} ticketId={currentRow?.id} />

      <RefundModal modalState={refundModalState} selectedRows={selectedRows} actionRef={actionRef} />

      <CheckModal modalState={checkModalState} selectedRows={selectedRows} actionRef={actionRef} onFinish={onFinish} />
    </>
  );
};

export default TicketSale;
