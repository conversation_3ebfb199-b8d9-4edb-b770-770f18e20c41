// @import './common/styles/index.less';

html,
body,
#root {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
}

.ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {
  left: unset;
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

@media (max-width: 768px) {
  .ant-table {
    width: 100%;
    overflow-x: auto;

    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;

        > span {
          display: block;
        }
      }
    }
  }
}

// 二级页面返回按钮样式
.backIcon {
  margin-right: 16px;
  transition: 0.3s;

  &:hover {
    color: #1890ff;
  }
}

.text-overflow {
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.required::before {
  display: inline-block;
  margin-right: 4px;
  color: #ff4d4f;
  font-size: 14px;
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: '*';
}

.flex {
  display: flex;
}

.justify-content-between {
  justify-content: space-between;
}

.justify-content-center {
  justify-content: center;
}

.justify-content-around {
  justify-content: space-around;
}

.align-items-center {
  align-items: center;
}

.pointer {
  cursor: pointer;
}

.primary-color {
  color: @primary-color;
}

.w-100 {
  width: 100%;
}

.fixed {
  position: fixed;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.text-red {
  color: #ff4d4f;
}

.ant-form.ant-form-vertical {
  .ant-col .ant-form-item-label > label {
    width: 100%;
  }
}

table {
  white-space: nowrap;
}

.ant-pro-card {
  box-shadow: 0 0 2px 1px rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02),
    0 2px 4px 0 rgba(0, 0, 0, 0.02);
}

.disableRow {
  color: #bfbfbf;
}

.ant-pro-form-group-title {
  margin-block-end: 16px;
}
.ant-descriptions-item-content {
  white-space: initial;
}

.requiredAsterisk::before {
  display: inline-block;
  color: #ff4d4f;
  font-size: 14px;
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: '*';
  margin-inline-end: 4px;
}
