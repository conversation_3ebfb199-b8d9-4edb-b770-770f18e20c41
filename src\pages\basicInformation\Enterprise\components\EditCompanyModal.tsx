import "./EditCompanyModal.less";

import EditPop2 from "@/common/components/EditPop2";
import Province from "@/common/components/Province";
import { DocumentTypeSelect, ExpirationDateTypeEnum, companyType, whetherEnum } from "@/common/utils/enum";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import { mobileValidator } from "@/common/utils/validate";
import ImageUpload from "@/components/ImageUpload";
import {
  apiBankList,
  apiCheckCoCode,
  apiScenicList,
  coSyncAuth,
  getCompanyInfo,
  postCoInfo,
  updateCoInfo
} from "@/services/api/erp";
import {
  ProFormDateRangePicker,
  ProFormFieldSet,
  ProFormRadio,
  type ProFormColumnsType,
  type ProFormInstance
} from "@ant-design/pro-components";
import { Alert, Input, Modal, message } from "antd";
import dayjs from "dayjs";
import type { FC } from "react";
import { useEffect, useState } from "react";
import { useModel, useRequest } from "@umijs/max";

const dateFormat = "YYYY-MM-DD";

interface AddressItem {
  addressId: string;
  addressName: string;
}
interface EditCompanyModalProps {
  visible: boolean;
  setVisible: (visible: boolean) => void;
  currentRow?: any;
  onSuccess?: () => void;
}

const EditCompanyModal: FC<EditCompanyModalProps> = ({ visible, setVisible, currentRow, onSuccess }) => {
  const { initialState } = useModel("@@initialState");

  const { userId } = initialState?.userInfo || {};

  const [associationMV, setAssociationMV] = useState(false); //关联并更新的弹窗
  const [coAddress, setCoAddress] = useState<AddressItem[]>([]);

  const [coInfo, setCoInfo] = useState<any>({}); //获取到的企业信息

  // 景区下拉列表
  const scenicDownListReq = useRequest(apiScenicList, {
    formatResult(res) {
      return res.data?.data?.map(({ isEnable, scenicId, scenicName }: any) => {
        return {
          label: scenicName,
          value: scenicId,
          disabled: isEnable == 0
        };
      });
    },
    initialData: []
  });

  // 银行下拉列表
  const bankDownListReq = useRequest(apiBankList, {
    formatResult(res) {
      return res.data?.map(({ bankId, bankName }: any) => {
        return {
          label: bankName,
          value: bankId
        };
      });
    },
    initialData: []
  });

  // 企业信息
  const coInfoReq = useRequest(getCompanyInfo, {
    manual: true,
    onSuccess(res: any, params) {
      const arr = ["Province", "City", "Area"].map(i => ({
        addressId: res[`co${i}Code`],
        addressName: res[`co${i}Name`]
      }));
      setCoAddress(arr);

      //转换时间数据
      if (res.businessLicenseBeginDate || res.businessLicenseEndDate) {
        res.businessLicenseDate = [
          res.businessLicenseBeginDate ? dayjs(res.businessLicenseBeginDate, dateFormat) : null,
          res.businessLicenseEndDate ? dayjs(res.businessLicenseEndDate, dateFormat) : null
        ];
      }
      if (res.documentBeginDate || res.documentEndDate) {
        res.documentDate = [
          res.documentBeginDate ? dayjs(res.documentBeginDate, dateFormat) : null,
          res.documentEndDate ? dayjs(res.documentEndDate, dateFormat) : null
        ];
      }

      setCoInfo({
        ...res,
        id: currentRow?.coId,
        relationCoAndType: res.coTypes?.map((i: any) => i.type),
        relationCoAndScenic: res.scenic?.map((i: any) => i.scenicId),
        province: [res.coProvinceName, res.coCityName, res.coAreaName]
      });
    }
  });

  const isCreate = currentRow == undefined;

  const editColumns: ProFormColumnsType[] = [
    isCreate
      ? {}
      : {
          valueType: "dependency",
          name: ["settlementStatus", "companyUpdateStatus", "updateComment", "authComment"],
          columns: ({ settlementStatus, companyUpdateStatus, updateComment, authComment }) => [
            {
              title: "",
              colProps: { xs: 24, sm: 24 },
              renderFormItem: () => {
                {
                  /**
                   *  0: '未认证',1: '已认证',2: '已拒绝',3: '认证中',4 : '认证失败'
                   */
                  let message: React.ReactNode = "";
                  if (settlementStatus == 0) {
                    message = "完善信息后可提交企业认证审核！";
                  } else if (settlementStatus == 3) {
                    message = "企业认证审核中！";
                  } else if (settlementStatus == 1) {
                    message = "企业认证审核通过！";
                    //企业在“已认证”状态下可进行企业信息修改的方案
                    if (companyUpdateStatus == "WAITING") {
                      message = "企业信息修改审核中！";
                    } else if (companyUpdateStatus == "REFUSE") {
                      message = `企业信息修改审核不通过${
                        updateComment ? `，具体原因：${updateComment}` : ""
                      }，请修改后重新提交！`;
                    } else if (companyUpdateStatus == "PASS") {
                      message = "企业信息修改审核通过！";
                    }
                  } else if (settlementStatus == 2) {
                    message = `企业认证审核不通过${
                      authComment ? `，具体原因：${authComment}` : ""
                    }，请修改后重新提交！`;
                  } else if (settlementStatus == 4) {
                    message = (
                      <>
                        认证失败，已存在同一认证企业请审核相关信息后进行结算账户关联！{" "}
                        <a
                          onClick={() => {
                            setAssociationMV(true);
                          }}
                        >
                          关联并更新
                        </a>
                      </>
                    );
                  }
                  return <Alert message={message} type="success" />;
                }
              }
            }
          ]
        },
    {
      title: "基础信息",
      valueType: "group",
      columns: [
        {
          title: "企业名称",
          dataIndex: "coName",
          formItemProps: {
            rules: [
              {
                required: true,
                max: 50
              }
            ]
          }
        },
        {
          title: "统一社会信用代码",
          dataIndex: "coCode",
          fieldProps: {
            disabled: !isCreate
          },
          formItemProps: {
            rules: [
              {
                required: true,
                len: 18
              },
              {
                validateTrigger: "onSubmit",
                validator: async (_, value) => {
                  if (value != undefined && value.length == 18 && currentRow == undefined) {
                    //新增的时候才判定
                    //没输入就不用查
                    const { data }: any = await apiCheckCoCode(value);
                    if (!data) {
                      return Promise.reject("统一社会信用代码已存在！");
                    }
                  }
                  return Promise.resolve();
                }
              }
            ]
          }
        },
        {
          title: "企业所在地（省市区）",
          dataIndex: "province",
          fieldProps: {
            onChange: (values: AddressItem[]) => {
              setCoAddress(values);
            }
          },
          renderFormItem: () => {
            return <Province />;
          }
        },
        {
          title: "详细地址",
          dataIndex: "coAddressInfo",
          formItemProps: {
            rules: [
              {
                max: 100
              }
            ]
          }
        },
        {
          valueType: "dependency",
          name: ["licenseExpirationDateType", "businessLicenseDate"],
          columns: ({ licenseExpirationDateType }) => [
            {
              title: (
                <>
                  <span
                    className="requiredAsterisk"
                    style={{
                      display: licenseExpirationDateType == ExpirationDateTypeEnum[1].value ? "inherit" : "none"
                    }}
                  />
                  营业执照有效期
                </>
              ),
              colProps: { xs: 12, sm: 12 },
              renderFormItem: (_, __, form) => (
                <ProFormFieldSet>
                  <div className="overlap-date">
                    <div className="overlap-date-picker">
                      <ProFormDateRangePicker
                        name="businessLicenseDate"
                        disabled={
                          licenseExpirationDateType == undefined ||
                          licenseExpirationDateType == ExpirationDateTypeEnum[0].value
                        }
                        transform={(date: any) => {
                          return {
                            businessLicenseBeginDate: date[0],
                            businessLicenseEndDate: date[1]
                          };
                        }}
                        rules={[
                          {
                            required: licenseExpirationDateType == ExpirationDateTypeEnum[1].value,
                            message: "请选择"
                          }
                        ]}
                      />
                    </div>
                    <div className="overlap-date-type">
                      <ProFormRadio.Group
                        name="licenseExpirationDateType"
                        options={ExpirationDateTypeEnum}
                        rules={[{ required: true, message: "请输入" }]}
                        fieldProps={{
                          onChange: () => {
                            form.validateFields(["businessLicenseDate", "licenseExpirationDateType"]);
                          }
                        }}
                      />
                    </div>
                  </div>
                </ProFormFieldSet>
              )
            }
          ]
        },
        {
          title: "营业执照上传",
          dataIndex: "businessLicenseImageUrl",
          renderFormItem: (_, config) => {
            const defaultValue = config.value ?? "";
            return <ImageUpload maxCount={1} defaultValue={defaultValue} size={5} />;
          },
          formItemProps: {
            rules: [{ required: true }]
          }
        }
      ]
    },
    {
      title: "法人信息",
      valueType: "group",
      columns: [
        {
          title: "法人名称",
          dataIndex: "legalRepresentativeName",
          formItemProps: {
            rules: [
              {
                required: true,
                max: 10
              }
            ]
          }
        },
        {
          title: "证件类型",
          dataIndex: "documentType",
          valueType: "select",
          valueEnum: DocumentTypeSelect,
          formItemProps: {
            rules: [{ required: true }]
          }
        },
        {
          title: "证件号码",
          dataIndex: "documentNumber",
          formItemProps: {
            rules: [
              {
                required: true,
                len: 18
              }
            ]
          }
        },

        {
          valueType: "dependency",
          name: ["documentExpirationDateType", "documentDate"],
          columns: ({ documentExpirationDateType }) => [
            {
              title: (
                <>
                  <span
                    className="requiredAsterisk"
                    style={{
                      display: documentExpirationDateType == ExpirationDateTypeEnum[1].value ? "inherit" : "none"
                    }}
                  />
                  证件有效期
                </>
              ),
              colProps: { xs: 12, sm: 12 },
              renderFormItem: (_, __, form) => (
                <ProFormFieldSet>
                  <div className="overlap-date">
                    <div className="overlap-date-picker">
                      <ProFormDateRangePicker
                        name="documentDate"
                        disabled={
                          documentExpirationDateType == undefined ||
                          documentExpirationDateType == ExpirationDateTypeEnum[0].value
                        }
                        transform={(date: any) => {
                          return {
                            documentBeginDate: date[0],
                            documentEndDate: date[1]
                          };
                        }}
                        rules={[
                          {
                            required: documentExpirationDateType == ExpirationDateTypeEnum[1].value,
                            message: "请选择"
                          }
                        ]}
                      />
                    </div>
                    <div className="overlap-date-type">
                      <ProFormRadio.Group
                        name="documentExpirationDateType"
                        options={ExpirationDateTypeEnum}
                        rules={[{ required: true, message: "请输入" }]}
                        fieldProps={{
                          onChange: () => {
                            form.validateFields(["documentDate", "documentExpirationDateType"]);
                          }
                        }}
                      />
                    </div>
                  </div>
                </ProFormFieldSet>
              )
            }
          ]
        },
        {
          title: "证件正反面上传",
          dataIndex: "documentImageUrl",
          renderFormItem: (_, config) => {
            const defaultValue = config.value ?? "";
            return <ImageUpload maxCount={2} defaultValue={defaultValue} size={5} />;
          },
          formItemProps: {
            rules: [
              { required: true, message: "请上传证件正反面" },
              {
                validator: (_, value) => {
                  if (value.split(",").length == 2) {
                    return Promise.resolve();
                  }
                  return Promise.reject("必须上传两张");
                }
              }
            ]
          }
        }
      ]
    },
    {
      title: "收款信息",
      valueType: "group",
      columns: [
        {
          valueType: "dependency",
          name: ["coName"],
          columns: ({ coName }) => [
            {
              colProps: { xs: 24, sm: 12 },
              title: "开户名称",
              dataIndex: "accountName",
              renderFormItem: () => <Input value={coName} disabled />
            }
          ]
        },
        {
          title: "开户银行",
          valueType: "select",
          dataIndex: ["coBankAccount", "bankId"],
          fieldProps: {
            showSearch: true,
            options: bankDownListReq.data,
            loading: bankDownListReq.loading
          },
          formItemProps: {
            rules: [{ required: true }]
          },
          transform: (bankId: any) => {
            //后端需要银行名称,这里同时取一并传了
            const bankName = bankDownListReq.data?.filter((item: any) => item.value == bankId)?.[0]?.label ?? "";
            return { bankId, bankName };
          }
        },
        {
          title: "银行账号",
          dataIndex: ["coBankAccount", "account"],
          formItemProps: {
            rules: [
              {
                required: true,
                max: 30
              }
            ]
          },
          transform: (account: any) => {
            return { account };
          }
        }
      ]
    },
    {
      title: "联系信息",
      valueType: "group",
      columns: [
        {
          title: "联系人",
          dataIndex: "contactName",
          formItemProps: {
            rules: [
              {
                required: true,
                max: 10
              }
            ]
          }
        },
        {
          title: "联系手机",
          dataIndex: "contactPhone",
          formItemProps: {
            rules: [{ required: true }, mobileValidator()]
          }
        }
      ]
    }
  ];

  const logList: any[] = [
    {
      title: "企业名称",
      dataIndex: "coName"
    },
    {
      title: "统一社会信用代码",
      dataIndex: "coCode"
    },
    {
      title: "企业类型",
      dataIndex: "relationCoAndType",
      renderText(val: string[], record: any) {
        return (val || []).map(i => companyType?.[i]).join("、");
      }
    },
    {
      title: "是否 OTA",
      dataIndex: "isOTA",
      valueEnum: whetherEnum
    },
    {
      title: "企业电话",
      dataIndex: "coPhone"
    },
    {
      title: "对应景区",
      dataIndex: "relationCoAndScenic",
      renderText(val, { relationCoAndScenic }) {
        return relationCoAndScenic
          ?.map((i: string) => scenicDownListReq.data?.find(option => option.value === i)?.label)
          .join("、");
      }
    },

    {
      title: "企业传真",
      dataIndex: "coFax"
    },

    {
      title: "企业网址",
      dataIndex: "coSite"
    },
    {
      title: "企业电话",
      dataIndex: "coPhone"
    },
    {
      title: "省市区",
      dataIndex: "province",
      renderText(val, { coProvinceName, coCityName, coAreaName }) {
        return coProvinceName + coCityName + coAreaName;
      }
    },
    {
      title: "详细地址",
      dataIndex: "coAddressInfo"
    },
    {
      title: "企业邮箱",
      dataIndex: "coEmail"
    },
    {
      title: "联系人",
      dataIndex: "contactName"
    },
    {
      title: "联系方式",
      dataIndex: "contactPhone"
    },
    {
      title: "域名地址",
      dataIndex: "coHostAddress"
    },
    {
      title: "标题栏名称",
      dataIndex: "coHostTitle"
    }
  ];

  // 提交
  const onFinish = async (values: Record<string, any>, formRef: React.MutableRefObject<ProFormInstance<any>>) => {
    const { coId, isEnable } = currentRow || {};
    const { coTypes, scenic } = coInfo;
    const _formDatas = {
      ...coInfo,
      ...values
    };

    const {
      coCode,
      coEmail,
      coFax,
      coName,
      coPhone,
      coSite,
      isOTA,
      contactName,
      contactPhone,
      coAddressInfo,
      coBackstageAbrLogo,
      coBackstageLoginLogo,
      coBackstageLogo,
      coStoreLogo,
      coHostAddress,
      coHostTitle,
      coRemark,

      businessLicenseBeginDate,
      businessLicenseEndDate,
      businessLicenseImageUrl,
      legalRepresentativeName,
      licenseExpirationDateType,
      documentBeginDate,
      documentEndDate,
      documentImageUrl,
      documentNumber,
      documentType,
      documentExpirationDateType,
      account,
      bankId,
      bankName
    } = _formDatas;

    // 后端数据格式
    const formateValues = {
      coId,
      isSale: false,
      co: {
        //企业信息对象
        coId,
        creatorId: userId,
        coCode,
        coEmail,
        coFax,
        coName,
        coPhone,
        coSite,
        isEnable,
        isOTA,
        contactName,
        contactPhone,

        licenseExpirationDateType,
        businessLicenseBeginDate,
        businessLicenseEndDate,
        businessLicenseImageUrl,
        legalRepresentativeName,
        documentBeginDate,
        documentEndDate,
        documentExpirationDateType,
        documentImageUrl,
        documentNumber,
        documentType
      },
      coAddress: {
        //企业地区对象
        coAddressInfo,
        creatorId: userId,
        coProvinceCode: coAddress?.[0]?.addressId,
        coProvinceName: coAddress?.[0]?.addressName,
        coCityCode: coAddress?.[1]?.addressId,
        coCityName: coAddress?.[1]?.addressName,
        coAreaCode: coAddress?.[2]?.addressId,
        coAreaName: coAddress?.[2]?.addressName
      },
      coInfo: {
        //企业详情对象
        coBackstageAbrLogo,
        coBackstageLoginLogo,
        coBackstageLogo,
        coStoreLogo,
        coHostAddress,
        coHostTitle,
        coId,
        coRemark,
        creatorId: userId
      },
      relationCoAndScenic: scenic,
      relationCoAndType: coTypes,
      coBankAccount: {
        //企业银行账户
        account,
        accountName: coName,
        bankId,
        bankName
      }
    };

    console.log(formateValues);
    if (currentRow?.coId) {
      await updateCoInfo(formateValues);
      message.success("编辑成功");
      try {
        addOperationLogRequest({
          action: "edit",
          changeConfig: {
            list: logList,
            beforeData: coInfo,
            afterData: { ...values, ...formateValues.coAddress }
          },
          content: `编辑企业【${coInfoReq.data?.coName}】`
        });
      } catch (error) {}
    } else {
      await postCoInfo(formateValues);
      addOperationLogRequest({
        action: "add",
        content: `新增企业【${coName}】`
      });
      message.success("新增成功");
    }

    onSuccess?.();
    return true;
  };

  useEffect(() => {
    if (visible) {
      // 编辑
      if (currentRow) {
        // 又是拿分页当下拉列表
        scenicDownListReq.run({
          current: 1,
          pageSize: 999
        });
        coInfoReq.run(currentRow.coId);
        bankDownListReq.run({}); //取银行下拉列表
      }
    } else {
      setCoAddress([]);
      setCoInfo({});
    }

    return () => {
      scenicDownListReq.cancel();
      bankDownListReq.cancel();
    };
  }, [currentRow, visible]);

  return (
    <>
      <EditPop2
        title="企业"
        visible={visible}
        setVisible={setVisible}
        columns={editColumns}
        loading={coInfoReq.loading}
        dataSource={coInfo}
        disable={coInfo.companyUpdateStatus == "WAITING"}
        onFinish={onFinish}
      />
      <Modal
        title="确认进行结算账户关联吗?"
        open={associationMV}
        onOk={() => {
          coSyncAuth(coInfo.coCode); //不用理会结果,直接关闭弹窗
          setAssociationMV(false);
          onSuccess?.();
          message.info("企业关联成功！");
        }}
        onCancel={() => {
          setAssociationMV(false);
        }}
        zIndex={1001}
      >
        <p>请进行企业信息审核后慎重操作</p>
      </Modal>
    </>
  );
};

export default EditCompanyModal;
