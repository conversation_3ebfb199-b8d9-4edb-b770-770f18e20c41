//设置默认样式
html,
body {
  width: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
  color: #fff;
  font-weight: 400;
  font-size: 16px;
  font-family: 'PingFangSC-Regular, PingFang SC;';
  background: linear-gradient(180deg, #011015 0%, #071a1f 53%, rgba(5, 24, 30, 0) 100%);
  background-image: url('../../../assets/imgs/bg.png') !important;
  background-repeat: no-repeat;
  background-position: top center;
  // background-size: auto;
  background-size: cover;
  // height: 99999px;
}

// 垂直居中布局
.center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  // width: 100%;
  padding: 92px 0;
  overflow: hidden;
  text-align: center;
}

// 慧景云 logo
// .logo {
//   display: flex;
//   flex: 1 1;
//   width: 170px;
//   height: 59px;
//   // margin-top: 66px;
//   color: #fff;
//   img {
//     flex: 1;
//     width: 170px;
//     background-color: rgba(1, 1, 24, 0.5);
//     background-repeat: no-repeat;
//   }
// }
//落地页容器
.scenic_box {
  display: flex;
  align-items: center;
  justify-content: space-around;
  // width: 1500px;
  width: 100%;
  // margin-top: 121px;
  margin-top: 83px;
  margin-bottom: 24px;
}

//版权
.copyright {
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: center;
  width: 100%;
  height: 40px;
  color: #ccc;
  font-size: 14px;
  line-height: 40px;

  // opacity: 0.8;
  span {
    a {
      color: #eee;
    }

    .apkStyle {
      position: relative;
      // color: #3cf0ff;
      color: #fff;
      font-weight: 600;
      cursor: pointer;
    }
  }
}

// 安卓检票
.apkStyle:hover {
  .codeImg {
    display: block !important;
  }
}

.codeImg {
  position: absolute;
  top: -110px;
  right: -10px;
  display: none;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;

  // border: 1px solid #ccc;
  img {
    width: 70px;
    margin-bottom: -40px;
    // height: 100%;
  }
}

// @media screen and (min-width: 1940px) {
//   .logo {
//     margin-top: 158px;
//   }
// }
//媒体查询
@media screen and (max-width: 1601px) {
  .center {
    padding: 40px 0;

    .scenic_box {
      margin-top: 40px;
    }
  }
}

@media screen and (min-width: 1921px) {
  .center {
    padding: 0;

    .logo {
      margin-top: 158px;
    }
  }
}

// .footer {
//   p {
//     color: #fff;
//     font-size: 14px;
//     line-height: 40px;
//     text-align: center;
//   }
// }
