/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-04-29 11:39:43
 * @LastEditTime: 2022-10-26 11:49:13
 * @LastEditors: zhang<PERSON><PERSON>i
 */
import { request } from "@umijs/max";
import { scenicHost } from ".";

// 库存列表
export async function getStockList(params: API.StockListParams, options: Record<string, any> = {}) {
  return request<ResponseListData<API.StockListItem[]>>(`${scenicHost}/manageStock/travelCardManageStockList`, {
    method: "POST",
    data: params,
    ...options
  });
}

/** 查询库存详情 */
export async function getStockInfo(params: API.StockInfoParams, options: Record<string, any> = {}) {
  return request<ResponseData<API.StockListItem>>(`${scenicHost}/manageStock/info`, {
    method: "GET",
    params,
    ...options
  });
}

// 库存新增
export function addStock(params: API.AddStockParams, options: Record<string, any> = {}) {
  return request<ResponseData<null>>(`${scenicHost}/manageStock/travelCardInfo`, {
    method: "POST",
    data: params,
    ...options
  });
}

// 权益卡库存编辑
export async function updateTravelCardStock(params: API.AddTravelCardStockParams, options: Record<string, any> = {}) {
  return request<ResponseData<null>>(`${scenicHost}/manageStock/travelCardInfo`, {
    method: "PUT",
    data: params,
    ...options
  });
}

/** 禁用/启用库存 */
export async function switchTravelCardStock(
  params: Pick<API.StockListItem, "id" | "isEnable">,
  options: Record<string, any> = {}
) {
  return request<ResponseData<null>>(`${scenicHost}/manageStock/travelCardStatus`, {
    method: "PUT",
    data: params,
    ...options
  });
}

/** 删除权益卡库存 */
export async function deleteTravelCardStock(params: Pick<API.StockListItem, "id">, options: Record<string, any> = {}) {
  return request<ResponseData<null>>(`${scenicHost}/manageStock/travelCardInfo/${params.id}`, {
    method: "DELETE",
    ...options
  });
}
