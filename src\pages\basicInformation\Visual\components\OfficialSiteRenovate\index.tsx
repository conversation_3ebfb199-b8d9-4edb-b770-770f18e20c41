import Delete from "@/common/components/Delete";
import { tableConfig } from "@/common/utils/config";
import { modelWidth } from "@/common/utils/gConfig";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import { getEnv } from "@/common/utils/getEnv";
import { apiBatchDelRenovate, apiDelRenovate, apiEditRenovateState, apiGetRenovateList } from "@/services/api/erp";
import { InfoCircleTwoTone, PlusOutlined } from "@ant-design/icons";
import type { ActionType, ProColumnType } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import { useModel } from "@umijs/max";
import { Button, Card, Modal, Space, Tag, Typography, message } from "antd";
import Meta from "antd/lib/card/Meta";
import { useContext, useRef, useState } from "react";
import { TabKeyContext } from "../..";
// import emptyCard from './emptyCard.png';
import styles from "./index.module.less";
import tlp_01 from "./tlp01.png";
import tlp_02 from "./tlp02.png";
import tlp_03 from "./tlp03.png";
import tlp_04 from "./tlp04.png";

const OfficialSiteRenovate = () => {
  const { initialState } = useModel("@@initialState");
  const { scenicId, uniqueIdentity } = initialState?.scenicInfo || {};
  const actionRef = useRef<ActionType>();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const tabKey = useContext(TabKeyContext);

  // 用于保存每个 Card 的 ref
  const cardRefs = useRef<any[]>([]); // 用来记录每个卡片的滚动动画ID
  const animationRefs = useRef<any[]>([]); // 用于存储每个动画的ID
  const { DESIGN_WEBSITE_URL } = getEnv();
  const tableColumns: ProColumnType[] = [
    {
      title: "页面名称",
      dataIndex: "pageName",
      render: (dom: any, entity: any) => (
        <Space>
          {dom}
          {entity.homePageState == 2 && <Tag color="blue">主页</Tag>}
        </Space>
      )
    },
    {
      title: "页面备注",
      dataIndex: "remark",
      search: false,
      renderText: (text: any) => (
        <Typography.Paragraph ellipsis style={{ maxWidth: 200, marginBottom: 0 }}>
          {text}
        </Typography.Paragraph>
      )
    },
    {
      title: "操作用户",
      dataIndex: "modifyUserName",
      search: false
    },
    {
      title: "修改时间",
      dataIndex: "modifyTime",
      search: false
    },
    {
      title: "页面状态",
      dataIndex: "pageState",
      valueEnum: { 1: "未发布", 2: "已发布" }
    },
    {
      title: "有无草稿",
      dataIndex: "pageDraftState",
      valueEnum: { 1: "有草稿", 2: "无草稿" }
    },
    {
      title: "操作",
      valueType: "option",
      render: (_: any, entity: any) => [
        <a
          key="edit"
          href={`${DESIGN_WEBSITE_URL}?identifier=${uniqueIdentity}&pageId=${entity.id}&type=edit&project=scenic`}
          target="_blank"
          rel="noreferrer"
        >
          编辑
        </a>,
        <a
          key="copy"
          href={`${DESIGN_WEBSITE_URL}?identifier=${uniqueIdentity}&pageId=${entity.id}&type=copy&project=scenic`}
          target="_blank"
          rel="noreferrer"
        >
          复制
        </a>,
        entity.homePageState != 2 && (
          <Delete
            key="delete"
            access={true}
            status={entity.homePageState == 2}
            params={{ id: entity.id }}
            request={async (params: any) => {
              const data = await apiDelRenovate(params?.id);
              addOperationLogRequest({
                action: "del",
                module: tabKey,
                content: `删除【${entity.pageName}】页面`
              });
              return data;
            }}
            actionRef={actionRef}
            content="主页不可删除！"
          />
        ),
        entity.homePageState == 1 && entity.pageState == 2 && (
          <a
            key="setting"
            onClick={() => {
              apiEditRenovateState({ id: entity.id }).then(() => {
                addOperationLogRequest({
                  action: "edit",
                  module: tabKey,
                  content: `设置页面【${entity.pageName}】为主页`
                });
                actionRef.current?.reload();
              });
            }}
          >
            设为主页
          </a>
        )
      ]
    }
  ];
  const addButton = (
    <Button
      key="addButton"
      type="primary"
      onClick={() => {
        setIsModalOpen(true);
      }}
    >
      <PlusOutlined /> 新增
    </Button>
  );
  const handleCancel = () => {
    setIsModalOpen(false);
  };
  const cardList = [
    {
      title: "通用模板",
      cover: tlp_01
    },
    {
      title: "乐园类景区",
      cover: tlp_02
    },
    {
      title: "自然类景区",
      cover: tlp_03
    },
    {
      title: "文化类景区",
      cover: tlp_04
    }
  ];

  // 平滑滚动的函数
  const smoothScroll = (element: any, to: number, duration: number, animationId: any) => {
    const start = element.scrollTop;
    const change = to - start;
    const startTime = performance.now();

    const animateScroll = (currentTime: number) => {
      const timeElapsed = currentTime - startTime;
      const progress = Math.min(timeElapsed / duration, 1); // 计算动画进度
      element.scrollTop = start + change * progress; // 更新 scrollTop

      if (timeElapsed < duration) {
        animationRefs.current[animationId] = requestAnimationFrame(animateScroll); // 继续执行动画
      } else {
        animationRefs.current[animationId] = null; // 动画结束
      }
    };

    animationRefs.current[animationId] = requestAnimationFrame(animateScroll); // 启动动画
  };

  const handleMouseEnter = (index: number) => {
    const card = cardRefs.current[index];
    if (card) {
      // 在鼠标进入时滚动到底部
      smoothScroll(card, card.scrollHeight, 3000, index); // 500ms 滚动到底部
    }
  };

  const handleMouseLeave = (index: number) => {
    const card = cardRefs.current[index];
    if (card) {
      // 在鼠标离开时停止动画，并立即恢复到顶部
      if (animationRefs.current[index]) {
        cancelAnimationFrame(animationRefs.current[index]); // 取消当前的滚动动画
      }
      smoothScroll(card, 0, 200, index); // 500ms 滚动回顶部
    }
  };

  return (
    <>
      <ProTable
        {...tableConfig}
        rowKey="id"
        rowSelection={{
          getCheckboxProps: (record: any) => ({
            disabled: record.homePageState == 2
          })
        }}
        tableAlertRender={({ selectedRowKeys, onCleanSelected }) => (
          <Space size={8}>
            <span>已选 {selectedRowKeys.length} 项</span>
            <a onClick={onCleanSelected}>取消选择</a>
          </Space>
        )}
        tableAlertOptionRender={({ selectedRowKeys, onCleanSelected }) => (
          <a
            onClick={() => {
              Modal.confirm({
                title: "确认删除吗？",
                icon: <InfoCircleTwoTone />,
                content: "删除后不可恢复",
                okText: "确认",
                cancelText: "取消",
                onOk: () => {
                  apiBatchDelRenovate(selectedRowKeys.join(",")).then(() => {
                    message.success("删除成功");
                    actionRef.current?.reload();
                    onCleanSelected();
                  });
                }
              });
            }}
          >
            批量删除
          </a>
        )}
        actionRef={actionRef}
        columns={tableColumns}
        toolBarRender={() => [addButton]}
        params={{ businessId: scenicId }}
        request={apiGetRenovateList}
      />
      <Modal width={modelWidth.lg} title="选择模板" open={isModalOpen} onCancel={handleCancel} footer={false}>
        <Space size={[20, 20]} wrap>
          {cardList.map((item: any, index: any) => (
            <Card
              key={index}
              hoverable
              cover={
                <div
                  ref={el => (cardRefs.current[index] = el)} // 获取每个Card的ref
                  style={{
                    width: 450,
                    height: 280,
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    position: "relative",
                    borderColor: "inherit",
                    overflow: "hidden" // 让内容溢出部分隐藏
                  }}
                  onMouseEnter={() => handleMouseEnter(index)} // 移入时滚动
                  onMouseLeave={() => handleMouseLeave(index)} // 移出时恢复
                >
                  <img
                    style={{
                      maxWidth: "100%",
                      position: "absolute",
                      inset: 0
                    }}
                    src={item.cover}
                    alt={item.title}
                  />
                  <div
                    style={{
                      position: "absolute",
                      inset: 0,
                      border: "1px solid",
                      borderColor: "inherit",
                      borderBottomWidth: 0
                    }}
                  />
                </div>
              }
              actions={[<Meta title={item.title} />]}
              onClick={() => {
                // open(
                //   `${
                //     getEnv().DESIGN_WEBSITE_URL
                //   }?identifier=${uniqueIdentity}&type=add&template=${index + 1}`,
                // );
                open(
                  `${DESIGN_WEBSITE_URL}?identifier=${uniqueIdentity}&type=add&template=scenic${
                    index + 1
                  }&project=scenic`
                );
              }}
              className={styles.cardStyle}
            />
          ))}
        </Space>
      </Modal>
    </>
  );
};

export default OfficialSiteRenovate;
