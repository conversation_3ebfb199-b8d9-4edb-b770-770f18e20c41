/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-08-24 15:25:05
 * @LastEditTime: 2023-08-28 13:38:21
 * @LastEditors: zhang<PERSON><PERSON>i
 */

import type { IBarChartProps } from "@/components/BaseChart";
import BaseChart from "@/components/BaseChart";
import { filterBarData1, filterBarData2, filterLineDataOut, goodsChartData } from "@/components/BaseChart/filterData";
import { Card, List } from "antd";
import dayjs from "dayjs";
import type { FC } from "react";

interface ChartsProps {}

const Charts: FC<ChartsProps> = () => {
  // 首页图表数据
  const chartData: IBarChartProps[] = [
    {
      //01 出票  02 退票
      id: "1",
      type: "bar",
      title: "出退票统计",
      datePickerConfig: {
        picker: "rangePicker",
        defaultValue: [dayjs().subtract(6, "day"), dayjs()]
      },
      filterDataFunc: filterBarData1,
      unit: "张"
    },
    {
      id: "2",
      type: "line",
      title: "实际出入园统计",
      datePickerConfig: {
        picker: "datePicker",
        showMenu: false,
        defaultValue: dayjs()
      },
      filterDataFunc: filterLineDataOut,
      unit: "人"
    },
    {
      id: "3",

      type: "bar",
      title: "预订入园统计",
      datePickerConfig: {
        picker: "rangePicker",
        defaultValue: [dayjs().subtract(6, "day"), dayjs()]
      },
      filterDataFunc: filterBarData2,
      unit: "人"
    },
    {
      id: "4",
      type: "bar",
      title: "商品退订统计",
      datePickerConfig: {
        picker: "rangePicker",
        defaultValue: [dayjs().subtract(6, "day"), dayjs()]
      },
      filterDataFunc: goodsChartData,
      unit: "人"
    }
  ];

  return (
    <List
      id="chartBox"
      grid={{
        gutter: 20,
        xs: 1,
        sm: 1,
        md: 1,
        lg: 1,
        xl: 2,
        xxl: 2
      }}
      dataSource={chartData}
      renderItem={({ datePickerConfig, type, title, unit, filterDataFunc, id }) => (
        <List.Item>
          <Card>
            <section>
              <div style={{ width: "100%", position: "relative" }}>
                <BaseChart
                  id={id}
                  datePickerConfig={datePickerConfig}
                  unit={unit}
                  type={type}
                  title={title}
                  filterDataFunc={filterDataFunc}
                />
              </div>
            </section>
          </Card>
        </List.Item>
      )}
      style={{ margin: "20px 0" }}
    />
  );
};

export default Charts;
