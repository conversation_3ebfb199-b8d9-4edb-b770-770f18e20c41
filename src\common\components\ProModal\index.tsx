import type { FormInstance, ProFormColumnsType } from '@ant-design/pro-components';
import Edit from './Edit';
import Info from './Info';

type ModalType = 'add' | 'edit' | 'info' | null;
type AttributeType = {
  /** 二级页面 */
  page?: any;
  layout?: 'horizontal' | 'vertical' | 'inline';
  /** 标题 */
  title?: string;
  fullTitle?: string;
  /**
   * 弹窗类型
   * add：新增；edit：编辑；info：详情
   */
  type: ModalType;
  /** 设置弹窗类型 */
  setType: React.Dispatch<React.SetStateAction<ModalType>>;
  /** 表单项配置 */
  columns: ProFormColumnsType[];
  /** 接口参数 */
  params?: Record<string, any>;
  /** 表单数据 */
  dataSource?: Record<string, any>;
  /**
   * 详情接口函数
   * 回调格式：{ data: {} }
   * 当 dataSource 不传时，该项必传
   */
  infoRequest?: any;
  /** 表单提交回调 */
  onFinish?:
    | (((formData: any) => Promise<boolean | void>) & ((formData: any) => Promise<any>))
    | undefined;
  /**
   * 表格对象
   * 当 onFinish 不传时，该项必传
   */
  actionRef?: any;
  /**
   * 新增接口函数
   * 当 onFinish 不传时，该项必传
   */
  addRequest?: any;
  /**
   * 编辑接口函数
   * 当 onFinish 不传时，该项必传
   */
  editRequest?: any;
  /**  form 实例 */
  formInstance?: FormInstance<any>;
  onValuesChange?: any;
  onOpenChange?: any;
  onCancel?: any;
};

export default (props: AttributeType) => {
  return (
    props.type &&
    {
      add: <Edit {...props} />,
      edit: <Edit {...props} />,
      info: <Info {...props} />,
    }[props.type]
  );
};
