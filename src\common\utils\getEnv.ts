const envMap = {
  local: {
    ENV: "dev", // 环境变量
    API_HOST: "/api", // 接口地址
    CAS_API_HOST: "/casApi", // CAS 接口地址
    UPLOAD_HOST: "/upload", // 图片上传地址
    CHART_HOST: "/chartApi", // 首页图表
    OPERATION_HOST: "/operationApi", // 操作日志接口
    AI_HOST: "/aiApi" // AI 接口
  },
  dev: {
    ENV: "dev", // 运行环境
    API_HOST: "https://dev.shukeyun.com/common/common-gateway", // 接口地址
    CAS_API_HOST: "https://dev.shukeyun.com/cas/api/v1", // CAS 接口地址
    LOGIN_HOST: "https://dev.shukeyun.com/cas/login", // 登录页地址
    CAS_HOST: "https://dev.shukeyun.com/cas/web", // CAS 地址
    UPLOAD_HOST: "https://test.shukeyun.com/maintenance/deepfile/", // 图片上传地址
    IMG_HOST: "https://test.shukeyun.com/maintenance/deepfile/", // 图片预览地址
    IMG_HOST2: "https://minio-test.shukeyun.com/scenic-test/", // 图片预览地址 v2
    EXCHANGE_URL: "https://dev.shukeyun.com/scenic/exchange/", // 电商管理系统地址
    TRADING_URL: "https://dev.shukeyun.com/cas/login/#/login?appId=LNExQluBB40cv1DyBYuQ", // 交易所系统地址
    HOUSE_URL: "https://dev.shukeyun.com/scenic/house/", // 售票窗口地址
    MERCHANTS_URL: "https://dev.shukeyun.com/upaypal/hp/#/", // 结算系统地址
    WEBSITE_URL: "https://dev.shukeyun.com/scenic/website/", // 景区官网地址
    CHART_HOST: "https://test.shukeyun.com/data/api/model/exec", // 景区首页图表
    MERCHANT_HOST: "https://dev.shukeyun.com/upaypal/hp/", // 结算中心地址
    DEFAULT_LOGO: "data/2022-12-08/upload_a1c14b0ddec37ec158a04a1685decac0.png", // 新增景区默认 logo 半路径
    OPERATION_HOST: "https://dev.shukeyun.com/data/sensor/db", // 操作日志接口
    SCENIC_HOST: "https://dev.shukeyun.com/scenic/backend-v2", // 景区地址
    HLY_HOST: "https://dev.shukeyun.com/scenic/paas/", // 慧旅云主页
    MAIN_HOST: "https://dev.shukeyun.com/", // 主站域名
    HELP_URL: "https://dev.shukeyun.com/scenic/help-frontend", // 帮助中心
    SHOP_URL: "https://dev.shukeyun.com/scenic/shop/", // 易旅宝域名
    FILE_HOST: "https://minio-test.shukeyun.com/scenic-test/", // 图片音频视频查看地址
    FIT_URL: "https://dev.shukeyun.com/scenic/interface-designer-frontend/#/ticket", // 门票模板设计地址
    DESIGN_WEBSITE_URL: "https://dev.shukeyun.com/exchange/exchange-portal-designer/#/home", // 景区官网设计器地址
    DESIGN_WEBSITE_OLDURL: "https://dev.shukeyun.com/scenic/pc-website-designer/#/home", // 景区官网设计器地址
    CHAIN_URL: "https://dev-explorer.shukechain.com", // 智旅链浏览器
    Attestation: "https://dev.shukeyun.com/upaypal/financial-frontend-exchange#/user/login-debtor", // 交易所认证地址
    OFFICIAL_HOST: "https://dev.shukeyun.com/scenic/official-website/#/", // 官网预览 地址
    AI_HOST: "https://dev-gcluster.shukeyun.com", // ai 接口地址
    ORGANIZATION_HOST: "https://dev.shukeyun.com/exchange/organization-web/#" // 机构后台
  },
  test: {
    ENV: "test", // 接口地址
    API_HOST: "https://test.shukeyun.com/common/common-gateway", // 接口地址
    CAS_API_HOST: "https://test.shukeyun.com/cas/api/v1", // CAS 接口地址
    LOGIN_HOST: "https://test.shukeyun.com/cas/login", // 登录页地址
    CAS_HOST: "https://test.shukeyun.com/cas/web", // CAS 地址
    UPLOAD_HOST: "https://test.shukeyun.com/maintenance/deepfile/", // 图片上传地址
    IMG_HOST: "https://test.shukeyun.com/maintenance/deepfile/", // 图片预览地址
    IMG_HOST2: "https://minio-test.shukeyun.com/scenic-test/", // 图片预览地址 v2
    EXCHANGE_URL: "https://test.shukeyun.com/scenic/exchange/", // 电商管理系统地址
    TRADING_URL: "https://test.shukeyun.com/cas/login/#/login?appId=cEKEZaHyQ0MnWZhQeWMO", // 交易所系统地址
    HOUSE_URL: "https://test.shukeyun.com/scenic/house/", // 售票窗口地址
    MERCHANTS_URL: "https://test.shukeyun.com/upaypal/hp/#/", // 结算系统地址
    WEBSITE_URL: "https://test.shukeyun.com/scenic/website/", // 景区官网地址
    CHART_HOST: "https://test.shukeyun.com/data/api/model/exec", // 景区首页图表
    MERCHANT_HOST: "https://test.shukeyun.com/upaypal/hp/", // 结算中心地址
    DEFAULT_LOGO: "data/2022-12-08/upload_a1c14b0ddec37ec158a04a1685decac0.png", // 新增景区默认 logo 半路径
    OPERATION_HOST: "https://test.shukeyun.com/data/sensor/db", // 操作日志接口
    SCENIC_HOST: "https://test.shukeyun.com/scenic/backend-v2", // 景区地址
    HLY_HOST: "https://test.shukeyun.com/scenic/paas/", // 慧旅云主页
    MAIN_HOST: "https://test.shukeyun.com/", // 主站域名
    HELP_URL: "https://test.shukeyun.com/scenic/help-frontend", // 帮助中心
    SHOP_URL: "https://test.shukeyun.com/scenic/shop/", // 易旅宝域名
    FILE_HOST: "https://minio-test.shukeyun.com/scenic-test/", // 图片音频视频查看地址
    FIT_URL: "https://test.shukeyun.com/scenic/interface-designer-frontend/#/ticket", // 门票模板设计地址
    DESIGN_WEBSITE_URL: "https://test.shukeyun.com/exchange/exchange-portal-designer/#/home", // 景区官网设计器地址
    DESIGN_WEBSITE_OLDURL: "https://test.shukeyun.com/scenic/pc-website-designer/#/home", // 景区官网设计器地址
    CHAIN_URL: "https://test-explorer.shukechain.com", // 智旅链浏览器
    Attestation: "https://test.shukeyun.com/upaypal/financial-frontend-exchange#/user/login-debtor", // 交易所认证地址
    OFFICIAL_HOST: "https://test.shukeyun.com/scenic/official-website/#/", // 官网预览 地址
    AI_HOST: "https://test-gcluster.shukeyun.com", // ai 接口地址
    ORGANIZATION_HOST: "https://test.shukeyun.com/exchange/organization-web/#" // 机构后台
  },
  canary: {
    ENV: "canary", // 运行环境
    API_HOST: "https://canary.shukeyun.com/common/common-gateway", // 接口地址
    CAS_API_HOST: "https://canary.shukeyun.com/cas/api/v1", // CAS 接口地址
    LOGIN_HOST: "https://canary.shukeyun.com/cas/login", // 登录页地址
    CAS_HOST: "https://canary.shukeyun.com/cas/web", // CAS 地址
    UPLOAD_HOST: "https://huijingyun.net/maintenance/deepfile/", // 图片上传地址
    IMG_HOST: "https://huijingyun.net/maintenance/deepfile/", // 图片预览地址
    IMG_HOST2: "https://minio-prod.shukeyun.com/scenic-prod/", // 图片预览地址 v2
    EXCHANGE_URL: "https://canary.shukeyun.com/scenic/exchange/", // 电商管理系统地址
    TRADING_URL: "https://canary.shukeyun.com/cas/login/#/login?appId=Haa4HpAlSRJpAR9a3fUB", // 交易所系统地址
    HOUSE_URL: "https://canary.shukeyun.com/scenic/house/", // 售票窗口地址
    MERCHANTS_URL: "https://canary.shukeyun.com/upaypal/hp/#/", // 结算系统地址
    WEBSITE_URL: "https://canary.shukeyun.com/scenic/website/", // 景区官网地址
    CHART_HOST: "https://canary.shukeyun.com/data/api/model/exec", // 景区首页图表
    MERCHANT_HOST: "https://canary.shukeyun.com/upaypal/hp/", // 结算中心地址
    DEFAULT_LOGO: "data/2022-12-08/upload_a1c14b0ddec37ec158a04a1685decac0.png", // 新增景区默认 logo 半路径
    OPERATION_HOST: "https://canary.shukeyun.com/data/sensor/db", // 操作日志接口
    SCENIC_HOST: "https://canary.shukeyun.com/scenic/backend-v2", // 景区地址
    HLY_HOST: "https://canary.shukeyun.com/scenic/paas/", // 慧旅云主页
    MAIN_HOST: "https://canary.shukeyun.com/", // 主站域名
    HELP_URL: "https://canary.shukeyun.com/scenic/help-frontend", // 帮助中心
    SHOP_URL: "https://canary.shukeyun.com/scenic/shop/", // 易旅宝域名

    FILE_HOST: "https://minio-prod.shukeyun.com/scenic-prod/", // 图片音频视频查看地址
    FIT_URL: "https://canary.shukeyun.com/scenic/interface-designer-frontend/#/ticket", // 店铺装修
    DESIGN_WEBSITE_URL: "https://canary.shukeyun.com/exchange/exchange-portal-designer/#/home", // 景区官网设计器地址
    DESIGN_WEBSITE_OLDURL: "https://canary.shukeyun.com/scenic/pc-website-designer/#/home", // 景区官网设计器地址
    CHAIN_URL: "https://canary-explorer.shukechain.com", // 智旅链浏览器
    Attestation: "https://canary.shukeyun.com/upaypal/financial-frontend-exchange#/user/login-debtor", // 交易所认证地址
    OFFICIAL_HOST: "https://canary.shukeyun.com/scenic/official-website/#/", // 官网预览 地址
    AI_HOST: "https://canary-gcluster.shukeyun.com", // ai 接口地址
    ORGANIZATION_HOST: "https://canary.shukeyun.com/exchange/organization-web/#" // 机构后台
  },
  prod: {
    ENV: "prod", // 运行环境
    API_HOST: "https://huijingyun.net/common/common-gateway", // 接口地址
    CAS_API_HOST: "https://prod.shukeyun.com/cas/api/v1", // CAS 接口地址
    LOGIN_HOST: "https://prod.shukeyun.com/cas/login", // 登录页地址
    CAS_HOST: "https://cas.shukeyun.com", // CAS 地址
    UPLOAD_HOST: "https://huijingyun.net/maintenance/deepfile/", // 图片上传地址
    IMG_HOST: "https://huijingyun.net/maintenance/deepfile/", // 图片预览地址
    IMG_HOST2: "https://minio-prod.shukeyun.com/scenic-test/", // 图片预览地址 v2
    EXCHANGE_URL: "https://yeahtrip.com/", // 电商管理系统地址
    TRADING_URL: "https://prod.shukeyun.com/cas/login/#/login?appId=mszTd1wkfcZo930RM2lB", // 交易所系统地址
    HOUSE_URL: "https://yeahtrip.com/house/", // 售票窗口地址
    MERCHANTS_URL: "https://www.upaypal.com/#/", // 结算系统地址
    WEBSITE_URL: "https://prod.shukeyun.com/scenic/website/", // 景区官网地址
    CHART_HOST: "https://prod.shukeyun.com/data/api/model/exec", // 景区首页图表
    MERCHANT_HOST: "https://www.upaypal.com/", // 结算中心地址
    DEFAULT_LOGO: "data/2022-12-08/upload_d7ab9da0c0e01de8334e79e01d8323a4.png", // 新增景区默认 logo 半路径
    OPERATION_HOST: "https://huijingyun.net/data/sensor/db", // 操作日志接口
    SCENIC_HOST: "https://huijingyun.net", // 景区地址
    HLY_HOST: "https://hly.net", // 慧旅云主页
    MAIN_HOST: "https://prod.shukeyun.com/", // 主站域名
    HELP_URL: "https://help.hly.net", // 帮助中心
    SHOP_URL: "https://yilvbao.cn/", // 易旅宝域名
    FILE_HOST: "https://minio-prod.shukeyun.com/scenic-prod/", // 图片音频视频查看地址
    FIT_URL: "https://prod.shukeyun.com/scenic/interface-designer-frontend/#/ticket", // 门票模板设计地址
    DESIGN_WEBSITE_URL: "https://prod.shukeyun.com/exchange/exchange-portal-designer/#/home", // 景区官网设计器地址
    DESIGN_WEBSITE_OLDURL: "https://prod.shukeyun.com/scenic/pc-website-designer/#/home", // 景区官网设计器地址
    CHAIN_URL: "https://explorer.shukechain.com", // 智旅链浏览器
    Attestation: "https://prod.shukeyun.com/upaypal/financial-frontend-exchange#/user/login-debtor", // 交易所认证地址
    OFFICIAL_HOST: "https://prod.shukeyun.com/scenic/official-website/#/", // 官网预览 地址
    AI_HOST: "https://gcluster.shukeyun.com", // ai 接口地址
    ORGANIZATION_HOST: "https://prod.shukeyun.com/exchange/organization-web/#" // 机构后台
  }
};

export const getEnv = () => {
  // console.log("process.env.PROXY_ENV:  ", process.env.PROXY_ENV);
  const hostname = location.hostname;
  if (hostname.includes("test")) {
    return envMap.test;
  } else if (hostname.includes("canary")) {
    return envMap.canary;
  } else if (hostname.includes("huijingyun.net") || hostname.includes("prod")) {
    return envMap.prod;
  } else if (hostname.includes("local") || hostname.includes("192")) {
    if (process.env.PROXY_ENV === "test") {
      return { ...envMap.test, ...envMap.local };
    } else if (process.env.PROXY_ENV === "canary") {
      return { ...envMap.canary, ...envMap.local };
    } else if (process.env.PROXY_ENV === "prod") {
      return { ...envMap.prod, ...envMap.local };
    }
    return { ...envMap.dev, ...envMap.local };
  } else {
    return envMap.dev;
  }
};
