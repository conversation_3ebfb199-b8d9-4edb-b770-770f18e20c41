/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-08-15 13:46:41
 * @LastEditTime: 2023-09-08 11:38:37
 * @LastEditors: zhangfengfei
 */
import { getUniqueId } from '@/common/utils/tool';
import type { ProDescriptionsProps } from '@ant-design/pro-descriptions';
import ProDescriptions from '@ant-design/pro-descriptions';
import type { ModalProps } from 'antd';
import { Modal } from 'antd';
import type { FC } from 'react';

export type ProDescriptionsGroup<T> = Omit<ProDescriptionsProps<T>, 'params' | 'request'>;

interface ModalDescriptionsProps {
  modalProps: ModalProps;
  list: ProDescriptionsGroup<any>[];
  dataSource: any;
  loading: boolean;
}

const ModalDescriptions: FC<ModalDescriptionsProps> = ({
  modalProps,
  list,
  dataSource,
  loading,
}) => {
  return (
    <Modal destroyOnClose {...modalProps}>
      {list.map((group, index) => {
        return (
          <div key={getUniqueId()}>
            <ProDescriptions
              column={2}
              labelStyle={{ fontWeight: 'bold' }}
              loading={loading}
              {...group}
              dataSource={dataSource}
            />
          </div>
        );
      })}
    </Modal>
  );
};

export default ModalDescriptions;
