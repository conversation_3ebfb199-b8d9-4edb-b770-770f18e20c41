/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-07-05 11:41:45
 * @LastEditTime: 2023-08-28 18:38:24
 * @LastEditors: zhangfengfei
 */
import { deleteTicketTemplate, getTicketTemplateList } from "@/services/api/settings";
import { DeleteOutlined, EditOutlined } from "@ant-design/icons";
import { Card, Image, Modal, Space, message } from "antd";
import type { FC } from "react";
import { useEffect, useState } from "react";
import { useAccess, useModel } from "@umijs/max";
import { getEnv } from "@/common/utils/getEnv";

interface ParamsConfigProps {}

const { confirm } = Modal;

const TicketTemplateConfig: FC<ParamsConfigProps> = () => {
  const { FIT_URL, FILE_HOST } = getEnv();
  const { scenicInfo } = useModel("@@initialState", model => model.initialState) || {};
  const access = useAccess();

  const [visible, setVisible] = useState(false);
  const [ticketTemplateList, setTicketTemplateList] = useState([]); // 门票模板列表

  // 获取门票模板列表
  const getTicketTemplate = () => {
    getTicketTemplateList({ scenicid: scenicInfo?.scenicId }).then(res => {
      setTicketTemplateList(res.data || []);
    });
  };

  useEffect(() => {
    // 获取门票模板列表
    getTicketTemplate();
  }, []);

  return (
    <Card
      title={
        <div style={{ position: "relative" }}>
          <span
            style={{
              display: "inline-block",
              width: "2px",
              height: "16px",
              background: "#1890ff",
              marginRight: 8,
              marginBottom: -2
            }}
          />
          <span style={{ fontWeight: 600 }}>{"门票打印模板"}</span>
          <div style={{ float: "right" }}>
            {/* <Access key="link" accessible={access.canGlobalSettings_paramEdit}> */}
            <a
              style={{
                fontWeight: "400"
              }}
              onClick={() => {
                location.href = `${FIT_URL}?scenicName=${scenicInfo?.scenicName}&scenicId=${scenicInfo?.scenicId}&code=${scenicInfo?.uniqueIdentity}`;
              }}
            >
              新增
            </a>
            {/* </Access> */}
          </div>
        </div>
      }
    >
      <Space direction="horizontal" size={24} wrap>
        {ticketTemplateList.map(item => {
          return (
            <Card
              key={item.id}
              size="small"
              style={{ width: 316 }}
              title={item.templateName}
              actions={[
                <EditOutlined
                  key="edit"
                  onClick={() => {
                    location.href = `${FIT_URL}?scenicName=${scenicInfo?.scenicName}&id=${item.id}&code=${scenicInfo?.uniqueIdentity}&scenicId=${scenicInfo?.scenicId}`;
                  }}
                />,
                <DeleteOutlined
                  onClick={() => {
                    // 删除模板
                    if (ticketTemplateList.length === 1) {
                      message.error("无法删除，请您最少保留一个模板");
                      return;
                    }
                    confirm({
                      title: "确定删除模板？",
                      // icon: <ExclamationCircleOutlined />,
                      // content: 'Some descriptions',
                      onOk() {
                        deleteTicketTemplate({
                          id: item.id,
                          scenicId: scenicInfo?.scenicId
                        }).then(() => {
                          message.success("删除成功");
                          getTicketTemplate();
                        });
                      },
                      onCancel() {
                        console.log("Cancel");
                      }
                    });
                  }}
                  key="del"
                />
              ]}
            >
              <Image height={100} src={FILE_HOST + item.templateUrl} style={{ objectFit: "contain" }} />
            </Card>
          );
        })}
      </Space>
    </Card>
  );
};

export default TicketTemplateConfig;
