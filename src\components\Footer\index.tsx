import './index.less';

interface FooterProps {
  color?: string;
}

const Footer: React.FC<FooterProps> = ({ color }) => {
  const currentYear = new Date().getFullYear();

  return (
    <div id="footer" style={{ color }}>
      Copyright
      {` © 2001-${currentYear} `}
      <a href="http://www.hqshuke.com/" target="_blank" rel="noreferrer" style={{ color }}>
        环球数科股份有限公司
      </a>
      {` 提供技术支持 版权所有 | `}
      <a
        href="https://beian.miit.gov.cn/#/Integrated/index"
        target="_blank"
        rel="noreferrer"
        style={{ color }}
      >
        粵 ICP 备 09156180 号
      </a>
    </div>
  );
};
export default Footer;
