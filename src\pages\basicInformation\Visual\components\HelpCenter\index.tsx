import EditPop from "@/common/components/EditPop";
import ProModal from "@/common/components/ProModal";
import useModal from "@/common/components/ProModal/useModal";
import { tableConfig } from "@/common/utils/config";
import { GuideStepStatus } from "@/common/utils/enum";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import { removeStateFromUrl } from "@/common/utils/tool";
import MDEditor from "@/components/MDEditor";
import { useGuide } from "@/hooks/useGuide";
import {
  articleAdd,
  articleCategorySort,
  articleDel,
  articleEdit,
  articleEnable,
  articlePage,
  categoryAdd,
  categoryDel,
  categoryEdit,
  categoryTree
} from "@/services/api/erp";
import { DownOutlined, FileTextOutlined, PlusOutlined } from "@ant-design/icons";
import ProCard from "@ant-design/pro-card";
import type { ProFormColumnsType } from "@ant-design/pro-form";
import type { ActionType, ProColumns } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import { Button, Col, Dropdown, Input, Popconfirm, Row, Space, Switch, Tree, message } from "antd";
import type { DataNode } from "antd/lib/tree";
import { useEffect, useRef, useState } from "react";
import { useModel } from "@umijs/max";
import "./index.less";

const { Search } = Input;
let addDate = {};
let dataList: { key: React.Key; name: string; id: string }[] = [];

export default () => {
  // 【景区】信息
  const { initialState } = useModel("@@initialState");
  const { scenicId, isBlockChain } = initialState?.scenicInfo || {};
  const modalState = useModal();
  const { updateGuideInfo } = useGuide();

  const actionRef = useRef<ActionType>();
  const actionRef2 = useRef<ActionType>();
  const searchRef = useRef<any>();
  const [categoryId, setCategoryId] = useState<any>();
  const [categorySort, setCategorySort] = useState<any>();
  const [treeData, setTreeData] = useState<any>();
  const [visible, setVisible] = useState<boolean>(false);
  const [articleDataSource, setArticleDataSource] = useState<any>({});
  const [categoryVisible, setCategoryVisible] = useState<boolean>(false);
  const [categoryDataSource, setCategoryDataSource] = useState<any>({});
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [searchKeys, setSearchKeys] = useState<React.Key[]>([]);
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const [defaultSelectedKeys, setDefaultSelectedKeys] = useState<any>([]);

  // 格式化数据
  const removeEmpty = (arr: any) => {
    const arr2 = arr.filter(Boolean);
    arr2.map((item: any) => {
      if (item?.children?.length) {
        item.children = removeEmpty(item.children);
      }
    });
    return arr2;
  };
  // 初始化数据
  const initTree = (initialize: boolean = false) => {
    categoryTree({ scenicId: scenicId }).then((res: any) => {
      const setData = (arr: any) => {
        const data: any = [];
        const dataOther: any = [];
        arr.map((v: any) => {
          const obj = {
            id: v.id,
            categoryId: v.categoryId, // 文章（目录id）
            parentId: v.parentId, // 目录（上级id）
            title: v.name ? (
              <span title={v.name}>
                {v.name}
                <Dropdown
                  className="float"
                  // trigger={['click']}
                  menu={{
                    items: [
                      {
                        key: "edit",
                        label: "重命名",
                        onClick(e: any) {
                          e.domEvent.stopPropagation();
                          edit({ id: v.id, name: v.name });
                        }
                      },
                      {
                        key: "add",
                        label: "加同级",
                        onClick: (e: any) => {
                          e.domEvent.stopPropagation();
                          add(v.parentId, "");
                        }
                      },
                      {
                        key: "del",
                        label: "删除",
                        onClick: (e: any) => {
                          e.domEvent.stopPropagation();
                          del(v.id, v.name);
                        }
                      }
                    ]
                  }}
                >
                  <a>
                    <DownOutlined />
                  </a>
                </Dropdown>
              </span>
            ) : (
              <span title={v.title}>
                <FileTextOutlined /> {v.title}
              </span>
            ),
            name: v.name || v.title,
            key: v.sort,
            children: [],
            selectable: v.categoryId == undefined
          };
          try {
            const s: any = v.sort
              .toString()
              .split("-")
              .map((v_sort: any, k_sort: any) => `${k_sort ? ".children" : ""}[${v_sort}]`)
              .join("");
            eval(`data${s}=obj`);
          } catch (error) {
            dataOther.push(obj);
          }
        });
        // 异常数据处理
        const otherIndex = data.length;
        dataOther.map((v: any, k: any) => {
          v.key = `${otherIndex}-${k}`;
        });
        const dataResult = [...removeEmpty(data), ...dataOther];
        setTreeData(dataResult);
        // 获取 key 集合
        const generateList = (data: any[]) => {
          for (let i = 0; i < data.length; i++) {
            const node = data[i];
            const { key, name, id } = node;
            dataList.push({ key, name, id });
            if (node.children) {
              generateList(node.children);
            }
          }
        };
        dataList = [];
        generateList(dataResult);
      };
      const data = [...res.data.categoryList, ...res.data.articleList];
      setData(data);
      // 初始化展开目录
      if (initialize) {
        setExpandedKeys(data.map(item => item.sort));
      } else {
        // 回显筛选项
        if (searchRef.current?.input?.value) onChange({ target: { value: searchRef.current.input.value } });
        // 回显选中项
        if (categoryId) setDefaultSelectedKeys([dataList.filter((item: any) => item.id == categoryId)[0]?.key]);
      }
    });
  };

  useEffect(() => {
    initTree(true);
  }, []);

  // 拖动排序
  const onDrop = (info: any) => {
    // 拦截根目录文章
    if (info.dragNode.categoryId && info.node.parentId == "0" && info.dropToGap) {
      message.info("文章需指定目录");
      return;
    }
    const dropKey = info.node.key;
    const dragKey = info.dragNode.key;
    const dropPos = info.node.pos.split("-");
    const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1]);

    const loop = (
      data: DataNode[],
      key: React.Key,
      callback: (node: DataNode, i: number, data: DataNode[]) => void
    ) => {
      for (let i = 0; i < data.length; i++) {
        if (data[i].key === key) {
          return callback(data[i], i, data);
        }
        if (data[i].children) {
          loop(data[i].children!, key, callback);
        }
      }
    };
    const data = [...treeData];

    // Find dragObject
    let dragObj: DataNode;
    loop(data, dragKey, (item, index, arr) => {
      arr.splice(index, 1);
      dragObj = item;
    });

    if (!info.dropToGap) {
      // Drop on the content
      loop(data, dropKey, item => {
        item.children = item.children || [];
        // where to insert. New item was inserted to the start of the array in this example, but can be anywhere
        item.children.unshift(dragObj);
      });
    } else if (
      ((info.node as any).props.children || []).length > 0 && // Has children
      (info.node as any).props.expanded && // Is expanded
      dropPosition === 1 // On the bottom gap
    ) {
      loop(data, dropKey, item => {
        item.children = item.children || [];
        // where to insert. New item was inserted to the start of the array in this example, but can be anywhere
        item.children.unshift(dragObj);
        // in previous version, we use item.children.push(dragObj) to insert the
        // item to the tail of the children
      });
    } else {
      let ar: DataNode[] = [];
      let i: number;
      loop(data, dropKey, (_item, index, arr) => {
        ar = arr;
        i = index;
      });
      if (dropPosition === -1) {
        ar.splice(i!, 0, dragObj!);
      } else {
        ar.splice(i! + 1, 0, dragObj!);
      }
    }
    // 排序
    const submitData: any = {
      articlesSortData: [],
      categorySortData: []
    };
    const traverse = (arr: any, key: any, id: any) => {
      arr.forEach((v: any, k: any) => {
        submitData[v.categoryId ? "articlesSortData" : "categorySortData"].push({
          id: v.id,
          sort: key + (k + 1),
          [v.categoryId ? "categoryId" : "f"]: id
        });
        traverse(v.children, key + (k + 1) + "-", v.id);
      });
    };
    traverse(data, "", "0");
    articleCategorySort(submitData)
      .then(() => {
        initTree();
        actionRef?.current?.reload();
      })
      .catch(() => {
        initTree();
      });
    // setTreeData(data);
  };

  // 搜索
  const onExpand = (newExpandedKeys: React.Key[]) => {
    setExpandedKeys(newExpandedKeys);
    setAutoExpandParent(false);
  };
  const onChange = (e: any) => {
    const { value } = e.target;
    let keyList = [];
    if (value == "") {
      keyList = dataList.map((item: any) => item.key);
      setSearchKeys([]);
    } else {
      keyList = dataList.filter((item: any) => value && item.name.indexOf(value) != -1).map((item: any) => item.key);
      setSearchKeys(keyList);
    }
    setExpandedKeys(keyList);
    setAutoExpandParent(true);
  };
  // 表格配置
  const columns: ProColumns[] = [
    {
      title: "文章标题",
      dataIndex: "title"
    },
    {
      title: "发布时间",
      dataIndex: "publishTime",
      search: false
    },
    {
      title: "阅读量",
      dataIndex: "readCount",
      search: false
    },
    {
      title: "文章状态",
      dataIndex: "enableState",
      valueEnum: { "2": "启用", "1": "禁用" },
      hideInTable: true
    },
    {
      title: "启用状态",
      dataIndex: "enableState",
      search: false,
      fixed: "right",
      render: (dom: any, entity: any) => (
        <Switch
          checkedChildren="启用"
          unCheckedChildren="禁用"
          checked={dom == 2}
          onChange={b => editArticleStatus(entity.id, b, entity.title)}
        />
      )
    },
    {
      width: "auto",
      title: "操作",
      valueType: "option",
      fixed: "right",
      render: (_, entity) => (
        <Space>
          <a style={{ color: "#1890FF" }} onClick={() => editArticle(entity)}>
            编辑
          </a>
          <Popconfirm title="确定删除？" onConfirm={() => delArticle(entity.id, entity.title)}>
            <a style={{ color: "#FF4D4F" }}>删除</a>
          </Popconfirm>
        </Space>
      )
    }
  ];
  const request = async (params: any) => {
    params.scenicId = scenicId;
    const { data } = await articlePage(params);
    return data;
  };
  // 表单配置
  const editColumns: ProFormColumnsType<any>[] = [
    {
      title: "",
      valueType: "group",
      columns: [
        {
          title: "文章标题",
          dataIndex: "title",
          formItemProps: { rules: [{ required: true, max: 100 }] }
        },
        {
          title: "发布日期",
          dataIndex: "publishTime",
          span: 3,
          valueType: "date",
          formItemProps: { rules: [{ required: true }] }
        },
        {
          title: "文章内容",
          dataIndex: "content",
          formItemProps: { rules: [{ required: true }] },
          colProps: {
            span: 24
          },
          renderFormItem: () => <MDEditor />
        }
      ]
    }
  ];
  const categoryEditColumns: ProFormColumnsType<any>[] = [
    {
      title: "",
      valueType: "group",
      columns: [
        {
          title: "目录名称",
          dataIndex: "name",
          formItemProps: { rules: [{ required: true }] }
        }
      ]
    }
  ];
  const add = (parentId: string, sort: string) => {
    addDate = { parentId, sort };
    setCategoryDataSource({});
    setCategoryVisible(true);
  };
  const del = (id: string, name: string) => {
    categoryDel(id)
      .then((res: any) => {
        if (res.code == 20000) {
          addOperationLogRequest({
            action: "del",
            content: `删除目录【${name}】`
          });
          message.success("删除成功");
          initTree();
        }
      })
      .catch(() => {});
  };
  const edit = (val: any) => {
    setCategoryDataSource(val);
    setCategoryVisible(true);
  };
  const editArticle = (val: any) => {
    setArticleDataSource(val);
    modalState.setType("edit");
  };
  const delArticle = (id: string, title: string) => {
    articleDel(id)
      .then((res: any) => {
        if (res.code == 20000) {
          addOperationLogRequest({
            action: "del",
            content: `删除文章【${title}】`
          });
          message.success("删除成功");
          initTree();
          actionRef?.current?.reload();
        }
      })
      .catch(() => {});
  };
  const editArticleStatus = (id: string, b: boolean, title: string) => {
    articleEnable({ id: id, enableState: b ? 2 : 1 })
      .then((res: any) => {
        if (res.code == 20000) {
          if (b) {
            // 更新引导
            updateGuideInfo({ tabIndex: 1, status: GuideStepStatus.step1_6 });
          }
          addOperationLogRequest({
            action: "disable",
            content: `${b ? "启用" : "禁用"}文章【${title}】`
          });
          message.success(b ? "已启用" : "已禁用");
          initTree();
          actionRef?.current?.reload();
        }
      })
      .catch(() => {});
  };

  return (
    <>
      {/** style={modalState.tableStytle} 是用来做为整页切换时所隐藏的标记用的 */}
      <Row style={modalState.tableStytle} gutter={20} wrap={false}>
        <Col flex="300px">
          <ProCard>
            <Search ref={searchRef} style={{ marginBottom: 8 }} placeholder="搜索关键字" onChange={onChange} />
            <Tree
              defaultExpandAll
              filterTreeNode={e => searchKeys.indexOf(e.key) != -1}
              showLine
              rootClassName="treeClass"
              draggable={{ icon: false }}
              blockNode
              treeData={treeData}
              onDrop={onDrop}
              allowDrop={({ dropNode, dropPosition }: any) => {
                return dropNode.parentId || dropPosition;
              }}
              onSelect={(_, e: any) => {
                setCategoryId(e.selected ? e.node.id : "");
                setCategorySort(e.node.key);
                setDefaultSelectedKeys(e.selected ? [e.node.key] : []);
              }}
              onExpand={onExpand}
              expandedKeys={expandedKeys}
              autoExpandParent={autoExpandParent}
              selectedKeys={defaultSelectedKeys}
            />
          </ProCard>
        </Col>
        <Col flex="auto">
          <ProTable
            {...tableConfig}
            rowClassName={row => (row.enableState == 2 ? "" : "disableRow")}
            actionRef={actionRef}
            toolBarRender={() => [
              <Button
                type="primary"
                onClick={() => {
                  if (categoryId) {
                    setArticleDataSource({});
                    modalState.setType("add");
                  } else {
                    message.info("请先指定目录");
                  }
                }}
              >
                <PlusOutlined />
                新增
              </Button>
            ]}
            params={{ categoryId }}
            columns={columns}
            request={request}
          />
        </Col>
      </Row>

      <ProModal
        page
        {...modalState}
        title="文章"
        actionRef={actionRef2}
        columns={editColumns}
        params={{ categoryId, scenicId }}
        dataSource={articleDataSource}
        addRequest={async (params: any) => {
          const data = await articleAdd(params);
          history.pushState(null, null, removeStateFromUrl(["tabKey"]));
          addOperationLogRequest({
            action: "add",
            content: `新增文章【${params.title}】`
          });
          initTree();
          actionRef?.current?.reload();
          return data;
        }}
        editRequest={async (params: any) => {
          params.id = articleDataSource.id;
          delete params.categoryId; //删除多余属性
          delete params.scenicId; //删除多余属性
          const data = await articleEdit(params);

          addOperationLogRequest({
            action: "edit",
            changeConfig: {
              list: [
                {
                  title: "文章标题",
                  dataIndex: "title"
                },
                {
                  title: "发布时间",
                  dataIndex: "publishTime"
                }
              ],
              beforeData: articleDataSource,
              afterData: params
            },
            content: `编辑文章【${articleDataSource.title}】`
          });
          initTree();
          actionRef?.current?.reload();

          return data;
        }}
      />

      <EditPop
        title="目录"
        visible={categoryVisible}
        setVisible={setCategoryVisible}
        columns={categoryEditColumns}
        dataSource={categoryDataSource}
        width={374}
        onFinish={(val: any) => {
          if (categoryDataSource.id) {
            categoryEdit({ id: categoryDataSource.id, ...val })
              .then((res: any) => {
                if (res.code == 20000) {
                  message.success("重命名成功");
                  addOperationLogRequest({
                    action: "edit",
                    changeConfig: {
                      list: [
                        {
                          dataIndex: "name",
                          title: "目录名称"
                        }
                      ],
                      beforeData: categoryDataSource,
                      afterData: val
                    },
                    content: `编辑目录【${categoryDataSource.name}】`
                  });
                  setCategoryVisible(false);
                  initTree();
                }
              })
              .catch(() => {});
          } else {
            categoryAdd({ scenicId: scenicId, ...addDate, ...val })
              .then((res: any) => {
                if (res.code == 20000) {
                  addOperationLogRequest({
                    action: "add",
                    content: `新增目录【${val.name}】`
                  });
                  message.success("添加成功");
                  setCategoryVisible(false);
                  initTree();
                }
              })
              .catch(() => {});
          }
        }}
      />
    </>
  );
};
