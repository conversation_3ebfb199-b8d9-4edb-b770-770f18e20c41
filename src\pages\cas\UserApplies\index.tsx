import { modelWidth } from "@/common/utils/gConfig";
import { jumpPage } from "@/common/utils/tool";
import ProCard from "@ant-design/pro-card";
import { Button, Col, Input, message, Row } from "antd";
import { useEffect, useRef, useState } from "react";
import { useModel } from "@umijs/max";

// import {getUserApply} from '../../../services/api/cas'
import { getOrgStructureVerifyCert, getUserApply, getUserSocial } from "@/services/api/cas";
export default function UserApplies() {
  const actionRef = useRef();
  const [modalVisit, setModalVisit] = useState(false);
  //   输入框的值
  const [nameValue, setNameValue] = useState(undefined);
  //当前用户信息
  const [userMessage, setUserMessage] = useState({});
  const { initialState }: any = useModel("@@initialState");

  console.log("yyyyyyyyyyyyyyy", initialState);
  const { nickname, userName, userId } = initialState.userInfo;
  //从地址获取数据
  const Defaultpath = urlTool(window.location.href);
  console.log("Defaultpath", initialState);
  //获取用户 id
  const getUserId = async () => {
    const result = await getUserSocial();
    console.log("ppppppppp", result.data);
    setUserMessage(result.data);
    // setUserId(userId)
  };
  useEffect(() => {
    // console.log('hujiajijaijijijijaiajiajijia',`${getScenicIdentifier()}/userCenter/ApplicationApproved?name=${companyName}`)
    getUserId();
    verification();
  }, []);
  //验证
  const [dataStatus, setDataStatus] = useState(false);
  // const { cert, userId, companyName }: any = history.location.query;
  const cert = Defaultpath.cert;
  const userId2 = Defaultpath.userId;
  const companyName = decodeURIComponent(Defaultpath.companyName);
  const userName2 = decodeURIComponent(Defaultpath.userName);
  const companyId = Defaultpath.companyId;
  const verification = async () => {
    const pars = {
      cert: cert,
      userId: userId2,
      companyId: companyId
    };
    // console.log('2222222222222222222222', pars);
    try {
      const result = await getOrgStructureVerifyCert(pars);
      setDataStatus(result.data);
    } catch (e) {
      console.error(e);
    }
  };
  // const [username,setuserName] =useState('')
  // let userName2
  const GetInvolved = async () => {
    const pars = {
      applyName: nameValue,
      userId: userId,
      companyId,
      systemTypes: 2
    };
    // console.log('.......................', pars);
    try {
      if (nameValue) {
        if (dataStatus) {
          const result = await getUserApply(pars);
          message.success("申请成功，等待管理员审批");
          jumpPage.push(`/ApplicationApproved?name=${companyName}`);
        } else {
          message.info("此链接已经失效");
        }
      } else {
        message.info("请输入姓名");
      }
    } catch (e) {
      console.error(e);
    }
  };

  const getName = e => {
    setNameValue(e.target.value);
    // message.success(e.target.value)
  };

  // ：使用字符串截取
  function urlTool(url) {
    //将 url 用“?”和“&”分割;
    const array = url.split("?", 2).pop().split("&");
    console.log(url.split("?", 2).pop());
    //声明一个空对象用来储存分割后的参数；
    const data = {};

    array.forEach(ele => {
      //将获得到的每个元素用 "="进行分割
      const dataArr = ele.split("=");

      //将数组的每一个元素遍历到对象中;
      data[dataArr[0]] = dataArr[1];
    });
    return data;
  }

  console.log("rrrrrrr", Defaultpath);

  return (
    <>
      <>
        <ProCard bordered layout="center" style={{ maxWidth: modelWidth.sm, margin: "300px auto" }}>
          <div style={{ display: "flex", justifyContent: "center", flexDirection: "column" }}>
            <Row gutter={[16, 16]} justify="center">
              <Col>
                {`${userName2}`}
                邀请你加入
              </Col>
            </Row>
            <Row gutter={[16, 16]} justify="center">
              <Col>
                <h2>{companyName}</h2>
              </Col>
              <Col>
                <Input style={{ width: modelWidth.sm - 100 }} placeholder="姓名" onChange={e => getName(e)} />
              </Col>
              <Col>
                <Button type="primary" style={{ width: modelWidth.sm - 100 }} onClick={GetInvolved}>
                  立即加入
                </Button>
              </Col>
            </Row>
          </div>
        </ProCard>
      </>
    </>
  );
}
