import EditPop from "@/common/components/EditPop";
import { getUniqueId } from "@/common/utils/tool";
import { apiSosAddUpdate, apiSosDelete, apiSosDetails, apiSosEnable, apiSosPageList } from "@/services/api/device";
import { PlusOutlined } from "@ant-design/icons";
import ProForm, { ModalForm, ProFormText } from "@ant-design/pro-form";
import type { ActionType, ProColumns } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import { Button, Input, message, Modal, Tag } from "antd";
import React, { useRef, useState } from "react";
import { Access, useAccess, useModel } from "@umijs/max";
import Details from "./Details";
const { confirm } = Modal;
const AlarmPoint: React.FC = () => {
  const formRef = useRef();
  const access = useAccess();
  const formObj = useRef();
  const actionRef = useRef<ActionType>();
  const [modalVisit, setModalVisit] = useState(false);
  //获取当前 ID
  const [Id, setId] = useState(undefined);
  // 【表单】数据绑定
  const [editVisible, setEditVisible] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<any>({ id: "", isEnable: 0 });
  // 获取景区 ID
  const { initialState }: any = useModel("@@initialState");
  const scenicId = initialState?.scenicInfo?.scenicId;
  console.log(scenicId, initialState);

  //获取景区名称
  const scenicName = initialState?.scenicInfo?.scenicName;

  //初始视化数据
  const [initialValues, setInitialValues] = useState({});

  const [isEnableStatus, setIsEnableStatus] = useState(undefined);

  // 详情
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [detailData, setDetailsData] = useState([]);
  // //获取当前 id
  // const [isId,setIsId]=useState()

  const showModal = async (val: any) => {
    const id = val.id;
    const result: any = await apiSosDetails(id);
    const { data } = result;
    console.log("yyds", data, val);
    //回选
    // formObj?.current?.setFieldsValue(data);
    setDetailsData(data);
    setInitialValues(data);
    // setDataSource(data);
    setId(val.id);
    setIsEnableStatus(data.isEnable);
    // setModalVisit(true);
    setIsModalVisible(true);
  };

  const handleOk = () => {
    setIsModalVisible(false);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  //编辑
  const updateMethod = async (val: any) => {
    const id = val.id;
    const { data }: any = await apiSosDetails(id);

    setInitialValues(data);

    setIsEnableStatus(val.isEnable);
    setId(val.id);
    setIsModalVisible(false);
    setModalVisit(true);
  };

  //启用/禁用
  const onStatus = async (val: any) => {
    console.log(val.isEnable);
    confirm({
      title: `您确定${val.isEnable == 1 ? "禁用" : "启用"}吗？`,
      // content: 'Some descriptions',
      onOk: async () => {
        try {
          const result = await apiSosEnable(val.id);
          message.success(val.isEnable == 0 ? "启用成功" : "禁用成功");
          console.log(val);
          // getNoticeList()
          // 刷新
          actionRef?.current?.reload();
          setIsModalVisible(false);
        } catch (e) {
          console.error(e);
        }
      },
      onCancel() {
        console.log("Cancel");
      }
    });
  };
  //删除
  // const onDelete = () => { }
  const onDelete = (id: any) => {
    confirm({
      title: `您确定删除吗？`,
      // content: 'Some descriptions',
      onOk: async () => {
        try {
          const result = await apiSosDelete(id);
          // console.log(result);
          message.success("删除成功");
          // 刷新
          actionRef?.current?.reload();
          setIsModalVisible(false);
        } catch (e) {
          console.log(e);
        }
      },
      onCancel() {
        console.log("Cancel");
      }
    });
  };

  //获取列表
  const getSosPageList = async (params: any) => {
    const pars = { ...params, scenicId };
    try {
      const result = await apiSosPageList(pars);
      const { data } = result.data;
      console.log("hujiajia", result);
      return {
        data,
        success: true,
        total: data.total
      };
    } catch (e) {
      console.error(e);
    }
  };

  // 保存数据
  const submitData = async (val: any) => {
    console.log("123131321321231", val);
    const pras = {
      ...val,
      scenicId
    };
    try {
      const result = await apiSosAddUpdate(pras);
      // message.success('保存成功');
      console.log(result);
      message.success(val.hasOwnProperty("id") ? "编辑成功" : "新增成功");
      actionRef?.current?.reload();
      setEditVisible(false);
    } catch (e) {
      console.error(e);
    }
  };

  const columns: ProColumns[] = [
    {
      title: "所属景区",
      dataIndex: "scenicName",
      hideInSearch: true,
      width: "10%"
    },
    {
      title: "点位名称",
      dataIndex: "sosName",
      ellipsis: true,
      width: "10%",
      render: (dom, record) => {
        return <a onClick={() => showModal(record)}>{record.sosName}</a>;
      }
      // renderFormItem: () => {
      //   return <Input placeholder="请输入点位名称" />;
      // },
    },

    {
      title: "品牌",
      dataIndex: "brand",
      width: "10%",
      renderFormItem: () => {
        return <Input placeholder="请输入品牌" />;
      }
      // valueType: 'select',
      // hideInSearch: true,
      // valueEnum: {
      //   // '0': '海康',
      //   // '1': '腾达',
      //   // '2': '先科',
      //   // '3': '刻锐',
      // },
    },
    {
      title: "链接地址",
      dataIndex: "linkAddress",
      hideInSearch: true,
      width: "10%"
    },
    {
      title: "状态",
      dataIndex: "isEnable",
      valueType: "select",
      width: "10%",
      // hideInSearch: true,
      valueEnum: {
        "0": {
          text: "禁用"
        },
        "1": {
          text: "启用"
        }
      },
      render: (dom: any, record: any) => {
        return <Tag color={record.isEnable == 1 ? "blue" : "red"}>{record.isEnable == 1 ? "已启用" : "已禁用"}</Tag>;
      }
      // render: (_, record) => <Tag color={record.isEnable.color}>{record.isEnable.text}</Tag>,
    }
  ];
  const editColumns = [
    {
      columns: [
        {
          title: "所属景区",
          dataIndex: "scenicName",
          name: "scenicName",
          initialValue: `${scenicName}`,
          key: "scenicName",
          // valueEnum: {
          //   '0': '公告',
          // },
          formItemProps: {
            // rules: [{ required: true }],
            // disable: false
          },
          fieldProps: {
            disabled: true
          }
        },
        {
          dataIndex: "sosName",
          title: "点位名称",
          name: "sosName",
          ellipsis: true,
          key: "sosName",
          fieldProps: {},
          formItemProps: {
            rules: [{ required: true }, { max: 100, message: "最多可输入 100 个字符" }]
          }
        },
        {
          dataIndex: "loginName",
          title: "账号",
          name: "loginName",
          key: "loginName",
          fieldProps: {},
          formItemProps: {
            rules: [{ required: true }, { max: 100, message: "最多可输入 100 个字符" }]
          }
        },
        {
          dataIndex: "loginPassword",
          title: "密码",
          name: "loginPassword",
          key: "loginPassword",
          fieldProps: {},
          formItemProps: {
            rules: [{ required: true }, { max: 100, message: "最多可输入 100 个字符" }]
          }
        },
        // {
        //   dataIndex: 'longitude',
        //   title: '经度',
        //   name: 'longitude',
        //   key: 'longitude',
        //   fieldProps: {},
        //   formItemProps: {
        //     rules: [{ required: true }],
        //   },
        // },
        // {
        //   dataIndex: 'latitude',
        //   title: '纬度',
        //   name: 'latitude',
        //   key: 'latitude',
        //   fieldProps: {},
        //   formItemProps: {
        //     rules: [{ required: true }],
        //   },
        // },
        {
          title: "品牌",
          dataIndex: "brand",
          name: "brand",
          // valueEnum: {
          //     '0': '1',
          //     "1": '2'
          // },
          key: "brand",
          formItemProps: {
            rules: [{ required: true }, { max: 100, message: "最多可输入 100 个字符" }]
          }
        },
        {
          dataIndex: "linkAddress",
          title: "链接地址",
          name: "linkAddress",
          key: "linkAddress",
          fieldProps: {},
          formItemProps: {
            rules: [{ required: true }, { max: 100, message: "最多可输入 100 个字符" }]
          }
        },
        {
          dataIndex: "point",
          title: "管理端口",
          name: "point",
          key: "point",
          fieldProps: {},
          formItemProps: {
            rules: [{ max: 100, message: "最多可输入 100 个字符" }]
          }
        },
        {
          dataIndex: "channel",
          title: "通道号",
          name: "channel",
          key: "channel",
          fieldProps: {},
          // formItemProps: {
          //   rules: [{ required: true }],
          // },
          formItemProps: {
            rules: [{ max: 100, message: "最多可输入 100 个字符" }]
          }
        }
      ]
    }
  ];

  return (
    <>
      {/* 详情 */}
      <Modal
        title="SOS报警点"
        visible={isModalVisible}
        width={800}
        onOk={handleOk}
        onCancel={handleCancel}
        footer={[
          <Access key={getUniqueId()} accessible={access.canHardwareSOS_openClose}>
            <Button
              type="primary"
              ghost
              danger={detailData.isEnable == 1 ? true : false}
              key="isEnable"
              onClick={() => onStatus(detailData)}
            >
              {detailData.isEnable == 1 ? "禁用" : "启用"}
            </Button>
          </Access>,
          <Access key={getUniqueId()} accessible={access.canHardwareSOS_delete}>
            <Button key="del" type="primary" danger onClick={() => onDelete(Id)}>
              删除
            </Button>
          </Access>,
          <Access key={getUniqueId()} accessible={access.canHardwareSOS_edit}>
            <Button key="update" type="primary" onClick={() => updateMethod(detailData)}>
              编辑
            </Button>
          </Access>,
          <Button key="col" onClick={handleCancel}>
            取消
          </Button>
        ]}
      >
        <Details detailData={detailData} />
      </Modal>
      {/* 编辑 */}
      {JSON.stringify(initialValues) !== "{}" ? (
        <ModalForm
          title="编辑SOS报警点"
          visible={modalVisit}
          // formRef={formObj}
          initialValues={initialValues}
          onFinish={async val => {
            val.id = Id;

            // val.isEnable = isEnableStatus
            console.log("编辑", val);
            submitData(val);
            return true;
          }}
          onVisibleChange={val => {
            setModalVisit(val);
            if (!val) {
              setInitialValues({});
            }
          }}
        >
          <ProForm.Group>
            <ProFormText width="md" name="scenicName" label="所属景区" initialValue={scenicName} disabled={true} />
            <ProFormText
              width="md"
              name="sosName"
              label="点位名称"
              placeholder="请输入点位名称"
              rules={[
                { required: true, message: "请输入点位名称" },
                { max: 100, message: "最多可输入100个字符" }
              ]}
            />
          </ProForm.Group>

          <ProForm.Group>
            <ProFormText
              width="md"
              name="loginName"
              label="账号"
              placeholder="请输入账号"
              rules={[
                { required: true, message: "请输入账号" },
                { max: 100, message: "最多可输入100个字符" }
              ]}
            />
            <ProFormText
              width="md"
              name="loginPassword"
              label="密码"
              placeholder="请输入密码"
              rules={[
                { required: true, message: "请输入密码" },
                { max: 100, message: "最多可输入100个字符" }
              ]}
            />
          </ProForm.Group>

          <ProForm.Group>
            <ProFormText
              width="md"
              name="brand"
              label="品牌"
              placeholder="请输入品牌"
              rules={[
                { required: true, message: "请输入品牌" },
                { max: 100, message: "最多可输入100个字符" }
              ]}
            />
            <ProFormText
              width="md"
              name="linkAddress"
              label="链接地址"
              placeholder="请输入链接地址"
              rules={[
                { required: true, message: "请输入链接地址" },
                { max: 100, message: "最多可输入100个字符" }
              ]}
            />
          </ProForm.Group>

          <ProForm.Group>
            <ProFormText
              width="md"
              name="point"
              label="管理端口"
              placeholder="请输入管理端口号"
              rules={[{ max: 100, message: "最多可输入100个字符" }]}
            />
            <ProFormText
              width="md"
              name="channel"
              label="通道号"
              placeholder="请输入通道号"
              rules={[{ max: 100, message: "最多可输入100个字符" }]}
            />
          </ProForm.Group>
        </ModalForm>
      ) : (
        ""
      )}
      <EditPop
        title="SOS 报警点"
        visible={editVisible}
        setVisible={setEditVisible}
        columns={editColumns}
        dataSource={dataSource}
        // 新增/编辑
        onFinish={(val: any) => {
          submitData(val);
          console.log(val);
        }}
      />
      <>
        <ProTable<API.RuleListItem, API.PageParams>
          // headerTitle={'查询表格'}
          actionRef={actionRef}
          rowKey="id"
          options={false}
          search={{
            labelWidth: 120,
            collapseRender: false,
            collapsed: false
          }}
          toolBarRender={() => [
            <Access key={getUniqueId()} accessible={access.canHardwareSOS_insert}>
              <Button
                type="primary"
                key="primary"
                onClick={() => {
                  // handleOptionId('');
                  // handleModalVisible(true);
                  setDataSource({ id: "", isEnable: 0 });
                  setEditVisible(true);
                }}
              >
                <PlusOutlined /> 新增
              </Button>
            </Access>
          ]}
          request={getSosPageList}
          columns={columns}
        />
      </>
    </>
  );
};

export default AlarmPoint;
