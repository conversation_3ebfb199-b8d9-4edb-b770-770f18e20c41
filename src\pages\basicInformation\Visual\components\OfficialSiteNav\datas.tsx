import { getAllPath } from '@/common/utils/tool';

const UrlNavType = {
  system: 'systemPage',
  homePage: 'homePage',
  ticketBook: 'ticketBook',
  combinedTicket: 'combinedTicket',
  debitCard: 'debitCard',
  tipsTravelogue: 'tipsTravelogue',
  activityInquiry: 'activityInquiry',
  helpCenter: 'helpCenter',
  activity: 'activity',
  scenicDetail: 'scenicDetail',
  articleDetail: 'articleDetail',
  helpDetail: 'helpDetail',
  custom: 'custom',
};

const UrlNavEnum = [
  {
    value: UrlNavType.system,
    label: '系统页面',
    children: [
      {
        value: UrlNavType.homePage,
        label: '主页',
      },
      {
        value: UrlNavType.ticketBook,
        label: '门票预订',
      },
      {
        value: UrlNavType.combinedTicket,
        label: '组合票',
      },
      {
        value: UrlNavType.debitCard,
        label: '权益卡',
      },
      {
        value: UrlNavType.tipsTravelogue,
        label: '攻略游记',
      },
      {
        value: UrlNavType.activityInquiry,
        label: '活动咨询',
      },
      {
        value: UrlNavType.helpCenter,
        label: '帮助中心',
      },
    ],
  },
  {
    value: UrlNavType.activity,
    label: '活动页面',
  },
  {
    value: UrlNavType.scenicDetail,
    label: '景区详情页',
  },
  {
    value: UrlNavType.articleDetail,
    label: '文章详情页',
  },
  {
    value: UrlNavType.helpDetail,
    label: '帮助中心详情页',
  },
  {
    value: UrlNavType.custom,
    label: '自定义链接',
  },
];

const AddPageUrlEnum = {
  [UrlNavType.articleDetail]: `${getAllPath()}/basic-information/visual?tabKey=article&type=add`,
  [UrlNavType.helpDetail]: `${getAllPath()}/basic-information/visual?tabKey=help`,
};

export { AddPageUrlEnum, UrlNavEnum, UrlNavType };
