import { addOperationLogRequest } from "@/common/utils/operationLog";
import { apiScenicConfig, apiScenicConfigInfo } from "@/services/api/settings";
import ProDescriptions from "@ant-design/pro-descriptions";
import type { ActionType } from "@ant-design/pro-table";
import { Card, Switch, Typography, message } from "antd";
import { useEffect, useRef } from "react";
import { useAccess, useModel, useRequest } from "@umijs/max";
const { Text } = Typography;

export default () => {
  const { initialState } = useModel("@@initialState");
  const { scenicId, uniqueIdentity } = initialState?.scenicInfo || {};
  const actionRef = useRef<ActionType>();

  const access = useAccess();

  const getInfoReq = useRequest(apiScenicConfig, {
    manual: true,
    formatResult: res => res
  });

  const updateInfoReq = async params => {
    await apiScenicConfigInfo({ ...getInfoReq.data, ...params, uniqueIdentity, scenicId });
    message.success("修改成功");
    getInfoReq.refresh();
  };

  useEffect(() => {
    getInfoReq.run({ id: scenicId });
  }, []);

  return (
    <>
      <Card
        style={{ marginBottom: "24px" }}
        title={
          <div style={{ position: "relative" }}>
            <span
              style={{
                display: "inline-block",
                width: "2px",
                height: "16px",
                background: "#1890ff",
                marginRight: 8,
                marginBottom: -2
              }}
            />
            <span style={{ fontWeight: 600 }}>{"实名信息"}</span>
          </div>
        }
      >
        <ProDescriptions
          column={{ xs: 1, sm: 2 }}
          columns={[
            {
              title: "是否支持实名制",
              key: "isRealName",
              dataIndex: "isRealName",
              render: (dom: any) => (
                <Switch
                  checked={!!dom}
                  disabled={!access.canGlobalSettings_realNameEdit}
                  checkedChildren="是"
                  unCheckedChildren="否"
                  onChange={async checked => {
                    await updateInfoReq({
                      isRealName: checked ? 1 : 0
                    });

                    // 添加日志
                    addOperationLogRequest({
                      action: "disable",
                      content: `${!checked ? "禁用" : "启用"}支持实名制`
                    });
                  }}
                />
              )
            },
            {
              title: "是否支持人脸识别",
              key: "isFace",
              dataIndex: "isFace",
              render: (dom: any) => (
                <Switch
                  checked={!!dom}
                  disabled={!access.canGlobalSettings_realNameEdit}
                  checkedChildren="是"
                  unCheckedChildren="否"
                  onChange={async checked => {
                    await updateInfoReq({
                      isFace: checked ? 1 : 0
                    });

                    // 添加日志
                    addOperationLogRequest({
                      action: "disable",
                      content: `${!checked ? "禁用" : "启用"}支持人脸识别`
                    });
                  }}
                />
              )
            },
            {
              title: "人脸识别队列名",
              key: "faceQueue",
              dataIndex: "faceQueue",
              copyable: true,
              render: (dom: any) => <Text code>{dom}</Text>
            },
            {
              title: "开闸 MQ 队列名",
              key: "openGateQueue",
              dataIndex: "openGateQueue",
              copyable: true,
              render: (dom: any) => <Text code>{dom}</Text>
            }
          ]}
          dataSource={getInfoReq.data}
        />
      </Card>
    </>
  );
};
