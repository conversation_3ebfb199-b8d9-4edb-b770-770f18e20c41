/*
 * 商品列表
 * @param
 *
 * */
import { RightsTicketStatusEnum, enableEnum, isChainEnum, ticketTypeEnum } from "@/common/utils/enum";
import { apiSimpleGoods, apiSimpleGoodsStatus } from "@/services/api/ticket";
import type { ActionType, ProColumns } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import { Modal, Switch, Tabs, Tag, message } from "antd";
import { isNil } from "lodash";
import React, { useEffect, useRef, useState } from "react";
import { useAccess, useModel } from "@umijs/max";

// let commodityPermission = {};
const Commoditys = ({
  proType,
  ticketId,
  timeShareId,
  timeShare,
  ticketName,
  commodityVisible,
  setCommodityVisible,
  marketPrice,
  productionRef
}: any) => {
  const access = useAccess();
  const actionRef = useRef<ActionType>();
  const { initialState } = useModel("@@initialState");
  const { scenicId = "", isBlockChain } = initialState?.scenicInfo || {};
  const { coId = "" } = initialState?.currentCompanyInfo || {};

  // 是否为权益票 同时作为 tabKey
  const [isRights, setIsRights] = useState<React.Key>("0");

  // 【商品表格】数据绑定
  const columns: ProColumns[] = [
    {
      title: "所属产品名称",
      dataIndex: "name",
      search: false
    },
    {
      title: "商品名称",
      dataIndex: "goodsName"
    },
    {
      title: "票种",
      dataIndex: "type",
      valueEnum: ticketTypeEnum
    },
    {
      title: "数字资产",
      dataIndex: "isDigit",
      valueEnum: isChainEnum,
      search: false,
      hideInTable: isBlockChain != 1 || isRights == "1"
    },
    {
      search: false,
      title: "分销折扣区间（%）",
      key: "discount",
      render: (_, entity: any) =>
        !isNil(entity.beginDiscount ?? entity.endDiscount) ? entity.beginDiscount + " ~ " + entity.endDiscount : "-"
    },
    {
      title: "商品有效期（天）",
      dataIndex: "validityDay",
      hideInSearch: true
    },
    {
      title: "分时时段",
      search: false,
      dataIndex: "timeShareVoList",
      render: (_, { timeShareVoList }: any) => {
        return timeShareVoList
          ? timeShareVoList.map((item: any) => <Tag key={item.id}>{[item.beginTime, item.endTime].join("-")}</Tag>)
          : "-";
      }
    },
    {
      title: "审核状态",
      dataIndex: "rightsStatus",
      search: false,
      // 非权益票隐藏
      hideInTable: isRights === "0",
      valueType: "select",
      valueEnum: RightsTicketStatusEnum
    },
    {
      title: "启用状态",
      dataIndex: "isEnable",
      fixed: "right",
      valueEnum: enableEnum,
      renderText: (dom: any, entity: any) => (
        <Switch
          disabled={entity.rightsStatus == 1 || entity.rightsStatus == 3}
          checkedChildren="启用"
          unCheckedChildren="禁用"
          checked={dom == 1}
          onChange={() => {
            apiSimpleGoodsStatus({
              goodsId: entity.id,
              isEnable: 1 - entity.isEnable,
              scenicId,
              productId: ticketId
            })
              .then(() => {
                message.success(dom == 1 ? "已禁用" : "已启用");
                actionRef.current?.reload();
                productionRef.current?.reload();
              })
              .catch(() => {});
          }}
        />
      )
    }
  ];

  useEffect(() => {
    if (commodityVisible) {
      setIsRights("0");
    }
  }, [commodityVisible]);

  return (
    <Modal
      width={1500}
      title={"查看商品"}
      visible={commodityVisible}
      footer={false}
      onCancel={() => {
        setCommodityVisible(false);
      }}
      destroyOnClose
    >
      <Tabs
        style={{ background: "#fff" }}
        tabBarStyle={{ padding: "0 24px", margin: "0" }}
        onChange={key => {
          setIsRights(key as React.Key);
        }}
        items={[
          {
            label: "普通票",
            key: "0"
          },
          {
            label: "权益票",
            key: "1"
          }
        ]}
      />
      {/* 商品表格 */}
      <ProTable<API.RuleListItem, API.PageParams>
        rowClassName={(row: any) => (row.isEnable == 1 ? "" : "disableRow")}
        tableLayout="auto"
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: "auto",
          collapseRender: false,
          collapsed: false
        }}
        params={{ scenicId, ticketId, timeShareId, isRights, operatorId: coId }}
        request={ticketId ? apiSimpleGoods : undefined}
        columns={columns}
        scroll={{ x: 1200 }}
      />
    </Modal>
  );
};

export default Commoditys;
