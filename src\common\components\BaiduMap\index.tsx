/*
 * @Author: z<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-05-08 15:00:19
 * @LastEditTime: 2023-08-22 16:26:18
 * @LastEditors: zhang<PERSON><PERSON>i
 */
/*
 * 百度地图
 * @param width 宽度
 * @param height 高度
 * @param value 经纬度 {lng, lat}
 * @param onChange
 * */
import type { FC } from 'react';
import { useEffect, useState } from 'react';

interface BaiduMapProps {
  width?: number | string;
  height?: number | string;
  latitude: number;
  longitude: number;
  id: string;
  drag?: boolean;
  scroll?: boolean;
}

const BaiduMap: FC<BaiduMapProps> = ({
  width = 300,
  height = 600,
  latitude,
  longitude,
  id,
  drag = true,
  scroll = true,
}) => {
  const [map, setMap] = useState<any>(null);

  const makePoint = (lng: number, lat: number, instance: any) => {
    const points = new window.BMap.Point(lng, lat);
    instance.centerAndZoom(points, 16);
    instance.clearOverlays();
    const marker = new window.BMap.Marker(points);
    instance.addOverlay(marker); // 将标注添加到地图中
  };

  useEffect(() => {
    const BMap = window.BMap;
    const instance = new BMap.Map(id);
    instance.enableScrollWheelZoom(scroll);
    if (!scroll) {
      map.disableScrollWheelZoom();
    }
    if (!drag) {
      instance.disableDragging();
    }
    setMap(instance);
    makePoint(longitude, latitude, instance);
  }, []);

  useEffect(() => {
    if (map) {
      makePoint(longitude, latitude, map);
    }
  }, [latitude, longitude]);

  return <div id={id} style={{ width, height }} />;
};

export default BaiduMap;
