/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-09-25 09:36:49
 * @LastEditTime: 2025-06-13 15:07:27
 * @LastEditors: 李悍宇 <EMAIL>
 */
/*
 * @Author: z<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-09-25 09:36:49
 * @LastEditTime: 2023-10-30 15:35:12
 * @LastEditors: zhangfengfei
 */

import PrefixTitle from "@/common/components/PrefixTitle";
import { baseProductTypeEnum, whetherEnum } from "@/common/utils/enum";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import { getUniqueId, jumpPage } from "@/common/utils/tool";
import MDEditor from "@/components/MDEditor";
import { apiSimpleTicketAdd, apiSimpleTicketEdit, apiSimpleTicketInfo, getCoInfo } from "@/services/api/ticket";
import { LeftOutlined, MinusCircleOutlined, PlusCircleOutlined } from "@ant-design/icons";
import ProCard from "@ant-design/pro-card";
import { ProFormFieldSet } from "@ant-design/pro-components";
import type { ProFormColumnsType, ProFormInstance } from "@ant-design/pro-form";
import ProForm, { BetaSchemaForm, ProFormDigit, ProFormSelect } from "@ant-design/pro-form";
import type { ActionType } from "@ant-design/pro-table";
import { useLocation } from "@umijs/max";
import { Button, Space, TimePicker, message } from "antd";
import dayjs from "dayjs";
import type { FC } from "react";
import { useEffect, useRef, useState } from "react";
import { useModel, useRequest } from "@umijs/max";

//埋点格式数据
const logList = [
  {
    title: "产品类型",
    dataIndex: "proType",
    valueEnum: baseProductTypeEnum
  },
  {
    title: "产品名称",
    dataIndex: "name"
  },
  {
    title: "C 端显示名称",
    dataIndex: "pcName"
  },
  {
    title: "市场标准价",
    dataIndex: "marketPrice"
  },

  {
    title: "参与入园统计",
    dataIndex: "parkStatistic",
    valueEnum: whetherEnum
  }
];

const weekOptions = [
  { value: 0, label: "星期日" },
  { value: 1, label: "星期一" },
  { value: 2, label: "星期二" },
  { value: 3, label: "星期三" },
  { value: 4, label: "星期四" },
  { value: 5, label: "星期五" },
  { value: 6, label: "星期六" }
];
let timeList: any = [];

const defaultTime = {
  id: "",
  beginTime: "",
  endTime: "",
  unique: getUniqueId()
};

interface ProductFormProps {
  type: string;
  id: string;
  actionRef: React.MutableRefObject<ActionType | undefined>;
}
const format = "HH:mm";

const ProductForm: FC<ProductFormProps> = ({ type, id, actionRef }) => {
  const { initialState } = useModel("@@initialState");
  const { scenicId = "", scenicName = "" } = initialState?.scenicInfo || {};
  const { coId = "" } = initialState?.currentCompanyInfo || {};

  const formRef = useRef<ProFormInstance<any>>();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);

  const [timeData, setTimeData] = useState(false);
  const [dataSource, setDataSource] = useState<any>({ id: "", isEnable: 0, notice: "无" });

  const getCoInfoReq = useRequest(getCoInfo, {
    manual: true,
    formatResult(res) {
      const coInfoMap = new Map();
      for (const item of res.data ?? []) {
        coInfoMap.set(item.coId, item.coName);
      }
      return coInfoMap;
    }
  });

  const getProductInfoReq = useRequest(apiSimpleTicketInfo, {
    manual: true,
    // initialData: {},
    formatResult(res) {
      setDataSource(res);
      return res;
    },
    onSuccess(data, params) {
      console.log(formRef.current);

      formRef.current?.setFieldsValue({
        ...data,
        proType: String(data.proType),
        use: [data.useType, data.useCount]
      });
      timeList = data.timeShare;
      setTimeData(!timeData);
    }
  });

  const changeValue = (index: any, type: any, e: any) => {
    timeList[index][type] = e;
    setTimeData(!timeData);
  };

  const TimeList: any = () => {
    return timeList?.map((item: any, index: any) => (
      <div
        key={item.unique ? item.unique : item.id}
        style={{ width: "100%", display: "flex", marginBottom: "12px", alignItems: "center" }}
      >
        {/* <TimePicker
          style={{ width: '300px' }}
          format={format}
          onChange={(time, timeString) => {
            changeValue(index, 'beginTime', timeString[0]);
          }}
        /> */}
        <TimePicker.RangePicker
          style={{ width: "calc(100% - 40px)" }}
          defaultValue={item.beginTime ? [dayjs(item.beginTime, format), dayjs(item.endTime, format)] : [null, null]}
          disabled={dataSource.id}
          format={format}
          onChange={(_, e) => {
            changeValue(index, "beginTime", e[0]);
            changeValue(index, "endTime", e[1]);
          }}
        />

        <div
          style={{
            flex: 1,
            display: "flex",
            justifyContent: "flex-end",
            alignItems: "center"
          }}
        >
          <PlusCircleOutlined
            style={{
              marginLeft: "16px",
              cursor: !dataSource.id ? "pointer" : "not-allowed",
              fontSize: "16px"
            }}
            onClick={() => {
              if (!dataSource.id) {
                defaultTime.unique = getUniqueId();
                timeList.splice(index + 1, 0, { ...defaultTime });
                setTimeData(!timeData);
              }
            }}
          />
          <MinusCircleOutlined
            style={{
              marginLeft: "16px",
              cursor: !dataSource.id && timeList.length > 1 ? "pointer" : "not-allowed",
              fontSize: "16px"
            }}
            onClick={() => {
              if (!dataSource.id && timeList.length > 1) {
                timeList.splice(index, 1);
                setTimeData(!timeData);
              }
            }}
          />
        </div>
      </div>
    ));
  };

  const columns: ProFormColumnsType<any>[] = [
    {
      title: "基础信息",
      valueType: "group",
      columns: [
        {
          title: "景区名称",
          dataIndex: "scenicId",
          valueType: "select",
          valueEnum: { [scenicId]: scenicName },
          initialValue: scenicId,
          fieldProps: { disabled: true }
        },
        {
          title: "所属服务商",
          dataIndex: "operatorId",

          valueType: "select",
          initialValue: coId,
          valueEnum: getCoInfoReq.data,
          fieldProps: {
            allowClear: false,
            disabled: true
          }
        },
        {
          title: "产品类型",

          dataIndex: "proType",
          valueType: "select",
          valueEnum: baseProductTypeEnum,
          fieldProps: {
            allowClear: false,
            getPopupContainer: (node: any) => node.parentNode
          },
          initialValue: "0",
          formItemProps: { rules: [{ required: true }] }
        },
        {
          title: "产品名称",

          dataIndex: "name",
          formItemProps: { rules: [{ required: true }] }
        },
        {
          title: "C 端显示名称",

          dataIndex: "pcName",
          formItemProps: { rules: [{ required: true }] }
        },
        {
          title: "市场标准价",

          dataIndex: "marketPrice",
          valueType: "digit",
          fieldProps: { min: 0 },
          formItemProps: { rules: [{ required: true }] }
        },
        {
          title: "有效时长天数（天）",
          dataIndex: "validityDay",
          valueType: "digit",
          fieldProps: {
            min: 0,
            max: 3650,
            precision: 0,
            disabled: type === "edit"
          },
          formItemProps: {
            rules: [
              {
                required: true
              }
            ]
          }
        },
        {
          title: "是否首日激活",
          valueType: "switch",
          dataIndex: "isActivate",
          initialValue: false,

          fieldProps: {
            checkedChildren: "是",
            unCheckedChildren: "否",
            disabled: type === "edit"
          },
          transform: val => {
            return {
              isActivate: val ? 1 : 0
            };
          },
          formItemProps: {
            style: {
              width: 328
            }
          }
        },
        {
          title: "可入园天数",
          tooltip: "有效时长为门票允许使用的最长时间，如有效时长 7 天、可入园天数 3 天表示 7 天内选 3 天使用门票",

          dataIndex: "availableDays",
          valueType: "digit",
          fieldProps: {
            min: 0,
            max: 3650,
            precision: 0,
            disabled: type === "edit"
          },
          formItemProps: {
            rules: [
              {
                required: true
              }
            ]
          }
        },
        {
          title: <span className="required">使用次数</span>,
          dataIndex: "use",
          tooltip: "0 表示可以无限次使用，超过 1 次使用次数无法创建一票多人门票",
          transform: values => {
            return {
              useType: values[0],
              useCount: values[1]
            };
          },
          renderFormItem: (schema, { value, onChange }, form) => {
            return (
              <ProFormFieldSet type="group" name={"use"}>
                <ProFormSelect
                  colProps={{ span: 12 }}
                  options={[
                    {
                      label: "每天",
                      value: 0
                    },
                    {
                      label: "一共",
                      value: 1
                    }
                  ]}
                  fieldProps={{
                    allowClear: false
                  }}
                  disabled={type === "edit"}
                />
                <ProFormDigit
                  colProps={{ span: 12 }}
                  disabled={type === "edit"}
                  min={0}
                  max={10000}
                  rules={[
                    {
                      required: true
                    }
                  ]}
                />
              </ProFormFieldSet>
            );
          }
        }
      ]
    },
    {
      title: "票务属性信息",
      valueType: "group",
      columns: [
        {
          title: "是否按星期控制",
          dataIndex: "restrictType",
          valueType: "switch",
          fieldProps: {
            disabled: type === "edit"
          }
        },
        {
          valueType: "dependency",
          fieldProps: {
            name: ["restrictType"]
          },
          columns: ({ restrictType }) => {
            if (!restrictType) {
              return [];
            }
            return [
              {
                title: "以下星期不可售该票",
                colProps: { xs: 24, sm: 12, xl: 8, xxl: 6 },
                dataIndex: "restrictWeekList",
                valueType: "select",
                fieldProps: {
                  showSearch: false,
                  mode: "multiple",
                  options: weekOptions,
                  disabled: type === "edit"
                }
              }
            ];
          }
        },
        // {
        //   title: '分时预约',
        //   dataIndex: 'timeRestrict',
        //   valueType: 'switch',
        //   fieldProps: {
        //     disabled: dataSource?.id,
        //   },
        // },
        // {
        //   valueType: 'dependency',
        //   fieldProps: {
        //     name: ['timeRestrict'],
        //   },
        //   columns: ({ timeRestrict }) => {
        //     if (!timeRestrict) {
        //       return [];
        //     }
        //     return [
        //       {
        //         title: '分时预约列表',
        //         colProps: { xs: 24, sm: 12, xl: 8, xxl: 6 },
        //         renderFormItem: TimeList,
        //       },
        //     ];
        //   },
        // },

        {
          title: "参与入园统计",
          dataIndex: "parkStatistic",
          valueType: "switch",
          initialValue: true
        },
        {
          dataIndex: "notice",
          colProps: {
            span: 24
          },
          renderFormItem: () => (
            <ProForm.Item
              name="notice"
              label="入园须知"
              rules={[
                {
                  type: "string",
                  max: 2000
                }
              ]}
            >
              <MDEditor />
            </ProForm.Item>
          )
        },
        {
          title: "备注",
          colProps: {
            span: 24
          },
          dataIndex: "remark",
          valueType: "textarea",
          fieldProps: {
            showCount: true,
            maxLength: 1000,
            style: {
              height: 100
            }
          }
        }
      ]
    }
  ];

  // 统一风格
  const newColumns = columns.map((item: any) => {
    const childColumns = (item.columns ?? []).map(i => ({
      width: "100%",
      colProps: { xs: 24, sm: 12, xl: 8, xxl: 6 },
      ...i
    }));

    return {
      rowProps: { gutter: 24 },
      valueType: "group",
      ...item,
      title: item.title && <PrefixTitle>{item.title} </PrefixTitle>,
      columns: childColumns
    };
  });

  useEffect(() => {
    getCoInfoReq.run({ scenicId });
    console.log(id);

    if (type === "edit" && id) {
      getProductInfoReq.run(id);
    } else {
      setDataSource({});
    }
    timeList = [{ ...defaultTime }];
    return () => {
      formRef.current?.resetFields();
    };
  }, [id, scenicId, type]);

  useEffect(() => {
    if (searchParams.get("type") && searchParams.get("type") === "add") {
      formRef.current?.setFieldsValue({
        name: searchParams.get("name") || "",
        pcName: searchParams.get("pcName") || "",
        marketPrice: searchParams.get("marketPrice") ? String(searchParams.get("marketPrice")) : "",
        validityDay: searchParams.get("validityDay") ? String(searchParams.get("validityDay")) : "",
        availableDays: searchParams.get("availableDays") ? String(searchParams.get("availableDays")) : ""
        // use: [data.useType, data.useCount]
      });
      if (searchParams.get("proType")) {
        formRef.current?.setFieldsValue({
          proType: String(searchParams.get("proType")) || ""
        });
      }
      if (searchParams.get("useCount")) {
        formRef.current?.setFieldsValue({
          use: [0, searchParams.get("useCount")]
        });
      }
    }
  }, []);

  return (
    <ProCard
      title={
        <div
          className="flex align-items-center primary-color pointer"
          onClick={() => jumpPage.push("/ticket/ticket-type")}
        >
          <LeftOutlined style={{ marginRight: 10 }} />
          {type == "edit" ? "编辑产品" : "新增产品"}
        </div>
      }
      className="relative"
      headerBordered
    >
      <BetaSchemaForm
        width={800}
        formRef={formRef}
        layoutType="Form"
        preserve={false}
        columns={newColumns}
        submitter={false}
        initialValues={{
          use: [0, undefined]
        }}
        grid
        scrollToFirstError
        onFinishFailed={value => {
          console.log(formRef.current?.getFieldsValue());
          setImmediate(() => {
            document.querySelector(".ant-form-item-has-error")?.scrollIntoView({ behavior: "smooth", block: "center" });
          });
        }}
        onFinish={async (val: any) => {
          console.log(val);
          val.timeShare = timeList;
          try {
            if (val.timeRestrict) {
              // 判断两个去交是否有交集
              function isIntersect(arr1: any, arr2: any) {
                const start = [Math.min(...arr1), Math.min(...arr2)]; //区间的两个最小值
                const end = [Math.max(...arr1), Math.max(...arr2)]; //区间的两个最大值
                return Math.max(...start) <= Math.min(...end); //最大值里的最小值 是否 小于等于 最大值的最小值
              }

              const arr: any = [];
              val.timeShare?.map((item: any) => {
                if (!item.beginTime || !item.endTime) {
                  message.info("时间区间不能为空");
                  throw new Error("");
                }
                // 向区间集合新增新的区间时，判断区间集合中是否存在与新区间有交集的，如果存在，去除区间集合中有交集的区间，再加入新区间
                const arr2 = [item.beginTime.slice(0, 2), item.endTime.slice(0, 2) - 1];
                for (let i = arr.length - 1; i >= 0; i--) {
                  if (isIntersect(arr[i], arr2)) {
                    // arr.splice(i, 1);
                    message.info("时间区间存在交集");
                    throw new Error("");
                  }
                }
                arr.push(arr2);
              });
            }

            // 格式化数据
            if (dataSource.id) {
              val.id = dataSource.id;
            }
            val.scenicName = scenicName;
            val.restrictType = val.restrictType ? 1 : 0;
            val.timeRestrict = val.timeRestrict ? 1 : 0;
            val.parkStatistic = val.parkStatistic ? 1 : 0;
            val.pictureUrl = val.pictureUrl || " ";
            const msgType = val.id ? "编辑" : "新增";
            const hide = message.loading("正在" + msgType);
            const { use, ...rest } = val;
            try {
              await (dataSource.id
                ? apiSimpleTicketEdit({ ...rest, remark: val.remark || " " })
                : apiSimpleTicketAdd(rest));

              if (dataSource.id) {
                addOperationLogRequest({
                  action: "edit",
                  changeConfig: {
                    list: logList,
                    beforeData: dataSource,
                    afterData: val
                  },
                  content: `编辑【${val.name}】产品`
                });
              } else {
                addOperationLogRequest({
                  action: "add",
                  content: `新增【${val.name}】产品`
                });
              }

              message.success(msgType + "成功");
              actionRef.current?.reload();
              jumpPage.push("/ticket/ticket-type");
              // 埋点
              // const isAdd = !dataSource.id;
              // 需要处理的数据
              // const getOther = (obj: any) => ({
              //   scenicName,
              //   proType: baseProductTypeEnum[obj.proType],
              //   operatorId: getCoInfoReq.data?.get(obj.operatorId),
              // });

              // const content = setOperationContent({
              //   title: `${isAdd ? '新增' : '编辑'}【${val.pcName}】产品信息`,
              //   before: isAdd ? {} : { ...dataSource, ...getOther(dataSource) },
              //   after: {
              //     ...dataSource,
              //     ...val,
              //     ...getOther(val),
              //   },
              //   data: operationData,
              // });

              // await setOperationLog({
              //   module: 'ticketType',
              //   content,
              //   function: isAdd ? 'add' : 'edit',
              // });
            } catch (error) {}
            hide();
          } catch (error) {}
        }}
      />
      <div
        className="flex w-100 justify-content-center align-items-center"
        style={{
          position: "sticky",
          height: 72,
          backgroundColor: "white",
          bottom: 0,
          zIndex: 2,
          boxShadow: "0px -2px 9px -1px rgba(208,208,208,0.5)"
        }}
      >
        <Space>
          <Button onClick={() => jumpPage.push("/ticket/ticket-type")} key="1">
            取消
          </Button>
          <Button type="primary" onClick={() => formRef.current?.submit()} key="2">
            确定
          </Button>
        </Space>
      </div>
    </ProCard>
  );
};

export default ProductForm;
