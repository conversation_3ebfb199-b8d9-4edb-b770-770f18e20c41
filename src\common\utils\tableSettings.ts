/*
 * @Author: z<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-27 14:25:56
 * @LastEditTime: 2022-10-09 11:57:24
 * @LastEditors: zhangfengfei
 */
import { getUniqueId } from './tool';

export const tableProps: object = {
  rowKey: () => getUniqueId(),
  search: {
    defaultCollapsed: false,
    collapseRender: false,
    labelWidth: 100,
  },
  options: {
    density: false,
    setting: false,
  },
  revalidateOnFocus: false,
  pagination: { defaultPageSize: 10 },
};
