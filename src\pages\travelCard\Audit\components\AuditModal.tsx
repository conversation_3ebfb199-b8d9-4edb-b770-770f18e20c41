/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-04-24 10:16:48
 * @LastEditTime: 2022-11-18 15:08:57
 * @LastEditors: zhang<PERSON><PERSON>i
 */

import DetailsPop from "@/common/components/DetailsPop";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import type { ProDescriptionsGroup } from "@/components/ModalDescriptions";
import type { ModalState } from "@/hooks/useModal";
import { getAuditInfo } from "@/services/api/auditManage";
import type { ActionType } from "@ant-design/pro-table";
import { Image, Space } from "antd";
import type { FC } from "react";
import { useEffect } from "react";
import { useRequest } from "@umijs/max";
import { getEnv } from "@/common/utils/getEnv";

type AuditModalProps = ModalState & {
  dataItem?: API.AuditListItem;
  actionRef: React.MutableRefObject<ActionType | undefined>;
  tabKey: React.Key;
};

const AuditModal: FC<AuditModalProps> = ({ visible, type, dataItem, tabKey, actionRef, setVisible }) => {
  // 审核详情请求
  const auditInfoReq = useRequest(getAuditInfo, {
    manual: true,
    onSuccess(data, params) {
      addOperationLogRequest({
        action: "info",
        module: tabKey,
        content: `查看【${dataItem?.travelGoodsName}】权益卡审核详情`
      });
    }
  });

  const detailColumns: ProDescriptionsGroup<any>[] = [
    {
      title: "基本信息",

      columns: [
        {
          title: "用户名",
          dataIndex: "userName"
        },
        {
          title: "手机号",
          dataIndex: "phone"
        },
        {
          title: "身份证号",
          dataIndex: "idCard"
        },
        {
          title: "卡号",
          dataIndex: "travelCardId"
        }
      ]
    },
    {
      title: "其他信息",
      column: 1,
      columns: [
        {
          title: "图片信息",
          dataIndex: "picture",
          renderText: (text: string) =>
            text ? (
              <Space>
                {text.split(",").map(item => (
                  <Image
                    key={item}
                    width={80}
                    height={80}
                    style={{ border: "1px solid #d9d9d9" }}
                    placeholder
                    src={item.includes("http") ? item : `${getEnv().IMG_HOST}${item}`}
                  />
                ))}
              </Space>
            ) : (
              "-"
            )
        },
        // {
        //   title: '附件信息',
        //   dataIndex: 'enclosure',
        //   render: (text: string) =>
        //     text ? (
        //       <Image
        //         width={80}
        //         height={80}
        //         style={{ border: '1px solid #d9d9d9' }}
        //         placeholder
        //         src={text}
        //       />
        //     ) : (
        //       '-'
        //     ),
        // },
        {
          title: "文本信息",
          dataIndex: "notice"
        }
      ]
    }
  ];

  useEffect(() => {
    if (visible && type !== "add" && dataItem) {
      auditInfoReq.run({
        id: dataItem.id
      });
    }
  }, [type, dataItem?.id, visible, dataItem]);

  return (
    <DetailsPop
      title="审核详情"
      visible={visible}
      isLoading={auditInfoReq.loading}
      setVisible={setVisible}
      dataSource={auditInfoReq.data}
      columnsInitial={detailColumns}
    />
  );
};

export default AuditModal;
