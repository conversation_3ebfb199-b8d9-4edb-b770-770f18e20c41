---
title: 全局公共组件
---

## **[1] tsx 组件标准结构**

### 代码演示

```tsx
import React from 'react';
import { useEffect, useState } from 'react';

interface FCNameProps {
  name?: string;
  sexy: 'man' | 'woman';
}
/**
 * @description: tsx 组件标准结构
 */
const FCName: React.FC<FCNameProps> = ({ name = '张三', sexy }) => {
  // 状态 state 放在第一
  const [num, setNum] = useState<number>(0);

  // 自定义变量或者函数其次
  const logName = () => {
    console.log(name);
  };

  // 副作用 effect 放在最后
  useEffect(() => {
    logName();
  }, [num]);

  return (
    <div>
      <button
        onClick={() => {
          setNum((val) => val + 1);
        }}
      >
        +1
      </button>
      <br />
      <span>num:{num}</span>
      <br />
      <span>name:{name}</span>
    </div>
  );
};

export default FCName;
```

## **[2] 表格页面**

### API [高级表格](https://procomponents.ant.design/components/table/#api)

### 代码演示

```tsx
import { PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button } from 'antd';
import React, { useRef, useState } from 'react';

const fetchData = (params: TableListParams) =>
  new Promise<TableListItem[]>((resolve, reject) => {
    setTimeout(() => {
      resolve([
        {
          key: '1',
          name: '我的名字很长你隐藏一下 233333333333333333333333333',
          class: '三年（2）班',
          num: '37 号',
          info: {
            age: 18,
          },
          sexy: 'man',
          time: '2022-10-31 17:50:00',
        },
      ]);
    }, 1000);
  });

type TableListItem = {
  key: string;
  class: string;
  name: string;
  sexy: string;
  num: string;
  time: string;
  info: {
    age: number;
  };
};

type TableListParams = {
  sexy?: 'man' | 'woman';
};

interface DemoProps {
  scenicId?: string;
}

const Demo: React.FC<DemoProps> = ({ scenicId }) => {
  const actionRef = useRef<ActionType>();

  const [currentRow, setCurrentRow] = useState<TableListItem>();

  const tableReq = async (params: TableListParams) => {
    const data = await fetchData(params);
    return {
      data: data,
      total: 10,
    };
  };

  // 表格配置对象
  const columns: ProColumns<TableListItem>[] = [
    {
      title: '序号',
      dataIndex: 'key',
      search: false, // 在搜索栏中隐藏
      hideInTable: true, // 在表格中隐藏
    },
    {
      title: '班级',
      dataIndex: 'class',
    },
    {
      title: '姓名',
      dataIndex: 'name',
      ellipsis: true, // 超出宽度省略 tooltip 显示（可关闭）
    },
    {
      title: '性别',
      dataIndex: 'sexy',
      valueEnum: {
        woman: '女',
        man: '男',
      },
    },
    {
      title: '时间',
      search: false,
      dataIndex: 'time',
    },
    {
      title: '时间',
      dataIndex: 'time',
      valueType: 'dateRange',
      hideInTable: true, // 在表格中隐藏
      search: {
        // 转换查询参数
        transform: (values) => ({
          start: values[0],
          end: values[1],
        }),
      },
    },
    {
      title: '座号',
      dataIndex: 'num',
    },
    {
      title: '年龄',
      search: false,
      dataIndex: ['info', 'age'], // 嵌套结构
    },
    {
      title: '操作',
      fixed: 'right', // 固定表尾 table 需设置 scrollX 并且超出宽度才能体现
      valueType: 'option',
      render: (text, record) => [
        <a
          key="view"
          onClick={() => {
            console.log(record);
            setCurrentRow(record);
          }}
        >
          查看
        </a>,
      ],
    },
  ];
  return (
    <ProTable<TableListItem, TableListParams>
      columns={columns}
      actionRef={actionRef}
      params={{ sexy: 'man' }}
      request={tableReq}
      rowKey={(row) => row.key}
      options={{ setting: false, density: false }}
      search={{ labelWidth: 'auto', collapseRender: false, collapsed: false }}
      headerTitle="班级表"
      toolBarRender={() => [
        <Button
          key="button"
          icon={<PlusOutlined />}
          onClick={() => {
            setCurrentRow(undefined);
          }}
          type="primary"
        >
          新建
        </Button>,
      ]}
    />
  );
};

export default Demo;
```

## ②.表单弹窗

## ③.详情弹窗

### API

| 参数            | 说明                               | 类型                         | 默认值 |
| --------------- | ---------------------------------- | ---------------------------- | ------ |
| value           | 输入框的值                         | `string`                     | -      |
| onChange        | 值编辑后触发                       | `(value?: string) => void`   | -      |
| onSearch        | 查询后触发                         | `(value?: string) => void`   | -      |
| options         | 选项菜单的的列表                   | `{label,value}[]`            | -      |
| defaultVisible  | 输入框默认是否显示，只有第一次生效 | `boolean`                    | -      |
| visible         | 输入框是否显示                     | `boolean`                    | -      |
| onVisibleChange | 输入框显示隐藏的回调函数           | `(visible: boolean) => void` | -      |
