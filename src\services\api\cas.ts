import { message } from "antd";
import { request } from "@umijs/max";
import { scenicHost } from ".";

export const platformId = "02";

/** 登录接口 POST /user/no_auth/login */
export async function login(body: API.LoginParams, options?: Record<string, any>) {
  return request<API.LoginResult>(`${scenicHost}/user/noAuth/login`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    data: { autoRegister: true, ...body },
    ...(options || {})
  });
}
/** 退出登录 */
export async function logout(params) {
  return request<API.LoginResult>(`${scenicHost}/user/logout`, {
    method: "PUT",
    data: params
  });
}
interface DepartmentItem {
  companyId: string;
  deptId: string;
  name: string;
  parentDeptId: string;
}

//通过公司 ID 获取部门
export async function getDepartment(params: { id: string[] }) {
  return request<ResponseData<DepartmentItem[]>>(`${scenicHost}/orgStructure/deptList`, {
    method: "POST",
    data: params
  });
}
//新增部门
export async function addDepartment(params: any) {
  return request(`${scenicHost}/orgStructure/addDept`, {
    method: "POST",
    data: params
  });
}

//编辑部门信息
export async function setDepartment(params: any) {
  return request(`${scenicHost}/orgStructure/editDept`, {
    method: "POST",
    data: params
  });
}

//新增用户
export async function addUser(params: any) {
  return request(`${scenicHost}/orgStructure/addUser`, {
    method: "POST",
    data: params
  });
}

//用户禁用 or 启用
export async function disableUser(params: any) {
  return request(`${scenicHost}/orgStructure/disableUser`, {
    method: "POST",
    data: params
  });
}

//授权权限
export async function postPermissionAuthorize(params: any) {
  return request(`${scenicHost}/permission/authorize`, {
    method: "POST",
    data: params
  });
}

//撤销权限
export async function postPermissionRevoke(params: any) {
  return request(`${scenicHost}/permission/revoke`, {
    method: "DELETE",
    data: params
  });
}

//禁用用户并登出
export async function putUserDisable(params: any) {
  return request(`${scenicHost}/user/disable`, {
    method: "PUT",
    data: params
  });
}

//查询某个权限是否存在
export async function confirmPermission(params: any) {
  return request(`${scenicHost}/permission/confirm`, {
    method: "POST",
    data: params
  });
}

//根据 userId 和景区获取该景区下用户的可登录企业列表
export async function getCompanyPermissionList(params: any) {
  return request(`${scenicHost}/orgStructure/companyPermissionList`, {
    method: "GET",
    params
  });
}

//编辑用户信息
export async function setUser(params: any) {
  params.type = platformId; //type，01 智旅云平台，非 01，type，relationId
  return request(`${scenicHost}/orgStructure/editUser`, {
    method: "POST",
    data: params
  });
}

//查看用户信息
export async function checkUser(params: any) {
  return request(`${scenicHost}/orgStructure/userCoInfo`, {
    method: "GET",
    params: { ...params }
  });
}

//重置密码
export async function resetUserPasswork(params: any) {
  console.log(JSON.stringify(params));
  return request(`${scenicHost}/user/resetPassword`, {
    method: "POST",
    data: params
  });
}

//查询权限列表（post）
export async function getPermissionListByPost(params: any) {
  return request(`${scenicHost}/permission/retrieve`, {
    method: "POST",
    data: params
  });
}

//查询公司所有用户
export function getCoAllUsers(params: { companyId: string; scenicId?: string; userStatus?: number }) {
  const { companyId, ...rest } = params;
  return request<ResponseData<API.UserPageListItem[]>>(`${scenicHost}/orgStructure/${companyId}/allUsers`, {
    method: "GET",
    params: rest
  });
}

/**
 * @description: 获取用户列表（新）
 * @see https://dev.shukeyun.com/scenic/api-v2/doc.html#/%E6%99%AF%E5%8C%BA%E6%9D%83%E9%99%90%E6%A8%A1%E5%9D%97/%E7%BB%84%E7%BB%87%E6%9E%B6%E6%9E%84%E6%A8%A1%E5%9D%97/userPageListUsingGET_2
 */
export function getUserPageList(params: any) {
  return request<ResponseListData<API.UserPageListItem[]>>(`${scenicHost}/orgStructure/userPageList3`, {
    params
  });
}

//增加角色
export async function addRole(params: any) {
  params.type = platformId; //type，01 智旅云平台，非 01，type，relationId
  return request(`${scenicHost}/role/add`, {
    method: "POST",
    data: params
  });
}

//删除角色
export async function deleteRole(roleId: string) {
  return request(`${scenicHost}/role/del/${roleId}`, {
    method: "DELETE"
  });
}

//编辑角色
export async function setRole(params: any) {
  return request(`${scenicHost}/role/edit`, {
    method: "POST",
    data: params
  });
}

//获取角色列表
export async function getRoleList(params: Record<string, any>) {
  return request<ResponseListData<API.RoleListItem[]>>(`${scenicHost}/role/list`, {
    method: "GET",
    params
  });
}

//useless 2021 年 11 月 9 日
export async function getRoleListByPermission(q: string[]) {
  const r: any = await request<API.RuleListItem>(`${scenicHost}/role/rolePermissionList`, {
    method: "GET",
    params: { permission: q }
  });
  console.log(r);
  if (r.code === 20000) {
    return r.data.filter((e: any) => {
      return e.status === 1; //只要启用的
    });
  }
  return [];
}

//通过角色 ID 获取权限列表，如不传则为取全部权限，全部权限用做新增
export async function getPermissionList(param: API.GetPermissionListParam) {
  if (!param.type) {
    param.type = platformId;
  }

  return request(`${scenicHost}/role/listPermission`, {
    method: "GET",
    params: param,
    skipErrorHandler: true
  });
}

// 创建旅游场景，初始化默认角色
export async function postCreateDefaultRole(params) {
  return request(`${scenicHost}/role/defaultRole`, {
    method: "POST",
    data: params
  });
}

export function checkDefaultRole(params: { relationId: string; type: "02" | "03" }) {
  return request<ResponseData<any[]>>(`${scenicHost}/role/checkDefaultRole`, {
    method: "GET",
    params
  });
}

//查询权限列表（菜单权限）,2022 年 9 月 16 日
//@see https://dev.shukeyun.com/scenic/api-v2/doc.html#/%E6%99%AF%E5%8C%BAerp%E6%A8%A1%E5%9D%97/%E5%BE%AE%E6%9C%8D%E5%8A%A1%E9%85%8D%E7%BD%AE%E6%A8%A1%E5%9D%97/scenicPermissionListUsingGET
export async function getPermissionListNew(param: API.GetPermissionListParam) {
  param.type = platformId;

  return request(`${scenicHost}/servicePackage/scenicPermissionList`, {
    method: "GET",
    params: param,
    skipErrorHandler: true
  });
}

//通过 user ID 获取权限列表，如不传则为取自己的权限
export async function getPermissionListByUserId(userids: any[], appId, groups?: any[]) {
  return request(`${scenicHost}/permission/retrieve`, {
    method: "GET",
    params: { users: userids, groups, appId }
  });
}

// ================== 个人信息  ===================
//获取用户 social 信息
export async function getUserSocial() {
  return request(`${scenicHost}/user/userSocial`, {
    method: "GET"
  });
}

//编辑密码
export async function updatePassword(params: object) {
  return request(`${scenicHost}/user/updatePassword`, {
    method: "POST",
    data: params
  });
}

//编辑手机
export async function updatePhone(params: object) {
  return request(`${scenicHost}/user/bindPhone`, {
    method: "POST",
    data: params
  });
}

//手机邮箱验证码
export async function getOtp(phone: string, type: string) {
  const t = type === "phone" ? 2 : 1;
  return request(`${scenicHost}/user/otp`, {
    method: "GET",
    params: {
      type: t,
      credential: phone
    }
  });
}

//邮箱忘记密码验证码
export async function getResetOtp(email: string) {
  return request(`${scenicHost}/user/otp`, {
    method: "GET",
    params: {
      type: 1,
      credential: email,
      template: "t0001"
    }
  });
}

//查询是否已经注册
export async function checkAccountExist(account: string) {
  return request(`${scenicHost}/user/checkCredential`, {
    method: "GET",
    params: {
      credential: account
    },
    skipErrorHandler: true
  });
}

//编辑邮箱
export async function updateEmail(params: object) {
  return request(`${scenicHost}/user/bindEmail`, {
    method: "POST",
    data: params
  });
}

export async function addUserSim(options?: Record<string, any>) {
  return request<API.RuleListItem>("/api/ban/userAdd", {
    method: "PUT",
    params: options
  });
}

export async function delUserSim(options?: Record<string, any>) {
  return request<API.RuleListItem>("/api/ban/userDelete", {
    method: "DELETE",
    params: options
  });
}

/**
 * //操作之类的统一提示请求，包括新增删除编辑
 *
 * @param netWork 网络请求的操作
 * @param successDeal 请求成功之后的动作
 * @param doingRemind 正在操作的提示字符串
 * @param successRemind 操作成功之后的提示字符串
 * @param failRemind 操作失败之后的提示字符串
 */
export async function operationRequest(
  netWork: Promise<any>,
  successDeal?: () => void,
  doingRemind?: string,
  successRemind?: string,
  failRemind?: string
) {
  const hide = message.loading(doingRemind ? doingRemind : "正在操作"); //带默认值提示编辑
  try {
    await netWork; //等待请求完成，加了 errorConfig 之后，非 200 会直接抛出异常
    hide();
    message.success(successRemind ? successRemind : "操作成功");
    if (successDeal) {
      successDeal(); //请求完成后需要的处理
    }
  } catch (e: any) {
    hide();
    if (e.name === "BizError") {
      /**
       * 系统默认有提示
       * @see /app.tsx
       */
    } else {
      message.error(`${failRemind ? failRemind : "操作失败"}:${e}`);
    }
  }
}

export async function setCookie(id: string) {
  return request<API.RuleListItem>(`${scenicHost}/user/noAuth/setCookie/${id}`, {
    method: "PUT"
    // params: options,
  });
}

//用户审批列表
export async function getUserApproval(params: any) {
  console.log(params);

  return request<API.RuleListItem>(`${scenicHost}/orgStructure/applyList`, {
    method: "GET",
    params
  });
}

//用户邀请
export async function getOrgStructure(params: any) {
  return request<API.RuleListItem>(`${scenicHost}/orgStructure/invite`, {
    method: "GET",
    params: {
      ...params
    }
  });
}

//用户申请加入
export async function getUserApply(params: any) {
  return request(`${scenicHost}/orgStructure/apply`, {
    method: "POST",
    data: params
  });
}
//用户审批
export async function getOrgStructureApproval(params: any) {
  return request(`${scenicHost}/orgStructure/approval`, {
    method: "POST",
    data: params
  });
}
//验证
export async function getOrgStructureVerifyCert(params: any) {
  return request(`${scenicHost}/orgStructure/verifyCert`, {
    method: "PUT",
    // params:{
    //   ...params
    // }
    data: params
  });
}

//复制权限
// api @see https://dev.shukeyun.com/scenic/api-v2/doc.html#/%E6%9D%83%E9%99%90%E6%A0%B8%E5%BF%83%E6%A8%A1%E5%9D%97/%E6%9D%83%E9%99%90%E6%A8%A1%E5%9D%97/changePermissionUsingPUT
export async function userDataMigrate(params: any) {
  params.type = platformId;
  return request(`${scenicHost}/permission/changePermission`, {
    method: "PUT",
    data: params
  });
}
//根据用户 id 获取对应角色
export function getRoleInfoList(params: { type: string; userId: string; relationId: string }) {
  return request<ResponseData<API.RoleInfoListItem[]>>(`${scenicHost}/role/roleInfoList`, {
    method: "GET",
    params
  });
}

/**
 * @description: 获取用户企业在当前系统的权限列表
 * @see https://dev.shukeyun.com/scenic/api-v2/doc.html#/%E6%9D%83%E9%99%90%E6%A0%B8%E5%BF%83%E6%A8%A1%E5%9D%97/%E6%9D%83%E9%99%90%E6%A8%A1%E5%9D%97/menuPermissionUsingGET
 */
export function getMenuPermissions(params: API.MenuPermissionsParams) {
  return request<ResponseData<API.Permission[]>>(`${scenicHost}/permission/menuPermission`, {
    method: "GET",
    params
  });
}
