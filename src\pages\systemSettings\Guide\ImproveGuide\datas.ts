const STEP_ITEMS = [
  {
    key: 1,
    label: '完成权益卡创建',
    isUsed: true,
    desc: '权益卡可为游客提供专属优惠或特权，增加客户粘性和复购率，提升店铺竞争力',
  },
  {
    key: 2,
    label: '完成权益卡库存创建',
    isUsed: true,
    desc: '合理设置权益卡库存，能确保销售顺畅，避免超售或浪费资源，提升运营效率',
  },
  {
    key: 3,
    label: '完成景区地图定位',
    isUsed: true,
    desc: '精准的地图定位能帮助游客快速找到景区位置，提升游览体验，吸引更多游客',
  },
  {
    key: 4,
    label: '官网商品管理',
    isUsed: true,
    desc: '优化官网门票展示，能让游客更便捷地选购，提升官网转化率，增加线上销售',
  },
  {
    key: 5,
    label: '创建宣传文章',
    isUsed: true,
    desc: '文章是提升品牌影响力和用户粘性的好帮手！快来发布优质内容，吸引顾客关注',
  },
  {
    key: 6,
    label: '创建帮助说明',
    isUsed: true,
    desc: '创建后，可为用户提供清晰的操作指南，提升用户体验',
  },
  {
    key: 7,
    label: '装修官网导航',
    isUsed: true,
    desc: '清晰的导航栏能让游客快速找到门票购买入口，提升官网用户体验，增加用户停留时间',
  },
  {
    key: 8,
    label: '装修官网主页',
    isUsed: true,
    desc: '一个吸引人的官网主页能提升品牌形象，吸引更多游客购买门票，促进用户转化',
  },
];

const UrlEnum = {
  1: {
    url: '/travel-card/setting?type=add',
    isUsed: true,
  },
  2: {
    url: '/travel-card/stock?type=add',
    isUsed: true,
  },
  3: {
    url: '/basic-information/scenic-information',
    isUsed: true,
  },
  4: {
    url: '/basic-information/visual?tabKey=product',
    isUsed: true,
  },
  5: {
    url: '/basic-information/visual?tabKey=article&type=add',
    isUsed: true,
  },
  6: {
    url: '/basic-information/visual?tabKey=help',
    isUsed: true,
  },
  7: {
    url: '/basic-information/visual?tabKey=officialSiteNav',
    isUsed: true,
  },
  8: {
    url: '/basic-information/visual?tabKey=officialSiteRenovate',
    isUsed: true,
  },
};

export { STEP_ITEMS, UrlEnum };
