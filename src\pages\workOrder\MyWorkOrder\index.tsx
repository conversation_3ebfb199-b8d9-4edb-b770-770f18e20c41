/**
 * name: 我的工单
 * path: /workOrder/myWorkOrder
 */
import { tableConfig } from "@/common/utils/config";
import { toValueEnum } from "@/common/utils/tool";
import useModal from "@/hooks/useModal";
import { getWorkOrderList } from "@/services/api/workOrder";
import type { ActionType, ProColumns } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import { Tabs } from "antd";
import { useRef, useState } from "react";
import { useModel } from "@umijs/max";
import RecordDetails from "../ApplicationRecord/components/RecordDetails";
import { ApprovalStatusEnum, WorkOrderStatusEnum, WorkOrderTypeEnum } from "../common/data";

enum TagEnum {
  待审批 = "pendingApproval",
  已审批 = "haveApproval",
  我的申请 = "myApplication"
}

export const workOrderTabItems = [
  {
    label: "待审批",
    key: TagEnum.待审批
  },
  {
    label: "已审批",
    key: TagEnum.已审批
  },
  {
    label: "我的申请",
    key: TagEnum.我的申请
  }
];

const Table: React.FC = () => {
  const { initialState } = useModel("@@initialState");
  const { scenicId = "" } = initialState?.scenicInfo || {};
  const { userId = "" } = initialState?.userInfo || {};
  const { coId = "" } = initialState?.currentCompanyInfo || {};
  const [issueStatus, setIssueStatus] = useState<ApprovalStatusEnum>(ApprovalStatusEnum.待审批);
  const [tag, setTag] = useState<TagEnum>(TagEnum.待审批);

  const modalState = useModal();
  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.WorkOrderListItem>();

  const tableListReq = async (params: API.WorkOrderListParams) => {
    const { data } = await getWorkOrderList(params);
    return data;
  };

  const columns: ProColumns<API.WorkOrderListItem>[] = [
    {
      title: "审批编号",
      dataIndex: "id",
      search: false
    },
    {
      title: "工单名称",
      dataIndex: "issueName"
    },
    {
      title: "工单类型",
      dataIndex: "issueType",
      valueEnum: toValueEnum(WorkOrderTypeEnum),
      renderText: text => Number(text)
    },
    {
      title: "申请人姓名",
      dataIndex: "username",
      hideInTable: tag === TagEnum.我的申请,
      hideInSearch: tag === TagEnum.我的申请
    },
    {
      title: "申请提交时间",
      dataIndex: "createTime",
      valueType: "dateRange",
      search: {
        transform: values => {
          return {
            startTime: values[0],
            endTime: values[1]
          };
        }
      },
      render: (text, record) => record.createTime
    },
    {
      title: "审批状态",
      dataIndex: "issueStatus",
      valueEnum: toValueEnum(WorkOrderStatusEnum),
      hideInTable: tag !== TagEnum.我的申请,
      hideInSearch: tag !== TagEnum.我的申请
    },
    {
      title: "操作",
      valueType: "option",
      render: (_, record) =>
        tag == TagEnum.待审批 ? (
          <a
            onClick={() => {
              setCurrentRow(record);
              modalState.setTypeWithVisible("update");
            }}
          >
            审批
          </a>
        ) : (
          <a
            onClick={() => {
              setCurrentRow(record);
              modalState.setTypeWithVisible("info");
            }}
          >
            查看
          </a>
        )
    }
  ];

  return (
    <>
      <Tabs
        tabBarStyle={{ padding: "0 24px", margin: "0", background: "#fff" }}
        onChange={key => {
          setTag(key);
        }}
        activeKey={tag}
        items={workOrderTabItems}
      />
      <ProTable<API.WorkOrderListItem, API.WorkOrderListParams>
        {...tableConfig}
        columns={columns}
        actionRef={actionRef}
        params={{
          // 我的审批:issue  我的申请:approval 动态
          [tag === TagEnum.我的申请 ? "issuePlatformId" : "approvalPlatformId"]: `${scenicId}/${coId}`,
          // 已审批 待审批
          approvalStatus:
            tag === TagEnum.我的申请
              ? undefined
              : tag == TagEnum.待审批
              ? ApprovalStatusEnum.待审批
              : ApprovalStatusEnum.已审批,
          [tag === TagEnum.我的申请 ? "issueGroup" : "approvalGroup"]: "backend",
          [tag === TagEnum.我的申请 ? "issueUserId" : "approvalUserId"]: userId
        }}
        request={tableListReq}
      />
      {/* 详情和审批 */}
      <RecordDetails modalState={modalState} actionRef={actionRef} currentRow={currentRow} tabKey={tag} />
    </>
  );
};

export default Table;
