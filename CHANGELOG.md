# Changelog

All notable changes to this project will be documented in this file.

## [unreleased]

### 🚀 Features

- 在基本信息页面和景区信息编辑模态中添加景区别名字段
- 测试
- 提交husky和代码格式化功能
- *(AiService)* 更新 apiChatCompletion 函数以支持回调方式处理流式响应，并在 AiService 组件中实现相应逻辑，优化消息处理和错误处理机制
- 添加数据脱敏功能，更新相关组件以支持用户隐私信息的处理
- *(Welcome)* 更新景区列表点击逻辑，支持单个景区直接跳转和多个景区弹出选择框
- *(enum)* 添加其他支付选项到支付类型枚举中
- 提交分支名,提交信息，提交信息管理议题id功能 
- *(api)* 更新知识库相关接口，统一使用helpHost 
- 添加分支名和提交信息检查功能 
- 添加分支名和提交信息检查功能，增强提交信息管理，支持合并提交解析和议题ID处理 
- 修改Jenkins 分支名和GITLAB参数不匹配引发的报错 
- 修改Jenkins分支名校验包安装逻辑 
- 消息通知库存创建跳转 
- 慧景云-库存创建新增交易所发行 
- 更新知识库相关接口，统一使用helpHost 
- 更新知识库接口，统一使用helpHost，优化代码格式 
- 智能客服位置调整优化 
- 智能客服开发 
- 智能客服整体逻辑优化 
- 完成数据检索和票务管理对接 
- 移除用户不存在时的退出登录逻辑 
- 景区最大发行量限制 
- 校验景区最大发行量 #2098 
- 交易所发行的库存，支持分时预约的商品 

### 🐛 Bug Fixes

- 更新 aiServeice.png 图片文件
- *(Welcome)* 修改按钮点击逻辑，从直接跳转链接改为在新窗口中打开链接
- *(Tag)* 更新退款状态描述，将“已退款”修改为“退款成功”以更准确反映状态
- 测试 
- 宽度优化 
- Test 
- 慧景云，门票设置，新建产品，产品字段显示为null修复 
- 配合后端修复接口 
- 修复问题 
- 修复校验景区最大发行量的一些问题 
- 调整交易所发行的位置 
- 修复库存创建限制问题 #16 
- 智能客服文本展示优化 
- 数据检索问题联调修复 
- 修复景区最大发行量限制问题 
- 编辑库存不可以操作入园时间年份 

### 🚜 Refactor

- 重构Husky钩子脚本，简化逻辑并更新配置 

### 📚 Documentation

- Auto-generate changelog [ci-skip] 

## [release-v4.2.1] - 2025-04-18

### 🚀 Features

- *(Visual)* 添加智能客服组件及相关样式和逻辑 #HLY-358
- *(上传组件)* 增加拖拽上传功能并优化文件类型提示
- *(RightContent)* 添加AiService组件以提供客服功能 #HLY-384
- *(AiService)* 实现客服聊天功能并启用组件 #HLY-384
- *(知识库)* 添加AI知识库识别状态枚举并优化知识库页面功能  #HLY-384
- *(用户管理)* 添加用户不存在的错误处理逻辑并更新相关文档和状态码 #HLY-360
- *(知识库)* 添加文件上传功能和文件列表展示 #HLY-360
- *(知识库)* 增强文件列表获取逻辑，支持上传成功后自动更新文件列表 #HLY-360
- *(AiService)* 优化客服聊天功能，添加消息发送状态管理和滚动效果 #HLY-383
- *(config)* 更新AI接口地址，简化API路径配置 #HLY-360
- *(知识库)* 增强文件管理功能，支持文件上传、删除及文件列表获取 #HLY-360
- *(知识库)* 更新文件删除接口，调整文件数量渲染逻辑，优化文件列表获取 #HLY-360
- *(知识库)* 优化文件上传回调逻辑，简化文件删除反馈处理 #HLY-360
- *(知识库)* 更新知识库组件中的访问控制逻辑，优化状态渲染 #HLY-360
- *(AiService)* 添加知识库列表获取功能，优化客服聊天逻辑 #HLY-387
- *(AiService)* 更新AI聊天功能，集成知识库ID获取和流式响应处理 #HLY-387
- *(AiService)* 优化知识库文件ID获取逻辑，调整消息发送处理和状态管理 #HLY-387
- *(AiService)* 集成AI对话接口，优化消息ID生成和状态管理 #HLY-387
- *(AiService)* 优化流式响应处理，累积内容更新逻辑 #HLY-387
- *(知识库)* 添加知识库文件识别状态，更新状态渲染逻辑 #HLY-360
- *(AiService)* 添加聊天记录保存功能，优化消息处理逻辑 #HLY-382
- *(知识库)* 添加知识库文件上传功能，优化上传回调逻辑和状态管理 #HLY-360
- *(知识库)* 更新知识库文件识别状态结构，优化状态渲染为带颜色标签 #HLY-360
- 修改设计器跳转配置地址
- *(AiService)* 添加聊天记录获取功能，优化消息初始化逻辑 #HLY-385
- *(tool)* 添加 Markdown 转换功能，支持将 Markdown 文本转换为 HTML 和纯文本 #HLY-385
- *(Knowledge)* 添加文件列表分页功能，更新表格组件以支持分页显示 #CSZ-954
- *(AiService)* 添加历史记录显示功能，优化消息处理逻辑 #CSZ-980
- 添加 @microsoft/fetch-event-source 依赖并更新 apiChatCompletion 函数以支持事件源流处理，同时优化工具函数的默认参数设置和列表样式

### 🐛 Bug Fixes

- 弹窗 #HLY-359
- Merge branch '0418/knowledge' into dev
- *(api)* 修复apiDelKnowledge函数中的参数传递方式，将data改为params以符合请求规范 #HLY-360
- Merge branch '0418/Knowledge' into dev
- *(api)* 更新helpHost的API路径，移除多余的'/api'前缀 #HLY-387
- *(知识库)* 更新访问控制逻辑，仅允许识别成功状态可点击 #HLY-360
- Merge branch 'test' into dev
- Merge branch 'lhy/0418' into dev
- *(AiService)* 解决滚动到底部不生效的问题 #HLY-385
- *(AiService)* 移除平滑滚动，优化滚动到底部的实现方式 #HLY-385
- *(AiService)* 处理换行符问题 #HLY-385
- #HLY-385
- *(AiService, Knowledge)* 将 businessId 修改为固定值 8888，更新 API 调用参数，优化聊天记录保存逻辑 #HLY-360
- *(Knowledge)* 将 businessId 从固定值 8888 修改为动态值 scenicId，更新 API 调用参数
- *(Knowledge)* 更新文件上传组件的接受文件类型，移除对 JPEG 格式的支持 #CSZ-963
- *(AiService)* 为抽屉组件设置 zIndex 属性，确保其在其他元素之上显示 #CSZ-956
- *(Knowledge)* 禁用表格组件的分页大小和快速跳转功能，以简化用户体验 #CSZ-954
- *(ApiService)* 忽略包含 ping 的消息，优化数据处理逻辑 #CSZ-947
- *(requestErrorConfig)* 启用 systemType 属性，确保请求配置正确 #CSZ-980
- *(config)* 更新 AI 接口地址，确保开发环境配置正确
- Merge branch 'lhy/0418' into test
- Merge branch 'test' of git.shukeyun.com:scenic/backend-v2 into test
- *(AiService)* 更新 scenicId 和 roleId 的赋值逻辑，移除不必要的 accountNumber 字段
- *(AiService)* 更新 userId 的获取逻辑，确保从 userInfo 中正确提取
- *(Knowledge)* 按更新时间倒序排序知识文件列表 #CSZ-1015
- *(Knowledge)* 删除知识文件后自动刷新文件列表，更新上传文件类型限制 #CSZ-1093
- *(enum)* 更新订单状态描述以更准确反映实际状态
- 设计器跳转通用模版修改

### 🚜 Refactor

- *(Service)* 重构Service组件，移除冗余代码并引入Knowledge组件 #HLY-358
- *(UploadFile)* 优化文件类型校验逻辑 #HLY-381
- *(Knowledge)* 优化文件上传表单逻辑和状态管理
- *(api)* 添加知识库相关API服务并更新表单验证规则 #HLY-360
- *(Knowledge)* #HLY-360

## [release-v4.2.0] - 2025-04-03

### 🐛 Bug Fixes

- #CSZ-476 优化面包屑
- #CSZ-476 优化

### 🚜 Refactor

- *(api)* 网关统一，处理漏网之鱼 #XINFANSHI-836

## [release-v4.1.1] - 2025-03-21

### 🚀 Features

- #HLY-212 #HLY-213 #HLY-214 #HLY-215 新增引导
- #HLY-210 #HLY-211 慧景云引导开发联调
- #HLY-237 【前端开发】交易所快捷跳转
- 提交

### 🐛 Bug Fixes

- COMMON_API_HOST
- 修复问题
- #CSZ-395 修复引导跳转链接问题
- #CSZ-352 处理UI验收问题
- #CSZ-405 #CSZ-407 #CSZ-415 #CSZ-416 修复引导的bug
- 修复 #CSZ-468
- Merge branch '0321/guide' into test

## [release-v4.1.0] - 2025-03-11

### 🚀 Features

- 新增商品交互优化
- 处理新增商品时isDigit默认值问题

### 🐛 Bug Fixes

- 下载中心、报表导出
- 景区报表配置
- 报表导出配置
- 报表导出
- 优化
- 修复新增商品跳转问题

## [release-v4.0.0] - 2025-02-24

### 🚀 Features

- 新增景区列表跟企业列表
- *(角色管理模块)* 添加2021年10月29日
- *(组织架构模块)* 添加2021年11月1日
- *(组织架构模块)* 添加功能2021年11月3日
- *(角色模块)* 添加功能2021年11月4日
- *(角色架构模块)* 添加功能2021年11月4日
- Debug 2021年11月5日
- *(组织构架功能)* Debug 2021年11月12日
- *(票务)* 添加菜单，售票设备-2021.11.5
- Debug 2021年11月12日
- Debug 2021年11月15日
- Debug 2021年11月16日
- Debug 2021年11月18日
- Debug 2021年11月19日
- Merge
- *(景区)* 添加根据url区分不同景区的功能
- 增加多景区功能
- 2021年12月3日
- 拆分景区跟慧旅云平台
- 增加订单管理
- Add order
- 增加《统一错误处理》文档说明
- Debug
- *(权限管理)* 更新权限字段；修复对应的用户管理问题
- 角色管理
- 角色功能权限变更
- Add eslint
- *(登录)* 是否有景区的权限
- Add eslin
- *(角色管理)* 更新树形选择
- Home page
- 添加生产环境配置
- Sync
- 添加https前缀
- Add notice
- SeachTree
- Unity login
- 添加门票销售管理
- 初始化接口异常时，跳转到异常页
- 退回登录页带参数
- 🎸 完成销售管理旅游卡库存UI
- 🎸 旅游卡feat UI完成
- 权益管理接口联调
- Merge commit 'e4669244a13538cea24c234a3b43a4cbe30f71e3' into debug/common
- 1.角色管理移动到用户中心 2.添加未设置密码提示 3.增加新建账号密码复制 4.新增同级部门功能 5.新增数据迁移功能
- Welcome page charts
- Pull branch dev into feat/chart
- Change optional date
- Add scenic_id params
- Pull dev into feat/chart
- 注释首页图表
- Pull origin dev into feat/chart
- 添加环境变量【景区默认 logo 半路径】
- 旅游卡审核迭代需求
- 操作日志埋点
- 操作日志新加内容变化表单
- *(操作日志)* 添加 project
- Markdown试用
- 景区全局参数和库存校验
- 修改线上域名
- 价格区间
- 添加启禁用用户功能
- 请求拦截器添加全局参数
- 当天购票起止时间精确到秒
- 权益审核 暂时注销
- 启用状态接口更换
- *(操作日志)* 添加技术服务费率
- *(官网配置)* 门票添加购买图片
- 更换核销 app 二维码
- 添加头像的显示
- 接口添加企业筛选条件
- 首页数据筛选企业
- 微服务有关权限的改动
- *(用户权限)* 添加企业筛选
- 权限相关接口添加 appId
- Cas 统一登录人数
- 工单路由配置
- *(权限管理)* 添加默认角色按钮
- 景区首页图表-实际入园人数
- 更改结算系统地址
- 技术服务费率审批
- 根路由跳转到慧旅云主页
- 用户管理所属企业现在当无部门时显示为公司名称
- 所属企业冲突问题
- 去除首页欢迎语 & 新增景区数据大屏跳转
- 修复权限重叠问题
- 修改结算地址
- 替换 logo
- 更换 logo
- 菜单顺序调整
- 发行服务费率弹窗优化
- 检测浏览器版本
- 权益销售明细
- 根路由跳转慧旅云前增加倒计时页面
- 对接用户列表改动
- 优化出票规则使用体验
- 出票规则-权益票，实名方式限制身份证
- 试用景区临期截图
- 添加结算方式：pos 机
- 多日票完成
- 售票设备新增自助售票宣传信息
- 权益卡商品产品合并
- 封装上传组件 更换自助售票地址
- 操作日志
- 操作日志完成第一版
- Add TicketTemplateConfig component to systemSettings/Config page
- 添加请求唯一标识符
- 检票设备/售票设备页面优化
- 添加数字资产
- 普通库存新增、编辑、详情
- 经销价格
- 经销价格筛选
- 数字资产已打开，不可修改
- 去除原先的官网配置，初始搭建tab页 https://git.shukeyun.com/new-paradigm/workflow/-/issues/251
- 建立分页tab；增加商品管理表头 https://git.shukeyun.com/new-paradigm/workflow/-/issues/250
- 增加选择，批量删除；增加导入窗口；增加导入窗口表头 https://git.shukeyun.com/new-paradigm/workflow/-/issues/250
- 增加帮助中心表格 https://git.shukeyun.com/new-paradigm/workflow/-/issues/252
- 复制慧景云相同界面；去除权限有关代码 https://git.shukeyun.com/new-paradigm/workflow/-/issues/252
- 增加接口声明；去除排序有关代码 https://git.shukeyun.com/new-paradigm/workflow/-/issues/252
- 去除排序字段；将原有的三级目录结构改为两级；调通目录增加、删除、重命名接口 https://git.shukeyun.com/new-paradigm/workflow/-/issues/252
- 增加ahooks依赖；迁移电商的文章管理页 https://git.shukeyun.com/new-paradigm/workflow/-/issues/251
- 对接文章接口；修改差异布局 https://git.shukeyun.com/new-paradigm/workflow/-/issues/251
- 增加文章导入界面；增加店铺获取；https://git.shukeyun.com/new-paradigm/workflow/-/issues/251
- 增加获取店铺中的文章；增加批量添加文章的选择；https://git.shukeyun.com/new-paradigm/workflow/-/issues/251
- 去掉商品管理导入页；完成批量添加文章接口对接；https://git.shukeyun.com/new-paradigm/workflow/-/issues/251
- 增加内嵌添加布局；https://git.shukeyun.com/new-paradigm/workflow/-/issues/251
- 对接新增、修改文章接口；完善新增、修改文章界面；https://git.shukeyun.com/new-paradigm/workflow/-/issues/252
- 添加商品列表；迁移枚举类； https://git.shukeyun.com/new-paradigm/workflow/-/issues/250
- 添加商品查看详细页；添加管理商品跳转 https://git.shukeyun.com/new-paradigm/workflow/-/issues/250
- 添加官网预览跳转 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/345
- 完成门票销售（铸造记录兼容），添加类型9: '库存修改'　https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/565
- 库存创建（区块链铸造记录） https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/565
- 库存创建（经销价格兼容） https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/565
- 库存创建记录修改样式 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/565
- 初始化完成
- 基础设施管理、基础信息配置
- 权益卡
- UI 升级完成
- 交易所审核状态更改
- 调整交易所按钮样式 https://git.shukeyun.com/research-and-development/scenic/1-huilvyun/-/issues/815
- 调整交易所链接 https://git.shukeyun.com/research-and-development/scenic/1-huilvyun/-/issues/815
- 合并代码 https://git.shukeyun.com/research-and-development/scenic/1-huilvyun/-/issues/815
- 库存创建 经营状况统计添加状态判断展示
- 第九次迭代，添加商品名称和票种
- 用户输入保存数据和搜索时前端过滤
- 区块链库存新增文字提示
- 迁移企业注册优化功能 - 数据展示与编辑 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/720
- 迁移企业注册优化功能 - 解决时间显示问题 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/720
- 完成企业优化功能迁移 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/720
- 出票规则-出票类型根据规则类型动态变动
- 增加认证状态 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/720
- 增加排序算法 https://git.shukeyun.com/research-and-development/scenic/1-huilvyun/-/issues/2
- 开发ai点位语音选择功能
- Ai
- Ai 导览优化
- 解决旧数据无法回显ai回复问题
- 语音示例播放功能
- 检票规则---快捷跳转优化
- 快捷跳转优化
- 库存限制优化
- 议题优化创建交易所库存时，获取交易所列表逻辑和跳转
- 新增CustomErrorBoundary
- 官网页面搭建 --- 80%
- 官网导航 --- 页面开发及联调
- 官网导航联调
- 官网装修页面搭建+初步联调
- 处理navigationUrl
- 处理官网导航未完成的部分
- 【点位管理】兼容修改接口过滤英文版枚举问题
- 通过官网链接跳转默认进入首页
- 官网装修新增模板框卡片增加模板照片展示已经滚动动画
- 固定模版优化
- 固定模版跳转调整
- 库存创建迭代16的内容
- 兼容处理区块链商品库存批次redis缓存覆盖导致数据异常的问题
- 添加 Jenkinsfile 以支持 CI/CD 流程
- 官网导航接口兼容
- 页面装修接口兼容

### 🐛 Bug Fixes

- 优化景区企业列表
- 优化景区企业
- *(检票)* 新增检票页面
- *(检票)* 新增检票设备等页面
- *(检票)* 新增售票设备等页面
- *(检票)* 修改部分页面
- *(检票)* 更新dev代码
- *(检票)* 增加手机号验证
- *(检票)* 增加页面
- *(检票)* 修改检票设备模块
- Merge dev
- Merge branch 'dev'
- 切换景区跳回首页
- Dev
- 11.23
- Merge branch 'chenzhiyi-git-test' of git.hqshuke.com:scenic/backend-v2 into chenzhiyi-git-test
- 统一请求处理
- 'dev'
- Merge branch 'test'
- 组织架构优化
- 样式修改
- Merge branch 'dev' into 'test'
- Merge org
- 修复初次打开系统，跳转登陆页时，弹出报错信息的问题
- 更新 git工作流程
- 修复默认景区登陆页logo
- 修改readme.md
- Title使用景区logo
- *(票务)* 修复test合并冲突
- 优化 初始化数据
- Bug
- H
- U
- Merge branch 'dev' of git.hqshuke.com:scenic/backend-v2 into dev
- Merge branch 'dev' of git.hqshuke.com:scenic/backend-v2 into debug/org
- Merge branch 'feat/order'
- OrderPage debug
- Merge branch 'dev' of git.hqshuke.com:scenic/backend-v2 into feat/order
- Order debug
- Add ico
- Merge remote-tracking branch 'origin/dev' into dev
- 组织架构try
- 组织架构bug
- 处理统一错误报错问题
- 权限控制
- 权限控制修改
- Merge branch 'feat/limit' of git.hqshuke.com:scenic/backend-v2 into dev
- 添加 组织架构 权限
- Org权限bug
- 屏蔽订单管理
- 多余org删除
- 权限更改bug
- 挪动 global.tsx 里面的通用方法到 tool.tsx
- 修改方法名 getUniqueId
- 修复没权限时菜单依旧显示问题
- 路由权限展示
- 更换logo 图片
- 员工销售bug
- Merge branch 'dev' of  debug/org
- 权限添加景区判断
- SalesAuthority查看
- Org 取ScenicId
- Canary
- Org规范优化
- Cas界面优化
- Icon
- Add view
- 优化代码结构
- 重构架构
- Merge commit '98401ab6eb080e38abe8540621d4fdb065a9104e' into dev
- Merge common
- Merge commit '3fe35be5db66831d9325f8401c28ef6abfffc70a' into dev
- Merge 'dev'
- '官网首页'
- Merge branch 'dev' of  into feat/view
- Delete
- Merge commit '9a41870d97280cdcb22c18c2070ff8708cf4a204' into dev
- Merge remote-tracking branch 'origin/feat/view' into dev
- 替换域名
- Merge branch 'feat/view' of git.hqshuke.com:scenic/backend-v2 into dev
- Merge branch 'dev' of into feat/view
- Merge feat/view
- Org
- 添加景区官网地址跳转
- 可视化配置 预览问题
- Update
- Merge branch 'dev' of git.hqshuke.com:scenic/backend-v2 into feat/view
- Merge branch 'test' of git.hqshuke.com:scenic/backend-v2 into dev
- Orgtree显示景区
- 去除退款单管理
- Add favicon.ico
- Merge branch 'dev' of git.hqshuke.com:scenic/backend-v2 into unifyLogin
- Merge commit '9ff063558b363d62dc1723d20f8defd9f7cded9b' into dev
- Merge branch 'master' of git.hqshuke.com:scenic/backend-v2 into dev
- Add wifi page
- Add permission
- 优化统一登录
- Merge commit '9965d0acf5264d6b2e9383bd3d59e6d96097e172' into dev
- Merge branch 'test' of git.hqshuke.com:scenic/backend-v2 into feat/view
- 优化启动命令
- 查不到景区时，跳到没权限页面
- 库存
- 合并
- 退票规则默认费率
- 退票规则
- 全局人脸身份识别配置
- 身份识别类型展示
- 合并调试
- 商品授权企业列表死循环调试
- 实名方式
- 商品授权
- 员工销售权限
- Hb
- Prod
- 更新退出登录接口
- Add yarn.lock
- Update yarn.lock
- 更新pack,lock
- 全局配置队列名
- 回退最新lock
- 固定依赖版本
- Merge feat/viwe
- Merge branch 'feat/new_view' into feat/view
- 优化用户名
- 修复dependency表单组件，pro-form版本。增加服务商不可编辑。
- 发包
- 更新库存产品
- 1
- 处理多组图片上传
- Org Tooltip
- 授权列表参数名更改
- 首页移除窗口售票
- 移除销售渠道，新增商品名称。
- 移除商品名称检索
- 移除售价，员工授权
- 修改环境变量 MERCHANTS_URL
- Feat/view
- 退票规则，景区名称
- *(门票销售管理)* 修复入园时间
- *(退票规则)* 不可退票时，其他退票费率显示 -
- 分时预约时间
- Master
- Merge commit '51417756eb4bcdba5057c556edccc581c1185c47' into dev
- 用户审批
- 旅游卡退票规则
- 人脸识别全局配置
- 🐛 完成票种设置旅游卡UI
- 完成权益管理
- 完善审核管理附件信息
- 审核管理通过、驳回confirm
- Issue字段修改
- 添加旅游卡权限控制
- Merge branch 'dev' into feat/travel
- 修改ts错误、警告
- Merge commit 'dad22f81214a7442ce4c86d9041a1d3ae7d86e2f' into feat/view
- Merge commit 'f85186fd2b61f5d8b6657820a33c8854e5571759' into feat/view
- 审核接口对接
- Merge commit '5a348cb850de11dfd8daf39576c1b41f7865feee' into feat/view
- 库存接口变动
- 库存详情接口
- Megde dev
- Merge commit common
- 审核管理权限bug修复
- Merge commit  from common
- Nerge dev
- 景区默认logo
- Common
- 重置图片
- 产品类型，基础票枚举
- 产品商品字段枚举迁移
- Test
- 次数枚举，全局配置复制
- 旅游卡枚举
- 发库存防抖
- Merge branch 'dev' of git.shukeyun.com:scenic/backend-v2 into debug/common
- 旅游卡bug
- 旅游卡审批改动
- 限制新建旅游卡有效天数为正数
- 旅游卡库存下拉列表修改
- 基础票商品添加技术服务费率
- 旅游卡新增加景区id参数
- 旅游卡时长
- 添加商品指导价必填
- 修改一些字段错误
- 旅游卡商品新增添加景区id参数
- 落地页修改存在样式和交互问题
- 新增库存防抖错误
- 指导价格区间最小改为0
- 注册页路由问题
- 修改景区参数上传和点击查看问题
- 登录地址跳转
- 首页图表
- 新增剪切图片组件
- 操作日志新加页面
- Merge branch 'dev' of git.shukeyun.com:scenic/backend-v2 into feat/operation_log
- Merge branch 'feat/operation_log' of git.shukeyun.com:scenic/backend-v2 into feat/operation_log
- 添加了查看列表
- 旅游卡新增字段迁移到商品
- 落地页跳转
- 修改剪切图片组件
- 新增规则当天购票起止时间字段没传
- 景区信息启用节点
- 旅游卡删除有效天数字段展示
- 景区信息获取经纬度
- 景区图片上传问题
- 清空图片
- 微服务埋点
- 区分操作模块
- Merge branch 'feat/operation_log' of git.hqshuke.com:scenic/backend-v2 into feat/operation_log
- 查看列表
- 修复图片无法删除问题
- 添加审核状态
- 操作日志优化
- Merge branches 'feat/operation_log' and 'feat/operation_log' of git.hqshuke.com:scenic/backend-v2 into feat/operation_log
- 权限
- 修复审核后刷新列表
- 权限管理
- 审核状态调换位置
- 图表时间排序
- 修改埋点字段错误
- 埋点传值修改
- Merge branch 'dev' of git.hqshuke.com:scenic/backend-v2 into feat/view_luodiye
- 修改宽度
- 修改操作日志的列表渲染
- 加空格
- 落地页适配
- 分控管理权限修改
- 所有特权列表去掉指导价格区间
- 修改实名方式枚举值
- 指导价格区间显示错误
- Footer 文案
- Footer 抽离公共组件
- 落地页无法点击的问题
- Merge branch 'feat/view' of git.hqshuke.com:scenic/backend-v2 into feat/view_luodiye
- 发送短信倒计时问题
- 规则选择表，身份证审核
- 修改操作日志参数结构
- Merge branches 'feat/operation_log' and 'feat/operation_log' of git.shukeyun.com:scenic/backend-v2 into feat/operation_log
- *(操作日志)* 字段改用 | 隔开
- 优化操作日志字段结构
- 操作指南
- 修改操作指南icon
- Markdown
- 图片上传问题
- Markdown组件完成
- 更新退出登录逻辑
- Merge commit '0bac37c5b244af004545bedf2ec0a4e217c38b03' into feat/operation_log
- 优化登录逻辑
- Merge commit '61d2b738354660e9e54aa4253a2df2b7e2e91608' into feat/operation_log
- 公告--查看详情
- 全局参数配置
- 文案修改
- 权益列表删除手机号
- Luodiye debug
- 权限添加
- 添加缺失的权限
- 解决鼠标放上去防抖问题
- 防抖
- 修复全局参数配置缺失 paramId 参数
- 添加公用方法 objToUrlPath
- 邀请接口
- 邀请完成
- 修改邀请条件
- 操作日志分时预约显示错误
- 修改路由位置
- Merge branch 'dev' of git.shukeyun.com:scenic/backend-v2 into feat/menu_hu
- 身份证输入框，规则样式文案
- 优化 goToLogin 方法
- 修改模板name
- 最小起订量和最大预订量限制
- 旅游卡出票规则，输入框防抖
- Appid 改为动态获取
- 修复方法goToLogin
- 放开接收邀请路由
- 接收邀请
- 路由跳转
- 修改地址
- 覆盖字体颜色
- 修改权限参数
- 落地页优化
- 修改落地页bug
- Merge branch 'dev' of git.shukeyun.com:scenic/backend-v2 into feat/luodiye
- 清楚多余定时器
- 图表 token
- Update app.tsx
- 落地页文案修改
- 首页提示语
- 分时预约&&路由权限&&首页标语
- 默认模板错误
- 修复出票规则使用时间不能为负数
- 接受邀请后刷新页面
- Merge branch 'test' of git.shukeyun.com:scenic/backend-v2 into feat/hu_homecontent
- 门票设置的bug
- 修复查看详情跳转问题
- 门票销售不显示问题
- Merge branch 'dev' of git.shukeyun.com:scenic/backend-v2 into feat/hu_homecontent
- 邀请移动端显示
- 注释折扣&&市场标价
- 注释折扣区间
- 商品名称
- 权限菜单，分时预约
- 更改markdown样式为github-markdown-css
- 修复验证0开头被格式化
- 修复门票&&落地页 BUG
- 初始化接口不为 200 时，不跳错误页
- 落地页bug
- 批量操作
- Merge branch 'test' of git.shukeyun.com:scenic/backend-v2 into feat/hu_ticket_sales
- 门票销售- 新增功那么n能批量核销、批量退票
- 图片回显
- 图片上传组件封装完成
- 添加自定义裁剪功能
- 图片上传增加默认值
- 更换所有上传组件
- 邀请企业跳转问题
- 修改核销状态
- 分时预约组件
- 图片地址错误
- 新增图片上传错误
- 图片上传错误
- Merge branch 'test' of git.hqshuke.com:scenic/backend-v2 into test
- 更新电商系统地址
- Merge branch 'master' of git.hqshuke.com:scenic/backend-v2 into test
- 修复图片回显
- 修复验证码丢失0
- 启用价格策略
- 景区权限处理
- Merge from test
- 权限微调
- 打包优化去掉console
- 修复价格策略bug
- 详情组件默认权限
- 无权限重刷
- 修复价格区间回显
- 修复操作日志bug
- 邀请用户无权限
- 403
- 邀请管理员赋予启用权限
- 增加景区权限启用
- 市场标准价校验
- 修改ticket枚举值
- 申请审批接口传值错误
- 修改编辑服务技术费率传值
- Merge bug
- 更改路由和文件目录
- 修复邀请用户没权限问题
- 审批接口传值错误
- 修改企业地址，权限重刷
- *(操作日志)* 将景区 id 信息添加到 project 字段里
- 修复景区邀请用户功能
- RSA证书
- *(官网配置)* 修复模板修改问题
- 旅游卡续期
- *(无权限页面)* 修复点击去登录丢失景区信息问题
- 邀请管理员加请求
- 邀请接口添加电商权限
- 编辑技术服务费率去掉effectiveTime字段
- 旅游卡权益能启用
- 接受邀请
- 接受邀请授权接口加userId
- 需求一
- 分时预约检票枚举
- *(全局配置)* 显示改用 code 标签
- 取消ip、mac地址必填
- 邀请用户
- 商品折扣默认值100
- 优化邀请结果信息展示
- 技术服务费率最大为100
- 用户审批接口改为数组
- 邀请成功区分设备
- 出票规则权益展示
- 移除库存删除按钮
- 合并冲突
- 库存限制
- 取消当天购票起止时间初始值
- 细化TimePicker组件
- 关联旅游卡逗号换中文的
- Merge test
- SystemType改为backend
- 全局参数systemType
- 接口完成
- Merge branch 'feat/permission' of git.hqshuke.com:scenic/backend-v2 into feat/permission
- 前端显示名称，库存限制，游玩时间限制
- 订单枚举
- *(订单模块)* 订单状态改为读取枚举
- 重置密码权限
- 修改组织用户非必填
- 库存接口修复
- 产品名称
- 企业接口
- 组织架构接口修复
- 审批接口修复
- 修复app空字符串json错误
- 修复companyPermissionList接口
- 基础票产品名和c端名称联动反了
- 首页权限
- 商品详情 TODO
- 规则路由
- 撤销rsa证书
- SystemType加s
- 接受邀请接口修复
- 旅游卡权益审核
- 暂存
- 旅游卡权益审批
- 删除出票规则出票方式字段
- 限制生效日期最大值
- 景区信息去掉区块链
- 更新validate.ts
- 删除附件信息
- 技术服务费率最大值限制
- 技术服务费率传参改变
- *(用户管理)* 禁用用户时退出该用户的登录状态
- 权益票审核加userId参数
- 票添加商品加参createUserId
- 出票规则参数修改
- 旅游卡规则
- 测试任意路径
- 撤回
- 图表标题错误
- Meage test
- 商品规则，名称
- 规则详情
- 旅游卡出票规则
- 权益票规则详情
- 更新核销APP二维码
- 商品规则详情
- 应用到其他分时预约时段
- 预订时间退票费率
- 上传产品图片
- 预订
- 移除非分时一键应用
- 屏蔽头像
- 修复关联权益规则提示
- 邀请修改不开通电商服务
- Merge branch 'feat/organizational' into test
- Merge branch 'test' of git.shukeyun.com:scenic/backend-v2 into feat/access
- 退票时间校验
- 邀请换新接口
- 查询个人权限添加companyId
- 修复用户列表企业于当前企业不对应的问题
- 接口添加 appID
- 角色列表接口参数变动
- ForEach
- 回退问题调试
- 顽固代码
- 删除顽固代码文件
- 恢复顽固文件
- 移除公共 git
- 区块链主题
- 隐藏设置
- 区块链预页面
- 库存区块链展示
- 申请记录
- 工单图标
- 工单
- 我的工单
- 工单创建模板
- WorkOrder
- 优化一些逻辑
- 工单记录
- 工单模板
- 基本完成
- 添加权限
- 工单完成
- 小bug
- 修复默认角色传参问题
- *(权限管理页面)* 处理添加默认角色没有刷新
- 解决　权限管理-新建角色时，可选的菜单权限根据微服务改变
- 添加用户参数加 appid
- 修改商品去掉list和times字段
- 取消旅游卡工单流程、更换用户列表接口
- 处理bug
- 景区取值问题
- 审批操作加上权限
- 工单名称bug修复
- 文件重命名
- 修改工单模板日期格式
- 商品详情增加workSate字段
- 工单bug修复
- Bug修复
- 少/
- Meage
- 区块链交易展示
- 注销旅游卡工单流程
- 图片地址
- 优化工单创建交互逻辑
- 库存新增参数变更
- 所有审批人添加模糊搜索
- 修复英文日历问题
- 修改用户权限接口字段 relationId 改成 scenicId/companyId
- 修改旅游卡库存列表参数
- 修改 bug
- 删除无用依赖
- 兼容大数据接口 code 码
- 修改订单状态枚举
- /role/permission 接口添加 appId 字段
- 批量核销接口参数改变
- 景区图片字段修改
- 调整markdown文字显示宽度
- 修改字段
- 至
- Log
- 微服务授权接口
- 更改落地页授权参数
- 修复分时预约产品不能展示分时预约的问题
- 人脸识别类型
- 20000
- 删除用户审批多余字段
- 库存预警
- 修复bug
- 兼容生产成功code码
- 精确小数点后两位
- 对textArea增加长度限制
- 修复未引用问题
- 修改为空时添加空格
- 修复旅游卡产品备注空值
- 编辑角色的权限时，不作任何操作点击确定，该角色的权限会消失
- 添加默认角色增加二次确认
- 默认角色去重
- 修复空格
- 去掉库存禁用按钮
- 修复已选权益字段为空
- Merge remote-tracking branch 'origin/master' into test
- 发行服务费需求修改
- 旅游卡改权益卡
- 提示修复
- 修复库存列表入参
- 产品和商品禁用先后顺序
- 新增库存管理增加warning
- 创建库存调换“产品名称”和“总库存量”位置
- 创建库存超出库存提示
- Mock
- 修改枚举值
- 退票规则表单增加限制
- 新增权益卡表单验证修改
- 权限菜单排序
- 提交审批增加发起人
- 修改审批返回状态null的错误
- 商品管理权限
- 去除续卡期限
- Merge branches 'test' and 'test' of git.hqshuke.com:scenic/backend-v2 into test
- 删除多余文件
- 富文本限制2000个字符
- 修改文案提示
- 添加入园须知
- 修复无法修改出票规则
- 落地页
- 修复景区过期续期和提示
- 创建工单添加goodsName字段
- 试用景区过期页
- 修复工单节点必选
- 查看上链信息页面报错
- 关联权益卡后不能修改
- 权益卡商品关联权益票后禁止修改权益
- 工单bug
- 修复权益卡明细名称错误
- 修复权益卡明细的一些bug
- 修改查询参数
- 修改入园时间参数格式
- 权益卡新增下载销售账单
- 去掉权益卡上传图片
- 修改必选提示
- Merge branches 'test' and 'master' of git.hqshuke.com:scenic/backend-v2 into test
- 修复景区娱乐项目菜单错误
- 官网配置增加名称检索
- 解决官网配置搜索框清除内容报错
- 环境变量、本地代理
- 添加环境变量
- 切换测试环境跟开发环境的首页图表地址为生产地址
- 替换首页图表的数据接口
- 优化申请记录页面，申请提交时间筛选字段
- 修改退票原因字符数限制
- 修复跳转地址错误
- 修复权益卡库存创建筛选条件无效
- 出票规则上传审批内容规范说明必选
- 修复日期格式错误
- 门票销售批量删除
- 修复权益卡审核名称样式
- 省市区id错误
- 门票核销新增字段
- Merge branches 'dev' and 'dev' of git.hqshuke.com:scenic/backend-v2 into dev
- 出票时间入园时间查询
- 支付方式枚举值修改
- 更新首页数据统计域名
- 所属权益卡名称筛选项查询后关闭窗口未清空问题
- 用户下拉列表接口
- 用户和权限接口调整
- 用户管理接口对接
- 修复用户接口误提交到 test
- Merge from dev
- 景区续期跳转落地页
- 修复角色禁用状态
- 新增环境变量，修改首页图表数据接口
- 修改试用期景区跳转和提示文案
- 优化出票规则
- 修复过期景区登录 bug
- 帮助中心权限，角色名称限制
- 帮助中心入口
- 【出票规则】权益卡、权益票默认开启实名相关选项
- Merge branch 'debug/test' into dev
- 用户下拉列表替换
- 权限接口替换
- 全局新增编辑字眼，详情编辑弹窗样式
- 全局编辑字眼
- 全局字眼新增
- Ui
- 景区信息重写完成
- 自动宽度
- 限制出票规则详情字段展示
- 电话
- 门票设置选项卡，商品管理编辑
- 首页图表完成
- 全局配置
- 节假日管理编辑
- 全局配置完成
- Merge branch 'test' into canary
- 修复权益票卡表单禁用
- Merge branch 'canary' into dev
- 修复营业时间遗漏问题
- 修复全局配置
- 修复营业时间不合法
- 全局面包屑
- 全局面包屑，下拉滚动，新增按钮，详情样式
- App
- 图表5
- 门票核销 key
- 商品管理权限，库存详情，工单审批弹窗，下拉弹窗相对定位
- 门票销售查询
- 首页图标响应式
- 参与入园统计
- 图标 tab，接口参数
- 首页图标刻度优化
- 优化图表自适应，全局下拉框、弹框定位
- 移除详情操作栏
- Ticket批量删除
- 1. 图标日期选项卡占位 2. 表单校验失败定位
- 修复 ui 改版问题
- 修改日期选择器范围
- Fetch from test
- 限制7天选区
- 修复状态
- 核销退票
- 修复disabled
- 放开退票限制
- 退票原因
- 库存防抖
- 修复弹窗顺序错误
- 退票修改
- 退款金额
- 仅限窗口售票
- 退票金额问题
- 兼容按票按身份证情况
- 修复二维码
- 售票窗口switch
- 检票app
- 检票和出票配合使用
- 修改首页图表参数
- 产品二级页面
- 商品二级完成
- 修复权益票普通票tab
- 人脸设备
- 权益票枚举string
- 删除图片列表
- 修复出票检票规则匹配
- 修复选中票类型
- 数据回显
- 字段统一
- 修复人脸设备开关
- 系统到期提示优化
- 多日票新增产品
- 多日票规则字段迁移
- 检票记录
- 测试环境可修改多日票
- 修复返回按钮样式
- 取消测试
- 修复详情信息缺少
- 修复展示问题
- 参数修改
- 规则类型编辑禁用
- 修复编辑商品联动问题
- 修改分销折扣区间显示
- Zancun
- 打断退票弹窗
- 修复日期选中
- 修复核销记录终端展示
- 修复退票提示
- 核销添加操作人
- 修复检票设备名称
- 修复核销名称
- 修复批量核销
- 修复多日篇新增参数
- 表格响应，详情栅格，表单栅格加载
- 详情栅格，图表间隔
- 编辑详情弹窗组件
- ProModal 组件
- 权益卡管理交互
- 修复无限次核销错误
- 规则管理添加字段和修复
- 权益卡管理 UI 升级
- 工单管理 UI 交互升级
- 用户中心、系统设置 UI 交互升级
- 修复 0 空值枚举回显
- 全局样式线上优先级
- 规则弹窗
- 全局公共样式抽离，关闭虚拟滚动
- 修复出票规则启用状态
- 简化 ProModal 交互状态
- ProModal 支持二级页面、点位管理
- 详情二级页面、线路管理
- 景区地图选点、范围
- 景区信息只读状态
- 景区信息地图只读状态
- 只读状态取消鼠标滚轮缩放地图
- 路线管理
- 点位路线，导览链接
- 初始化点位管理数据
- 地图容器
- 导览链接
- 过滤权益卡出票规则
- 修复设置续卡期限提示
- 重构了操作日子的提交规则
- 工单商品价格修改
- 检票设备新增报错(issues_2767)
- 放开断点
- 修复设备图片显示错误
- 点位图片
- 地图层级、枚举初值
- 点位禁用路线异常
- 点位介绍
- 语音讲解类型
- 语音讲解枚举
- 地图内存释放
- 2d建筑、修复点位类型回显
- 语音类型枚举
- 自适应展示景区范围
- 地图点位自适应
- 地图路线自适应
- 导览链接二维码
- 空点位路线添加异常
- 放开百度地图api
- 易旅宝域名配置
- 去掉多余筛选
- 自助售票宣传信息查看样式修复
- 修复销售明细
- 删除多余搜索
- 修复旅景区类型枚举值错误
- *(2958)* 权益卡设置表单修复
- 权益卡删除使用记录字段
- 修复修改价格工单审批报错
- 修复工单审批节点校验错误
- Add Footer component and update code comments and formatting
- 修复操作日志 分时预约时间 undefined 问题
- 地址错误
- 自定义上传事件请求
- 修复格式校验错误
- *(3005)* 修复企业信息列表部分字段回显有问题
- GuideId
- 移除区块链暗黑主题
- 路线回显
- 出票规则身份证号自动选中
- 出票规则身份证号支持同号跳格
- 修复出票规则数据回显异常
- 图床域名
- 修改生产环境上传地址
- Merge remote-tracking branch 'origin/master' into feat/tour
- *(3016)* 企业信息地址回显错误
- *(3027)* 权益票审核失败后可禁用
- 权益新增回填至下拉列表
- *(3040)* 游客服务项目列表管理单位显示问题
- Merge remote-tracking branch 'origin/test' into feat/tour
- 增加console
- 权益卡禁用后库存详情页下拉框回显问题
- 修改价格展示
- *(3034)* 调整字数限制
- 首页图表数据前端写死
- 开启点位图片多图片上传
- 修复图表x轴
- ProModal统一规范
- 景区信息界面统一
- 权益卡规范统一
- *(3151)* 自助售票宣传信息展示错误
- Update copyright format in Footer component
- *(3151)* 修复bug
- *(3160)* 修复检票点名称提示错误
- 统一权益卡票务管理规范
- 全局参数
- 参数添加进请求头
- 修复参数错误
- 身份证过滤字符异常
- Add UnAccessible component to handle 403 page
- 权益卡UI统一补充
- *(3196)* 出票类型筛选根据票种调整
- 审核中的权益票，限制编辑修改内容
- 权益卡模块操作日志完成
- 统一样式
- 操作日志初步完成
- 购票人名称改为联系人
- 标签切回刷新企业认证状态
- *(3247)* 操作日志额外路由
- 登录问题
- 撤销首页假数据
- 修复景区id来源错误
- 权益卡审核状态
- 慧景云编制操作日志
- 修复modal
- Merge branch 'dev-0328' of git.shukeyun.com:scenic/backend-v2 into dev-0328
- Commented out code for '门票打印模板' in GoodsForm.tsx
- 用户账号限制修改
- 兼容生产图片
- 操作日志编辑
- Fix ticket template configuration and add delete confirmation
- Update IMG_HOST2 URL in config.canary.ts
- Update image source URL in TicketTemplateConfig.tsx
- Canary 用生产图片地址
- 权益卡使用记录参数修改
- 修改config配置
- 操作日志补充
- 发行服务费
- 出票规则仅窗口售票限制去除
- 是否窗口售票
- 工单操作日志记录
- 修改有效时长入园时间限制
- 操作日志文案修改
- 修复景区娱乐项目编辑报错
- 添加区块链数字资产字段
- 添加弹窗
- 报表导出（门票销售、门票核销）
- 报表导出 - 添加操作 dataIndex
- 修复按钮样式异常
- 优化状态栏布局
- 更新 useCount 字段
- Pull
- Update TicketTemplateConfig.tsx and CommodityDetails.tsx
- Merge branch 'dev-0328' into test
- Fix issue with setting templateId to null in GoodsForm.tsx
- Update CommodityDetails.tsx to handle rendering based on entity properties
- Refactor system settings page to comment out TicketTemplateConfig component
- Push
- 新增商品-门票模板选择框不做隐藏
- 数字资产、区块链库存
- 上链景区权限
- 数字资产默认配置
- 修复区块链弹窗判断字段错误
- 库存批次号
- 修复区块链弹窗跳转错误
- 修复数字资产回显异常
- 库存批次号筛选
- 库存筛选
- 库存时间筛选
- 核销哈希
- Merge branch 'debug/master' into test
- 非区块链景区隐藏数字资产字段
- Merge branch 'feat/qkl' into test
- 溯源记录
- Fix style
- 修改智旅链浏览器地址变量
- 库存创建模块添加交易所标识跟筛选
- 退票信息排版优化
- 商品详情字段调整
- 商品详情退票规则
- 价格信息
- Merge branches 'test' and 'test' of git.shukeyun.com:scenic/backend-v2 into test
- 替换区块链地址
- 账号长度限制
- 修复密码回填
- 景区也一并将空值显示改为 -- https://git.shukeyun.com/scenic/workflow/-/issues/3956
- 库存二级页面
- 经营状况
- 区块链库存列表、审核状态
- 库存操作权限、审批理由
- 审批原因
- 金额格式
- 更新 ProModal 组件（支持模块受控）、经销价格、分时预约模块受控
- 携带编辑信息
- 审核状态
- 有效库存、有效时间校验
- 认证入口、经销价格受控拦截
- 更新交易所认证地址
- 门票核销页面优化
- 票号、核销总次数
- 核销时间筛选默认值、核销次数定位
- 核销提示优化
- Merge branch 'canary' of git.shukeyun.com:scenic/backend-v2 into canary
- 分页异常
- 点位管理优化
- 排查非区块链景区非交易所库存校验
- 更换退票接口
- Merge branch 'feat/aaa' into test
- 修复退票接口请求方式
- 数字资产只有新增才能修改
- 慧景云全局优化
- 修改目录问题 https://git.shukeyun.com/new-paradigm/workflow/-/issues/252
- 修复 https://git.shukeyun.com/scenic/workflow/-/issues/4126
- 修复 https://git.shukeyun.com/scenic/workflow/-/issues/4127
- Pull test
- 景区优化问题
- 铸造记录
- 申请交易所异常提示
- 门票核销企业下拉
- 购买有效时间
- 隐藏官网配置
- 官网配置
- 用户审批时间筛选
- 库存发布校验
- Https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/616
- Https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/598
- 解决溯源按钮不显示问题 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/614
- 延时加到1秒 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/598
- 编辑添加batchId https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/613
- 解决带回价格的问题 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/612
- 修复 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/635
- 更改node版本
- 添加 git hook
- 修复门票
- Merge branch 'dev-0328' into dev
- 库存最大限制
- 修复退票bug
- 区块链跳转地址更改
- 修复权益卡编辑
- 修复公司名字
- Merge remote-tracking branch 'origin/feat-upgrade' into feat/iteration-8
- 修改备案信息
- Merge branch 'feat/yp' into dev
- 库存日历组件样式
- 分时商品
- 库存组件
- 门票设置、库存创建列表字段，上链库存，更新 createRoot
- 总库存赋值
- 修复区块链弹窗样式
- 跳转地址修复
- 修复 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/713
- 增加已认证的判定 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/713
- 修复【首页】商品退订统计-按日-时间控件无法选择指定的某一天查询的问题
- 调整企业下拉宽最小宽度
- Merge branch 'feat/iteration-8' of git.shukeyun.com:scenic/backend-v2 into feat/iteration-8
- *(900)* 修复对话框样式
- 调整有效期选择样式 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/744
- 添加canary启动命令
- 修复第九次迭代冲突
- 更新全局框架样式、移除剩余库存信息
- 全局样式、库存信息回显、权益卡购买时间
- 景区数据大屏
- 修复组件数据回显问题
- 企业信息 --- 查看编辑与慧旅云同步
- Merge branch 'feat/iteration-8' into feat/test-publish
- 修复noFound页报错问题
- 修复企业管理--编辑时，地址报错的问题
- Merge branch 'feat/iteration-8' into canary
- Merge branch 'canary' into test
- Merge branch 'feat/ai' into test
- 修复企业注册不选择企业报错的问题
- 库存区间筛选组件
- 修复库存详情经销价格异常
- Merge branch 'dev' of git.shukeyun.com:scenic/backend-v2 into dev
- 修复企业注册的必填逻辑
- 分时库存
- Merge branch 'feat/iteration-8' into feat/publish_20240920
- Merge branch 'test' into feat/publish_20240920
- 修复企业注册编辑传参的问题、修复用户管理编辑传参问题
- 核销核销跳转详情
- 修复EditPop弹窗form数据设置问题
- Merge branch 'feat/hotfix_0930' into canary
- Merge branch 'canary' into feat/iteration-11
- 线路管理点位类型切换
- 屏蔽英文版文案
- 修复EditPop组件问题
- 修复交易所库存发布审核不通过，具体原因回显问题
- 修复库存创建---经营状况信息初始化不展示的问题
- 修复管理商品跳转显示问题
- 修复Editpop组件初始化值的问题
- 修复【权益卡权益】查看所有特权列表未展示分时时间的问题
- 优化登录失效页面提示重复的问题
- 修复默认logo的展示问题
- 组织架构新增 appId 参数
- 库存创建 -- 非区块链景区不展示经营状况
- 修复库存相关的修改限制问题
- 修复formdata上传的问题
- 修复文件上传响应被拦截的问题
- 修复帮助中心-文章管理查询关键字的展示问题
- AI 智能批量生成开发
- 库存创建 -- 编辑区块链库存时，不允许修改开始时间
- 新增音频生成进度展示
- 点位介绍为空时，拦截提示
- 放开置灰
- 库存创建 -- 编辑 -- 有效时间改成俩个时间框，解决禁用状态需要修改时间的问题
- CustomErrorBoundary默认采用antd的ErrorBoundary
- 修复操作日志的查询问题
- 优化音频轮训接口卸载
- 解决点位管理-语音讲解第一次点击会重置所有的勾选
- 修复编辑页面异常
- 解决激活点位介绍的bug
- 修复库存产品名称异常
- 修复权益票审核详情、驳回弹窗样式问题
- 更新库存预警接口
- 销售明细报表导出、组织架构操作项
- 检票点回显异常
- 官网导航--优化自定义链接
- 添加景区官网设计器环境变量
- 替换景区官网设计器跳转路径
- 官网 -- 活动页面联调
- 修改链接
- Merge branch 'feat/iteration12' into feat/test-publish
- 修复官网预览地址
- 修复
- 修复跳转页面的问题
- 修复ant-descriptions-item-content换行的问题
- 修复官网导航的一些问题
- 修复样式问题
- 处理官网导航列表展示收起的问题
- 修复首页库存预警跳转之后展示数据不对的问题
- 修改官网导航枚举
- 解决CustomErrorBoundary被拦截错误渲染的问题
- 修复onDropdownVisibleChange请求的数据没有init的问题
- 修改
- 交易所系统链接
- 隐藏交易所审核
- 修复用户未登录时，无法重定向到邀请页面的问题
- 优化重定向逻辑
- 库存创建---隐藏不必要的代码
- 库存校验，库存去掉原始校验
- 图表兼容性处理
- 修复企业注册---关联并更新弹窗的层级问题
- 库存设置图表终极兼容
- 修复检票规则不能修改的问题
- 修复---库存设置编辑的时候的校验问题
- 优化看看
- 优
- 这把成
- 改成tyarn
- 还原
- 修复库存创建，自动获取总库存的时候，校验错误的问题
- ErrorBoundary兼容css等各类问题
- 门票设置--商品，增加isDigit逻辑
- 修复库存创建 --- 编辑是，库存百分比未回显的问题
- 修改接口

### 💼 Other

- 增加一些less文件
- 临时解决根路由问题
- 编辑技术服务费率交互改变
- 附件信息UI调整
- 退票路由文案调整
- 票商品tab栏调整
- 布局优化
- 规范计量单位字段，整理默认排序

### 🚜 Refactor

- Update voiceType enum values for better readability

### 📚 Documentation

- *(模块)* 更新了商品管理
- *(模块)* 更新dev
- *(模块)* 更新
- Readme
- 更改文档
- Package添加接入生产命令
- 更新文档
- 文档

### ⚙️ Miscellaneous Tasks

- 移除无用代码和注释
- 更换大数据接口
- 更换大数据接口 merge
- Merge branch 'feat/six' into test

<!-- generated by git-cliff -->
