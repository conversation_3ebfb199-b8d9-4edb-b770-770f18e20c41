/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-04-10 09:51:35
 * @LastEditTime: 2023-04-12 10:04:30
 * @LastEditors: z<PERSON><PERSON><PERSON>i
 */

import DetailsPop from "@/common/components/DetailsPop";
import { tableConfig } from "@/common/utils/config";
import { orderTicketTypeEnum, orderTypeEnum, payTypeEnum, saleChannelEnum } from "@/common/utils/enum";
import { modelWidth } from "@/common/utils/gConfig";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import DataMask from "@/components/DataMask";
import type { ProDescriptionsGroup } from "@/components/ModalDescriptions";
import useMask from "@/hooks/useMask";
import type { ModalState } from "@/hooks/useModal";
import { getOrderInfo } from "@/services/api/travelCard";
import type { ProColumns } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import { uniqueId } from "lodash";
import type { FC } from "react";
import { useEffect } from "react";
import { useRequest } from "@umijs/max";

type SubOrderDetailProps = ModalState & {
  orderId?: string;
};

/**
 * @description: 订单详情
 */
const SubOrderDetail: FC<SubOrderDetailProps> = ({ visible, setVisible, orderId }) => {
  // 订单详情信息
  const getOrderInfoReq = useRequest(getOrderInfo, {
    manual: true,
    onSuccess(data, params) {
      addOperationLogRequest({
        action: "info",
        content: `查看【${orderId}】权益卡订单详情`
      });
    }
  });

  const [handleDetailsMaskChange, maskDetailsDataFn] = useMask();
  const rightsCardColumns: ProColumns<API.OrderTravelCardItem>[] = [
    {
      title: "权益卡号",
      dataIndex: "cardId",
      width: 150,
      copyable: true,
      ellipsis: true
    },
    {
      title: "旅游场景名称",
      dataIndex: "scenicName"
    },
    {
      title: "权益卡名称",
      dataIndex: "productName"
    },

    {
      title: "购卡人姓名",
      dataIndex: "identityName",
      render: text => maskDetailsDataFn(text)
    },
    {
      title: "购卡人身份证",
      dataIndex: "identity",
      render: text => maskDetailsDataFn(text)
    },
    {
      title: "金额（元）",
      dataIndex: "productPrice",
      align: "right",
      render: (dom, record) => (record.productPrice * 1).toFixed(2)
    },
    {
      title: "佣金（元）",
      dataIndex: "actualAmount",
      align: "right",
      render: (dom, record) => (record.actualAmount * 1).toFixed(2)
    }
  ];

  const ticketHolderColumns: ProColumns<API.OrderInfo>[] = [
    {
      title: "联系人",
      dataIndex: "pilotName"
    },
    {
      title: "联系人手机号",
      dataIndex: "pilotPhone",
      render: text => maskDetailsDataFn(text)
    },
    {
      title: "联系人身份证",
      dataIndex: "pilotIdentity",
      render: text => maskDetailsDataFn(text)
    }
  ];

  const orderInfoColumns: ProDescriptionsGroup<API.OrderInfoType>[] = [
    {
      title: "基本信息",
      columns: [
        {
          title: "子订单号",
          dataIndex: ["order", "orderId"]
        },
        {
          title: "结算单号",
          dataIndex: ["order", "tradeNo"]
        },
        {
          title: "订单类型",
          dataIndex: ["order", "orderType"],
          valueEnum: orderTicketTypeEnum
        },
        {
          title: "订单状态",
          dataIndex: ["order", "orderStatus"],
          valueEnum: orderTypeEnum
        },
        {
          title: "支付方式",
          dataIndex: ["order", "payType"],
          valueEnum: payTypeEnum
        },
        {
          title: "支付金额（元）",
          dataIndex: ["order", "payAmount"],
          render: (dom, record) => (dom ? (dom * 1).toFixed(2) : "-")
        },
        {
          title: "下单时间",
          dataIndex: ["order", "createTime"],
          valueType: "dateTime"
        },
        {
          title: "登录账号",
          dataIndex: ["order", "username"]
        },
        {
          title: "购票终端",
          dataIndex: ["order", "sourceType"],
          valueEnum: saleChannelEnum
        },
        {
          title: "佣金（元）",
          dataIndex: ["order", "commissionAmount"],
          render: (dom, record) => (dom * 1).toFixed(2)
        },
        {
          title: "出卡时间",
          dataIndex: ["order", "payTime"]
        }
      ]
    },
    {
      title: "权益卡信息",
      className: "no-bgColor",
      columns: [
        {
          span: 2,
          render: (_, record) => {
            return (
              <ProTable
                {...tableConfig}
                style={{ padding: 0, margin: 0, width: "100%" }}
                rowKey={uniqueId()}
                search={false}
                options={false}
                pagination={false}
                dataSource={record.orderTravelCard}
                columns={rightsCardColumns}
                bordered
              />
            );
          }
        }
      ]
    },
    {
      title: "领票人信息",
      className: "no-bgColor",
      columns: [
        {
          span: 2,
          render: (_, record) => {
            return (
              <ProTable
                style={{ padding: 0, margin: 0, width: "100%" }}
                rowKey={uniqueId()}
                search={false}
                options={false}
                pagination={false}
                dataSource={[record.order]}
                columns={ticketHolderColumns}
                bordered
              />
            );
          }
        }
      ]
    }
  ];

  useEffect(() => {
    if (visible && orderId) {
      getOrderInfoReq.run({
        orderId
      });
    }
  }, [orderId, visible]);

  return (
    <DetailsPop
      title={
        <>
          <span>订单详情</span>
          <DataMask onDataMaskChange={handleDetailsMaskChange} logContent="查看【权益卡订单详情】用户隐私信息" />
        </>
      }
      width={modelWidth.lg}
      visible={visible}
      setVisible={setVisible}
      columnsInitial={orderInfoColumns}
      isLoading={getOrderInfoReq.loading}
      dataSource={getOrderInfoReq.data}
    />
  );
};

export default SubOrderDetail;
