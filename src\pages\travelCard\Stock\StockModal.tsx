/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-04-22 15:38:32
 * @LastEditTime: 2024-01-05 16:39:03
 * @LastEditors: zhangfeng<PERSON>i
 */
import DetailsPop from "@/common/components/DetailsPop";
import EditPop from "@/common/components/EditPop";
import { GuideStepStatus } from "@/common/utils/enum";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import { jumpPage, removeStateFromUrl } from "@/common/utils/tool";
import { useGuide } from "@/hooks/useGuide";
import type { ModalState } from "@/hooks/useModal";
import { getTravelCardDownList } from "@/services/api/travelCard";
import { addStock, getStockInfo, updateTravelCardStock } from "@/services/api/travelCardStock";
import type { ProFormColumnsType } from "@ant-design/pro-form";
import type { ActionType } from "@ant-design/pro-table";
import { message } from "antd";
import type { FC } from "react";
import { useEffect, useMemo } from "react";
import { useModel, useRequest } from "@umijs/max";

type StockModalProps = ModalState & {
  stockItem?: API.StockListItem;
  actionRef?: React.MutableRefObject<ActionType | undefined>;
};

const StockModal: FC<StockModalProps> = ({ visible, type, stockItem, setTypeWithVisible, actionRef, setVisible }) => {
  const { initialState } = useModel("@@initialState");
  const { updateGuideInfo } = useGuide();

  const { scenicId, scenicName } = initialState!.scenicInfo!;
  const { companyIds, currentCompanyInfo } = initialState!;

  const travelCardDownListReq = useRequest(getTravelCardDownList, {
    manual: true
  });

  const [downListMap, operatorMap] = useMemo(() => {
    const downList: Record<string, string> = {};
    const operator: Record<string, string> = {};
    travelCardDownListReq.data?.forEach(item => {
      downList[item.travelCardId] = item.travelCardName;
      operator[item.travelCardId] = item.operatorId;
    });
    return [downList, operator];
  }, [travelCardDownListReq.data]);

  // 权益卡库存详情请求
  const stockInfoReq = useRequest(getStockInfo, {
    manual: true,
    onSuccess: (data, params) => {
      addOperationLogRequest({
        action: "info",
        content: `查看【${data.ticketName}】库存详情`
      });
    }
  });

  const editColumns: ProFormColumnsType<API.StockListItem>[] = [
    {
      title: "基本信息",
      valueType: "group",
      columns: [
        {
          title: "所属旅游场景",
          width: "md",
          dataIndex: "scenicId",
          valueType: "select",
          valueEnum: { [scenicId]: scenicName },
          initialValue: scenicId,
          fieldProps: { disabled: true }
        },
        {
          title: "总库存量",
          width: "md",
          valueType: "digit",
          dataIndex: "totalStock",
          formItemProps: { rules: [{ required: true }] },
          fieldProps: {
            min: 1,
            max: 1000000,
            disabled: type === "update"
          }
        },
        {
          title: (
            <div className="flex justify-content-between w-100">
              权益卡名称
              <a
                style={{ marginLeft: "8px" }}
                onClick={() => {
                  jumpPage.push("/travel-card/setting");
                }}
              >
                新增
              </a>
            </div>
          ),
          width: "md",
          dataIndex: "ticketId",
          valueType: "select",
          // valueEnum: downListMap,
          fieldProps: {
            disabled: type === "update",
            showSearch: true,
            allowClear: false,
            options: travelCardDownListReq.data?.map(({ travelCardId, isEnable, travelCardName }) => ({
              label: travelCardName,
              value: travelCardId,
              disabled: isEnable === 0
            }))
          },
          formItemProps: { rules: [{ required: true, message: "请输入权益卡名称" }] }
        },
        {
          title: "购买有效时间",
          width: "md",
          dataIndex: "purchaseTime",
          valueType: "dateRange",
          fieldProps: { disabled: type === "update" },
          transform: value => ({
            purchaseBeginTime: value[0],
            purchaseEndTime: value[1]
          }),
          formItemProps: { rules: [{ required: true }] }
        }
      ]
    },

    {
      title: "说明信息",
      valueType: "group",
      columns: [
        {
          colProps: { span: 24 },
          title: "备注",
          dataIndex: "remark",
          valueType: "textarea",
          fieldProps: {
            showCount: true,
            maxLength: 1000
          }
        }
      ]
    }
  ];
  const detailColumns: ProFormColumnsType<API.StockListItem>[] = [
    {
      title: "基本信息",
      columns: [
        {
          title: "所属旅游场景",
          dataIndex: "scenicName",
          render: () => scenicName
        },
        {
          title: "总库存量",
          valueType: "digit",
          dataIndex: "totalStock"
        },

        {
          title: "权益卡名称",
          dataIndex: "ticketId",
          renderText(text, record, index, action) {
            return downListMap[text] || "-";
          }
        },
        {
          title: "购买有效时间",
          key: "validTime",
          valueType: "dateRange",
          render: (_, record) => {
            return `${record.purchaseBeginTime} 至 ${record.purchaseEndTime}`;
          }
        }
      ]
    },

    {
      title: "说明信息",
      valueType: "group",
      columns: [
        {
          title: "备注",
          dataIndex: "remark",
          valueType: "textarea",
          fieldProps: {
            showCount: true,
            maxLength: 1000
          }
        }
      ]
    }
  ];

  const onFinish = async (params: API.AddTravelCardStockParams) => {
    // try {
    if (type === "add") {
      await addStock({
        travelCard: {
          ...params,
          ticketType: 20,
          operatorId: operatorMap[params.ticketId],
          ticketName: downListMap[params.ticketId]
        }
      });
      // 更新引导
      updateGuideInfo({ tabIndex: 1, status: GuideStepStatus.step1_2 });
      history.pushState(null, null, removeStateFromUrl("type"));
      addOperationLogRequest({
        action: "add",
        content: `新增【${downListMap[params.ticketId]}】库存`
      });

      message.success("新增成功");
    } else {
      await updateTravelCardStock({
        ...stockInfoReq.data,
        ...params,
        type: 7,
        operatorId: operatorMap[params.ticketId],
        ticketName: downListMap[params.ticketId],
        remark: params.remark || " "
      });
      message.success("编辑成功");
    }

    // 关闭弹窗并刷新列表
    setVisible(false);
    actionRef?.current?.reload();
    // } catch (error) {
    //   console.log(error);
    // }
  };

  // 权益卡详情请求触发
  useEffect(() => {
    if (visible && type !== "add" && stockItem) {
      stockInfoReq.run({
        id: stockItem.id,
        productType: 1
      });
    }
  }, [type, stockItem?.id, visible]);

  useEffect(() => {
    if (visible)
      travelCardDownListReq.run({
        scenicId,
        operatorIds: [currentCompanyInfo.coId]
      });
  }, [scenicId, visible]);

  return type === "info" ? (
    <DetailsPop
      limit={{
        openClose: "canTravelCardStorageManage_openClose",
        delete: "canTravelCardStorageManage_delete",
        edit: "canTravelCardStorageManage_edit"
      }}
      title="库存详情"
      visible={visible}
      isLoading={stockInfoReq.loading}
      setVisible={setVisible}
      columnsInitial={detailColumns}
      dataSource={stockInfoReq.data || {}}
      // onUpdate={() => {
      //   setTypeWithVisible('update');
      // }}
    />
  ) : (
    <EditPop
      title="库存"
      visible={visible}
      setVisible={v => {
        setVisible(v);
        if (!v) {
          history.pushState(null, null, removeStateFromUrl("type"));
        }
      }}
      columns={editColumns}
      dataSource={
        (type === "update" && {
          ...stockInfoReq.data,
          purchaseTime: [stockInfoReq.data?.purchaseBeginTime, stockInfoReq.data?.purchaseEndTime]
        }) ||
        {}
      }
      onFinish={onFinish}
    />
  );
};

export default StockModal;
