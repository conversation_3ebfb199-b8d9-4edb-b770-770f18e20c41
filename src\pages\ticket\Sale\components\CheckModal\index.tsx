import { addOperationLogRequest } from "@/common/utils/operationLog";
import type { ModalState } from "@/hooks/useModal";
import type { CheckedMultipleTicketParams } from "@/services/api/ticket";
import { checkedMultipleTicket, getAvailableCheckTicket } from "@/services/api/ticket";
import type { ActionType } from "@ant-design/pro-table";
import { Modal, Skeleton, Space, message } from "antd";
import { isEmpty } from "lodash";
import type { FC } from "react";
import { useEffect } from "react";
import { useModel, useRequest } from "@umijs/max";
import { useImmer } from "use-immer";
import type { TicketDataItem } from "./RealNameTable";
import RealNameTable from "./RealNameTable";

export type AvailableCheckTicketResItem = Ticket.AvailableCheckItem & {
  date: string;
};

interface TicketModalProps {
  modalState: ModalState;
  selectedRows: Ticket.SaleTicketItem[];
  actionRef: React.MutableRefObject<ActionType | undefined>;
  onFinish?: (rows: CheckedMultipleTicketParams["data"]) => void;
}

const TicketModal: FC<TicketModalProps> = ({
  modalState: { visible, type, setVisible },
  selectedRows = [],
  actionRef,
  onFinish
}) => {
  const { userId = "", username, phone, nickname } = useModel("@@initialState").initialState?.userInfo || {};

  // 批量查询票的信息
  const { data, run, loading } = useRequest(getAvailableCheckTicket, {
    manual: true,
    initialData: []
  });

  // 核销请求
  const checkedMultipleTicketReq = useRequest(checkedMultipleTicket, {
    manual: true,
    onSuccess(data, params) {
      message.success("核销成功");

      addOperationLogRequest({
        action: "edit",
        content: `编辑【${params[0].data.map(i => i.ticketNumber).join(",")}】批量核销`
      });
      if (onFinish) {
        onFinish(params[0].data);
      }
      setVisible(false);
      actionRef.current?.reload();
      if (actionRef.current?.clearSelected) {
        actionRef.current?.clearSelected();
      }
    }
  });

  // 选中的数据
  const [ticketData, setTicketData] = useImmer<Record<string, TicketDataItem[]>>({});

  // 批量核销
  const onChecked = () => {
    const result = [];
    // 处理数据
    for (const key in ticketData) {
      const tickets = ticketData[key];
      if (!isEmpty(tickets)) {
        const data = tickets.map(({ idCardNumber, useDate }) => ({
          checkCount: 1,
          idCardNumber: idCardNumber.includes("id") ? "" : idCardNumber,
          useDate
        }));
        result.push({
          data,
          ticketNumber: key
        });
      }
    }

    if (isEmpty(result)) {
      message.warning("至少选择一条数据");
    } else {
      checkedMultipleTicketReq.run({
        data: result,
        userId,
        username: username || phone || nickname || ""
      });
    }
  };

  useEffect(() => {
    if (visible && !isEmpty(selectedRows)) {
      run(selectedRows.map(i => i.id));
    }
    if (!visible) {
      setTicketData({});
    }
  }, [visible]);

  return (
    <Modal
      title={"批量核销"}
      width={900}
      visible={visible}
      onCancel={() => {
        setVisible(false);
      }}
      onOk={onChecked}
      okButtonProps={{
        loading: checkedMultipleTicketReq.loading
      }}
      destroyOnClose
    >
      <Skeleton loading={loading}>
        <Space direction="vertical" style={{ width: "100%" }} size="large">
          {(data ?? []).map((item, index) => {
            const currentTicket = selectedRows.find(i => i.id === item.ticketNumber) as Ticket.SaleTicketItem;
            return (
              <RealNameTable
                key={currentTicket?.id}
                checkInfo={item}
                ticket={currentTicket}
                setTicketData={setTicketData}
              />
            );
          })}
        </Space>
      </Skeleton>
    </Modal>
  );
};

export default TicketModal;
