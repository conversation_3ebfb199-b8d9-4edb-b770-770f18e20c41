/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-04-24 16:59:45
 * @LastEditTime: 2023-07-07 17:43:54
 * @LastEditors: zhang<PERSON><PERSON>i
 */

import { addOperationLogRequest } from "@/common/utils/operationLog";
import type { ModalState } from "@/hooks/useModal";
import { getRenewalLogList } from "@/services/api/rightsManage";
import type { ActionType, ProColumns } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import { Modal, Space } from "antd";
import dayjs from "dayjs";
import type { FC } from "react";
import { useRef } from "react";

type LogModalProps = ModalState & {
  privilegeUserItem?: API.PrivilegeUserListItem;
  rightsItem: API.RightsListItem;
  actionRef?: React.MutableRefObject<ActionType | undefined>;
};

/**
 * @description: 续费日志
 */
const LogModal: FC<LogModalProps> = ({ visible, setVisible, privilegeUserItem, rightsItem }) => {
  const actionRef = useRef<ActionType>();

  const columns: ProColumns<API.RenewalLogListItem>[] = [
    {
      title: "id",
      dataIndex: "id",
      hideInTable: true,
      search: false
    },
    {
      title: "更新时间",
      dataIndex: "modifyTime",
      search: false,
      renderText: text => (text ? dayjs(text).format("YYYY-MM-DD HH:mm:ss") : "-")
    },
    {
      title: "开始日期",
      dataIndex: "validityBeginTime",
      search: false
    },
    {
      title: "结束日期",
      dataIndex: "validityEndTime",
      search: false
    }
  ];

  return (
    <Modal
      width={1200}
      title="续费日志"
      visible={visible}
      footer={false}
      onCancel={() => {
        setVisible(false);
      }}
    >
      {privilegeUserItem && (
        <ProTable<API.RenewalLogListItem, API.RenewalLogListParams>
          actionRef={actionRef}
          rowKey="id"
          headerTitle={
            <Space size="large">
              <span>权益 ID：{rightsItem.id}</span>
              <span>权益名称：{rightsItem.rightsName || "-"}</span>
            </Space>
          }
          pagination={{ defaultPageSize: 10 }}
          options={{ setting: false, density: false }}
          search={false}
          params={{ id: privilegeUserItem.travelCardGoodsTransactionId }}
          request={async params => {
            const data = await getRenewalLogList(params);

            addOperationLogRequest({
              action: "info",
              content: `查看【${rightsItem.rightsName}】续费日志`
            });
            return data;
          }}
          columns={columns}
        />
      )}
    </Modal>
  );
};

export default LogModal;
