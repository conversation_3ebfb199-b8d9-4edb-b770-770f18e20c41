/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-09-13 09:43:40
 * @LastEditTime: 2023-10-17 14:36:48
 * @LastEditors: zhangfengfei
 */
import { ticketStatusEnum } from '@/common/utils/enum';
import { Space, Table } from 'antd';
import type { ColumnType } from 'antd/lib/table';
import { isEmpty } from 'lodash';
import type { FC } from 'react';
import { useEffect, useState } from 'react';
import type { Updater } from 'use-immer';

interface RealNameTableProps {
  ticket: Ticket.MultipleTicketInfoType & {
    checkedNum: number;
    playerNum: number;
  };
  setTicketData: Updater<Record<string, Ticket.RealNameListItem[]>>;
}

const RealNameTable: FC<RealNameTableProps> = ({
  ticket: { id, status, checkedNum, playerNum, realNameList, checkRetreatInfo, usedCount },
  setTicketData,
}) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);

  const columns: ColumnType<Ticket.RealNameListItem>[] = [
    {
      title: '排序',
      dataIndex: 'id',
      width: '20%',
      render: (value, record, index) => `游客${index + 1}`,
    },
    {
      title: '姓名',
      dataIndex: 'idCardName',
      width: '20%',
      render: (value) => value || '-',
    },
    {
      title: '身份证',
      dataIndex: 'idCardNumber',
      width: '40%',
      render: (value: string) => {
        if (value.includes('id')) {
          return '-';
        }
        return value;
      },
    },
    {
      title: '核销次数',
      dataIndex: 'usedCount',
      width: '20%',
    },
  ];

  // 未检票的身份证数据
  const unCheckedData = realNameList.filter((i) => i.isCheck === 0);

  useEffect(() => {
    // 有按票检过的情况
    if (unCheckedData.length > playerNum - checkedNum) {
      // 选前 playerNum - checkedNum 个
      const selectedKeys = unCheckedData
        .map((i) => i.idCardNumber)
        .slice(0, playerNum - checkedNum);

      setSelectedRowKeys(selectedKeys);
    } else {
      // 全按身份证检 默认可选的全选上
      const selectedKeys = unCheckedData.map((i) => i.idCardNumber);
      setSelectedRowKeys(selectedKeys);
    }
  }, []);

  useEffect(() => {
    const selectedRows = realNameList.filter((i) => selectedRowKeys.includes(i.idCardNumber));
    setTicketData((draft) => {
      draft[id] = selectedRows;
    });
  }, [selectedRowKeys]);

  return (
    <>
      <Space size={48} style={{ fontWeight: 'bold', marginBottom: 8 }}>
        <span>票号：{id}</span>
        <span>门票状态：{ticketStatusEnum[status]}</span>
        <span>人数：{playerNum}</span>
        <span>核销总次数：{usedCount}</span>
      </Space>
      {!isEmpty(realNameList) && (
        <Table<Ticket.RealNameListItem>
          size="middle"
          rowKey="idCardNumber"
          columns={columns}
          dataSource={realNameList}
          pagination={false}
          rowSelection={{
            type: 'checkbox',
            selectedRowKeys,
            onChange: (rowKeys: string[]) => {
              setSelectedRowKeys(rowKeys);
            },
            getCheckboxProps(record) {
              return {
                // add退票  update核销  isRetreat 0 不可退   isCheck 1 已检票
                disabled: record.isCheck === 1,
              };
            },
          }}
        />
      )}
    </>
  );
};

export default RealNameTable;
