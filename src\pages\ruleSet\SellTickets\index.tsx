import DetailsPop from "@/common/components/DetailsPop";
import EditPop from "@/common/components/EditPop";
import { tableConfig } from "@/common/utils/config";
import { IssueTypeEnum, realEnum, RuleTypeEnum, whetherEnum } from "@/common/utils/enum";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import { getAllPath, getUniqueId } from "@/common/utils/tool";
import { apiScenicConfig } from "@/services/api/settings";
import {
  AddTicketLssue,
  apiApproveConfigure,
  apiApproveConfInfo,
  delTicketLssue,
  downListApi,
  getIssueTicketRulePageList,
  getTicketLssueStatus,
  ticketLssueXq
} from "@/services/api/ticket";
import { InfoCircleTwoTone, MinusCircleOutlined, PlusCircleOutlined, PlusOutlined } from "@ant-design/icons";
import type { ProFormColumnsType } from "@ant-design/pro-form";
import type { ActionType, ProColumns } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import { Button, Input, message, Modal, Space, Switch } from "antd";
import { trim } from "lodash";
import dayjs from "dayjs";
import type { Dayjs } from "dayjs";
import React, { useEffect, useRef, useState } from "react";
import { Access, useAccess, useModel, useRequest } from "@umijs/max";

let cardsList: any = [];

const TableList: React.FC = () => {
  const access = useAccess();
  const [timeRangeInitialValue, setTimeRangeInitialValue] = useState<Dayjs[]>([]);
  const inputRef: any = [];
  const [cardsData, setCardsData] = useState(false);
  const CardsList: any = () => {
    return cardsList?.map((item: any, index: any) => (
      <div key={getUniqueId()} style={{ width: "100%", display: "flex", marginBottom: "12px", alignItems: "center" }}>
        <Input.Group compact>
          {item.map((itemInput: any, indexInput: any) => (
            <Input
              ref={e => {
                if (!inputRef[index]) inputRef[index] = [];
                inputRef[index].push(e);
              }}
              maxLength={1}
              style={{ width: "32px", textAlign: "center", padding: 4 }}
              key={getUniqueId()}
              defaultValue={itemInput}
              onInput={(e: any) => {
                cardsList[index][indexInput] = e.target.value;
                // 自动聚焦
                if (indexInput < 17 && e.target.value) {
                  setTimeout(() => {
                    inputRef[index][indexInput + 1].focus({ cursor: "all" });
                  }, 20);
                }
              }}
              onFocus={e => {
                e.target.select();
              }}
            />
          ))}
        </Input.Group>
        <div
          style={{
            flex: 1,
            display: "flex",
            justifyContent: "flex-end",
            alignItems: "center"
          }}
        >
          <PlusCircleOutlined
            style={{ marginLeft: "16px", cursor: "pointer", fontSize: "16px" }}
            onClick={() => {
              cardsList.splice(index + 1, 0, Array(18).fill("*"));
              setCardsData(!cardsData);
            }}
          />
          <MinusCircleOutlined
            style={{
              marginLeft: "16px",
              cursor: cardsList.length > 1 ? "pointer" : "not-allowed",
              fontSize: "16px"
            }}
            onClick={() => {
              if (cardsList.length > 1) {
                cardsList.splice(index, 1);
                setCardsData(!cardsData);
              }
            }}
          />
        </div>
      </div>
    ));
  };
  // 【景区】信息
  const { initialState } = useModel("@@initialState");
  const { scenicId, scenicName }: any = initialState?.scenicInfo;
  // 【表格】数据绑定
  const actionRef = useRef<ActionType>();
  const columns: ProColumns[] = [
    {
      title: "出票规则名称",
      dataIndex: "name",
      search: {
        transform: val => trim(val)
      }
    },
    {
      title: "出票类型",
      dataIndex: "type",
      valueEnum: IssueTypeEnum
    },
    {
      title: "规则类型",
      dataIndex: "ruleType",
      valueType: "select",
      valueEnum: RuleTypeEnum
    },
    {
      title: "实名方式",
      dataIndex: "isRealName",
      search: false,
      valueEnum: realEnum
    },
    {
      title: "检验账户实名认证",
      dataIndex: "realName",
      search: false,
      valueEnum: whetherEnum
    },
    {
      title: "限本人购买",
      dataIndex: "buyOwner",
      valueEnum: {
        0: "否",
        1: "是"
      }
    },
    {
      title: "出票即核销",
      dataIndex: "isCheck",
      search: false,
      valueEnum: {
        0: "否",
        1: "是"
      }
    },
    {
      title: "使用时间",
      dataIndex: "useTime",
      search: false,
      renderText: (dom: any) => `购买 ${dom} 小时后`
    },
    {
      title: "启用状态",
      dataIndex: "isEnable",
      valueType: "select",
      fixed: "right",
      valueEnum: {
        0: "禁用",
        1: "启用"
      },
      renderText: (dom: any, entity: any) => (
        <Switch
          disabled={!access.canSalesRule_openClose}
          checkedChildren="启用"
          unCheckedChildren="禁用"
          checked={!!dom}
          onChange={() => {
            getTicketLssueStatus({
              id: entity.id,
              isEnable: 1 - entity.isEnable,
              scenicId
            })
              .then(() => {
                message.success(dom ? "已禁用" : "已启用");
                addOperationLogRequest({
                  action: "disable",
                  content: `${dom ? "禁用" : "启用"}【${entity.name}】出票规则`
                });
                actionRef.current?.reload();
              })
              .catch(() => {});
          }}
        />
      )
    },
    {
      width: "auto",
      title: "操作",
      valueType: "option",
      fixed: "right",
      render: (_: any, record: any) => (
        <Space size="large">
          <a
            onClick={async () => {
              setLoading(true);
              setDetailsVisible(true);
              try {
                const { data } = await ticketLssueXq({ id: record.id });
                data.way *= 1;
                data.type *= 1;
                data.isRealName *= 1;
                let approveData: any = {};
                if (data.approveId && data.approveId != 0) {
                  const { data: approveData2 } = await apiApproveConfInfo({
                    approveId: data.approveId
                  });
                  approveData = approveData2;
                  approveData.approveType += "";
                }
                setDataSource({ ...data, ...approveData });
                setLoading(false);
                addOperationLogRequest({
                  action: "info",
                  content: `查看【${record.name}】出票规则详情`
                });
              } catch (error) {
                setDetailsVisible(false);
              }
            }}
          >
            查看
          </a>
          <Access accessible={access.canSalesRule_edit && record?.isDigit != "1"}>
            <a
              onClick={async () => {
                const { data } = await ticketLssueXq({ id: record.id });
                data.way *= 1;
                data.type *= 1;
                data.isRealName *= 1;
                let approveData: any = {};
                if (data.approveId && data.approveId != 0) {
                  const { data: approveData2 } = await apiApproveConfInfo({
                    approveId: data.approveId
                  });
                  approveData = approveData2;
                  approveData.approveType += "";
                }
                cardsList = [];
                const sum = approveData?.cardStr?.split(";") || ["******************"];
                sum.forEach((item: any, index: any) => {
                  cardsList[index] = [...item.split(""), ...Array(18 - item.length).fill("*")];
                });
                setDataSource({ ...data, ...approveData });
                setEditVisible(true);
              }}
            >
              编辑
            </a>
          </Access>
          <Access accessible={access.canSalesRule_delete && record?.isDigit != "1"}>
            <a
              style={{ color: "red" }}
              onClick={async () => {
                if (record.isEnable) {
                  Modal.warning({
                    title: "不可删除",
                    content: "请先禁用后删除"
                  });
                  return;
                }
                Modal.confirm({
                  title: "确认删除吗？",
                  icon: <InfoCircleTwoTone />,
                  content: "删除后不可恢复",
                  okText: "确认",
                  cancelText: "取消",
                  onOk: () => {
                    delTicketLssue(record.id)
                      .then(() => {
                        message.success("删除成功");

                        addOperationLogRequest({
                          action: "del",
                          content: `删除【${record.name}】出票规则`
                        });

                        actionRef.current?.reload();
                      })
                      .catch(() => {});
                  }
                });
              }}
            >
              删除
            </a>
          </Access>
        </Space>
      )
    }
  ];
  // 【表单】数据绑定
  const [editVisible, setEditVisible] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<any>({ id: "", isEnable: 0 });
  const editColumns: ProFormColumnsType<any>[] | ProFormColumnsType<any>[][] = [
    {
      title: "基础信息",
      columns: [
        {
          title: "旅游服务名称",
          dataIndex: "scenicId",
          valueType: "select",
          valueEnum: { [scenicId]: scenicName },
          initialValue: scenicId,
          fieldProps: { disabled: true }
        },
        {
          title: "出票规则名称",
          dataIndex: "name",
          formItemProps: { rules: [{ required: true }, { max: 30 }] }
        },
        {
          valueType: "dependency",
          fieldProps: {
            name: ["ruleType"]
          },
          columns: ({ ruleType }: { ruleType: any }) => [
            {
              colProps: { xs: 24, sm: 12 },
              title: "出票类型",
              dataIndex: "type",
              valueType: "select",
              fieldProps: {
                options:
                  ruleType == 3 || ruleType == undefined
                    ? [
                        {
                          value: 0,
                          label: "一票一人"
                        },
                        {
                          value: 1,
                          label: "一票多人"
                        }
                      ]
                    : [
                        {
                          value: 0,
                          label: "一票一人"
                        }
                      ]
              },
              initialValue: 0,
              formItemProps: { rules: [{ required: true }] }
            }
          ]
        },
        // {
        //   title: '出票类型',
        //   dataIndex: 'type',
        //   valueType: 'select',
        //   fieldProps: {
        //     options: [
        //       {
        //         value: 0,
        //         label: '一票一人',
        //       },
        //       {
        //         value: 1,
        //         label: '一票多人',
        //       },
        //     ],
        //   },
        //   initialValue: 0,
        //   formItemProps: { rules: [{ required: true }] },
        // },
        {
          title: "规则类型",
          dataIndex: "ruleType",
          valueType: "select",
          fieldProps: {
            disabled: dataSource?.id,
            options: [
              {
                value: 1,
                label: "权益票规则"
              },
              {
                value: 2,
                label: "权益卡规则"
              },
              {
                value: 3,
                label: "普通票规则"
              }
            ]
          },
          initialValue: 3,
          formItemProps: { rules: [{ required: true }] }
        }
      ]
    },
    {
      title: "时间信息",
      columns: [
        {
          title: "使用时间",
          dataIndex: "useTime",
          valueType: "digit",
          fieldProps: {
            min: 0,
            addonBefore: "购买",
            addonAfter: "小时后"
          },
          formItemProps: { rules: [{ required: true }] }
        },
        {
          title: "当天购票起止时间",
          dataIndex: "time",
          valueType: "timeRange",
          initialValue: timeRangeInitialValue,
          transform: (values: any[]) => {
            return {
              beginTime: values[0],
              endTime: values[1]
            };
          }
        }
      ]
    },
    {
      title: "实名信息",
      columns: [
        {
          valueType: "dependency",
          fieldProps: {
            name: ["ruleType"]
          },
          columns: ({ ruleType }: { ruleType: any }) => [
            {
              colProps: { xs: 24, sm: 12 },
              title: (
                <div>
                  实名方式
                  <a
                    style={{ marginLeft: "8px" }}
                    onClick={() => {
                      window.open(`${getAllPath()}/basic-information/config`, "_blank");
                    }}
                  >
                    设置
                  </a>
                </div>
              ),
              dataIndex: "isRealName",
              valueType: "select",
              initialValue: 0,
              fieldProps: {
                options:
                  ruleType == 2 || ruleType == 1
                    ? [
                        {
                          value: 1,
                          label: "身份证"
                        }
                      ]
                    : [
                        {
                          value: 0,
                          label: "非实名"
                        },
                        {
                          value: 1,
                          label: "身份证"
                        }
                      ]
              }
            }
          ]
        },

        {
          valueType: "dependency",
          fieldProps: {
            name: ["realName", "ruleType"]
          },
          columns: ({ realName, ruleType }: { realName: any; ruleType: any }) => [
            {
              colProps: { xs: 12, sm: 6 },
              title: "校验账户实名认证",
              dataIndex: "realName",
              valueType: "switch",
              fieldProps: {
                disabled: ruleType === 1 || ruleType === 2
              }
            }
          ]
        },
        {
          valueType: "dependency",
          fieldProps: {
            name: ["realName", "isRealName", "ruleType"]
          },
          columns: ({
            realName,
            isRealName,
            ruleType,
            type
          }: {
            realName: any;
            isRealName: any;
            ruleType: any;
            type: any;
          }) => {
            return [
              {
                colProps: { xs: 12, sm: 6 },
                title: "限本人购买",
                dataIndex: "buyOwner",
                valueType: "switch",
                tooltip: "此按钮需要【实名方式】为身份证且开启【校验账户实名认证】方可使用",
                fieldProps: {
                  disabled: !realName || isRealName !== 1 || ruleType === 1 || ruleType === 2 || type == 1
                }
              }
            ];
          }
        },
        {
          valueType: "dependency",
          fieldProps: {
            name: ["ruleType"]
          },
          columns: ({ ruleType }: { ruleType: any }) => [
            {
              colProps: { xs: 24, sm: 12 },
              title: "选择权益",
              dataIndex: "rightsId",
              valueType: "select",
              formItemProps: { rules: [{ required: true }] },
              hideInForm: ruleType !== 1,
              request: downListApi
            }
          ]
        }
      ]
    },
    {
      title: "其他说明",
      columns: [
        {
          valueType: "dependency",
          fieldProps: {
            name: ["ruleType", "type"]
          },
          columns: ({ ruleType, type }: { ruleType: any; type: 0 | 1 }) => [
            {
              colProps: { xs: 12, sm: 6 },
              title: <span style={{ width: "148px" }}>是否出票即核销</span>,
              dataIndex: "isCheck",
              hideInForm: ruleType === 2,
              valueType: "switch"
            },
            {
              colProps: { xs: 12, sm: 6 },
              title: "仅窗口售票",
              valueType: "switch",
              dataIndex: "isWindow",
              hideInForm: ruleType !== 3,
              initialValue: false,
              convertValue: value => value == 1,
              transform: value => {
                return {
                  isWindow: value ? 1 : 2
                };
              }
            },
            {
              colProps: { xs: 24, sm: 12 },
              title: "购买是否需审批",
              dataIndex: "isApprove",
              valueType: "switch",
              hideInForm: ruleType !== 2
            }
          ]
        },

        {
          valueType: "dependency",
          fieldProps: {
            name: ["isApprove"]
          },
          columns: ({ isApprove }: { isApprove: any }) => [
            {
              colProps: { xs: 24, sm: 12 },
              title: "审批条件",
              dataIndex: "approveType",
              valueEnum: { 1: "所有预订都需审批", 2: "按身份证过滤" },
              initialValue: dataSource.approveType,
              hideInForm: !isApprove
            }
          ]
        },
        {
          valueType: "dependency",
          fieldProps: {
            name: ["approveType", "isApprove"]
          },
          columns: ({ approveType, isApprove }: { approveType: any; isApprove: any }) => [
            {
              // width: '100%',
              colProps: { xs: 24, sm: 24 },
              title: "无需审批的身份证号",
              hideInForm: !isApprove || approveType != 2,
              renderFormItem: CardsList,
              tooltip: "仅可以数据数字或符号“*”。自上而下逐一匹配，仅填写数字的位数匹配，填写“*”代表不做匹配校验。"
            }
          ]
        },
        {
          valueType: "dependency",
          fieldProps: {
            name: ["isApprove"]
          },
          columns: ({ isApprove }: { isApprove: any }) => [
            {
              width: "100%",
              colProps: { xs: 24, sm: 24 },
              title: "上传审批内容规范说明",
              dataIndex: "approveContent",
              valueType: "textarea",
              hideInForm: !isApprove,
              fieldProps: {
                showCount: true,
                maxLength: 1000
              },
              formItemProps: {
                rules: [{ required: true }]
              }
            }
          ]
        }
      ]
    },
    {
      title: "说明信息",
      columns: [
        {
          width: "100%",
          colProps: { xs: 24, sm: 24 },
          title: "备注",
          dataIndex: "remark",
          valueType: "textarea",
          fieldProps: {
            showCount: true,
            maxLength: 1000
          }
        }
      ]
    }
  ];
  const getIsRealName = async () => {
    try {
      await apiScenicConfig({ id: scenicId });
    } catch (error) {}
  };
  // 【列表】数据绑定
  const [detailsVisible, setDetailsVisible] = useState<boolean>(false);
  const [isLoading, setLoading] = useState<boolean>(true);
  const downListReq = useRequest(downListApi, {
    manual: true,
    formatResult: res => res
  });
  const columnsInitial: ProFormColumnsType[] = [
    {
      title: "基础信息",
      columns: [
        {
          title: "旅游服务名称",
          dataIndex: "scenicName",
          render: () => scenicName
        },
        {
          title: "出票规则名称",
          dataIndex: "name"
        },
        {
          title: "出票类型",
          dataIndex: "type",
          valueType: "select",
          valueEnum: IssueTypeEnum
        },
        {
          title: "规则类型",
          dataIndex: "ruleType",
          valueEnum: RuleTypeEnum
        }
      ]
    },
    {
      title: "时间信息",
      columns: [
        {
          title: "使用时间",
          dataIndex: "useTime",
          renderText: (dom: any) => `购买 ${dom} 小时后`
        },
        {
          title: "当天购票起止时间",
          dataIndex: "beginTime",
          renderText: (dom: any, entity: any) => entity.beginTime + " - " + entity.endTime
        }
      ]
    },
    {
      title: "实名信息",
      columns: [
        {
          title: "校验账户实名认证",
          dataIndex: "realName",
          valueEnum: whetherEnum
        },
        {
          title: "仅限本人购买",
          dataIndex: "buyOwner",
          valueEnum: whetherEnum
        },
        {
          title: "实名方式",
          dataIndex: "isRealName",
          valueEnum: realEnum
          // render: (dom: any) => ['非实名', '身份证'][dom],
        },
        {
          title: "是否需校验权益",
          dataIndex: "isRights",
          hideInDescriptions: dataSource.ruleType !== 1,
          valueEnum: whetherEnum
        },
        {
          title: "已选权益",
          dataIndex: "rightsId",
          hideInDescriptions: dataSource.ruleType !== 1,
          // valueType: 'select',
          // request: ,
          renderText: (text, record) => {
            if (record.isRights == 0) {
              return "-";
            }

            return (downListReq.data ?? []).find((item: any) => item.value === text)?.label ?? "-";
          }
        },
        {
          title: "是否出票即核销",
          dataIndex: "isCheck",
          valueEnum: whetherEnum
        },
        {
          title: "仅窗口售票",
          dataIndex: "isWindow",
          valueEnum: {
            0: "-",
            1: "是",
            2: "否"
          },
          // hideInDescriptions: dataSource.type === 0,
          convertValue: value => value == 1
        },
        {
          title: "购买是否需审批",
          dataIndex: "isApprove",
          hideInDescriptions: dataSource.ruleType !== 2,
          valueEnum: whetherEnum
        },
        {
          title: "上传审批内容规范说明",
          dataIndex: "approveContent"
        }
      ]
    },
    {
      title: "说明信息",
      columns: [
        {
          title: "备注",
          dataIndex: "remark"
        }
      ]
    }
  ];

  const logList = [...columnsInitial[0].columns, ...columnsInitial[1].columns, ...columnsInitial[2].columns];

  // 展示新增弹窗
  const onAdd = () => {
    cardsList = [Array(18).fill("*")];
    setDataSource({ id: "", isEnable: 0 });
    setEditVisible(true);
  };

  useEffect(() => {
    if (dataSource?.beginTime && dataSource?.beginTime) {
      setTimeRangeInitialValue([dayjs(dataSource?.beginTime, "HH:mm:ss"), dayjs(dataSource?.endTime, "HH:mm:ss")]);
    } else {
      setTimeRangeInitialValue([]);
    }
  }, [dataSource]);

  useEffect(() => {
    getIsRealName();
    downListReq.run();

    const pageOperateType = localStorage.getItem("pageOperateType");
    if (pageOperateType === "add") {
      onAdd();
    }
  }, []);

  return (
    <>
      <ProTable<API.RuleListItem, API.PageParams>
        {...tableConfig}
        rowClassName={(row: any) => (row.isEnable == 1 ? "" : "disableRow")}
        actionRef={actionRef}
        toolBarRender={() => [
          <Access key={getUniqueId()} accessible={access.canSalesRule_insert}>
            <Button
              type="primary"
              key="primary"
              onClick={() => {
                onAdd();
              }}
            >
              <PlusOutlined /> 新增
            </Button>
          </Access>
        ]}
        params={{ scenicId }}
        request={async params => {
          const { data } = await getIssueTicketRulePageList(params);
          return data;
        }}
        columns={columns}
      />

      {/* 新增编辑 */}
      <EditPop
        title="出票规则"
        visible={editVisible}
        setVisible={(v: boolean) => {
          setEditVisible(v);
          localStorage.removeItem("pageOperateType");
        }}
        columns={editColumns}
        dataSource={dataSource}
        onValuesChange={(formRef: any, e: any) => {
          if (e.hasOwnProperty("way")) {
            if (e.way == 2) {
              formRef?.current?.setFieldsValue({ isRealName: 1 });
            }
          }
          if (e.hasOwnProperty("isRealName")) {
            if (e.isRealName == 0 && formRef?.current?.getFieldsValue().way == 2) {
              formRef?.current?.setFieldsValue({ way: "" });
            }
          }
          // 实名方式不为身份证或校验账户实名认证关闭时，仅限本人购买关闭
          if (e.hasOwnProperty("isRealName") || e.hasOwnProperty("realName")) {
            if (e.isRealName !== 1 || e.realName == 0) {
              formRef?.current?.setFieldsValue({ buyOwner: 0 });
            }
          }

          // 规则类型
          if (e.hasOwnProperty("ruleType")) {
            if (e.ruleType == 1) {
              // 规则类型为权益票时，【实名方式】默认为身份证
              formRef?.current?.setFieldsValue({ isRealName: 1 });
              // 【校验账户实名认证】默认开启
              formRef?.current?.setFieldsValue({ realName: 1 });
              // 【仅限本人购买】默认开启
              formRef?.current?.setFieldsValue({ buyOwner: 1 });
            }
            if (e.ruleType == 2) {
              // 规则类型为权益卡时，【实名方式】默认为身份证
              formRef?.current?.setFieldsValue({ isRealName: 1 });
              // 【校验账户实名认证】默认开启
              formRef?.current?.setFieldsValue({ realName: 1 });
              // 【仅限本人购买】默认开启
              formRef?.current?.setFieldsValue({ buyOwner: 1 });
            }
          }
        }}
        // 新增/编辑
        onFinish={async (val: any) => {
          val.buyOwner = val.buyOwner == 1 ? 1 : 0;
          val.isApprove *= 1;
          val.isCheck *= 1;
          val.isRights *= 1;
          // 规则类型为权益票规则时，校验权益默认开启
          if (val.ruleType == 1) {
            val.isRights = 1;
          }
          if (val.isRights == 0) {
            val.rightsId = "0";
          }
          val.realName *= 1;
          val.scenicName = scenicName;
          if (dataSource.id) val.id = dataSource.id;
          const msgType = val.id ? "编辑" : "新增";
          const hide = message.loading("正在" + msgType);
          try {
            // 审批配置
            if (val.isApprove && val.isApprove != 0) {
              const { data } = await apiApproveConfigure({
                approveId: val.approveId,
                approveType: val.approveType,
                cardStr: cardsList.join(";").replaceAll(",", ""),
                flag: 1
              });
              val.approveId = data;
            } else {
              val.approveId = dataSource.approveId;
            }
            await AddTicketLssue({
              ...val,
              remark: val.remark || " ",
              isWindow: val.isWindow || 2
            });

            if (val.id) {
              addOperationLogRequest({
                action: "edit",
                changeConfig: {
                  list: logList,
                  beforeData: dataSource,
                  afterData: val
                },
                content: `编辑【${val.name}】出票规则`
              });
            } else {
              addOperationLogRequest({
                action: "add",
                content: `新增【${val.name}】出票规则`
              });
            }

            message.success(msgType + "成功");
            // 关闭弹窗并刷新列表
            setEditVisible(false);
            actionRef?.current?.reload();
          } catch (error) {}
          hide();
        }}
      />

      {/* 详情 */}
      <DetailsPop
        title="出票规则详情"
        visible={detailsVisible}
        isLoading={isLoading}
        setVisible={setDetailsVisible}
        columnsInitial={columnsInitial}
        dataSource={dataSource}
      />
    </>
  );
};

export default TableList;
