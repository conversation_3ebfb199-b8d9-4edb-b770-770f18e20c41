import { logout, setCookie } from "@/services/api/cas";
import { history } from "@umijs/max";
import { parse } from "querystring";
import { addOperationLogRequest } from "./operationLog";
import { goToLogin, jumpPage } from "./tool";
import { getEnv } from "./getEnv";

const isDev = getEnv().ENV === "dev";

// 登录
export async function login() {
  const { hash } = location;

  const { tk, path, scenicCode } = parse(hash.split("?")[1]);

  //  有 token
  if (tk) {
    // 本地开发 需要转回 localhost
    if (isDev && path) {
      location.href = `${path}/#/${scenicCode}/welcome?tk=${tk}`;
    }

    sessionStorage.removeItem("currentCompanyId");
    sessionStorage.removeItem("userInfo");
    try {
      await setCookie(tk as string);
      // 记录日志
      addOperationLogRequest({
        action: "login",
        content: "登录慧景云系统"
      });
      // 判断是否有需要重定向的路径
      if (localStorage.getItem("redirectUrlHash")?.length) {
        const redirectUrlHash = localStorage.getItem("redirectUrlHash") || "";
        localStorage.removeItem("redirectUrlHash");
        jumpPage.replace(redirectUrlHash);
      } else {
        history.replace(`/${scenicCode}/welcome`);
      }
    } catch (error) {
      console.log(error);
    }
  }
}

/**
 * 退出登录
 */
export async function loginOut() {
  try {
    await logout({
      appId: JSON.parse(sessionStorage.getItem("scenicInfo"))?.appId
    });
    sessionStorage.removeItem("userInfo");
    sessionStorage.removeItem("currentCompanyId");
    goToLogin();
  } catch (error) {
    if (error.data?.code === 30001) {
      //没设置 cookie 跳转登陆页
      goToLogin();
    }
  }
}

// 检测浏览器版本
export const upgradeVersion = () => {
  const userAgent = window.navigator.userAgent;
  const browsers = [
    { name: "Firefox", regex: /Firefox\/(\d+)/, minVersion: 90 },
    { name: "Chrome", regex: /Chrome\/(\d+)/, minVersion: 92 },
    { name: "Safari", regex: /Version\/(\d+).*Safari/, minVersion: 15.4 },
    { name: "Opera", regex: /OPR\/(\d+)/, minVersion: 78 },
    { name: "IE", regex: /MSIE (\d+)/, minVersion: 92 }
  ];

  const browser = browsers.find(({ regex }) => regex.test(userAgent));

  if (browser) {
    const match = userAgent.match(browser.regex);
    if (match && match[1]) {
      const version = parseInt(match[1], 10);
      // 低版本
      if (version < browser.minVersion) {
        history.replace("/upgrade");
      }
    } else {
      // 未知
      history.replace("/upgrade");
    }
  } else {
    // 未知浏览器
    history.replace("/upgrade");
  }
};
