import { getShopGoodsPageList } from '@/services/api/article';
import { articlePage } from '@/services/api/erp';

// 商品详情页
const getGoodsDetailListRequest = async (params: any) => {
  try {
    const { data } = await getShopGoodsPageList(params);
    console.log(data);
    return data || [];
  } catch (error) {
    return [];
  }
};

// 帮助中心文章
const getHelpDetailListRequest = async (params: any) => {
  try {
    const { data } = await articlePage(params);
    return data || [];
  } catch (error) {
    return [];
  }
};

export { getGoodsDetailListRequest, getHelpDetailListRequest };
