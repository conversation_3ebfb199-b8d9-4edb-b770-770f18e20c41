body {
  overflow-x: hidden;
}

.block {
  position: absolute;
  top: 0;
  left: 0;
}

.card {
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-clip: border-box;
  border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
  margin-bottom: 0;
  padding: 0.75rem 1.25rem;
  background-color: rgba(0, 0, 0, 0.03);
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header:first-child {
  border-radius: calc(0.25rem - 1px) calc(0.25rem - 1px) 0 0;
}

.card-body {
  flex: 1 1 auto;
  padding: 1.25rem;
}

.sliderContainer {
  position: relative;
  color: #45494c;
  line-height: 40px;
  text-align: center;
  background: #f7f9fa;
  border-radius: 2px;
}

.sliderbg {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 40px;
  background-color: #f7f9fa;
  border: 1px solid #e6e8eb;
  border-radius: 2px;
}

.sliderContainer_active .slider {
  top: -1px;
  border: 1px solid #1991fa;
}

.sliderContainer_active .sliderMask {
  border-width: 1px 0 1px 1px;
}

.sliderContainer_success .slider {
  top: -1px;
  background-color: #52ccba !important;
  border: 1px solid #52ccba;
}

.sliderContainer_success .sliderMask {
  background-color: #d2f4ef;
  border: 1px solid #52ccba;
  border-width: 1px 0 1px 1px;
}

.sliderContainer_success .sliderIcon:before {
  content: '\f00c';
}

.sliderContainer_fail .slider {
  top: -1px;
  background-color: #f57a7a !important;
  border: 1px solid #f57a7a;
}

.sliderContainer_fail .sliderMask {
  background-color: #fce1e1;
  border: 1px solid #f57a7a;
  border-width: 1px 0 1px 1px;
}

.sliderContainer_fail .sliderIcon:before {
  content: '\f00d';
}

.sliderContainer_active .sliderText,
.sliderContainer_success .sliderText,
.sliderContainer_fail .sliderText {
  display: none;
}

.sliderMask {
  position: absolute;
  top: 0;
  left: 0;
  height: 40px;
  background: #d1e9fe;
  border: 0 solid #1991fa;
  border-radius: 2px;
}

.slider {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: #fff;
  border-radius: 2px;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);
  cursor: pointer;
  transition: background 0.2s linear;
}

.slider:hover {
  background: #1991fa;
}

.slider:hover .sliderIcon {
  background-position: 0 -13px;
}

.sliderText {
  position: relative;
}

.sliderIcon {
}

.refreshIcon {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 5;
  margin: 6px;
  color: rgba(0, 0, 0, 0.25);
  font-size: 1rem;
  cursor: pointer;
  transition: color 0.3s linear;
}

.refreshIcon:hover {
  color: #6c757d;
}
