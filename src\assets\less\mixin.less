//小间距
@small-gap: 8px;
//默认间距
@default-gap: 12px;
//中间距
@middle-gap: 16px;
//大间距
@large-gap: 24px;

/* 定义for函数 */
/* stylelint-disable */
.for(@list, @code) {
  & {
    .loop(@i:1) when (@i =< length(@list)) {
      @value: extract(@list, @i);

      @code();

      .loop(@i + 1);
    }

    .loop();
  }
}

.margin-default-loop() {
  @array: top, bottom, left, right;

  .for(@array, {
      .margin-@{value}-default {
        margin-@{value}: @default-gap;
      }
    }

  );

  .margin-all-default {
    margin: @default-gap;
  }
}

.padding-default-loop() {
  @array: top, bottom, left, right;

  .for(@array, {
      .padding-@{value}-default {
        padding-@{value}: @default-gap  !important;
      }
    }

  );

  .padding-all-default {
    padding: @default-gap !important;
  }
}

.margin-small-loop() {
  @array: top, bottom, left, right;

  .for(@array, {
      .margin-@{value}-small {
        margin-@{value}:@small-gap  !important;
      }
    }

  );

  .margin-all-small {
    margin: @small-gap !important;
  }
}

.padding-small-loop() {
  @array: top, bottom, left, right;

  .for(@array, {
      .padding-@{value}-small {
        padding-@{value}:@small-gap  !important;
      }
    }

  );

  .padding-all-small {
    padding: @small-gap !important;
  }
}

.margin-middle-loop() {
  @array: top, bottom, left, right;

  .for(@array, {
      .margin-@{value}-middle {
        margin-@{value}:@middle-gap  !important;
      }
    }

  );
}

.padding-middle-loop() {
  @array: top, bottom, left, right;

  .for(@array, {
      .padding-@{value}-middle {
        padding-@{value}:@middle-gap  !important;
      }
    }

  );

  .padding-all-middle {
    padding: @middle-gap !important;
  }
}

.margin-large-loop() {
  @array: top, bottom, left, right;

  .for(@array, {
      .margin-@{value}-large {
        margin-@{value}: @large-gap  !important;
      }
    }

  );
}

.padding-large-loop() {
  @array: top, bottom, left, right;

  .for(@array, {
      .padding-@{value}-large {
        padding-@{value}: @large-gap  !important;
      }
    }

  );

  .padding-all-large {
    padding: @large-gap !important;
  }
}

/* stylelint-enable */
