/*
 * 省市区组件
 * */
import { getProvince } from '@/services/api/erp';
import { Cascader } from 'antd';
import React from 'react';

const fetchProvince = async (params?: any) => {
  try {
    const msg = await getProvince(params);
    return msg;
  } catch (error) {
    console.log(error);
  }
  return undefined;
};
const Province = ({ value, onChange }: any) => {
  const [options, setOptions] = React.useState([]);
  React.useEffect(() => {
    fetchProvince().then((res) => {
      if (res) {
        res.map((e: any) => {
          //第一级非叶子
          e.isLeaf = false;
        });
      }
      setOptions(res);
    });
  }, []);
  //给父级传数据
  const handleProviceData = (_: any, selectedOptions: any) => {
    onChange(selectedOptions);
  };
  const loadData = (selectedOptions: any) => {
    const targetOption = selectedOptions[selectedOptions.length - 1];
    targetOption.loading = true;
    fetchProvince({ id: targetOption.addressId }).then((res) => {
      targetOption.loading = false;
      res.forEach((e) => {
        if (selectedOptions.length === 2) {
          //区没有下一级，是叶子
          e.isLeaf = true;
        } else {
          e.isLeaf = false;
        }
      });
      targetOption.children = res;
      setOptions([...options]);
    });
  };

  return (
    <Cascader
      value={value?.map((item: any) => (typeof item == 'object' ? item.addressId : item))}
      options={options}
      onChange={handleProviceData}
      fieldNames={{ label: 'addressName', value: 'addressId', children: 'children' }}
      loadData={loadData}
      changeOnSelect={false}
      placeholder="请选择省市区"
    />
  );
};

export default Province;
