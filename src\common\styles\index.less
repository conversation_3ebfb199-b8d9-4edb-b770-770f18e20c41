/** 自定义框架样式 */
// 主体
body {
  min-width: 458px;

  .ant-layout {
    min-height: 100vh !important;
  }
}

// 顶栏
.ant-pro-fixed-header {
  // z-index: 1000 !important;
  @media (max-width: 480px) {
    position: absolute !important;
  }
}

// 屏蔽面包屑、logo 点击事件
.ant-breadcrumb,
.ant-pro-global-header-logo {
  pointer-events: none;
}

// 面包屑间距
.ant-page-header.has-breadcrumb.ant-page-header-ghost {
  padding: 16px 24px 12px;
}

// 编辑 logo 标题
.ant-pro-global-header-logo a h1 {
  margin-top: -3px !important;
  padding-left: 9px !important;
  font-weight: 300 !important;
  font-size: 25px !important;
  border-left: 1px solid #ffffff4d !important;
}

// 登陆页标题
.ant-pro-form-login-logo {
  overflow: hidden !important;
}

// // 下拉层级
// .ant-select-dropdown {
//   z-index: 900;
// }
// 对话框弹窗样式
.ant-modal-confirm .ant-modal-body {
  margin-top: 10px;
}

// 定义 markdown 显示器的样式
.custom-html-style ul li {
  list-style: disc;
}

.custom-html-style ol li {
  list-style: auto;
}

// 表格样式
.ant-pro-table {
  // 搜索区
  .ant-pro-table-search {
    padding: 20px 24px;

    // 项间距
    .ant-row-start {
      row-gap: 16px;

      // 内容项
      .ant-form-item {
        margin-bottom: 0;
      }

      // 功能项
      .pro-form-query-filter-actions {
        .ant-form-item-label {
          display: none;
        }
      }
    }
  }

  // 内容区
  .ant-card-body {
    padding: 20px 24px !important;

    // 工具栏
    .ant-pro-table-list-toolbar-container {
      padding: 0 0 16px !important;

      // 右侧按钮
      .ant-pro-table-list-toolbar-right {
        // flex-direction: row-reverse;
        align-items: center;
      }
    }

    // 移动端工具栏单行
    .ant-pro-table-list-toolbar-container-mobile {
      flex-direction: row !important;
    }

    // 表格内容
    .ant-table-content {
      overflow: auto;
      white-space: nowrap;

      // 操作内容间距
      .ant-space {
        gap: 16px !important;
      }

      // 行样式
      .disableRow {
        color: #bfbfbf;
      }
    }

    // 分页栏
    .ant-pagination {
      margin: 16px 0 0 !important;
    }
  }

  > .ant-pro-card .ant-pro-card-body {
    .ant-table-content {
      overflow: auto;
      white-space: nowrap;

      // 操作内容间距
      .ant-space {
        gap: 16px !important;
      }

      // 行样式
      .disableRow {
        color: #bfbfbf;
      }
    }
  }
}

// 表格菜单（设置弹出层）
.ant-pro-table-column-setting-list-item-title {
  max-width: 80px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

// 弹窗样式
.ant-modal-body {
  max-height: calc(100vh - 308px);
  overflow: auto;

  // 选项卡样式
  .ant-tabs {
    margin-bottom: 24px;
  }

  // 表格样式
  .ant-pro-table {
    // 搜索区
    .ant-pro-table-search {
      padding: 0 0 8px !important;

      // 功能项排版
      .pro-form-query-filter-actions .ant-space-align-center {
        justify-content: end;
      }
    }

    // 内容区
    .ant-card-body {
      padding: 0 !important;

      // 表格边框
      .ant-table:not(.ant-table-bordered) .ant-table-container {
        border: 1px solid #f0f0f0;
        border-bottom: 0;
      }
    }
  }

  // 表单样式
  .ant-pro-form {
    // 组间距
    .ant-pro-form-group {
      margin-bottom: 8px;

      // 标签样式
      .ant-form-item-label > label {
        width: 100%;
      }
    }
  }

  // 描述样式
  .ant-descriptions {
    // 分组标题
    .ant-descriptions-header {
      margin-bottom: 12px !important;
    }

    // 分组内容
    .ant-descriptions-view {
      // margin-bottom: 30px;
      // padding: 16px 16px 4px;
      // background: #f8f8f9;
      // 项
      .ant-descriptions-item {
        padding-bottom: 12px !important;

        // 标签
        .ant-descriptions-item-label {
          // color: rgba(0, 0, 0, 0.55);
          font-weight: 400 !important;
        }
      }
    }
  }

  // 去除详情弹窗模块背景编辑
  .no-bgColor {
    .ant-descriptions-view {
      padding: 0;
      background: none;
    }
  }
}

// 样式兼容
.ant-statistic {
  display: block !important;
}

.ant-statistic-title {
  color: rgba(0, 0, 0, 0.45) !important;
}

// 移除边距
.on-margin {
  .ant-btn.ant-btn-block {
    width: calc(100% - 8px);
    margin: 0 4px;
  }
}
