/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-04-26 14:00:14
 * @LastEditTime: 2023-06-30 14:31:52
 * @LastEditors: zhang<PERSON><PERSON>i
 */

import { request } from "@umijs/max";
import { scenicHost } from ".";

/** 查询权益卡列表 */
export function getTravelCardList(params: API.TravelCardListParams, options: Record<string, any> = {}) {
  return request<ResponseListData<API.TravelCardListItem[]>>(`${scenicHost}/travelCard/pageList`, {
    method: "GET",
    params,
    ...options
  });
}

/** 禁用/启用权益卡 */
export function switchTravelCard(
  params: Pick<API.TravelCardListItem, "id" | "isEnable" | "scenicId">,
  options: Record<string, any> = {}
) {
  return request<ResponseData<null>>(`${scenicHost}/travelCard/disable`, {
    method: "PUT",
    data: params,
    ...options
  });
}

/** 新增权益卡 */
export function addTravelCard(params: Partial<API.TravelCardListItem>, options: Record<string, any> = {}) {
  return request<ResponseData<null>>(`${scenicHost}/travelCard/info`, {
    method: "POST",
    data: params,
    ...options
  });
}

/** 编辑权益卡 */
export function upDateTravelCard(params: Partial<API.TravelCardListItem>, options: Record<string, any> = {}) {
  return request<ResponseData<null>>(`${scenicHost}/travelCard/info`, {
    method: "PUT",
    data: params,
    ...options
  });
}

/** 查询权益卡详情 */
export function getTravelCardInfo(params: Pick<API.TravelCardListItem, "id">, options: Record<string, any> = {}) {
  return request<ResponseData<API.TravelCardListItem>>(`${scenicHost}/travelCard/info/${params.id}`, {
    method: "GET",
    ...options
  });
}

/** 删除权益卡 */
export async function deleteTravelCard(params: Pick<API.TravelCardListItem, "id">, options: Record<string, any> = {}) {
  return request<ResponseData<null>>(`${scenicHost}/travelCard/info/${params.id}`, {
    method: "DELETE",
    ...options
  });
}

/** 查询权益下拉列表 */
export async function getTravelCardDownList(
  params: {
    scenicId: string;
    operatorIds: string[];
  },
  options: Record<string, any> = {}
) {
  return request<ResponseData<API.TravelCardDownListItem[]>>(`${scenicHost}/travelCard/downList`, {
    method: "POST",
    data: params,
    ...options
  });
}
/******************** 权益卡商品 *******************************/
/** 权益卡商品列表 */
export async function getTravelCardGoodsGoodsList(
  params: API.TravelCardGoodsListParams,
  options: Record<string, any> = {}
) {
  return request<ResponseListData<API.TravelCardGoodsListItem>>(`${scenicHost}/travelGoods/pageList`, {
    method: "GET",
    params,
    ...options
  });
}

/** 禁用/启用权益卡商品 */
export async function switchTravelCardGoods(
  params: Pick<API.TravelCardGoodsListItem, "id" | "isEnable">,
  options: Record<string, any> = {}
) {
  return request<ResponseData<null>>(`${scenicHost}/travelGoods/disable`, {
    method: "PUT",
    data: params,
    ...options
  });
}

/** 新增权益卡商品 */
export async function addTravelCardGoods(
  params: Partial<API.TravelCardGoodsListItem>,
  options: Record<string, any> = {}
) {
  return request<ResponseData<null>>(`${scenicHost}/travelGoods/info`, {
    method: "POST",
    data: params,
    ...options
  });
}

/** 编辑权益卡商品 */
export async function upDateTravelCardGoods(
  params: Partial<API.TravelCardGoodsListItem>,
  options: Record<string, any> = {}
) {
  return request<ResponseData<null>>(`${scenicHost}/travelGoods/info`, {
    method: "PUT",
    data: params,
    ...options
  });
}

/** 查询权益卡商品详情 */
export function getTravelCardGoodsInfo(
  params: Pick<API.TravelCardGoodsListItem, "id">,
  options: Record<string, any> = {}
) {
  return request<ResponseData<API.TravelCardGoodsListItem>>(`${scenicHost}/travelGoods/info/${params.id}`, {
    method: "GET",
    ...options
  });
}

/** 删除权益卡商品 */
export function deleteTravelCardGoods(
  params: Pick<API.TravelCardGoodsListItem, "id">,
  options: Record<string, any> = {}
) {
  return request<ResponseData<null>>(`${scenicHost}/travelGoods/info/${params.id}`, {
    method: "DELETE",
    params,
    ...options
  });
}

/**
 * @description: 订单权益卡列表分页
 * @see https://dev.shukeyun.com/scenic/api-v2/doc.html#/%E8%AE%A2%E5%8D%95%E6%A8%A1%E5%9D%97/%E8%AE%A2%E5%8D%95%E6%A8%A1%E5%9D%97/getOrderTravelCardListUsingGET
 */
export function getOrderTCardList(params: API.OrderTCardListParams) {
  return request<
    ResponseListData<
      API.OrderTCardListItem[],
      {
        sumPayAmount: string;
      }
    >
  >(`${scenicHost}/order/orderTravelCardList`, {
    method: "GET",
    params
  });
}

/**
 * @description: 订单权益票 - 订单权益卡所有权益票列表
 * @see https://dev.shukeyun.com/scenic/api-v2/doc.html#/%E8%AE%A2%E5%8D%95%E6%A8%A1%E5%9D%97/%E8%AE%A2%E5%8D%95%E6%A8%A1%E5%9D%97/getRightsTicketListUsingGET
 */
export function getOrderRightsGoodsList(params: { serviceProviderId: string }) {
  return request<
    ResponseListData<
      API.OrderRightsGoodsListItem[],
      {
        sumPayAmount: string;
      }
    >
  >(`${scenicHost}/order/rightsGoodsList`, {
    method: "GET",
    params
  });
}

/**
 * @description: 下拉列表
 * @see https://dev.shukeyun.com/scenic/api-v2/doc.html#/%E8%AE%A2%E5%8D%95%E6%A8%A1%E5%9D%97/%E8%AE%A2%E5%8D%95%E6%A8%A1%E5%9D%97/getRightsTicketListUsingGET
 */
export function getOrderRightsGoodsDownList(params: {
  serviceProviderId: string;
  scenicId: string;
  /** 1.权益卡，2.权益票 , 3.景区*/
  type: 1 | 2 | 3;
}) {
  return request<ResponseData<API.OrderRightsGoodsDownListItem[]>>(`${scenicHost}/order/rightsGoodsInfoDownList`, {
    method: "GET",
    params
  });
}

/**
 * @description: 订单详情
 * @see   https://test.shukeyun.com/scenic/api-v2/doc.html#/%E8%AE%A2%E5%8D%95%E6%A8%A1%E5%9D%97/%E8%AE%A2%E5%8D%95%E6%A8%A1%E5%9D%97/infoUsingGET_23
 */
export function getOrderInfo(
  params: API.PageParams & {
    orderId: string;
    /** 票状态 0：未核销 1: 已核销 2：已过期 3：已完成 4：已作废 (退票) */
    status?: number;
  }
) {
  return request<ResponseData<API.OrderInfoType>>(`${scenicHost}/order/info`, {
    method: "GET",
    params
  });
}
/** 创建权益卡（新） */
export function addRightsCard(params: API.AddRightsCardParams) {
  return request<ResponseData<any>>(`${scenicHost}/travelCard/create`, {
    method: "PUT",
    data: params
  });
}
/** 编辑权益卡（新） */
export function editRightsCard(params: Record<string, any>) {
  return request<ResponseData<any>>(`${scenicHost}/travelCard/edit`, {
    method: "PUT",
    data: params
  });
}

/** 权益卡详情（新） */
export function getRightsCardInfo(params: { id: string }) {
  return request<ResponseData<any>>(`${scenicHost}/travelCard/detail/${params.id}`);
}

/** 创建权益（新） */
export function addRights(params: API.AddRightsParams) {
  return request<
    ResponseData<{
      id: string;
    }>
  >(`${scenicHost}/rightsService/create`, {
    method: "POST",
    data: params
  });
}
/**  导出报表  */
export function exportTravelCardBill(params) {
  return request<ResponseData<any>>(`${scenicHost}/order/export/orderTravelCardList`, {
    params,
    skipErrorHandler: true,
    responseType: "blob"
  });
}
