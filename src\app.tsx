import { getScenicIdentifier, jumpPage } from "@/common/utils/tool";
import Footer from "@/components/Footer";
import RightContent from "@/components/RightContent";
import UnAccessible from "@/pages/403";
import CustomErrorBoundary from "@/pages/CustomErrorBoundary";
import {
  getCompanyPermissionList,
  getMenuPermissions,
  getUserSocial,
  postPermissionAuthorize
} from "@/services/api/cas";
import { apiScenicUniqueIdentity, getCompanyInfo } from "@/services/api/erp";
import * as allIcons from "@ant-design/icons";
import { PageContainer, ProBreadcrumb } from "@ant-design/pro-components";
import type { MenuDataItem } from "@ant-design/pro-layout";
import type { RequestConfig, RunTimeLayoutConfig } from "@umijs/max";
import { Button, message, notification } from "antd";
import { isEmpty } from "lodash";
import dayjs from "dayjs";
import React from "react";
import { history } from "@umijs/max";
import defaultSettings from "../config/defaultSettings";
import routes from "../config/routes";
import access from "./access";
import { login, upgradeVersion } from "@/common/utils/app";
import { CodeType } from "@/common/utils/code";
import { getEnv } from "@/common/utils/getEnv";
import { saveCompanyInfoStore } from "./common/utils/storage";
import GuideDrawer from "@/components/GuideDrawer";
import { errorConfig } from "./requestErrorConfig";
import { apiUserCoInfo } from "@/services/api/ticket";

const { ENV } = getEnv();
const isDev = ENV === "dev";

const isOutRoute = () => {
  //无需登陆 de 路由
  const whiteList = ["/invite", "/homePage"];
  let isOut = false;
  for (let i = 0; i < whiteList.length; i++) {
    if (window.location.hash.indexOf(whiteList[i]) !== -1) {
      isOut = true;
    }
  }

  return isOut;
};
interface ScenicInfo {
  scenicId: string;
  scenicName: string;
  scenicLogo: string;
  creditCode: string;
  isEnable: string;
  uniqueIdentity: string;
  isBlockChain: number;
  appId: string;
  isExpired: boolean;
  screenId?: string;
  scenicAddress?: Record<string, any>;
  scenicType: number | string;
}

interface UserInfo {
  nickname?: string;
  username?: string;
  imgUrl?: string;
  userId?: string;
  phone?: string;
  email?: string;
}

/**
 * 应用加载时，初始化数据
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * */
export async function getInitialState(): Promise<{
  settings?: Partial<LayoutSettings>;
  userInfo?: UserInfo;
  scenicInfo?: ScenicInfo;
  permissionList: API.Permission[];
  currentCompanyInfo?: {
    coId: string;
    coName: string;
  };
  // isInitial: boolean;
  fetchUserInfo?: () => Promise<API.CurrentUser | undefined>;
  fetchScenicInfo?: () => Promise<API.CurrentUser | undefined>;
  fetchPermissionList?: () => Promise<API.Permission[] | undefined>;
  companyIds: string[];
  guideUpdateFlag?: any;
}> {
  let scenicInfo: ScenicInfo = {};
  let userInfo = {};
  let companyIds: string[];
  let companyList = []; //可登录公司列表
  let currentCompanyInfo = undefined; //当前选中的企业信息
  const isInitial = true; //是否初始化成功
  // 用来标记引导的更新状态
  const guideUpdateFlag = 0;

  // 先登录
  if (!isOutRoute()) {
    await login();
  }

  const { pathname, search } = history.location;

  // 如果是根路由，跳转到慧旅云主页
  if (pathname === "/" || pathname === "/homePage") {
    history.replace("/homePage");
    return {
      settings: defaultSettings
    };
  }

  //检查是否有设置密码，没有则提示跳转到设置
  const checkPasswordSet = (r: any) => {
    if (r.code === 20000) {
      const userStatus = r.data;
      if (!userStatus.password) {
        const key = `open${Date.now()}`;
        const btn = (
          <>
            <Button size="small" onClick={() => notification.close(key)}>
              不用了
            </Button>
            &emsp;
            <Button
              type="primary"
              size="small"
              onClick={() => {
                notification.close(key);
                open(`${getEnv().CAS_HOST}/#/accountsettings`);
              }}
            >
              好的
            </Button>
          </>
        );
        notification.open({
          message: null,
          description: (
            <>
              <br />
              你未设置密码，是否现在去设置？
              <br />
            </>
          ),
          duration: null, //duration	默认 4.5 秒后自动关闭，配置为 null 则不自动关闭	number	4.5
          key,
          btn,
          onClose: close //当通知关闭时触发
        });
      }
    } else {
      console.warn("取个人信息失败");
    }
  };

  //更新个人信息
  const fetchUserInfo = async () => {
    try {
      const userInfo = await getUserSocial();
      sessionStorage.setItem("userInfo", JSON.stringify(userInfo.data));
      // addOperationLogRequest({
      //   action: 'login',
      //   content: '登录慧景云系统',
      // });
      checkPasswordSet(userInfo);
      return {
        ...userInfo?.data
      };
    } catch (error) {
      console.error(error);
      // isInitial = false;
      // jumpPage.push('/result?type=network');
    }
    return undefined;
  };

  //取权限信息
  let permissionList: API.Permission[] = [];
  const fetchPermissionList = async (scenicId: string, userId: string, coId: string, appId: string) => {
    try {
      const { data = [] } = await getMenuPermissions({
        appId: appId,
        groups: `backend/${scenicId}/${coId}`,
        users: userId,
        type: "02",
        companyId: coId,
        scenicId
      });
      return data;
    } catch (error) {
      console.error(error);
    }
    return [];
  };

  //更新景区信息
  const fetchScenicInfo = async () => {
    try {
      const scenicIdentifier = getScenicIdentifier();
      const {
        data: { scenic, scenicAttribute, scenicDate, scenicAddress }
      } = await apiScenicUniqueIdentity(scenicIdentifier);

      // 景区过期验证
      const defaultInfo = {
        isExpired: false,
        expiredDate: -1
      };
      const result = new Promise<boolean>((resolve, reject) => {
        const { endDate } = scenicDate;
        const { serviceStatus } = scenic;
        //试用景区临期倒计时
        const expiredDate = dayjs(endDate).diff(dayjs().format("YYYY-MM-DD"), "day");

        if (serviceStatus !== 2 && expiredDate <= 7 && expiredDate >= 0) {
          defaultInfo.expiredDate = expiredDate;
        }

        //  过期
        const isExpired = dayjs().isAfter(endDate, "day");
        // 2 正式景区
        if (serviceStatus !== 2 && isExpired) {
          defaultInfo.isExpired = true;
          jumpPage.push("/expired");
          resolve(false);
        } else {
          resolve(true);
        }
      });

      if (!scenic?.id) jumpPage.push("/result?type=empty");
      if (scenic?.id && scenic?.isEnable === "0") jumpPage.push("/result?type=disable");

      scenicInfo = {
        scenicId: scenic?.id,
        scenicName: scenic?.name,
        scenicLogo: scenic?.scenicLogo ? getEnv().IMG_HOST + scenic?.scenicLogo : "./logo/logo.png",
        creditCode: scenic?.creditCode,
        isEnable: scenic?.isEnable,
        serviceStatus: scenic?.serviceStatus,
        uniqueIdentity: scenicAttribute?.uniqueIdentity,
        isBlockChain: scenicAttribute?.isBlockChain,
        appId: scenicAttribute?.appId,
        screenId: scenic?.screenId,
        scenicAddress,
        scenicType: scenic?.type,
        ...scenicDate,
        ...defaultInfo
      };

      sessionStorage.setItem(
        "scenicInfo",
        JSON.stringify({
          ...scenicInfo,
          scenicLogo: encodeURIComponent(scenicInfo?.scenicLogo)
        })
      ); //用于返回登录页时的传参
      return result;
    } catch (error) {
      message.error("获取景区信息失败，请刷新页面");
      return false;
      // isInitial = false;
      // jumpPage.push('/result?type=network');
    }
  };

  // 获取禁启用状态
  const fetchEnabledStatus = async scenicId => {
    const list: API.Permission[] = [];
    try {
      const { data } = await getCompanyPermissionList({ scenicId, appId: scenicInfo?.appId });
      if (data.length > 0) {
        //启用状态
        companyList = data;
        //初始化当前选中企业
        const currentCompanyId = localStorage.getItem("currentCompanyId");
        if (currentCompanyId) {
          //赋默认值
          currentCompanyInfo = companyList.find(e => e.coId === currentCompanyId);
        }
        if (!currentCompanyInfo) {
          //如果没有默认值、或者没赋上默认值
          currentCompanyInfo = companyList[0];
          localStorage.setItem("currentCompanyId", currentCompanyInfo.coId);
        }
      } else {
        //禁用状态
        jumpPage.replace("/result?type=noPermissions");
      }
      const coRes = await getCompanyInfo(currentCompanyInfo.coId);
      if (coRes.code === CodeType.SUCCUSS) {
        saveCompanyInfoStore(coRes.data);
      }
    } catch (error) {
      // jumpPage.push('/result?type=network');
      console.error(error);
    }
    // }
    return list;
  };

  const canNextStep = await fetchScenicInfo(); //这个要第一个请求
  if (!canNextStep) {
    // 可以在这打断
  }

  if (!isOutRoute()) {
    //请求用户信息
    userInfo = await fetchUserInfo();
    if (window.location.hash.indexOf("/UserApplies") === -1) {
      // TODO:临时解决
      // 查询当前账号下的服务商 id 列表
      const { data } = await apiUserCoInfo({ id: userInfo.userId });
      companyIds = data.companyIds;
      await fetchEnabledStatus(scenicInfo?.scenicId);
      //请求用户权限
      if (currentCompanyInfo) {
        permissionList = await fetchPermissionList(
          scenicInfo.scenicId,
          userInfo.userId,
          currentCompanyInfo.coId,
          scenicInfo?.appId
        );
      }

      // 统计 cas 登录人数
      try {
        postPermissionAuthorize({
          permissionCode: [
            {
              group: "cas",
              code: "UserInit",
              action: ""
            }
          ],
          appId: scenicInfo.appId
        });
      } catch (error) {}
    }
  }

  upgradeVersion();

  return {
    fetchUserInfo,
    userInfo,
    scenicInfo,
    settings: defaultSettings,
    permissionList,
    fetchPermissionList,
    fetchScenicInfo,
    companyIds,
    companyList,
    currentCompanyInfo,
    // isInitial,
    guideUpdateFlag
  };
}
const fixMenuItemIcon = (menus: MenuDataItem[], iconType = "Outlined"): MenuDataItem[] => {
  menus.forEach(item => {
    const { icon, routes } = item;
    if (typeof icon === "string") {
      const fixIconName = icon.slice(0, 1).toLocaleUpperCase() + icon.slice(1) + iconType;
      item.icon = React.createElement(allIcons[fixIconName] || allIcons[icon]);
    }
    if (routes && routes.length > 0) item.children = fixMenuItemIcon(routes);
  });
  return menus;
};
// ProLayout 支持的 api https://procomponents.ant.design/components/layout
export const layout: RunTimeLayoutConfig = ({ initialState, setInitialState }: any) => {
  let createTime;
  const { endDate, startDate, serviceStatus, scenicLogo, scenicName } = initialState?.scenicInfo || {};
  try {
    //获取景区试用期时间
    if (!isEmpty(initialState?.scenicInfo)) {
      createTime =
        serviceStatus == 1 ? (
          <span
            style={{
              border: "1px solid #ccc",
              borderRadius: "19px",
              padding: "5px",
              fontSize: "14px",
              backgroundColor: "#fff",
              color: "#666",
              verticalAlign: "bottom",
              marginLeft: "10px"
            }}
          >
            试用期 {dayjs(startDate).format("YYYY-MM-DD")} ~ {dayjs(endDate).format("YYYY-MM-DD")}
          </span>
        ) : (
          ""
        );
    }
  } catch (e) {
    console.error(e);
  }
  //试用期日期标题
  let TryoutTitle = <>{createTime}</>;

  // 区块链景区配置
  if (initialState?.scenicInfo?.isBlockChain) {
    // 区块链景区
    TryoutTitle = (
      <>
        {createTime}
        <img src={require("../public/chain/chain_logo.png")} style={{ margin: "0 8px 4px 8px" }} />
      </>
    );
  }

  return {
    ...initialState?.settings,
    ErrorBoundary: false,
    childrenRender: children => {
      // if (initialState?.loading) return <PageLoading />;
      return (
        <CustomErrorBoundary>
          <PageContainer
            ghost
            header={{
              title: false
            }}
            breadcrumbRender={entity => {
              const _items = (entity.breadcrumb?.items || [])?.map(item => {
                const _linkInBreadcrumb = (entity?.matchMenus || []).filter(e => e.key == item.linkPath)?.[0]
                  ?.linkInBreadcrumb;
                return {
                  ...item,
                  linkPath: _linkInBreadcrumb ? item.linkPath : ""
                };
              });
              return <ProBreadcrumb style={{ paddingBlockStart: 18 }} items={_items} />;
            }}
          >
            {children}
            {/* {isDev && (
              <SettingDrawer
                disableUrlParams
                enableDarkTheme
                settings={initialState?.settings}
                onSettingChange={(settings) => {
                  setInitialState((preInitialState) => ({
                    ...preInitialState,
                    settings,
                  }));
                }}
              />
            )} */}
            {/* 引导 */}
            <GuideDrawer />
          </PageContainer>
        </CustomErrorBoundary>
      );
    },
    // 自定 layout 头部信息
    headerContentRender: () => {
      return TryoutTitle;
    },
    title: `${scenicName}景区管理系统`,
    logo: scenicLogo,
    disableContentMargin: false,
    rightContentRender: () => <RightContent />,
    footerRender: () => <Footer />,
    // onPageChange: () => {
    //   console.log(location.pathname);
    // },

    menu: {
      params: initialState,
      request: async (params, defaultMenuData) => {
        const LimitObj = access(params);
        //递归替换景区路由
        const recursionReplace = ({ routes = [], field = "scenic" }) => {
          routes.map(obj => {
            if (obj.replacePathField) {
              obj[obj.replacePathField] = field;
            }
            if (obj?.routes?.length > 0) {
              recursionReplace({ routes: obj.routes, field });
            }
          });
        };
        const scenicField = getScenicIdentifier();
        recursionReplace({ routes: routes, field: scenicField });

        const fun = (arr: any) => {
          for (let i = arr.length - 1; i >= 0; i--) {
            if (arr[i].routes && arr[i].routes.length > 0) {
              fun(arr[i].routes);
              if (arr[i].routes.length == 0) {
                arr.splice(i, 1);
              }
            } else if (arr[i].access !== undefined && !LimitObj[arr[i].access]) {
              arr.splice(i, 1);
            }
          }
          return arr;
        };

        let menu = fun(JSON.parse(JSON.stringify(routes)));
        menu = fixMenuItemIcon(menu);
        return menu;
      }
    },

    // 自定义 403 页面
    unAccessible: <UnAccessible />
  };
};

/**
 * @name request 配置，可以配置错误处理
 * 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。
 * @doc https://umijs.org/docs/max/request#配置
 */
export const request: RequestConfig = {
  withCredentials: false,
  ...errorConfig
};
