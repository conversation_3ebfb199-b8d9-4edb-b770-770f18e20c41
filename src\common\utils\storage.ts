/**
 * BKW：用来区分系统，解决非生产环境同域名下的存储冲突问题
 */

export const DictInfoKey = 'BKW/DictInfo';
export const GuideInfoKey = 'BKW/GuideInfo';
export const CompanyInfoKey = 'BKW/CompanyInfo';

// 获取引导信息
export function getGuideInfo(): API.GuideInfo {
  return JSON.parse(localStorage.getItem(GuideInfoKey) || '{}');
}

// 保存引导信息
export function saveGuideInfo(val: any) {
  localStorage.setItem(GuideInfoKey, JSON.stringify(val || {}));
}

// 获取企业信息
export function getCompanyInfoStore(): any {
  return JSON.parse(localStorage.getItem(CompanyInfoKey) || '{}');
}

// 保存企业信息
export function saveCompanyInfoStore(val: any) {
  localStorage.setItem(CompanyInfoKey, JSON.stringify(val || {}));
}

export function clearStorage() {
  document.cookie = '';
  localStorage.removeItem(GuideInfoKey);
  localStorage.removeItem(CompanyInfoKey);
}
