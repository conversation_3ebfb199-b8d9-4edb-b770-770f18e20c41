import { getUniqueId } from "@/common/utils/tool";
import { helpHost } from "@/services/api";
import { apiAddKnowledgeFile, uploadKnowledgeFile } from "@/services/api/service";
import { LoadingOutlined, PlusOutlined } from "@ant-design/icons";
import type { UploadProps } from "antd";
import { Button, Upload, message } from "antd";
import type { RcFile, UploadChangeParam, UploadFile as UploadFileType } from "antd/es/upload/interface";
import { useEffect, useMemo, useState, type FC } from "react";
import { getEnv } from "@/common/utils/getEnv";

interface ResponseFileData {
  size: number;
  path: string;
  name: string;
  type: string;
  mtime: string;
}

interface UploadFileProps extends Omit<UploadProps, "onSuccess" | "fileList" | "beforeUpload" | "action"> {
  /** 单文件大小上限  */
  size?: number;
  /** 初始值 */
  defaultValue?: string;
  /** 上传成功的回调 */
  onSuccess?: (value: { name: string; url: string }[]) => void;
  /** 只读 */
  readonly?: boolean;
  /** 文件类型 如 .png .mp4 */
  accept?: string;
  /** 知识库 ID */
  knowledgeBaseId?: string;
}

/**
 * @description 封装 antd 文件上传组件 针对所有文件 用新的上传地址
 * @see API https://4x.ant.design/components/upload-cn/#API
 * @see  https://test.shukeyun.com/scenic/api-v2/doc.html#/aws%E6%96%87%E4%BB%B6%E4%B8%8A%E4%BC%A0/aws%E6%96%87%E4%BB%B6%E4%B8%8A%E4%BC%A0/uploadFileUsingPOST
 *  */

const UploadKnowledgeFile: FC<UploadFileProps> = ({
  size = 100,
  defaultValue = "",
  readonly = false,
  accept,
  knowledgeBaseId,
  onSuccess,
  ...otherProps
}) => {
  // 受控值
  const [fileList, setFileList] = useState<UploadFileType[]>([]);
  // 上传状态
  const [uploading, setUploading] = useState(false);
  // 类型校验
  const checkFileType = (acceptType: string, fileName: string) => {
    const arr = fileName.split(".");
    const format = arr[arr.length - 1];

    if (`.${format}` === acceptType) {
      return true;
    }

    // .jpg 和 .jpeg 是同一个格式
    if (format === "jpeg" && acceptType === ".jpg") {
      return true;
    }
    return false;
  };

  // 上传前图片格式和大小校验 不合法不会上传服务器
  const beforeUpload = (file: RcFile, FileList: RcFile[]) => {
    console.log(file, accept);
    // 类型校验
    if (accept) {
      const isValidType = accept.split(",").some(type => checkFileType(type, file.name));
      if (!isValidType) {
        message.error("文件格式不正确！");
        return Upload.LIST_IGNORE;
      }
    }

    // 文件大小验证
    const isOverSize = file.size / 1024 / 1024 > size;
    if (isOverSize) {
      message.error(`文件必须小于${size}M!`);
      return Upload.LIST_IGNORE;
    }
    return true;
  };

  // 自定义上传请求
  const uploadRequest: UploadProps["customRequest"] = async options => {
    const { file, onProgress, onError, onSuccess } = options;

    setUploading(true);
    onProgress?.({ percent: 10 });
    const params = {
      file: file as File,
      callback_url: `${helpHost}/knowledge/file/callback`
    };
    try {
      const res = await uploadKnowledgeFile(params);
      console.log(res);
      await apiAddKnowledgeFile({
        knowledgeBaseId,
        fileId: res?.doc_id
      });
      onProgress?.({ percent: 100 });
      onSuccess?.(res);
    } catch (e) {
      onError?.(e);
    } finally {
      setUploading(false);
    }
  };

  const { FILE_HOST } = getEnv();
  // onchange 回调
  const onFileListChange = (info: UploadChangeParam<UploadFileType<ResponseData<ResponseFileData>>>) => {
    const { file, fileList: list } = info;
    const { status } = file;

    // 上传成功
    if (status === "done") {
      // 处理数据
      const newList = list.map(item => {
        const { data } = item.response || {};
        if (item.status === "done") {
          return {
            ...item,
            // 预览地址
            thumbUrl: data ? FILE_HOST + (data?.path || "") : item.thumbUrl,
            // preview 字段暂时用来存后端图片返回地址
            preview: data ? data?.path : item.preview
          };
        }
        return item;
      });
      setFileList(newList);
      return;
    } else if (status === "error") {
      // 上传失败
      // 列表过滤掉上传失败的数据
      message.error("文件上传失败！");
      setFileList(fileList.filter(item => item.status !== "error" && item.status !== "uploading"));
      console.log(fileList);
      return;
    } else {
      setFileList(list);
    }
  };

  // 文件回显
  useEffect(() => {
    if (defaultValue) {
      const defaultFileList = defaultValue.split(",").map(item => ({
        uid: getUniqueId(),
        name: item,
        percent: 100,
        status: "done",
        thumbUrl: FILE_HOST + item,
        preview: item
      }));
      setFileList(defaultFileList as UploadFileType[]);
    }
  }, [defaultValue]);

  useEffect(() => {
    console.log("上传 1", fileList);
    if (onSuccess) {
      // 需过滤上传失败的图片
      const changedValue = fileList
        .filter(item => item.status === "done" && item.response.status === "ok")
        .map(item => {
          return {
            name: item.name,
            url: item.response.minio_url
          };
        });
      console.log("上传 2", changedValue);
      if (changedValue.length > 0) {
        onSuccess(changedValue);
        setFileList([]);
      }
    }
  }, [fileList]);

  const uploadButton = useMemo(() => {
    // 图片 卡片模式
    if (otherProps.listType === "picture-card") {
      if (fileList.length < (otherProps.maxCount ?? 0) && !readonly) {
        return uploading ? <LoadingOutlined /> : <PlusOutlined />;
      }
    } else {
      if (!readonly) {
        return (
          <Button type="primary" loading={uploading}>
            点击上传
          </Button>
        );
      }
    }

    return null;
  }, [fileList.length, otherProps.listType, otherProps.maxCount, readonly, uploading]);

  return (
    <Upload
      {...otherProps}
      accept={accept}
      showUploadList={false}
      customRequest={uploadRequest}
      beforeUpload={beforeUpload}
      onChange={onFileListChange}
      fileList={fileList}
    >
      {uploadButton}
    </Upload>
  );
};

export default UploadKnowledgeFile;
