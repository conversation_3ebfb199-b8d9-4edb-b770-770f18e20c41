/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-05-06 13:57:58
 * @LastEditTime: 2022-05-07 09:48:20
 * @LastEditors: zhangfengfei
 */
import type { FC, JSXElementConstructor, ReactElement } from 'react';
import { useState } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
// 忽略 或在 d.ts file中 declare module 'pdfjs-dist/build/pdf.worker.entry'
import pdfjsWorker from 'pdfjs-dist/build/pdf.worker.entry';
import { Modal, Pagination, Spin } from 'antd';
import styles from './index.less';

pdfjs.GlobalWorkerOptions.workerSrc = pdfjsWorker;

type PDFViewerProps = {
  /** 文件路径url或者base64 */
  file: string;
  /** 加载中loading组件 */
  loadingFC?: string | ReactElement<any, string | JSXElementConstructor<any>>;
  width?: number;
  visible: boolean;
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
};
/**
 * @description pdf预览组件
 * @see https://github.com/wojtekmaj/react-pdf
 *  */
const PDFViewer: FC<PDFViewerProps> = ({ file, loadingFC, width, visible, setVisible }) => {
  // 总页数
  const [numPages, setNumPages] = useState<number>(1);
  // 当前页
  const [pageNumber, setPageNumber] = useState(1);

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
  };

  const onLoadError = (error: Error) => {
    console.log(error);
  };
  const onChange = (page: number) => {
    setPageNumber(page);
  };
  const onCancel = () => {
    setVisible(false);
  };

  return (
    <Modal
      visible={visible}
      footer={false}
      maskClosable={false}
      onCancel={onCancel}
      className={styles.modal}
    >
      <div className={styles.main}>
        <Document
          file={file}
          loading={loadingFC || <Spin size="large" />}
          onLoadSuccess={onDocumentLoadSuccess}
          onLoadError={onLoadError}
        >
          <Page className={styles.page} pageNumber={pageNumber} width={width} />
          <Pagination
            className={styles.pagination}
            simple
            pageSize={1}
            defaultCurrent={pageNumber}
            total={numPages}
            onChange={onChange}
          />
        </Document>
      </div>
    </Modal>
  );
};
export default PDFViewer;
