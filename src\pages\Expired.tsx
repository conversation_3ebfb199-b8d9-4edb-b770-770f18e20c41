/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-03-22 16:51:26
 * @LastEditTime: 2023-09-28 15:04:04
 * @LastEditors: zhang<PERSON><PERSON>i
 */
import { getScenicIdentifier, goToLogin } from "@/common/utils/tool";
import { logout } from "@/services/api/cas";
import { PageLoading } from "@ant-design/pro-layout";
import { Modal, Space } from "antd";
import { useEffect } from "react";
import { useModel } from "@umijs/max";
import { getEnv } from "@/common/utils/getEnv";

export default () => {
  const { initialState, loading } = useModel("@@initialState");
  const { isExpired } = initialState?.scenicInfo || {};
  const { HLY_HOST } = getEnv();
  /**
   * 退出登录，并且将当前的 url 保存
   */
  const loginOut = async () => {
    try {
      const appId = JSON.parse(sessionStorage.getItem("scenicInfo"))?.appId || "";
      await logout({
        appId
      });
      const scenicCode = getScenicIdentifier();
      goToLogin({
        scenicCode
      });
    } catch (error) {
      if (error.name === "BizError" && error.data.code === 30001) {
        //没设置cookie跳转登陆页
        const scenicCode = getScenicIdentifier();
        goToLogin({
          scenicCode
        });
      } else {
        console.error(error);
      }
    }
  };

  useEffect(() => {
    if (isExpired && !loading) {
      Modal.confirm({
        icon: null,
        content: (
          <Space direction="vertical" align="center" style={{ width: "100%" }}>
            <span>当前景区已过免费试用期</span>
            <span>
              请前往<a href={HLY_HOST}>【慧旅云】</a>
              续期,或联系我司商务订阅正式版
            </span>
            <span>联系方式：40088一11138或(+86)755一88328999</span>
          </Space>
        ),
        onOk(...args) {
          loginOut();
          // if (HLY_HOST) {
          //   const scenicCode = getScenicIdentifier();
          //   goToLogin({
          //     scenicCode,
          //   });
          // }
        },
        cancelButtonProps: {
          style: {
            display: "none"
          }
        }
      });
    }
  }, [loading]);

  return <PageLoading />;
};
