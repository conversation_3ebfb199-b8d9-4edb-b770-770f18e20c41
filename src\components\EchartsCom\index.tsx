/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable @babel/no-unused-expressions */
import china from '@/assets/map/china.json';
import type { EChartsType } from 'echarts';
import * as echarts from 'echarts';
import type { GeoJSONCompressed } from 'echarts/types/src/coord/geo/geoTypes.js';
import type { RendererType } from 'echarts/types/src/util/types.js';
import { debounce } from 'lodash';
import { useCallback, useEffect, useRef } from 'react';
import styles from './index.module.less';

type OptionType = Record<string, unknown>;

interface Props
  extends React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement> {
  renderer?: RendererType;
  notMerge?: boolean;
  lazyUpdate?: boolean;
  options: OptionType;
  instanceHandle?: (instance: EChartsType) => void;
}

const state = {
  width: '100%',
  height: '100%',
};

echarts.registerMap('china', china as unknown as GeoJSONCompressed);

const EchartsCom = ({
  notMerge,
  lazyUpdate,
  instanceHandle,
  renderer,
  options,
  ...restProps
}: Props) => {
  const drawDomRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<EChartsType | null>(null);

  const dispose = () => {
    if (!chartRef.current) {
      return;
    }
    chartRef.current.dispose();
    chartRef.current = null;
  };

  const resize = debounce(() => {
    chartRef.current && chartRef.current.resize();
  }, 100);

  const setOptions = useCallback(
    (option: OptionType) => {
      if (!chartRef.current) {
        return;
      }
      chartRef.current.setOption(option, notMerge, lazyUpdate);
    },
    [notMerge, lazyUpdate],
  );

  // 初始化组件
  const initChart = (dom: HTMLDivElement | null) => {
    if (chartRef.current) return;
    if (!dom) return;
    // renderer 用于配置渲染方式 可以是 svg 或者 canvas
    const _renderer = renderer || 'canvas';
    chartRef.current = echarts.init(dom, null, {
      renderer: _renderer,
      width: 'auto',
      height: 'auto',
    });
    // 执行初始化的任务，例如注册地图
    if (instanceHandle) instanceHandle(chartRef.current);
    setOptions(options);
    // 监听屏幕缩放，重新绘制 echart 图表
    window.addEventListener('resize', resize);
  };

  const initHandle = () => {
    // 还没实例走初始化
    if (!chartRef.current) {
      initChart(drawDomRef.current);
    } else {
      setOptions(options);
    }
  };

  useEffect(() => {
    // 组件卸载
    return () => {
      window.removeEventListener('resize', resize);
      dispose();
    };
  }, []);

  // 每次更新组件都重置
  useEffect(() => {
    initHandle();
  }, [options]);

  return (
    <div
      className={styles.default_chart}
      ref={drawDomRef}
      style={{ width: state.width, height: state.height }}
      {...restProps}
    />
  );
};

export default EchartsCom;
