type ModalType = 'activity' | 'scenicDetail' | 'articleDetail' | 'helpDetail' | 'custom' | '';

interface RowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  'data-row-key': string;
}

interface ModalStateProps {
  type: ModalType;
  selectRowKeys?: string;
  selectRowRecord?: any;
  navigationUrl?: string;
  navigationUrlName?: string;
}

interface TableItem {
  navigationName: string;
  navigationSort?: string;
  navigationUrl: string;
  navigationUrlName: string;
  openMode: string;
  scenicId?: number;
  secondList?: SecondList[];
  children?: SecondList[];
  key?: string;
  uuid?: string;
  isExpand?: boolean;
}

interface SecondList {
  navigationName: string;
  navigationSort?: string;
  navigationUrl: string;
  navigationUrlName: string;
  openMode: string;
  scenicId?: number;
  secondList?: any[];
  key?: string;
  uuid?: string;
}

type TableItemKeys = keyof TableItem & keyof SecondList;

export { ModalStateProps, ModalType, RowProps, SecondList, TableItem, TableItemKeys };
