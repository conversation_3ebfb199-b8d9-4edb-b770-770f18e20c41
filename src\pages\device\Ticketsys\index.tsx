import Delete from "@/common/components/Delete";
import Disabled from "@/common/components/Disabled";
import ProModal from "@/common/components/ProModal";
import useModal from "@/common/components/ProModal/useModal";
import { tableConfig } from "@/common/utils/config";
import { checkType, enableEnum, productTypeEnum } from "@/common/utils/enum";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import { getUniqueId } from "@/common/utils/tool";
import { delCheckaddr, getCheckPageList, setCheckInPointStatus } from "@/services/api/device";
import { addCheckPoint, editCheckPoint, infoCheckPoint } from "@/services/api/facility";
import { PlusOutlined } from "@ant-design/icons";
import type { ProFormColumnsType } from "@ant-design/pro-components";
import type { ActionType, ProColumns } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import { Button, Space } from "antd";
import React, { useEffect, useRef, useState } from "react";
import { Access, useAccess, useModel } from "@umijs/max";

const TableList: React.FC = () => {
  const modalState = useModal();
  const access = useAccess();
  const [optionId, handleOptionId] = useState<string>("");
  const { initialState } = useModel("@@initialState");
  const { scenicId, scenicName }: any = initialState?.scenicInfo;
  const actionRef = useRef<ActionType>();
  const columns: ProColumns[] = [
    {
      title: "编号",
      dataIndex: "id",
      hideInSearch: true
    },
    {
      title: "检票点名称",
      dataIndex: "checkName"
    },
    {
      title: "所属景区",
      dataIndex: "scenicName",
      hideInSearch: true
    },
    {
      title: "联系人",
      dataIndex: "contacts"
    },
    {
      title: "联系方式",
      dataIndex: "contactsPhone",
      hideInSearch: true
    },
    {
      title: "启用状态",
      dataIndex: "isEnable",
      fixed: "right",
      valueEnum: enableEnum,
      renderText: (dom: any, entity: any) => (
        <Disabled
          access={access.canCheckPoint_openClose}
          status={entity.isEnable == 1}
          params={{ id: entity.id, isEnable: entity.isEnable == 1 ? 0 : 1 }}
          request={async params => {
            const data = await setCheckInPointStatus(params);
            // 添加日志
            addOperationLogRequest({
              action: "disable",
              content: `${dom == 1 ? "禁用" : "启用"}【${entity.checkName}】检票点`
            });
            return data;
          }}
          actionRef={actionRef}
        />
      )
    },
    {
      width: "auto",
      title: "操作",
      valueType: "option",
      fixed: "right",
      render: (_: any, record: any) => (
        <Space>
          <a
            onClick={async () => {
              handleOptionId(record.id);
              modalState.setType("info");
              addOperationLogRequest({
                action: "info",
                content: `查看【${record.checkName}】检票点详情`
              });
            }}
          >
            查看
          </a>
          <Access accessible={access.canCheckPoint_edit}>
            <a
              onClick={() => {
                handleOptionId(record.id);
                modalState.setType("edit");
              }}
            >
              编辑
            </a>
          </Access>
          <Delete
            access={access.canCheckPoint_delete}
            status={record.isEnable == 1}
            params={record.id}
            request={async params => {
              const data = await delCheckaddr(params);
              // 添加日志
              addOperationLogRequest({
                action: "del",
                content: `删除【${record.checkName}】检票点`
              });
              return data;
            }}
            actionRef={actionRef}
          />
        </Space>
      )
    }
  ];
  const columnsInitial: ProFormColumnsType[] = [
    {
      title: "基础信息",
      columns: [
        {
          title: "所属景区",
          dataIndex: "scenicId",
          valueEnum: { [scenicId]: scenicName },
          initialValue: scenicId,
          fieldProps: { disabled: true },
          formItemProps: { rules: [{ required: true }] }
        },
        {
          title: "联系人",
          dataIndex: "contacts",
          formItemProps: { rules: [{ required: true, max: 20 }] }
        },
        {
          title: "检票点名称",
          dataIndex: "checkName",
          formItemProps: { rules: [{ required: true, max: 30 }] }
        },
        {
          title: "联系方式",
          dataIndex: "contactsPhone",
          formItemProps: {
            rules: [{ required: true }, { pattern: /^\d+(-\d+)*$/, message: "不合法的手机号" }]
          }
        },
        {
          title: "检票点类型",
          dataIndex: "checkType",
          valueEnum: checkType,
          convertValue: value => String(value ?? "") || value
        },
        {
          title: "产品类型",
          dataIndex: "proType",
          valueEnum: productTypeEnum,
          formItemProps: { rules: [{ required: true }] }
        }
      ]
    },
    {
      title: "地理信息",
      columns: [
        {
          title: "检票点地址",
          dataIndex: "address"
        }
      ]
    },
    {
      title: "说明信息",
      columns: [
        {
          title: "备注",
          dataIndex: "remark",
          valueType: "textarea",
          fieldProps: {
            showCount: true,
            maxLength: 1000
          },
          colProps: { span: 24 }
        }
      ]
    }
  ];

  const logList = [...columnsInitial[0].columns, ...columnsInitial[1].columns];

  const [infoData, setInfoData] = useState();

  useEffect(() => {
    const pageOperateType = localStorage.getItem("pageOperateType");
    if (pageOperateType === "add") {
      handleOptionId("");
      modalState.setType("add");
    }
  }, []);

  return (
    <>
      <ProTable<API.RuleListItem, API.PageParams>
        {...tableConfig}
        rowClassName={row => (row.isEnable == 1 ? "" : "disableRow")}
        actionRef={actionRef}
        toolBarRender={() => [
          <Access key={getUniqueId()} accessible={access.canCheckPoint_insert}>
            <Button
              type="primary"
              key="primary"
              onClick={() => {
                handleOptionId("");
                modalState.setType("add");
              }}
            >
              <PlusOutlined /> 新增
            </Button>
          </Access>
        ]}
        params={{ scenicId }}
        request={getCheckPageList}
        columns={columns}
      />
      <ProModal
        title="检票点"
        {...modalState}
        columns={columnsInitial}
        params={{ id: optionId }}
        infoRequest={async params => {
          const data = await infoCheckPoint(params);
          setInfoData(data.data);
          return data;
        }}
        actionRef={actionRef}
        addRequest={async params => {
          const data = await addCheckPoint(params);
          addOperationLogRequest({
            action: "add",
            content: `新增【${params.checkName}】检票点`
          });
          return data;
        }}
        editRequest={async params => {
          const data = await editCheckPoint(params);
          addOperationLogRequest({
            action: "edit",
            changeConfig: {
              list: logList,
              beforeData: infoData,
              afterData: params
            },
            content: `编辑【${params.checkName}】检票点`
          });
          return data;
        }}
        onOpenChange={e => {
          localStorage.removeItem("pageOperateType");
        }}
      />
    </>
  );
};

export default TableList;
