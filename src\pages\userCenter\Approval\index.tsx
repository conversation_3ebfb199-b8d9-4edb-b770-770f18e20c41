import { tableConfig } from "@/common/utils/config";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import { getCo } from "@/services/api/erp";
import { QuestionCircleOutlined } from "@ant-design/icons";
import ProForm, { ModalForm, ProFormTextArea } from "@ant-design/pro-form";
import ProTable from "@ant-design/pro-table";
import { Popconfirm, Space, message } from "antd";
import { useEffect, useRef, useState } from "react";
import { Access, useAccess, useModel } from "@umijs/max";
import { getOrgStructureApproval, getUserApproval } from "../../../services/api/cas";

const setCoId = [];
export default function UserApproval() {
  const access = useAccess();
  const actionRef = useRef();
  const [modalVisit, setModalVisit] = useState(false);
  const [formObj] = ProForm.useForm();
  //企业下拉框
  const [companyData, setCompanyData] = useState([]);
  //企业下拉框 value
  const [companyValue, setCompanyValue] = useState("");
  const { initialState }: any = useModel("@@initialState");

  const { scenicId } = initialState.scenicInfo;
  const { currentCompanyInfo, scenicInfo } = initialState;
  //审批人
  const { phone, username, userId } = initialState.userInfo;
  //当前企业 id
  //拒绝理由参数
  const [reasonObj, setReasonObj] = useState({});
  //同意
  const onAgreement = async (val: any) => {
    console.log("kkkkkkkkkkkkk", val);
    // {
    //   "approvalUserName": "",
    //   "companyId": "",
    //   "refuseReason": "",
    //   "status": 0,
    //   "userId": ""
    // }
    const pars = {
      approvalUserName: username,
      companyId: val.companyId,
      companyName: val.companyName,
      // refuseReason:'',
      phone: val.phone,
      status: 1,
      userId: val.userId,
      userNickname: val.applyName,
      permissionCode: [
        {
          group: "bakcend/user_status",
          code: scenicId + "/" + val.companyId,
          action: ""
        }
      ],
      appId: scenicInfo?.appId
    };
    try {
      const result = await getOrgStructureApproval(pars);
      addOperationLogRequest({
        action: "audit",
        content: `通过${val.applyName}用户审批`
      });
      console.log(pars);
      message.success("已同意");
      // getNoticeList()
      // 刷新
      actionRef?.current?.reload();
    } catch (e) {
      console.error(e);
    }
  };

  //拒绝
  const refuse = async (val: any) => {
    // console.log(val);
    setReasonObj(val);

    setModalVisit(true);
    // try {
    //   //  const result = await getOrgStructureApproval(pars);
    //   //  message.success('已拒绝');
    //   // setModalVisit(true);
    //   // // 刷新
    //   // actionRef?.current?.reload();
    // } catch (e) {
    //   console.log(e);
    // }
  };

  //理由
  const getReason = async val => {
    const pars = {
      approvalUserName: username,
      companyName: reasonObj.companyName,
      companyId: reasonObj.companyId,
      refuseReason: val.text,
      phone: reasonObj.phone,
      // phone:'15576851379',
      status: 2,
      userId: reasonObj.userId,
      userNickname: reasonObj.applyName,
      permissionCode: [
        {
          group: "bakcend/user_status",
          code: scenicId + "/" + reasonObj.companyId,
          action: ""
        }
      ],
      appId: scenicInfo?.appId
    };
    try {
      if (val.text) {
        console.log("fsdfsfsdfdsf", reasonObj);
        const result = await getOrgStructureApproval(pars);
        addOperationLogRequest({
          action: "audit",
          content: `拒绝${reasonObj.applyName}用户审批`
        });
        setModalVisit(false);
        // setReason(val)
        message.success("已拒绝");
        formObj.setFieldsValue({ text: "" });
        // return true;
      } else {
        message.info("请填写拒绝的原因");
        setModalVisit(false);
        // return false;
      }

      // 刷新
      actionRef?.current?.reload();
    } catch (e) {
      console.error(e);
    }
  };

  const cancel = e => {
    console.log(e);
    // message.error('Click on No');
  };
  const columns = [
    {
      title: "编号",
      dataIndex: "number",
      hideInSearch: true
    },
    {
      title: "企业",
      dataIndex: "companyName",
      hideInSearch: true
    },
    {
      title: "姓名",
      dataIndex: "applyName"
    },
    {
      title: "手机号",
      dataIndex: "phone"
    },
    {
      title: "申请时间",
      dataIndex: "applyTime",
      valueType: "date",
      search: false
    },
    {
      title: "申请时间",
      dataIndex: "applyTime",
      valueType: "dateRange",
      hideInTable: true,
      search: {
        transform: (value: string) => {
          return {
            applyStartTime: value[0],
            applyEndTime: value[1]
          };
        }
      }
    },
    {
      title: "审核人",
      dataIndex: "approvalUserName"
    },
    {
      title: "审核时间",
      dataIndex: "approvalTime",
      valueType: "date",
      search: false
    },
    {
      title: "审核时间",
      dataIndex: "approvalTime",
      valueType: "dateRange",
      hideInTable: true,
      search: {
        transform: (value: string) => {
          return {
            approvalStartTime: value[0],
            approvalEndTime: value[1]
          };
        }
      }
    },
    {
      width: "auto",
      title: "操作",
      dataIndex: "option",
      key: "option",
      hideInSearch: true,
      fixed: "right",
      render: (dom: any, record: any) => {
        // console.log('record', record);
        return (
          <Space>
            {record.approvalStatus == 0 ? (
              <Access accessible={access.canUserApprove_refuseAgree}>
                <Space>
                  <Popconfirm
                    title={`您确定同意吗?`}
                    onConfirm={() => onAgreement(record)}
                    icon={<QuestionCircleOutlined style={{ color: "red" }} />}
                    onCancel={cancel}
                    okText="确认"
                    cancelText="取消"
                  >
                    <a>同意</a>
                  </Popconfirm>

                  <a onClick={() => refuse(record)}>拒绝 </a>
                </Space>
              </Access>
            ) : record.approvalStatus == 1 ? (
              <a>已同意</a>
            ) : (
              <a>已拒绝</a>
            )}
          </Space>
        );
      }
    }
  ];

  //获取分页列表
  const [dataMessge, setDataMessage] = useState([]);

  const getUserList = async (params: any) => {
    // companyIds.map((item,index)=>{

    // })
    const dataArray = await getCompanyList();
    dataArray.map(item => {
      setCoId.push(item.coId);
    });

    try {
      const { data } = await getUserApproval({
        ...params,
        systemTypes: 2,
        companyId: currentCompanyInfo.coId
      });
      const { data: applyList } = data;

      console.log(applyList);
      applyList.forEach((item, index) => {
        item.number = index + 1;
      });
      setDataMessage(applyList);
      return {
        ...data,
        data: applyList
      };
    } catch (e) {
      console.error(e);
    }
  };

  //获取企业列表
  const getCompanyList = async () => {
    // const pars = {
    //   userId,
    // };
    try {
      const result = await getCo(scenicId);
      const { data } = result;
      console.log("scenicIdscenicIdscenicIdscenicId");
      console.log(data);
      const children = data.map((item: any) => {
        // return <Option key={item.coId}>{item.coName}</Option>;
        return {
          value: item.coId,
          label: item.coName
        };
      });

      setCompanyData(children);
      return data;
    } catch (e) {
      console.error(e);
    }
  };

  useEffect(() => {
    //企业列表
    // getCompanyList();
    // getUserApprovalList().then(res=>{
    //     console.log('yyds',res.data)
    // })
  }, []);

  return (
    <>
      <ModalForm
        title="用户审批"
        visible={modalVisit}
        form={formObj}
        width={600}
        onFinish={val => getReason({ ...val, text: val.text || " " })}
        onVisibleChange={setModalVisit}
      >
        <ProForm.Group>
          <ProFormTextArea
            width={600}
            name="text"
            label="请填写拒绝的原因"
            placeholder="请输入拒绝的原因"
            fieldProps={{ autoSize: { minRows: 2, maxRows: 6 } }}
          />
        </ProForm.Group>
      </ModalForm>

      <ProTable {...tableConfig} columns={columns} actionRef={actionRef} request={getUserList} rowKey="number" />
    </>
  );
}
