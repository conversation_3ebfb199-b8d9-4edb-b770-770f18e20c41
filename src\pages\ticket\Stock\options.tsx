import { transformStringToArr } from '@/common/utils/tool';

interface Props {
  legend?: object;
  title: string;
  data: any[];
  totalInventory: number | string;
  innerWidth?: number;
}

export const InventoryEnum = {
  digitalAssetsSold: '挂牌数字资产出票',
  digitalAssetsForSale: '挂牌数字资产剩余',
  commonTicketsSold: '普通门票出票',
  commonTicketsForSale: '普通门票剩余',
  blockChainTicketsSold: '普通数字资产出票',
  blockChainTicketsForSale: '普通数字资产剩余',
};

export const options = ({
  legend = {},
  title = '',
  data = [],
  totalInventory = 0,
  innerWidth,
}: Props) => ({
  graphic: {
    type: 'text',
    left: '25%',
    top: 'center',
    style: {
      text:
        '库存总数' + //圆饼中心显示数据，这里是显示得总数
        '\n\n' +
        Number(totalInventory || 0).toLocaleString(),
      textAlign: 'center',
      fill: '#000',
      fontSize: 15,
    },
  },
  tooltip: {
    trigger: 'item',
    textStyle: {
      fontSize: 12,
    },
    formatter: (params) => {
      const arr = transformStringToArr(params?.name);
      return `${params?.seriesName} <br/>${arr?.[0] || ''}: ${arr?.[2] || ''} (${arr?.[1] || ''})`;
    },
  },
  legend: {
    orient: 'vertical',
    right: 0,
    top: 'middle',
    formatter(name) {
      const arr = transformStringToArr(name);
      if (innerWidth && ((innerWidth < 1350 && innerWidth > 1200) || innerWidth < 580)) {
        return `{name|${arr?.[0] || ''}}`;
      }
      return `{name|${arr?.[0] || ''}}\r{percent|| ${arr?.[1] || ''}}\r{value|  ${arr?.[2] || ''}}`;
    },
    textStyle: {
      fontSize: 10,
      rich: {
        name: {
          width: 100,
        },
        percent: {
          color: '#999',
          width: 30,
        },
        value: {},
      },
    },
    ...legend,
  },
  series: [
    {
      name: title,
      type: 'pie',
      minAngle: 1,
      radius: ['57%', '82%'],
      center: ['30%', '50%'],
      padAngle: 3,
      itemStyle: {
        borderRadius: 8,
      },
      labelLine: {
        show: false,
      },
      label: {
        show: false,
      },
      data,
    },
  ],
});
