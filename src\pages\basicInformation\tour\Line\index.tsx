import Delete from "@/common/components/Delete";
import Disabled from "@/common/components/Disabled";
import ProModal from "@/common/components/ProModal";
import useModal from "@/common/components/ProModal/useModal";
import { tableConfig } from "@/common/utils/config";
import { GuideStepStatus } from "@/common/utils/enum";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import { getHashParams, removeStateFromUrl } from "@/common/utils/tool";
import ImageUpload from "@/components/ImageUpload";
import { useGuide } from "@/hooks/useGuide";
import { addLine, deleteLine, editLine, enableLine, infoLine, pageLine } from "@/services/api/tour";
import { PlusOutlined } from "@ant-design/icons";
import type { ProFormColumnsType } from "@ant-design/pro-components";
import type { ActionType, ProColumnType } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import { Button } from "antd";
import { useEffect, useRef, useState } from "react";
import { useModel } from "@umijs/max";
import Map from "./Map";

export default () => {
  const { initialState } = useModel("@@initialState");
  const { scenicId }: any = initialState?.scenicInfo;
  const queryParams = getHashParams();
  const { updateGuideInfo } = useGuide();

  const actionRef = useRef<ActionType>();
  const modalState = useModal();
  const tableColumns: ProColumnType[] = [
    {
      title: "序号",
      valueType: "index"
    },
    {
      title: "线路名称",
      dataIndex: "lineName"
    },
    {
      title: "点位数量",
      dataIndex: "pointNumber",
      search: false
    },
    {
      title: "预计时长（小时）",
      dataIndex: "estimateHour",
      search: false
    },
    {
      title: "预计距离（公里）",
      dataIndex: "estimateDistance",
      search: false
    },
    {
      title: "启用状态",
      dataIndex: "isEnable",
      valueEnum: { 1: "禁用", 2: "启用" },
      fixed: "right",
      renderText: (dom: any, entity: any) => (
        <Disabled
          access={true}
          status={dom == 2}
          params={{
            id: entity.id,
            isEnable: dom == 2 ? 1 : 2
          }}
          request={async params => {
            const data = await enableLine(params);
            // 添加日志
            addOperationLogRequest({
              action: "disable",
              content: `${dom == 2 ? "禁用" : "启用"}【${entity.lineName}】游览线路`
            });
            return data;
          }}
          actionRef={actionRef}
        />
      )
    },
    {
      title: "操作",
      valueType: "option",
      fixed: "right",
      render: (_: any, entity: any) => [
        <a
          onClick={() => {
            setId(entity.id);
            modalState.setType("info");

            addOperationLogRequest({
              action: "info",
              content: `查看【${entity.lineName}】线路详情`
            });
          }}
          key="k1"
        >
          查看
        </a>,
        <a
          onClick={() => {
            setId(entity.id);
            modalState.setType("edit");
          }}
          key="k2"
        >
          编辑
        </a>,
        <Delete
          key="k3"
          access={true}
          status={entity.isEnable == 2}
          params={{ id: entity.id }}
          request={async params => {
            const data = await deleteLine(params);
            // 添加日志
            addOperationLogRequest({
              action: "del",
              content: `删除【${entity.lineName}】游览线路`
            });
            return data;
          }}
          actionRef={actionRef}
        />
      ]
    }
  ];
  const modalColumns: ProFormColumnsType[] = [
    {
      title: "基础信息",
      columns: [
        {
          title: "线路名称",
          dataIndex: "lineName",
          fieldProps: {
            maxLength: 20
          },
          formItemProps: { rules: [{ required: true }] }
        },
        {
          title: "预计时长",
          dataIndex: "estimateHour",
          valueType: "digit",
          fieldProps: {
            precision: 1,
            addonAfter: "小时"
          },
          formItemProps: { rules: [{ required: true }] }
        },
        {
          title: "预计距离",
          dataIndex: "estimateDistance",
          valueType: "digit",
          fieldProps: {
            precision: 1,
            addonAfter: "公里"
          },
          formItemProps: { rules: [{ required: true }] }
        },
        {
          title: "线路图片",
          dataIndex: "publicizeUrl",
          formItemProps: { rules: [{ required: true }] },
          renderText: (text: any) => <ImageUpload defaultValue={text} readonly />,
          renderFormItem: (_, __, formRef) => (
            <ImageUpload defaultValue={formRef.getFieldValue("publicizeUrl")} size={10240} />
          )
        }
      ]
    },
    {
      title: "路径信息",
      columns: [
        {
          title: "地图选点",
          dataIndex: "lineContent",
          colProps: { span: 24 },
          formItemProps: { rules: [{ required: true }] },
          renderText: (text: any, record: any) => text && <Map value={text} type="info" />,
          renderFormItem: () => <Map type={modalState.type} />
        }
      ]
    }
  ];
  const [id, setId] = useState<string | null>();

  const [infoData, setInfoData] = useState<Record<string, any>>();

  const logList = modalColumns[0].columns?.filter(item => item.dataIndex !== "publicizeUrl");

  useEffect(() => {
    if (queryParams?.type) {
      modalState.setType(queryParams?.type);
    }
  }, [queryParams]);

  return (
    <>
      <ProTable
        {...tableConfig}
        rowClassName={row => (row.isEnable == 2 ? "" : "disableRow")}
        style={modalState.tableStytle}
        actionRef={actionRef}
        columns={tableColumns}
        toolBarRender={() => [
          <Button
            key="k1"
            type="primary"
            onClick={() => {
              setId(null);
              modalState.setType("add");
            }}
          >
            <PlusOutlined /> 新增
          </Button>
        ]}
        params={{ scenicId }}
        request={pageLine}
      />
      <ProModal
        page
        {...modalState}
        title="路线"
        actionRef={actionRef}
        columns={modalColumns}
        params={{ id, scenicId }}
        onCancel={() => {
          history.pushState(null, null, removeStateFromUrl("type"));
        }}
        infoRequest={async params => {
          const data = await infoLine(params);

          setInfoData(data.data);

          return data;
        }}
        addRequest={async params => {
          const data = await addLine(params);
          // 更新引导
          updateGuideInfo({ tabIndex: 0, status: GuideStepStatus.step0_5 });
          history.pushState(null, null, removeStateFromUrl("type"));
          addOperationLogRequest({
            action: "add",
            content: `新增【${params.lineName}】游览线路`
          });
          return data;
        }}
        editRequest={async params => {
          const data = await editLine(params);
          addOperationLogRequest({
            action: "edit",
            changeConfig: {
              list: logList,
              beforeData: infoData,
              afterData: params
            },
            content: `编辑【${params.lineName}】游览线路`
          });
          return data;
        }}
      />
    </>
  );
};
