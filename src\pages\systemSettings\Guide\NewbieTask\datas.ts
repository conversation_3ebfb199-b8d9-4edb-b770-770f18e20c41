import { getEnv } from "@/common/utils/getEnv";
const STEP_ITEMS = [
  {
    key: 1,
    label: "完成企业认证",
    isUsed: true,
    desc: "完成认证后可使用收款功能"
  },
  {
    key: 2,
    label: "完成商品创建",
    isUsed: true,
    desc: "创建门票是开启销售的第一步，让游客能快速找到并购买，提升店铺曝光率"
  },
  {
    key: 3,
    label: "完成库存创建",
    isUsed: true,
    desc: "合理的库存设置能确保门票销售顺畅，避免超售或缺票，提升游客满意度"
  },
  {
    key: 4,
    label: "景区点位管理",
    isUsed: true,
    desc: "合理规划景区点位，能优化游客游览路线，提升景区运营效率和服务质量"
  },
  {
    key: 5,
    label: "景区线路管理",
    isUsed: true,
    desc: "完善的线路规划能让游客更好地游览景区，提升游客满意度和景区口碑"
  },
  {
    key: 6,
    label: "销售渠道搭建",
    isUsed: true,
    desc: "前往易旅通电商系统开启你的销售之旅！"
  }
];

const UrlEnum = {
  1: {
    url: "/basic-information/enterprise",
    isUsed: true
  },
  2: {
    url: "/ticket/ticket-type/edit?type=add&tag=goods",
    isUsed: true
  },
  3: {
    url: "/ticket/stock?tabKey=0&type=add",
    isUsed: true
  },
  4: {
    url: "/basic-information/tour/point?type=add",
    isUsed: true
  },
  5: {
    url: "/basic-information/tour/line?type=add",
    isUsed: true
  },
  6: {
    url: getEnv().EXCHANGE_URL,
    type: "full",
    isUsed: true
  }
};

export { STEP_ITEMS, UrlEnum };
