@import './reset.less';
@import './mixin.less';
@import './flex.less';

.margin-small-loop();

.padding-small-loop();

.margin-default-loop();

.padding-default-loop();

.margin-middle-loop();

.padding-middle-loop();

.margin-large-loop();

.padding-large-loop();

html,
body,
#root {
  height: 100%;
}

/* 固定宽度，文字内容过长自动截断，并新增 ... 后缀 */
.text-overflow () {
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.fl {
  float: left;
}

.fr {
  float: right;
}

.tr {
  text-align: right;
}

.tc {
  text-align: center;
}

.inline-block {
  display: inline-block;
}
