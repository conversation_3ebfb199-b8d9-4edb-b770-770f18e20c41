import DetailsPop from '@/common/components/DetailsPop';
import { discountRate, isChainEnum, ticketTypeEnum } from '@/common/utils/enum';
import { getTicketTemplateList } from '@/services/api/settings';
import { apiSimpleGoodsDetail } from '@/services/api/ticket';
import type { ProFormColumnsType } from '@ant-design/pro-components';
import { Space, Tag } from 'antd';
import { isNil } from 'lodash';
import { useEffect, useState } from 'react';
import { createRoot } from 'react-dom/client';
import CheckRuleDetails from '../../../ruleSet/Manage/components/CheckRuleDetails';
import RetreatRuleDetails from '../../../ruleSet/RefundTickets/components/RetreatRuleDetails';
import IssueRuleDetails from '../../../ruleSet/SellTickets/components/IssueRuleDetails';

const Detail = ({ id }: any) => {
  const { scenicId, isBlockChain } = JSON.parse(sessionStorage.getItem('scenicInfo') || '{}');
  const [detailsVisible, setDetailsVisible] = useState<boolean>(false);
  const [isLoading, setLoading] = useState<boolean>(true);
  const [dataSource, setDataSource] = useState<any>({ id: '', isEnable: 0 });
  const columnsInitial: ProFormColumnsType[] = [
    {
      title: '基础信息',
      columns: [
        {
          title: '所属产品名称',
          dataIndex: 'ticketName',
        },
        {
          title: '商品名称',
          dataIndex: 'goodsName',
        },
        {
          title: '票种',
          dataIndex: 'type',
          valueEnum: ticketTypeEnum,
        },
        {
          title: '数字资产',
          dataIndex: 'isDigit',
          valueEnum: isChainEnum,
          hideInDescriptions: isBlockChain != 1,
        },
      ],
    },
    {
      title: '价格信息',
      columns: [
        {
          title: discountRate[dataSource.type],
          render: (_: any, entity: any) => {
            return (
              <span>
                {entity.overallDiscount}%（{entity.marketPrice}元 * {entity.overallDiscount}% ={' '}
                {(entity.marketPrice * (entity.overallDiscount / 100)).toFixed(2)}元）
              </span>
            );
          },
        },
        {
          title: '分销折扣区间',
          render: (_: any, entity: any) => {
            return (
              <span>
                {!isNil(entity.beginDiscount ?? entity.endDiscount)
                  ? `${entity.beginDiscount}% ~ ${entity.endDiscount}%（
                  ${(
                    ((entity.beginDiscount * 1) / 100) *
                    (entity.marketPrice * (entity.overallDiscount / 100))
                  ).toFixed(2)}元
                  ~
                  ${(
                    ((entity.endDiscount * 1) / 100) *
                    (entity.marketPrice * (entity.overallDiscount / 100))
                  ).toFixed(2)}元
                ）`
                  : '-'}
              </span>
            );
          },
        },
      ],
    },
    {
      title: '分时信息',
      columns: [
        {
          title: '分时预约',
          dataIndex: 'timeRestrict',
          valueEnum: {
            0: '否',
            1: '是',
          },
        },
        {
          title: '分时时段',
          dataIndex: 'timeShareVoList',
          renderText: (text: any) => {
            return text ? (
              <Space>
                {text.map((item: any) => (
                  <Tag key={item.id}>{[item.beginTime, item.endTime].join('-')}</Tag>
                ))}
              </Space>
            ) : (
              '-'
            );
          },
        },
      ],
    },
    {
      title: '其他信息',
      columns: [
        {
          title: '是否允许购买数量控制',
          dataIndex: 'isPeopleNumber',
          render: (dom: any) => ['否', '是'][dom],
        },
        {
          title: '最小起订量',
          dataIndex: 'minPeople',
          render: (dom: any, entity: any) => (entity.isPeopleNumber ? dom : '-'),
        },
        {
          title: '单次最大预订量',
          dataIndex: 'maxPeople',
          render: (dom: any, entity: any) => (entity.isPeopleNumber ? dom : '-'),
        },
        // {
        //   title: '使用有效期',
        //   dataIndex: 'validityDay',
        //   render: (dom: any) => <span>游玩日期起 {dom} 天内有效</span>,
        // },
        {
          title: '门票打印模板',
          dataIndex: 'templateName',
          render: (dom: any, entity: any) => {
            console.log(entity, 'entity==');
            if (entity.isRights === 0 && entity.isTravel === 0) {
              return <span>{dom}</span>;
            } else {
              return <span>-</span>;
            }
          },
        },
      ],
    },
    {
      title: '关联规则',
      columns: [
        {
          title: '出票规则',
          dataIndex: 'issueName',
          renderText: (dom: any, entity: any) => (
            <a
              onClick={() => {
                IssueRuleDetails.show(entity.issueId);
              }}
            >
              {dom}
            </a>
          ),
        },
        {
          title: '检票规则',
          dataIndex: 'checkName',
          renderText: (dom: any, entity: any) => (
            <a
              onClick={() => {
                CheckRuleDetails.show(entity.checkId);
              }}
            >
              {dom}
            </a>
          ),
        },
        {
          title: '退票规则',
          dataIndex: 'retreatName',
          renderText: (dom: any, entity: any) => (
            <a
              onClick={() => {
                RetreatRuleDetails.show(entity.retreatId);
              }}
            >
              {dom}
            </a>
          ),
        },
      ],
    },
    {
      title: '说明信息',
      columns: [
        {
          title: '预定须知',
          span: 2,
          dataIndex: 'notice',
        },
        {
          title: '备注',
          dataIndex: 'remark',
        },
      ],
    },
  ];
  const init = async (e: string) => {
    setLoading(true);
    setDetailsVisible(true);
    const data = await apiSimpleGoodsDetail(e);
    // 获取门票模板
    const { data: templateList } = await getTicketTemplateList({
      scenicid: scenicId,
    });
    data.templateName = templateList.find((item: any) => item.id === data.templateId)?.templateName;

    data.useType += '';
    setDataSource(data);
    setLoading(false);
  };
  useEffect(() => {
    init(id);
  }, [id]);

  return (
    <DetailsPop
      title="商品详情"
      visible={detailsVisible}
      isLoading={isLoading}
      setVisible={setDetailsVisible}
      columnsInitial={columnsInitial}
      dataSource={dataSource}
    />
  );
};

Detail.show = (id: string) => {
  const detailBox = document.createElement('div');
  // document.body.appendChild(detailBox);
  createRoot(detailBox).render(<Detail id={id} />);
  // ReactDOM.render(<Detail id={id} />, detailBox);
};

export default Detail;
