import ProModal from "@/common/components/ProModal";
import useModal from "@/common/components/ProModal/useModal";
import { tableConfig } from "@/common/utils/config";
import { StoreGoodsTypeEnum, TicketTypeEnum, whetherEnum } from "@/common/utils/enum";
import { getStoreGoodsTypeText, toValueEnum } from "@/common/utils/tool";
import { getShopGoodsPageList } from "@/services/api/article";
import type { ProFormColumnsType } from "@ant-design/pro-components";
import type { ActionType, ProColumnType } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import { Button, Tag } from "antd";
import { useRef, useState } from "react";
import { useModel } from "@umijs/max";
import { getEnv } from "@/common/utils/getEnv";

/*
 * @Author: bankewei
 * @Date: 2024年7月16日
 */
export default () => {
  // 【景区】信息
  const { initialState } = useModel("@@initialState");
  const { scenicId } = initialState?.scenicInfo || {};

  const actionRef = useRef<ActionType>();
  const actionRef2 = useRef<ActionType>();
  const modalState = useModal();
  const [checkDataSource, setCheckDataSource] = useState<any>();

  const columns: ProColumnType[] = [
    {
      title: "店铺名称",
      dataIndex: "storeName",
      hideInSearch: true
    },
    {
      title: "景区名称",
      dataIndex: "scenicName"
    },
    {
      title: "产品名称",
      dataIndex: "name",
      hideInSearch: true
    },
    {
      title: "商品名称",
      dataIndex: "goodsName"
    },
    {
      title: "类别",
      dataIndex: "storeGoodsType",
      valueEnum: toValueEnum(StoreGoodsTypeEnum),
      hideInSearch: true
    },
    {
      title: "票种",
      dataIndex: "goodsType",
      hideInSearch: true,
      render: (text: any, { unitType, storeGoodsType, goodsType }) => {
        if (storeGoodsType === 2) {
          return text
            .split(",")
            .map((i: any) => TicketTypeEnum[i])
            .join("，");
        }
        return getStoreGoodsTypeText(unitType, storeGoodsType, goodsType);
      }
    },

    {
      title: "数字资产",
      dataIndex: "isDigit",
      valueEnum: whetherEnum
    },

    {
      title: "购买有效时间",
      dataIndex: "purchaseTime",
      hideInSearch: true,
      render: (dom: any, record: any) =>
        record.purchaseBeginTime ? (
          <span>
            {record.purchaseBeginTime} 至 {record.purchaseEndTime}
          </span>
        ) : (
          "-"
        )
    },
    {
      title: "入园有效时间",
      dataIndex: "time",
      hideInSearch: true,
      render: (dom: any, record: any) =>
        record.dayBegin ? (
          <span>
            {record.dayBegin} 至 {record.dayEnd}
          </span>
        ) : (
          "-"
        )
    },
    {
      title: "分时预约时间",
      search: false,
      editable: false,
      render: (_, entity: any) =>
        entity.timeShareBeginTime && entity.timeShareEndTime
          ? entity.timeShareBeginTime + " - " + entity.timeShareEndTime
          : "-"
    },
    {
      title: "供应商名称",
      dataIndex: "supplierNames",
      search: false
    },
    {
      title: "可用库存",
      dataIndex: "quantity",
      hideInSearch: true,
      align: "right",
      valueType: "digit"
    },
    {
      title: "用户售价（元）",
      dataIndex: "sellingPrice",
      align: "right",
      renderText: dom => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
      valueType: "digit",
      hideInSearch: true
    },
    {
      title: "启用状态",
      dataIndex: "goodsStatus",
      hideInSearch: true,
      fixed: "right",
      render: (dom: any) => <Tag color={dom ? "blue" : "red"}>{dom ? "启用" : "禁用"}</Tag>
    },
    {
      title: "上架状态",
      dataIndex: "upDown",
      hideInSearch: true,
      fixed: "right",
      render: (dom: any) => <Tag color={dom == 1 ? "blue" : "red"}>{dom == 1 ? "上架" : "下架"}</Tag>
    },
    {
      title: "操作",
      valueType: "option",
      fixed: "right",
      render: (_: any, entity: any) => [
        <a
          onClick={() => {
            setCheckDataSource(entity);
            modalState.setType("info");
          }}
        >
          查看
        </a>
      ]
    }
  ];

  // 查看表单的配置
  const showColumns: ProFormColumnsType<any>[] = [
    {
      title: "基本信息",
      valueType: "group",
      columns: [
        {
          title: "店铺名称",
          dataIndex: "storeName"
        },
        {
          title: "景区名称",
          dataIndex: "scenicName"
        },
        {
          title: "产品名称",
          dataIndex: "name"
        },
        {
          title: "商品名称",
          dataIndex: "goodsName"
        },
        {
          title: "类别",
          dataIndex: "storeGoodsType",
          valueEnum: toValueEnum(StoreGoodsTypeEnum)
        },
        {
          title: "票种",
          dataIndex: "goodsType",
          render: (text: any, { unitType, storeGoodsType, goodsType }) => {
            if (storeGoodsType === 2) {
              return text
                .split(",")
                .map((i: any) => TicketTypeEnum[i])
                .join("，");
            }
            return getStoreGoodsTypeText(unitType, storeGoodsType, goodsType);
          }
        },
        {
          title: "数字资产",
          dataIndex: "isDigit",
          valueEnum: whetherEnum
        },
        {
          title: "购买有效时间",
          dataIndex: "purchaseTime",
          hideInSearch: true,
          render: (dom: any, record: any) =>
            record.purchaseBeginTime ? (
              <span>
                {record.purchaseBeginTime} 至 {record.purchaseEndTime}
              </span>
            ) : (
              "-"
            )
        },
        {
          title: "入园有效时间",
          dataIndex: "time",
          hideInSearch: true,
          render: (dom: any, record: any) =>
            record.dayBegin ? (
              <span>
                {record.dayBegin} 至 {record.dayEnd}
              </span>
            ) : (
              "-"
            )
        },
        {
          title: "分时预约时间",
          editable: false,
          render: (_, entity: any) =>
            entity.timeShareBeginTime && entity.timeShareEndTime
              ? entity.timeShareBeginTime + " - " + entity.timeShareEndTime
              : "-"
        },
        {
          title: "供应商名称",
          dataIndex: "supplierNames"
        }
      ]
    },
    {
      title: "价格信息",
      valueType: "group",
      columns: [
        {
          title: "可用库存",
          dataIndex: "quantity",
          valueType: "digit"
        },
        {
          title: "用户售价（元）",
          dataIndex: "sellingPrice",
          renderText: dom => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
          valueType: "digit"
        }
      ]
    },
    {
      title: "其它信息",
      valueType: "group",
      columns: [
        {
          title: "启用状态",
          dataIndex: "goodsStatus",
          render: (dom: any) => <Tag color={dom ? "blue" : "red"}>{dom ? "启用" : "禁用"}</Tag>
        },
        {
          title: "上架状态",
          dataIndex: "upDown",
          render: (dom: any) => <Tag color={dom == 1 ? "blue" : "red"}>{dom == 1 ? "上架" : "下架"}</Tag>
        }
      ]
    }
  ];

  return (
    <>
      <ProTable<any>
        {...tableConfig}
        rowKey="id"
        actionRef={actionRef}
        params={{ scenicId, officialRecommend: 2 }}
        request={async params => {
          try {
            const { data } = await getShopGoodsPageList(params);
            return data;
          } catch (error) {
            return { data: [], total: 0 };
          }
        }}
        columns={columns}
        toolBarRender={() => [
          <Button
            type="primary"
            onClick={() => {
              location.href = `${getEnv().EXCHANGE_URL}#/business/my-shop`;
            }}
          >
            管理商品
          </Button>
        ]}
      />

      <ProModal
        {...modalState}
        title="商品"
        actionRef={actionRef2}
        columns={showColumns}
        params={{}}
        dataSource={checkDataSource}
      />
    </>
  );
};
