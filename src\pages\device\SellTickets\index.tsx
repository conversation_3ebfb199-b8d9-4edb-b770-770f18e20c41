import DetailsPop from "@/common/components/DetailsPop";
import EditPop from "@/common/components/EditPop";
import { tableConfig } from "@/common/utils/config";
import { enableEnum } from "@/common/utils/enum";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import { getUniqueId } from "@/common/utils/tool";
import useModal from "@/hooks/useModal";
import {
  addTicketOffice,
  delTicket,
  editTicketOffice,
  getTicketOffice,
  getTicketPageList,
  setTicketOfficeStatus
} from "@/services/api/device";
import { InfoCircleTwoTone, PlusOutlined } from "@ant-design/icons";
import type { ProFormColumnsType } from "@ant-design/pro-components";
import type { ActionType, ProColumns } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import { Button, Modal, Space, Switch, message } from "antd";
import React, { useEffect, useRef, useState } from "react";
import { Access, useAccess, useModel, useRequest } from "@umijs/max";

const TableList: React.FC = () => {
  const access = useAccess();
  const [currentRow, setCurrentRow] = useState<Record<string, any>>();
  const { initialState } = useModel("@@initialState");
  const { scenicId, scenicName }: any = initialState?.scenicInfo;
  const actionRef = useRef<ActionType>();
  const { visible, setVisible, setTypeWithVisible, type } = useModal();

  const {
    data: dataSource,
    loading,
    run,
    reset
  } = useRequest(getTicketOffice, {
    manual: true
  });

  const columns: ProColumns[] = [
    {
      title: "编号",
      dataIndex: "id",
      hideInSearch: true
    },
    {
      title: "售票点名称",
      dataIndex: "name"
    },
    {
      title: "所属景区",
      dataIndex: "scenicName",
      hideInSearch: true
    },
    {
      title: "联系人",
      dataIndex: "contracts"
    },
    {
      title: "联系方式",
      dataIndex: "contractsPhone",
      hideInSearch: true
    },
    {
      title: "启用状态",
      dataIndex: "isEnable",
      fixed: "right",
      valueEnum: enableEnum,
      renderText: (dom: any, entity: any) => (
        <Switch
          disabled={!access.canSalesSite_openClose}
          checkedChildren="启用"
          unCheckedChildren="禁用"
          checked={!!dom}
          onChange={() => {
            setTicketOfficeStatus({
              officeId: entity.id,
              isEnable: 1 - entity.isEnable,
              scenicId
            })
              .then(() => {
                message.success(dom ? "已禁用" : "已启用");
                // 添加日志
                addOperationLogRequest({
                  action: "disable",
                  content: `${dom == 1 ? "禁用" : "启用"}【${entity.name}】售票点`
                });
                actionRef.current?.reload();
              })
              .catch(() => {});
          }}
        />
      )
    },
    {
      title: "操作",
      valueType: "option",
      fixed: "right",
      render: (_: any, record: any) => (
        <Space size="large">
          <a
            onClick={async () => {
              setTypeWithVisible("info");
              setCurrentRow(record);
              // 添加日志
              addOperationLogRequest({
                action: "info",
                content: `查看【${record.name}】售票点详情`
              });
            }}
          >
            查看
          </a>
          <Access accessible={access.canSalesSite_edit}>
            <a
              onClick={() => {
                setCurrentRow(record);
                setTypeWithVisible("update");
              }}
            >
              编辑
            </a>
          </Access>
          <Access accessible={access.canSalesSite_delete}>
            <a
              style={{ color: "red" }}
              onClick={async () => {
                if (record.isEnable) {
                  Modal.warning({
                    title: "不可删除",
                    content: "请先禁用后删除"
                  });
                  return;
                }
                Modal.confirm({
                  title: "确认删除吗？",
                  icon: <InfoCircleTwoTone />,
                  content: "删除后不可恢复",
                  okText: "确认",
                  cancelText: "取消",
                  onOk: () => {
                    delTicket(record.id)
                      .then(() => {
                        message.success("删除成功");
                        // 添加日志
                        addOperationLogRequest({
                          action: "del",
                          content: `删除【${record.name}】售票点详情`
                        });
                        actionRef.current?.reload();
                      })
                      .catch(() => {});
                  }
                });
              }}
            >
              删除
            </a>
          </Access>
        </Space>
      )
    }
  ];
  /* 详情 */
  const columnsInitial = [
    {
      title: "基础信息",
      columns: [
        {
          title: "所属景区",
          dataIndex: "scenicName"
        },
        {
          title: "联系人",
          dataIndex: "contracts"
        },
        {
          title: "售票点名称",
          dataIndex: "name"
        },
        {
          title: "联系方式",
          dataIndex: "contractsPhone"
        }
      ]
    },
    {
      title: "地理信息",
      columns: [
        {
          title: "实际地址",
          dataIndex: "address"
        }
      ]
    },
    {
      title: "说明信息",
      columns: [
        {
          title: "备注",
          dataIndex: "remark"
        }
      ]
    }
  ];

  const editColumns: ProFormColumnsType<any>[] = [
    {
      title: "基础信息",
      columns: [
        {
          title: "所属景区",
          dataIndex: "scenicId",
          initialValue: scenicId,
          valueType: "select",
          fieldProps: {
            disabled: true
          },
          valueEnum: {
            [scenicId]: scenicName
          }
        },
        {
          title: "联系人",
          dataIndex: "contracts",
          formItemProps: {
            rules: [{ required: true, max: 20 }]
          }
        },
        {
          title: "售票点名称",
          dataIndex: "name",
          formItemProps: {
            rules: [{ required: true, max: 30 }]
          }
        },
        {
          title: "联系方式",
          dataIndex: "contractsPhone",
          formItemProps: {
            rules: [
              { required: true, message: "请输入联系方式" },
              {
                pattern: /^\d+(-\d+)*$/,
                message: "不合法的手机号"
              }
            ]
          }
        }
      ]
    },
    {
      title: "地理信息",
      columns: [
        {
          title: "实际地址",
          dataIndex: "address",
          formItemProps: {
            rules: [{ max: 50 }]
          }
        }
      ]
    },
    {
      title: "说明信息",
      columns: [
        {
          title: "描述",
          dataIndex: "remark",
          colProps: {
            span: 24
          },
          valueType: "textarea",
          formItemProps: {
            rules: [{ max: 100 }],
            style: {
              height: 100
            }
          }
        }
      ]
    }
  ];

  const logList = [...editColumns[0].columns, ...editColumns[1].columns];

  useEffect(() => {
    if (visible && currentRow && (type === "info" || type === "update")) {
      run({
        id: currentRow?.id
      });
    }
    return () => {
      reset();
    };
  }, [visible, type, currentRow?.id]);

  return (
    <>
      <ProTable<API.RuleListItem, API.PageParams>
        {...tableConfig}
        rowClassName={(row: any) => (row.isEnable == 1 ? "" : "disableRow")}
        actionRef={actionRef}
        toolBarRender={() => [
          <Access key={getUniqueId()} accessible={access.canSalesSite_insert}>
            <Button
              type="primary"
              key="primary"
              onClick={() => {
                setCurrentRow(undefined);
                setTypeWithVisible("add");
              }}
            >
              <PlusOutlined /> 新增
            </Button>
          </Access>
        ]}
        params={{ scenicId }}
        request={getTicketPageList}
        columns={columns}
      />

      <EditPop
        title={"售票点"}
        visible={type !== "info" && visible}
        setVisible={setVisible}
        columns={editColumns}
        dataSource={dataSource}
        onFinish={async value => {
          if (type === "add") {
            await addTicketOffice(value);
            addOperationLogRequest({
              action: "add",
              content: `新增【${value.name}】售票点`
            });
          } else if (type === "update") {
            await editTicketOffice({ ...value, id: currentRow?.id });
            addOperationLogRequest({
              action: "edit",
              changeConfig: {
                list: logList,
                beforeData: dataSource,
                afterData: value
              },
              content: `编辑【${value.name}】售票点`
            });
          }
          setVisible(false);
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      <DetailsPop
        isLoading={loading}
        title="售票点详情"
        visible={type === "info" && visible}
        setVisible={setVisible}
        columnsInitial={columnsInitial}
        dataSource={dataSource}
      />
    </>
  );
};

export default TableList;
