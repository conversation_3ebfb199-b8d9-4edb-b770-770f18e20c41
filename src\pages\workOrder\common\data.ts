/*
 * @Author: z<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-27 10:43:22
 * @LastEditTime: 2022-10-13 15:25:02
 * @LastEditors: zhangfengfei
 */

export interface FormValuesType {
  workOrderName: string;
  workOrderType: WorkOrderTypeEnum;
  approverType: ApproverTypeEnum;
  specifyApproverId?: string;
  nodeApproverType: ApproverRuleTypeEnum;
  sponsorCount?: number;
}

export interface WorkOrderTempType {
  approverType: number;
  createTime: string;
  createUser: string;
  id: string;
  isDelete: number;
  modifyTime: string;
  nodeApproverType: number;
  nodeData: NodeDataItem[];
  providerId: string;
  scenicId: string;
  specifyApproverId?: string;
  sponsorCount?: number;
  state: number;
  updateUser: string;
  workOrderName: string;
  workOrderType: WorkOrderTypeEnum;
}

export interface NodeDataItem {
  createTime?: string;
  createUser?: string;
  id: string;
  isDelete: number;
  modifyTime: string;
  nodeScriptData: NodeScriptDataItem[];
  templateId: string;
  updateUser: string;
  participateMode?: ParticipateModeEnum;
  approvalMode?: ApprovalModeEnum;
  approverType?: NodeApproverTypeEnum;
  departmentAppeoverId?: string;
  roleApproverId?: string;
  singleApproverId: string;
  nodeOperationId: NodeOperationEnum;
}

export interface NodeScriptDataItem {
  createTime: string;
  createUser: string;
  id: string;
  isDelete: number;
  modifyTime: string;
  nodeId: string;
  name: string;
  nodeOperationId: number;
  scriptId: string;
  updateUser: string;
}

/* 工单 enum */

/** 工单模板状态 */
export enum WorkOrderStateEnum {
  启用,
  禁用,
}

/** 工单类型 */
export enum WorkOrderTypeEnum {
  商品价格编辑审批 = 1,
}

/** 审批人类型 */
export enum ApproverTypeEnum {
  默认同意 = 1,
  指定人员处理,
}

/** 规则审批类型 */
export enum ApproverRuleTypeEnum {
  预设人选 = 1,
  由发起人确定,
}

/** 参与方式 */
export enum ParticipateModeEnum {
  单人 = 1,
  多人,
}

/** 节点审批方式*/
export enum ApprovalModeEnum {
  会签 = 1,
  或签,
}

/** 节点审批人方式*/
export enum NodeApproverTypeEnum {
  指定角色 = 1,
  指定成员或部门,
}

/** 节点操作 */
export enum NodeOperationEnum {
  同意 = 1,
  同意并加签,
  拒绝,
}

// 工单状态
export enum WorkOrderStatusEnum {
  待审批 = 1,
  已同意,
  已拒绝,
}
// 节点或者节点审批人审批状态
export enum ApprovalStatusEnum {
  待审批 = 1,
  已同意,
  已拒绝,
  加签,
  已审批 = 9,
}
