import Delete from "@/common/components/Delete";
import Disabled from "@/common/components/Disabled";
import useModal from "@/common/components/ProModal/useModal";
import { tableConfig } from "@/common/utils/config";
import { AIKnowledgeBaseFileStatus, AIKnowledgeBaseIdentifyStatus } from "@/common/utils/enum";
import { modelWidth } from "@/common/utils/gConfig";
import {
  apiDelKnowledge,
  apiDelKnowledgeFile,
  apiEnableKnowledge,
  apiGetKnowledgeFileList,
  apiGetKnowledgeInfo,
  apiGetKnowledgePage,
  apiSaveKnowledge
} from "@/services/api/service";
// import { getShopGoodsPageList } from '@/services/api/service';
import { ProForm, ProFormText } from "@ant-design/pro-components";
import type { ActionType, ProColumnType } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import { Button, message, Modal, Table, Tag } from "antd";
import { useRef, useState } from "react";
import { useModel } from "@umijs/max";
import UploadKnowledgeFile from "./UploadKnowledgeFile";

export default () => {
  // 【景区】信息
  const { initialState } = useModel("@@initialState");
  const { scenicId } = initialState?.scenicInfo || {};
  const [uploadNum, setUploadNum] = useState(0);
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ActionType>();
  const modalState = useModal();
  const [checkDataSource, setCheckDataSource] = useState<any>();
  const [uploadKnowledgeVisible, setUploadKnowledgeVisible] = useState(false);
  const [fileListVisible, setFileListVisible] = useState(false);
  const [currentKnowledgeId, setCurrentKnowledgeId] = useState<string>("");
  const [fileList, setFileList] = useState<any[]>([]);
  const [uploadStatus, setUploadStatus] = useState<"success" | "error" | null>(null);
  // 添加分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });

  // 获取文件列表的接口
  const getFileList = async (knowledgeId: string) => {
    const hide = message.loading("获取文件列表");
    const res = await apiGetKnowledgeInfo({ id: knowledgeId });
    if (res.code === 20000 && res.data) {
      // 获取文件列表详情
      const fileIds = res.data.fileIds?.split(",");
      const { data } = await apiGetKnowledgeFileList({
        ids: fileIds
      });
      // 按更新时间倒序排序
      const sortedData = data.sort((a: any, b: any) => {
        return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime();
      });
      setFileList(data);
      // 更新分页总数
      setPagination(prev => ({
        ...prev,
        total: data.length
      }));
      hide();
    }
  };

  // 处理上传成功
  const handleUploadSuccess = async () => {
    message.success("文件上传成功");
    // 上传成功后触发表格重新渲染
    actionRef.current?.reload();
    // 获取最新的知识库信息
    getFileList(currentKnowledgeId);
  };

  const handleDeleteFile = async (fileId: string) => {
    try {
      const res = await apiDelKnowledgeFile({ fileId: fileId, id: currentKnowledgeId });
      message.success("删除成功");
      getFileList(currentKnowledgeId);
      actionRef.current?.reload();
    } catch (error) {
      message.error("删除失败");
    }
  };

  const fileColumns = [
    {
      title: "文件名称",
      dataIndex: "original_name"
    },
    {
      title: "状态",
      dataIndex: "recognition_status",
      render: text => <Tag color={AIKnowledgeBaseFileStatus[text].color}>{AIKnowledgeBaseFileStatus[text].text}</Tag>
    },
    {
      title: "文件大小",
      dataIndex: "size",
      render: (size: number) => {
        if (size < 1024) {
          return `${size} B`;
        } else if (size < 1024 * 1024) {
          return `${(size / 1024).toFixed(2)} KB`;
        } else {
          return `${(size / (1024 * 1024)).toFixed(2)} MB`;
        }
      }
    },
    {
      title: "操作",
      key: "action",
      render: (_: any, record: any) => (
        <a
          style={{ color: "red" }}
          onClick={() => {
            Modal.confirm({
              title: "确认删除",
              content: "确定要删除该文件吗？",
              onOk: () => handleDeleteFile(record.id)
            });
          }}
        >
          删除
        </a>
      )
    }
  ];

  const columns: ProColumnType[] = [
    {
      title: "知识库名称",
      dataIndex: "knowledgeBaseName"
    },
    {
      title: "文件数量",
      dataIndex: "fileIds",
      render: (fileIds: string, record: any) => {
        if (!fileIds || fileIds === "--") {
          return "--";
        }
        const count = fileIds.split(",").length;
        return (
          <a
            style={{ color: "#1890ff" }}
            onClick={() => {
              setCurrentKnowledgeId(record.id);
              setFileListVisible(true);
              getFileList(record.id);
            }}
          >
            {count}
          </a>
        );
      }
    },
    {
      title: "启用状态",
      dataIndex: "isEnable",
      valueEnum: AIKnowledgeBaseIdentifyStatus,
      renderText: (dom: any, entity: any) => (
        <Disabled
          access={true}
          status={dom == "START"}
          params={{
            id: entity.id,
            isEnable: dom == "START" ? "DISABLE" : "START"
          }}
          request={async (params: any) => {
            const data = await apiEnableKnowledge(params);
            return data;
          }}
          actionRef={actionRef}
        />
      )
    },
    {
      title: "操作",
      valueType: "option",
      render: (_: any, entity: any) => [
        <a
          onClick={() => {
            setCurrentKnowledgeId(entity.id);
            setFileListVisible(true);
            // 获取文件列表
            console.log("entity.fileIds:", entity);
            const fileIds = entity.fileIds?.split(",");
            if (fileIds) {
              getFileList(entity.id);
            }
          }}
          key="k1"
        >
          上传文件
        </a>,
        <Delete
          key="k3"
          access={true}
          status={entity.enableState == 2}
          params={{ id: entity.id }}
          request={async (params: any) => {
            await apiDelKnowledge(params);
          }}
          actionRef={actionRef}
        />
      ]
    }
  ];

  // 处理分页变化
  const handleTableChange = (pagination: any) => {
    setPagination(pagination);
  };

  return (
    <>
      <ProTable<any>
        {...tableConfig}
        options={{
          reload: false,
          density: false,
          setting: false
        }}
        rowKey="id"
        actionRef={actionRef}
        search={false}
        params={{ businessId: scenicId }}
        request={apiGetKnowledgePage}
        columns={columns}
        toolBarRender={() => [
          <Button
            type="primary"
            onClick={() => {
              setUploadKnowledgeVisible(true);
            }}
          >
            创建知识库
          </Button>
        ]}
      />
      <Modal
        title={
          <div>
            上传文档{" "}
            <span style={{ fontSize: "12px", color: "#bfbfbf" }}>
              使用模型高效地解析您的文档，快速从上传文档中发现 FAQ 知识和知识图谱并应用于智能客服问答中
            </span>{" "}
          </div>
        }
        width={modelWidth.md}
        destroyOnClose={false}
        onCancel={() => {
          formRef.current?.resetFields();
          setUploadKnowledgeVisible(false);
        }}
        onFinish={() => {}}
        open={uploadKnowledgeVisible}
        onOk={() => {
          formRef.current?.submit?.();
        }}
      >
        <ProForm
          formRef={formRef}
          name="basic"
          onFinish={async values => {
            console.log("表单数据：", values);
            const hide = message.loading("创建中");
            await apiSaveKnowledge({
              businessId: scenicId,
              ...values
            });
            setUploadKnowledgeVisible(false);
            actionRef.current?.reload();
            formRef.current?.resetFields();
            hide();
            message.success("创建成功");
            return true;
          }}
          // labelCol={{ span: 8 }}
          // wrapperCol={{ span: 16 }}
          style={{ width: "100%" }}
          autoComplete="off"
          submitter={false}
        >
          <ProFormText
            label="知识库名称"
            name="knowledgeBaseName"
            rules={[
              { required: true, message: "请输入知识库名称" },
              { max: 10, message: "不能超过10个字符" }
            ]}
          />
          {/* <ProFormItem
            name="fileIds"
            label="上传文档"
            rules={[{ required: true, message: '请上传文档' }]}
          >
            <UploadFile
              // listType="picture-card"
              maxCount={200}
              size={100}
              accept=".pdf,.doc,.docx,.txt,.ppt,.png,.jpg,.jpeg"
              dragger={true}
              onChange={(fileList) => {
                setUploadNum(fileList.split(',').length);
              }}
              dragContent={
                <div>
                  <p className="ant-upload-drag-icon">
                    <InboxOutlined />
                  </p>
                  <p className="ant-upload-text">
                    请上传 .pdf .doc .docx .txt .ppt 格式的文件，大小在 100M 或 1000 页以内
                  </p>
                  <p className="ant-upload-hint">
                    点击或拖拽文件到此处，支持批量上传，最多支持（{uploadNum}/200）个
                  </p>
                </div>
              }
            />
          </ProFormItem> */}
        </ProForm>
      </Modal>
      <Modal
        title="文件列表"
        open={fileListVisible}
        onCancel={() => {
          setFileListVisible(false);
          setFileList([]);
        }}
        width={modelWidth.md}
        footer={[
          <UploadKnowledgeFile
            key="upload"
            accept=".pdf,.doc,.docx,.txt,.ppt"
            size={100}
            knowledgeBaseId={currentKnowledgeId}
            onSuccess={value => {
              console.log("上传成功", value);
              if (value) {
                handleUploadSuccess();
              }
            }}
          />
        ]}
      >
        <Table
          columns={fileColumns}
          dataSource={fileList}
          rowKey="fileName"
          pagination={{
            ...pagination,
            showSizeChanger: false,
            showQuickJumper: false,
            showTotal: total => `共 ${total} 条记录`
          }}
          onChange={handleTableChange}
        />
      </Modal>
    </>
  );
};
