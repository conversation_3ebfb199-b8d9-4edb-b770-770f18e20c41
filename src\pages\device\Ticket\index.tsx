import ProModal from "@/common/components/ProModal";
import useModal from "@/common/components/ProModal/useModal";
import { tableConfig } from "@/common/utils/config";
import { checkDeviceType, enableEnum } from "@/common/utils/enum";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import { getAllPath, getUniqueId } from "@/common/utils/tool";
import {
  checkEquipmentSimpleList,
  delCheckEquipment,
  getCheckEquipmentPageList,
  setCheckEquipmentStatus
} from "@/services/api/device";
import { addCheckDevice, editCheckDevice, infoCheckDevice } from "@/services/api/facility";
import { apiScenicConfig } from "@/services/api/settings";
import { InfoCircleTwoTone, PlusOutlined } from "@ant-design/icons";
import type { ProFormColumnsType } from "@ant-design/pro-components";
import type { ActionType, ProColumns } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import { useRequest } from "@umijs/max";
import { Button, Modal, Space, Switch, message } from "antd";
import React, { useEffect, useRef, useState } from "react";
import { Access, useAccess, useModel } from "@umijs/max";

const TableList: React.FC = () => {
  const modalState = useModal();
  const [face, setFace] = useState<boolean>(false);
  const { initialState } = useModel("@@initialState");
  const { scenicId, scenicName }: any = initialState?.scenicInfo;
  const access = useAccess();
  const [optionId, handleOptionId] = useState<string>("");
  const actionRef = useRef<ActionType>();

  // 获取检票点
  const getCheckInPointReq = useRequest(checkEquipmentSimpleList, {
    manual: true,
    initialData: [],
    formatResult: res => {
      const _list: any = {};
      (res || []).forEach(item => {
        _list[item.value] = item.label;
      });
      return _list;
    }
  });

  const tableColumns: ProColumns[] = [
    {
      title: "检票设备编号",
      dataIndex: "id",
      hideInSearch: true,
      fixed: "left"
    },
    {
      title: "检票设备名称",
      fixed: "left",
      dataIndex: "name"
    },

    {
      title: "所属检票点",
      dataIndex: "checkInName",
      hideInSearch: true
    },
    {
      title: "检票设备类型",
      dataIndex: "type",
      hideInSearch: true,
      valueEnum: checkDeviceType
      // convertValue: (value) => String(value ?? '') || value,
      // transform: (value) => value && Number(value),
      // formItemProps: { rules: [{ required: true }] },
    },
    {
      title: "MAC 地址",
      dataIndex: "mac",
      hideInSearch: true
      // formItemProps: { rules: [{ max: 48 }] },
    },
    {
      title: "IP 地址",
      dataIndex: "equIp",
      hideInSearch: true
    },
    {
      title: "支持人脸设备",
      dataIndex: "isFace",
      render: (dom: any) => (dom ? "是" : "否"),
      hideInSearch: true
    },
    {
      title: "启用状态",
      dataIndex: "isEnable",
      fixed: "right",
      valueEnum: enableEnum,
      renderText: (dom: any, entity: any) => (
        <Switch
          disabled={!access.canCheckDevice_openClose}
          checkedChildren="启用"
          unCheckedChildren="禁用"
          checked={!!dom}
          onChange={() => {
            setCheckEquipmentStatus({
              equipmentId: entity.id,
              isEnable: 1 - entity.isEnable,
              scenicId
            })
              .then(() => {
                // 添加日志
                addOperationLogRequest({
                  action: "disable",
                  content: `${dom == 1 ? "禁用" : "启用"}【${entity.name}】检票设备`
                });

                message.success(dom ? "已禁用" : "已启用");
                actionRef.current?.reload();
              })
              .catch(() => {});
          }}
        />
      )
    },
    {
      title: "操作",
      valueType: "option",
      fixed: "right",
      render: (_: any, entity: any) => (
        <Space size="large">
          <a
            onClick={async () => {
              handleOptionId(entity.id);
              modalState.setType("info");

              // 添加日志
              addOperationLogRequest({
                action: "info",
                content: `查看【${entity.name}】检票设备详情`
              });
            }}
          >
            查看
          </a>
          <Access accessible={access.canCheckDevice_edit}>
            <a
              onClick={async () => {
                handleOptionId(entity.id);
                modalState.setType("edit");
              }}
            >
              编辑
            </a>
          </Access>
          <Access accessible={access.canCheckDevice_delete}>
            <a
              style={{ color: "red" }}
              onClick={async () => {
                if (entity.isEnable) {
                  Modal.warning({
                    title: "不可删除",
                    content: "请先禁用后删除"
                  });
                  return;
                }
                Modal.confirm({
                  title: "确认删除吗？",
                  icon: <InfoCircleTwoTone />,
                  content: "删除后不可恢复",
                  okText: "确认",
                  cancelText: "取消",
                  onOk: () => {
                    delCheckEquipment(entity.id)
                      .then(() => {
                        message.success("删除成功");
                        actionRef.current?.reload();

                        // 添加日志
                        addOperationLogRequest({
                          action: "del",
                          content: `删除【${entity.name}】检票设备`
                        });
                      })
                      .catch(() => {});
                  }
                });
              }}
            >
              删除
            </a>
          </Access>
        </Space>
      )
    }
  ];

  const faceColumns = [
    {
      title: "人脸端口",
      dataIndex: "facePoint",
      colProps: { xs: 24, sm: 12 }
    },
    {
      title: "人脸 MAC 设备地址",
      dataIndex: "faceMac",
      colProps: { xs: 24, sm: 12 }
    },
    {
      title: "人脸设备地址",
      dataIndex: "faceAddress",
      colProps: { xs: 24, sm: 12 }
    },
    {
      title: "人脸 IP 设备地址",
      dataIndex: "faceIp",
      colProps: { xs: 24, sm: 12 }
    },
    {
      title: "人脸设备编号",
      dataIndex: "faceNumber",
      colProps: { xs: 24, sm: 12 }
    }
  ];

  const modalColumns: ProFormColumnsType[] = [
    {
      title: "基础信息",
      columns: [
        {
          title: "所属景区",
          dataIndex: "scenicId",
          valueEnum: { [scenicId]: scenicName },
          initialValue: scenicId,
          fieldProps: { disabled: true },
          formItemProps: { rules: [{ required: true }] }
        },
        {
          title: "型号",
          dataIndex: "equModel",
          formItemProps: { rules: [{ max: 100 }] }
        },
        {
          title: (
            <div className="flex justify-content-between w-100">
              <span>所属检票点</span>
              <a
                style={{ display: modalState.type == "info" ? "none" : "initial" }}
                onClick={() => {
                  localStorage.setItem("pageOperateType", "add");
                  window.open(`${getAllPath()}/infrastructure/device/ticketsys`, "_blank");
                }}
              >
                新增
              </a>
            </div>
          ),
          dataIndex: "checkInId",
          valueType: "select",
          params: { scenicId },
          valueEnum: getCheckInPointReq.data,
          // request: checkEquipmentSimpleList,
          fieldProps: {
            showSearch: true,
            loading: getCheckInPointReq.loading,
            onDropdownVisibleChange: e => {
              if (e) {
                getCheckInPointReq.run({ scenicId });
              }
            }
          },
          formItemProps: { rules: [{ required: true, message: "请选择所属检票点" }] }
        },
        {
          title: "MAC 地址",
          dataIndex: "equMac",
          formItemProps: { rules: [{ max: 48 }] }
        },
        {
          title: "检票设备名称",
          dataIndex: "name",
          formItemProps: { rules: [{ required: true, max: 30 }] }
        },
        {
          title: "IP 地址",
          dataIndex: "equIp",
          formItemProps: { rules: [{ max: 32 }] }
        },
        {
          title: "厂商",
          dataIndex: "manufacturerId",
          formItemProps: { rules: [{ max: 30 }] }
        },
        {
          title: "检票设备类型",
          dataIndex: "type",
          valueEnum: checkDeviceType,
          convertValue: value => String(value ?? "") || value,
          // transform: (value) => value && Number(value),
          formItemProps: { rules: [{ required: true }] }
        }
      ]
    },
    {
      title: "人脸设备信息",
      hideInForm: !face,
      hideInDescriptions: !face,
      columns: [
        {
          title: "支持人脸设备",
          dataIndex: "isFace",
          valueType: "switch",
          convertValue: value => !!value,
          transform: value => (value ? 1 : 0),
          fieldProps: {
            checkedChildren: "是",
            unCheckedChildren: "否"
          }
        },
        {
          hideInDescriptions: true,
          valueType: "dependency",
          name: ["isFace"],
          columns: ({ isFace }) => (isFace ? faceColumns : [])
        }
      ]
    },
    {
      title: "说明信息",
      columns: [
        {
          title: "备注",
          dataIndex: "remark",
          valueType: "textarea",
          colProps: { span: 24 }
        }
      ]
    }
  ];

  useEffect(() => {
    (async () => {
      const data = await apiScenicConfig({ id: scenicId });
      setFace(data.isFace);
    })();

    getCheckInPointReq.run({ scenicId });
  }, []);

  const [infoData, setInfoData] = useState<Record<string, any>>();

  const logList = [
    ...modalColumns[0].columns,
    {
      title: "是否支持人脸设备",
      dataIndex: "isFace",
      valueEnum: {
        0: "否",
        1: "是"
      }
    },
    ...faceColumns
  ];

  return (
    <>
      <ProTable<API.RuleListItem, API.PageParams>
        {...tableConfig}
        rowClassName={(row: any) => (row.isEnable == 1 ? "" : "disableRow")}
        actionRef={actionRef}
        toolBarRender={() => [
          <Access key={getUniqueId()} accessible={access.canCheckDevice_insert}>
            <Button
              type="primary"
              key="primary"
              onClick={() => {
                handleOptionId("");
                modalState.setType("add");
              }}
            >
              <PlusOutlined /> 新增
            </Button>
          </Access>
        ]}
        params={{ scenicId }}
        request={getCheckEquipmentPageList}
        columns={tableColumns}
      />
      <ProModal
        title="检票设备"
        {...modalState}
        columns={modalColumns}
        params={{ id: optionId }}
        infoRequest={async params => {
          const data = await infoCheckDevice(params);
          setInfoData(data.data);
          return data;
        }}
        actionRef={actionRef}
        addRequest={async params => {
          const data = await addCheckDevice(params);
          addOperationLogRequest({
            action: "add",

            content: `新增【${params.name}】检票设备`
          });
          return data;
        }}
        editRequest={async params => {
          const data = await editCheckDevice(params);
          addOperationLogRequest({
            action: "edit",
            changeConfig: {
              list: logList,
              beforeData: infoData,
              afterData: params
            },
            content: `编辑【${params.name}】检票设备`
          });
          return data;
        }}
      />
    </>
  );
};

export default TableList;
