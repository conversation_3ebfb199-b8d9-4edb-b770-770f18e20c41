.flex {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: -moz-box;
  display: -moz-flex;
  display: flex;
}
.flex-v {
  -webkit-flex-direction: column;
  -moz-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-orient: vertical;
  -moz-box-orient: vertical;
}
.flex-1 {
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}
.flex-align-center {
  -webkit-align-items: center;
  -moz-align-items: center;
  align-items: center;
  -webkit-box-align: center;
  -moz-box-align: center;
  -ms-flex-align: center;
}
.flex-justify-center {
  -webkit-justify-content: center;
  -moz-justify-content: center;
  justify-content: center;
  -webkit-box-pack: center;
  -moz-box-pack: center;
  -ms-flex-pack: center;
}
.flex-justify-between {
  -webkit-justify-content: space-between;
  -moz-justify-content: space-between;
  justify-content: space-between;
  -webkit-box-pack: justify;
  -moz-box-pack: justify;
  -ms-flex-pack: justify;
}
.flex-justify-evenly {
  -webkit-justify-content: space-evenly;
  -moz-justify-content: space-evenly;
  justify-content: space-evenly;
  -webkit-box-pack: justify;
  -moz-box-pack: justify;
  -ms-flex-pack: justify;
}

.flex-center {
  .flex();
  .flex-v();
  .flex-justify-center();
  .flex-align-center();
}
