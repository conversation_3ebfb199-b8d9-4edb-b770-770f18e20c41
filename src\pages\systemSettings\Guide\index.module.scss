.card {
  margin-bottom: 16px;
  :global {
    .ant-card-body {
      padding: 16px;
    }
  }
  .container {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  .content {
    flex: 1;
    padding: 0 16px;
    h1 {
      margin: 0;
      padding: 0 0 10px 0;
      font-weight: 500;
      font-size: 16px;
    }
  }
  .operate {
    button {
      padding: 0;
    }
  }
}

// collapse
.stepsContent {
  .desc1 {
    color: rgba(0, 0, 0, 0.88);
    font-size: 14px;
    font-style: normal;
    line-height: 22px;
    > i {
      display: inline-block;
      width: 16px;
      height: 16px;
      border-radius: 8px;
    }
  }
  .desc2 {
    color: rgba(0, 0, 0, 0.45);
  }
  h1 {
    margin: 0;
    padding: 12px 0;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 500;
    font-size: 20px;
    line-height: 28px;
  }
  .collapse {
    margin-top: 16px;
    background-color: #fff;
    border: 0px solid #e5e5e5;
    :global {
      .ant-collapse-header {
        align-items: center;
        padding: 16px;
      }
      .ant-collapse-item {
        margin-bottom: 16px;
        border: 0px;
        border-radius: 6px;
        outline: 1px solid #e5e5e5;
      }
      .ant-collapse-content {
        background-color: transparent;
        border-top: 0px;
        color: rgba(0,0,0,0.45);
      }

      .ant-collapse-item-active {
        background-color: rgba(0, 0, 0, 0.02);
        border: 0px;
        outline: 0px;
      }

      .ant-collapse-content-box {
        padding: 0px 54px 16px;
      }
    }
  }
  .close {
    position: absolute;
    top: 24px;
    right: 24px;
    z-index: 999;
    cursor: pointer;
  }
}

.card, .stepsContent {
  .skipBtn {
    color: #979797;
    padding: 0;
  }

  .stepIcon, .stepTitle {
    display: flex;
    gap: 8px;
    align-items: center;
    font-weight: 500;
    > span {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      color: rgba(22, 119, 255, 1);
      background-color: rgba(230, 244, 255, 1);
      border-radius: 50%;
    }

    .wait {
      color: rgba(0, 0, 0, 0.45);
      background: rgba(0, 0, 0, 0.06);
    }
  }
}
