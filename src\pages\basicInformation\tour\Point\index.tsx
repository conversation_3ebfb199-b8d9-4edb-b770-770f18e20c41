import Delete from "@/common/components/Delete";
import Disabled from "@/common/components/Disabled";
import ProModal from "@/common/components/ProModal";
import useModal from "@/common/components/ProModal/useModal";
import { tableConfig } from "@/common/utils/config";
import { GuideStepStatus, pointType, voiceType, wordTypeEnum } from "@/common/utils/enum";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import ImageUpload from "@/components/ImageUpload";
import { addPoint, deletePoint, editPoint, enablePoint, infoPoint, pagePoint, reloadAudio } from "@/services/api/tour";
import { PlusOutlined, RedoOutlined, SoundOutlined } from "@ant-design/icons";
import { ProTable, type ProFormColumnsType } from "@ant-design/pro-components";
import type { ActionType, ProColumnType } from "@ant-design/pro-table";
// import ProTable from '@ant-design/pro-table';
import { getHashParams, removeStateFromUrl } from "@/common/utils/tool";
import { useGuide } from "@/hooks/useGuide";
import { Button, message, Modal, Progress, Tag, Typography } from "antd";
import { useForm } from "antd/es/form/Form";
import QRCode from "qrcode.react";
import { useEffect, useRef, useState } from "react";
import { useModel } from "@umijs/max";
import Map from "./Map";
import PointIntroList from "./PointIntroList";
import { getEnv } from "@/common/utils/getEnv";

const logList = [
  {
    title: "点位名称",
    dataIndex: "pointName"
  },
  {
    title: "点位类型",
    dataIndex: "pointType",
    valueEnum: pointType
  },

  {
    title: "经度",
    dataIndex: "longitude"
  },
  {
    title: "纬度",
    dataIndex: "latitude"
  },
  {
    title: "智能讲解触发范围",
    dataIndex: "aiExplainTriggerRange"
  },
  {
    title: "地址",
    dataIndex: "pointAddress"
  },

  {
    title: "语音讲解",
    dataIndex: "aiVoiceType",
    valueEnum: voiceType
  }
];
let audio: any = null;
// 播放声音
const handleMouseEnter = (url: string) => {
  // 播放声音
  audio = new Audio(url);
  audio.play();
};
const handleMouseLeave = () => {
  audio.pause();
  audio = null;
};

export default () => {
  const { initialState } = useModel("@@initialState");
  const queryParams = getHashParams();
  const [tourOpen, setTourOpen] = useState(false);
  const { updateGuideInfo } = useGuide();

  const { scenicId, scenicName } = initialState?.scenicInfo || {};
  const actionRef = useRef<ActionType>();
  const [formPoint] = useForm();
  const modalState = useModal();
  const [hasDetail, setHasDetail] = useState(true);
  const [videoStateList, setVideoStateList] = useState<any[]>([]);
  const [infoData, setInfoData] = useState<Record<string, any>>();

  const [_, setRender] = useState(0);
  const timerRef = useRef<any>();
  const getPagePoint = async (params: any) => {
    const res = await pagePoint(params);
    setVideoStateList([...res.data]);
    // 定时刷新音频进度
    clearInterval(timerRef.current);
    timerRef.current = setTimeout(() => {
      getPagePoint(params);
    }, 3000);

    return res;
  };

  useEffect(() => {
    if (modalState.type) {
      clearInterval(timerRef.current);
    } else {
      actionRef.current?.reload();
    }
  }, [modalState.type]);
  useEffect(() => {
    return () => {
      clearInterval(timerRef.current);
    };
  }, []);

  const tableColumns: ProColumnType[] = [
    {
      title: "序号",
      valueType: "index"
    },
    {
      title: "点位名称",
      dataIndex: "pointName"
    },
    {
      title: "点位类型",
      dataIndex: "pointType",
      valueEnum: pointType,
      render: (dom: any) => <Tag>{dom}</Tag>
    },
    {
      title: "经度",
      dataIndex: "longitude",
      search: false
    },
    {
      title: "纬度",
      dataIndex: "latitude",
      search: false
    },
    {
      title: "地址",
      dataIndex: "pointAddress",
      search: false
    },
    {
      title: "音频生成进度",
      dataIndex: "scheduleDoneNum",
      search: false,
      width: 200,
      render: (dom, { scheduleTotal, id }, index) => {
        const curNum = videoStateList[index]?.scheduleDoneNum;
        const percent = scheduleTotal ? (curNum / scheduleTotal) * 100 : 0;
        const isFail = videoStateList[index]?.aiState == "fail";
        return scheduleTotal ? (
          <div style={{ display: "flex" }}>
            <Progress percent={percent} size="small" showInfo={false} status={isFail ? "exception" : null} />
            {scheduleTotal && (
              <div style={{ marginLeft: 10 }}>
                {videoStateList[index].scheduleDoneNum}/{scheduleTotal}
              </div>
            )}
            {isFail && (
              <RedoOutlined
                onClick={async () => {
                  const hide = message.loading("重新生成");
                  await reloadAudio(id);
                  actionRef.current?.reload();
                  setTimeout(hide, 1000);
                }}
                style={{ cursor: "pointer", marginLeft: "5px" }}
              />
            )}
          </div>
        ) : (
          "-"
        );
      }
    },
    {
      title: "启用状态",
      dataIndex: "isEnable",
      valueEnum: { 1: "禁用", 2: "启用" },
      fixed: "right",
      renderText: (dom: any, { id, pointName }) => (
        <Disabled
          access={true}
          status={dom == 2}
          params={{
            id,
            isEnable: dom == 2 ? 1 : 2
          }}
          request={async params => {
            const data = await enablePoint(params);
            // 添加日志
            addOperationLogRequest({
              action: "disable",
              content: `${dom == 2 ? "禁用" : "启用"}【${pointName}】点位`
            });
            return data;
          }}
          actionRef={actionRef}
        />
      )
    },
    {
      title: "操作",
      valueType: "option",
      fixed: "right",
      render: (_: any, entity: any, index) => [
        <a
          onClick={() => {
            setId(entity.id);
            modalState.setType("info");

            addOperationLogRequest({
              action: "info",
              content: `查看【${entity.pointName}】点位详情`
            });
          }}
          key="k1"
        >
          查看
        </a>,
        videoStateList[index].aiState !== "wait" && (
          <a
            onClick={() => {
              setId(entity.id);
              modalState.setType("edit");
            }}
            key="k2"
          >
            编辑
          </a>
        ),
        <Delete
          key="k3"
          access={true}
          status={entity.isEnable == 2}
          params={{ id: entity.id }}
          request={async params => {
            const data = await deletePoint(params);
            // 添加日志
            addOperationLogRequest({
              action: "del",
              content: `删除【${entity.pointName}】点位`
            });
            return data;
          }}
          actionRef={actionRef}
        />
      ]
    }
  ];

  const modalColumns: ProFormColumnsType[] = [
    {
      title: "基础信息",
      columns: [
        {
          title: "点位名称",
          dataIndex: "pointName",
          fieldProps: {
            maxLength: 20
          },
          formItemProps: { rules: [{ required: true }] }
        },
        {
          title: "点位类型",
          dataIndex: "pointType",
          valueEnum: pointType,
          initialValue: "1",
          convertValue: value => {
            setHasDetail(value < 6);
            return value && String(value);
          },
          transform: value => value && Number(value),
          fieldProps: {
            allowClear: false,
            onChange: (e: any) => {
              setHasDetail(e < 6);
            }
          },
          formItemProps: { rules: [{ required: true }] }
        }
      ]
    },
    {
      title: "位置信息",
      columns: [
        {
          title: "经度",
          dataIndex: "longitude",
          valueType: "digit",
          fieldProps: {
            max: 180,
            min: -180,
            step: 0.001
          },
          formItemProps: { rules: [{ required: true }] }
        },
        {
          title: "纬度",
          dataIndex: "latitude",
          valueType: "digit",
          fieldProps: {
            max: 90,
            min: -90,
            step: 0.001
          },
          formItemProps: { rules: [{ required: true }] }
        },
        {
          title: "智能讲解触发范围",
          dataIndex: "aiExplainTriggerRange",
          valueType: "digit",
          initialValue: 50,
          fieldProps: {
            max: 5000000,
            addonAfter: "米"
          },
          formItemProps: { rules: [{ required: true }] }
        },
        {
          title: "地址",
          dataIndex: "pointAddress",
          fieldProps: {
            maxLength: 50
          },
          formItemProps: { rules: [{ required: true }] }
        },
        {
          valueType: "dependency",
          name: ["longitude", "latitude", "aiExplainTriggerRange"],
          columns: props => [
            {
              hideInDescriptions: true,
              title: "地图选点",
              colProps: { span: 24 },
              renderFormItem: (_, __, formRef) => <Map {...{ ...props, formRef }} />
            }
          ]
        }
      ]
    },
    {
      title: "宣传信息",
      hideInForm: !hasDetail,
      columns: [
        {
          title: "语音讲解",
          dataIndex: "aiVoiceType",
          valueType: "checkbox",
          valueEnum: {
            白术: (
              <div>
                男声1{" "}
                <SoundOutlined
                  onMouseEnter={() =>
                    handleMouseEnter("https://minio-prod.shukeyun.com/scenic-prod/1727079735582voice.wav")
                  }
                  onMouseLeave={handleMouseLeave}
                />
              </div>
            ),
            神里绫人: (
              <div>
                男声2{" "}
                <SoundOutlined
                  onMouseEnter={() =>
                    handleMouseEnter("https://minio-prod.shukeyun.com/scenic-prod/1727080471281voice.wav")
                  }
                  onMouseLeave={handleMouseLeave}
                />
              </div>
            ),
            凯瑟琳: (
              <div>
                女声1{" "}
                <SoundOutlined
                  onMouseEnter={() =>
                    handleMouseEnter("https://minio-prod.shukeyun.com/scenic-prod/1727080573617voice.wav")
                  }
                  onMouseLeave={handleMouseLeave}
                />
              </div>
            ),
            阿贝多2lanlan: (
              <div>
                女声2{" "}
                <SoundOutlined
                  onMouseEnter={() =>
                    handleMouseEnter("https://minio-prod.shukeyun.com/scenic-prod/1727080633080voice.wav")
                  }
                  onMouseLeave={handleMouseLeave}
                />
              </div>
            ),
            八重神子2xingxing: (
              <div>
                女声3{" "}
                <SoundOutlined
                  onMouseEnter={() =>
                    handleMouseEnter("https://minio-prod.shukeyun.com/scenic-prod/1727080862882voice.wav")
                  }
                  onMouseLeave={handleMouseLeave}
                />
              </div>
            ),
            琴: (
              <div>
                女声4{" "}
                <SoundOutlined
                  onMouseEnter={() =>
                    handleMouseEnter("https://minio-prod.shukeyun.com/scenic-prod/1727080934847voice.wav")
                  }
                  onMouseLeave={handleMouseLeave}
                />
              </div>
            ),
            可莉: (
              <div>
                童声{" "}
                <SoundOutlined
                  onMouseEnter={() =>
                    handleMouseEnter("https://minio-prod.shukeyun.com/scenic-prod/1727080989501voice.wav")
                  }
                  onMouseLeave={handleMouseLeave}
                />
              </div>
            )
          },
          initialValue: ["白术"],
          colProps: { span: 24 },
          span: 2,
          convertValue: value => {
            if (value && typeof value === "string") {
              return value && value.split(",");
            } else {
              return value;
            }
          },
          transform: value => {
            console.log("valuevaluevalue----", value);
            let newVal = value || "";
            if (typeof newVal === "string") {
              newVal = newVal.split(",");
            }
            // 过滤英文版枚举值
            newVal = newVal.filter((e: any) => e !== "english_man").filter((e: any) => e !== "english_woman");
            return String(newVal);
          },
          render: (text: any) => {
            return text?.split(",").map((item: any) => <Tag key={item}>{voiceType[item]}</Tag>);
          }
        },
        {
          title: "文案类型",
          dataIndex: "wordType",
          valueType: "checkbox",
          valueEnum: wordTypeEnum,
          initialValue: "0",
          // tooltip: '英文版仅生成男声和女声',
          span: 2,
          colProps: { span: 24 },
          // convertValue: (value) => value && value.split(','),
          transform: value => value && String(value),
          render: (text: any) => {
            return text?.split(",").map((item: any) => <Tag key={item}>{wordTypeEnum[item]}</Tag>);
          }
        },
        {
          valueType: "dependency",
          name: ["wordType", "pointName"],
          columns: ({ wordType, pointName }) => {
            return [
              {
                title: <div>点位介绍</div>,
                dataIndex: "pointIntro",
                colProps: { span: 24 },
                formItemProps: { rules: [{ required: true, message: "请输入点位介绍" }] },
                span: 2,
                render: (text: any) => <PointIntroList wordType={wordType} value={text} />,
                renderFormItem: () => <PointIntroList wordType={wordType} pointName={pointName} edit />
              }
            ];
          }
        },
        {
          title: "点位图片",
          dataIndex: "publicizeList",
          span: 2,
          formItemProps: { rules: [{ required: true }] },
          transform: value => ({ publicizeList: value.split(",") }),
          renderText: (text: any) => <ImageUpload defaultValue={text && text.length ? text.join(",") : ""} readonly />,
          renderFormItem: (_, __, formRef) => {
            return (
              <ImageUpload
                defaultValue={
                  Array.isArray(formRef.getFieldValue("publicizeList"))
                    ? formRef.getFieldValue("publicizeList").join(",")
                    : ""
                }
                size={10240}
                multiple
                maxCount={10}
                // ImgCropConfig={{
                //   quality: 1,
                //   aspect: 16 / 9,
                // }}
              />
            );
          }
        }

        // {
        //   title: <div>点位介绍</div>,
        //   dataIndex: 'pointIntro',
        //   valueType: 'textarea',
        //   fieldProps: {
        //     maxLength: 1000,
        //     showCount: true,
        //     style: {
        //       height: 100,
        //     },
        //   },
        //   colProps: { span: 24 },
        //   formItemProps: { rules: [{ required: true }] },
        // },
        // {
        //   title: '语音讲解',
        //   dataIndex: 'aiVoiceType',
        //   valueType: 'radio',
        //   valueEnum: voiceType,
        //   initialValue: '白术',
        //   colProps: { span: 24 },
        //   // convertValue: (value) => value && String(value),
        //   // transform: (value) => value && Number(value),
        // },
      ]
    }
  ];
  const [id, setId] = useState<string | null>();
  const [tourLink] = useState<string>(
    `${getEnv().SHOP_URL}#/pages/tour/tour?guideId=${initialState?.scenicInfo?.scenicId}`
  );

  const checkParams = (params: any) => {
    const { pointIntro, wordType } = params;
    let isPass = true;
    if (pointIntro) {
      const introList = JSON.parse(pointIntro || "[]");
      // 补缺
      wordType.split(",").forEach(key => {
        if (!introList.find((item: any) => item.title == key)) {
          introList.push({ title: key, content: "" });
        }
      });
      const arr = [];
      for (let i = 0; i < introList.length; i++) {
        if (!introList[i].content) {
          arr.push(wordTypeEnum[introList[i].title]);
          isPass = false;
        }
      }
      if (!isPass) message.error(`请输入以下点位介绍：${arr.join("，")}`);
    }
    return isPass;
  };

  useEffect(() => {
    if (queryParams?.type) {
      modalState.setType(queryParams?.type);
    }
  }, [queryParams]);

  return (
    <>
      <ProTable
        {...tableConfig}
        rowClassName={row => (row.isEnable == 2 ? "" : "disableRow")}
        style={modalState.tableStytle}
        actionRef={actionRef}
        columns={tableColumns}
        toolBarRender={() => [
          <Button
            key="k1"
            type="primary"
            onClick={() => {
              setId(null);
              setHasDetail(true);
              modalState.setType("add");
            }}
          >
            <PlusOutlined /> 新增
          </Button>,
          <Button
            key="k2"
            type="primary"
            onClick={() => {
              setTourOpen(true);

              // 添加日志
              addOperationLogRequest({
                action: "info",
                content: `查看【${scenicName}】导览链接`
              });
            }}
          >
            分享导览链接
          </Button>
        ]}
        params={{ scenicId }}
        request={getPagePoint}
      />
      <ProModal
        page
        {...modalState}
        title="点位"
        actionRef={actionRef}
        formInstance={formPoint}
        columns={modalColumns}
        params={{ id, scenicId }}
        onCancel={() => {
          history.pushState(null, null, removeStateFromUrl("type"));
        }}
        infoRequest={async params => {
          const data = await infoPoint(params);
          setInfoData(data.data);

          return data;
        }}
        addRequest={async params => {
          const isPass = checkParams(params);

          if (!isPass) return false;

          const data = await addPoint(params);
          // 更新引导
          updateGuideInfo({ tabIndex: 0, status: GuideStepStatus.step0_4 });
          history.pushState(null, null, removeStateFromUrl("type"));
          addOperationLogRequest({
            action: "add",
            content: `新增【${params.pointName}】点位`
          });
          return data;
        }}
        editRequest={async params => {
          const isPass = checkParams(params);
          console.log("isPass", isPass, params);

          if (!isPass) return false;
          const data = await editPoint(params);
          addOperationLogRequest({
            action: "edit",
            changeConfig: {
              list: logList,
              beforeData: infoData,
              afterData: params
            },
            content: `编辑【${params.pointName}】点位`
          });
          return data;
        }}
      />
      <Modal title="导览链接" width={560} open={tourOpen} onCancel={() => setTourOpen(false)} footer={null}>
        <Typography.Paragraph copyable>{tourLink}</Typography.Paragraph>
        <QRCode style={{ margin: "40px auto" }} value={tourLink} size={200} fgColor="#000" />
      </Modal>
    </>
  );
};
