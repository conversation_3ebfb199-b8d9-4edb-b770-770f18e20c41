import CodeImg from "@/assets/imgs/jpcode.png";
import { getScenicIdentifier, getUniqueId, goToLogin } from "@/common/utils/tool";
import { logout } from "@/services/api/cas";
import { apiWikiList } from "@/services/api/erp";
import {
  AndroidOutlined,
  DownOutlined,
  LogoutOutlined,
  QuestionCircleOutlined,
  SettingOutlined,
  UserOutlined
} from "@ant-design/icons";
import { Avatar, Menu, Modal, Space, Spin } from "antd";
import type { MenuInfo } from "rc-menu/lib/interface";
import React, { useCallback, useEffect, useState } from "react";
import { Access, history, useAccess, useModel } from "@umijs/max";
import HeaderDropdown from "../HeaderDropdown";

import { addOperationLogRequest } from "@/common/utils/operationLog";
import styles from "./index.less";
import { getEnv } from "@/common/utils/getEnv";

export type GlobalHeaderRightProps = {
  menu?: boolean;
};

/**
 * 退出登录，并且将当前的 url 保存
 */
const loginOut = async () => {
  try {
    const appId = JSON.parse(sessionStorage.getItem("scenicInfo"))?.appId;
    await logout({
      appId
    });
    addOperationLogRequest({
      action: "login",
      module: "",
      content: "退出登录慧景云系统"
    });
    const scenicCode = getScenicIdentifier();
    goToLogin({
      scenicCode
    });
  } catch (error) {
    if (error.name === "BizError" && error.data.code === 30001) {
      //没设置 cookie 跳转登陆页
      const scenicCode = getScenicIdentifier();
      goToLogin({
        scenicCode
      });
    } else {
      console.error(error);
    }
  }
};

const AvatarDropdown: React.FC<GlobalHeaderRightProps> = ({ menu }) => {
  const { CAS_HOST, IMG_HOST, HELP_URL } = getEnv();
  const access = useAccess();
  const { initialState, setInitialState } = useModel("@@initialState");
  const [menuData, setMenuData] = useState([]);
  useEffect(() => {
    getMenuList();
  }, []);
  const onMenuClick = useCallback(
    (event: MenuInfo) => {
      const { key } = event;
      if (key === "app") {
        Modal.confirm({
          icon: false,
          title: "检票 APP",
          width: 364,
          content: (
            <>
              <h4 style={{ textAlign: "center", marginBottom: "10px" }}>下载请扫描二维码</h4>
              <img width={300} src={CodeImg} />
            </>
          ),
          maskClosable: true,
          okButtonProps: {
            style: {
              display: "none"
            }
          },
          cancelButtonProps: {
            style: {
              display: "none"
            }
          }
        });
        return;
      }
      if (key === "logout") {
        setInitialState(s => ({ ...s, currentUser: undefined }));
        loginOut();
        return;
      }
      if (key === "center") {
        // location.href = `${CAS_HOST}/#/`; //当前页开打，2022 年 6 月 2 日改为新窗口打开
        addOperationLogRequest({
          action: "link",
          content: "跳转个人中心",
          module: ""
        });

        open(`${CAS_HOST}/#/accountsettings`);
        return;
      }
      history.push(`/account/${key}`);
    },
    [setInitialState]
  );

  const loading = (
    <span className={`${styles.action} ${styles.account}`}>
      <Spin
        size="small"
        style={{
          marginLeft: 8,
          marginRight: 8
        }}
      />
    </span>
  );
  if (!initialState) {
    return loading;
  }

  //菜单页
  const getMenuList = async () => {
    const pars = {
      type: 1,
      isEnable: 1
    };
    const { data } = await apiWikiList(pars);
    setMenuData(data);
  };

  const onJumpUrl = val => {
    window.open(val);
  };
  const menu2 = (
    <Menu>
      {menuData.map(item => {
        return (
          <Menu.Item key={getUniqueId()} onClick={() => onJumpUrl(item.linkUrl)}>
            {item.name}
          </Menu.Item>
        );
      })}
    </Menu>
  );

  const menuHeaderDropdown = (
    <Menu className={styles.menu} selectedKeys={[]} onClick={onMenuClick}>
      {menu && (
        <Menu.Item key="center">
          <UserOutlined />
          个人中心
        </Menu.Item>
      )}
      {menu && (
        <Menu.Item key="settings">
          <SettingOutlined />
          个人设置
        </Menu.Item>
      )}
      {menu && <Menu.Divider />}
      <Menu.Item key="app">
        <AndroidOutlined />
        检票 APP
      </Menu.Item>
      <Menu.Item key="center">
        <UserOutlined />
        个人中心
      </Menu.Item>

      <Menu.Item key="logout">
        <LogoutOutlined />
        退出登录
      </Menu.Item>
    </Menu>
  );

  return (
    <Space align="center">
      <HeaderDropdown className="pointer" overlay={menuHeaderDropdown}>
        <Space size={"small"} className={`${styles.action} ${styles.account}`}>
          {initialState.userInfo?.avatar ? (
            <Avatar
              size="small"
              className={styles.avatar}
              src={`${IMG_HOST}${initialState.userInfo.avatar}`}
              alt="avatar"
            />
          ) : (
            ""
          )}
          <span>
            {initialState?.userInfo?.nickname || initialState?.userInfo?.username || initialState?.userInfo?.phone}
          </span>
          <DownOutlined />
        </Space>
      </HeaderDropdown>
      <Access accessible={access.canHelp}>
        <span
          className="pointer"
          onClick={() => {
            addOperationLogRequest({
              action: "link",
              content: "跳转帮助中心",
              module: ""
            });
            window.open(HELP_URL);
          }}
        >
          <QuestionCircleOutlined /> 帮助中心
        </span>
      </Access>
    </Space>
  );
};

export default AvatarDropdown;
