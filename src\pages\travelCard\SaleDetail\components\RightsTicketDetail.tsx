/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-04-10 16:51:46
 * @LastEditTime: 2023-04-12 10:04:13
 * @LastEditors: z<PERSON><PERSON><PERSON>i
 */
import DetailsPop from "@/common/components/DetailsPop";
import { ticketTypeEnum, useTypeEnum } from "@/common/utils/enum";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import MDEditor from "@/components/MDEditor";
import type { ProDescriptionsGroup } from "@/components/ModalDescriptions";
import type { ModalState } from "@/hooks/useModal";
import CheckRuleDetails from "@/pages/ruleSet/Manage/components/CheckRuleDetails";
import RetreatRuleDetails from "@/pages/ruleSet/RefundTickets/components/RetreatRuleDetails";
import IssueRuleDetails from "@/pages/ruleSet/SellTickets/components/IssueRuleDetails";
import { getSimpleGoodsInfo } from "@/services/api/ticket";
import { isNil, round } from "lodash";
import type { FC } from "react";
import { useEffect } from "react";
import { useRequest } from "@umijs/max";

const discountRate = {
  0: "特殊折扣率",
  1: "特殊折扣率",
  2: "特殊折扣率",
  3: "特殊折扣率",
  4: "网订折扣率",
  5: "特殊折扣率",
  7: "团体折扣率"
};

type RightsTicketDetailProps = ModalState & {
  id?: string;
};

/**
 * @description: 权益票详情
 */
const RightsTicketDetail: FC<RightsTicketDetailProps> = ({ visible, setVisible, id }) => {
  // 详情信息
  const getRightsTicketInfoReq = useRequest(getSimpleGoodsInfo, {
    manual: true,
    onSuccess(data, params) {
      addOperationLogRequest({
        action: "info",
        content: `查看【${data.goodsName}】商品详情`
      });
    }
  });

  const rightsTicketColumns: ProDescriptionsGroup<API.SimpleGoodsInfo>[] = [
    {
      title: "基础信息",
      columns: [
        {
          title: "所属产品名称",
          dataIndex: "ticketName"
        },
        {
          title: "商品名称",
          dataIndex: "goodsName"
        },

        {
          title: "票种",
          dataIndex: "type",
          valueEnum: ticketTypeEnum
        },
        {
          title: "使用方式",
          dataIndex: "useType",
          valueEnum: useTypeEnum
        }
      ]
    },
    {
      title: "价格信息",
      columns: [
        {
          title: `${discountRate[getRightsTicketInfoReq.data?.type ?? 0]}`,
          render: (_, { marketPrice, overallDiscount }) => {
            return (
              <div>
                <div>{overallDiscount} %</div>
                <div>
                  {marketPrice} * {overallDiscount}% = {round(marketPrice * (overallDiscount / 100), 2)}
                </div>
              </div>
            );
          }
        },
        {
          title: "分销折扣区间（%）",
          render: (_, { beginDiscount, marketPrice, endDiscount, overallDiscount }) => {
            if (isNil(beginDiscount ?? endDiscount)) {
              return "-";
            }
            return (
              <div>
                <div>
                  {beginDiscount} % ~ {endDiscount} %
                </div>
                <div>
                  {round(((beginDiscount * 1) / 100) * (marketPrice * (overallDiscount / 100)), 2)} ~{" "}
                  {round(((endDiscount * 1) / 100) * (marketPrice * (overallDiscount / 100)), 2)}
                </div>
              </div>
            );
          }
        }
      ]
    },
    {
      title: "其他信息",
      columns: [
        {
          title: "是否允许购买数量控制",
          dataIndex: "isPeopleNumber",
          renderText: dom => ["否", "是"][dom]
        },

        {
          title: "最小起订量",
          dataIndex: "minPeople",
          renderText: (dom, { isPeopleNumber }) => (isPeopleNumber ? dom : "-")
        },
        {
          title: "单次最大预订量",
          dataIndex: "maxPeople",
          renderText: (dom, { isPeopleNumber }) => (isPeopleNumber ? dom : "-")
        },
        {
          title: "使用有效期",
          dataIndex: "validityDay",
          renderText: dom => <span>游玩日期起 {dom} 天内有效</span>
        }
      ]
    },
    {
      title: "关联规则",
      columns: [
        {
          title: "出票规则",
          dataIndex: "issueName",
          renderText: (dom, entity) => (
            <a
              onClick={() => {
                IssueRuleDetails.show(entity.issueId);
              }}
            >
              {dom}
            </a>
          )
        },
        {
          title: "检票规则",
          dataIndex: "checkName",
          renderText: (dom, entity) => (
            <a
              onClick={() => {
                CheckRuleDetails.show(entity.checkId);
              }}
            >
              {dom}
            </a>
          )
        },
        {
          title: "退票规则",
          dataIndex: "retreatName",
          renderText: (dom, entity) => (
            <a
              onClick={() => {
                RetreatRuleDetails.show(entity.retreatId);
              }}
            >
              {dom}
            </a>
          )
        }
      ]
    },
    {
      title: "说明信息",
      columns: [
        {
          title: "预订须知",
          span: 2,
          dataIndex: "notice",
          render: value => (value ? <MDEditor value={value as string} readonly /> : "-")
        },
        {
          title: "备注",
          dataIndex: "remark"
        }
      ]
    }
  ];

  useEffect(() => {
    if (visible && id) {
      getRightsTicketInfoReq.run(id);
    }
  }, [id, visible]);

  return (
    <DetailsPop
      title="权益票详情"
      visible={visible}
      setVisible={setVisible}
      columnsInitial={rightsTicketColumns}
      isLoading={getRightsTicketInfoReq.loading}
      dataSource={getRightsTicketInfoReq.data}
    />
  );
};

export default RightsTicketDetail;
