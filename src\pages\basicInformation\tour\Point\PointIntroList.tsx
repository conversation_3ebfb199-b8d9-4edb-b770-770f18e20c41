import { wordTypeEnum } from "@/common/utils/enum";
import { LoadingOutlined } from "@ant-design/icons";
import { ProCard } from "@ant-design/pro-components";
import { useModel } from "@umijs/max";
import { Button, Checkbox, Form, message, Modal, Select } from "antd";
import TextArea from "antd/lib/input/TextArea";
import { useEffect, useRef, useState } from "react";
import { getEnv } from "@/common/utils/getEnv";

export default (props: any) => {
  const { wordType, pointName, value, onChange, edit } = props;
  const [form] = Form.useForm();
  const [aiOpen, setAiOpen] = useState(false);
  const { initialState } = useModel("@@initialState");
  const { scenicId } = initialState?.scenicInfo || {};
  const [activeKey, setActiveKey] = useState("");
  const [aiWordTypeList, setAiWordTypeList] = useState<any[]>([]);
  const [selectWordType, setSelectWordType] = useState<any[]>([]);
  const aKey = useRef("");
  let pointIntro = [];

  try {
    pointIntro = value ? JSON.parse(value) : [];
  } catch (error) {
    console.error("error", error);
  }

  const wordTypeList = wordType
    ? wordType.split(",").map(e => {
        return {
          title: e,
          content: pointIntro?.find((item: any) => item.title === e)?.content || ""
        };
      })
    : [];

  useEffect(() => {
    const list = wordType.split(",").map(key => {
      return {
        value: key,
        label: wordTypeEnum[key]
      };
    });
    setAiWordTypeList(list);

    // 当前选中的 tab 不存在时，置空
    let aKey = activeKey;
    if (!list.find(e => e.value === activeKey)) {
      aKey = "";
    }
    // 为空默认选中第一个
    if (aKey === "") {
      aKey = list[0].value;
    }
    console.log("aKey", aKey, list);

    setActiveKey(aKey);
  }, [wordType]);

  const onFinishAi = async (values: any, key: string) => {
    if (!pointName) {
      return message.error("请先填写点位名称");
    }
    setAiOpen(false);

    // 设置 loading
    const index = selectWordType.findIndex(e => e === key);
    const hide = message.loading(`AI 智能生成中 (${index + 1}/${selectWordType.length})`, 0);

    const { attractionDes, wordNum } = values;
    let str = "";

    setActiveKey(key);
    aKey.current = key;

    fetch(`${getEnv().AI_HOST}/algorithm/total-product-microservices/api/scenic_route_planning/attraction_introduce`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        word_type: aKey.current,
        scenic_id: scenicId,
        attraction_name: pointName,
        attraction_description: attractionDes,
        word_num: wordNum
      })
    })
      .then(response => {
        const reader = response.body?.pipeThrough(new TextDecoderStream()).getReader();
        return reader;
      })
      .then(function processText(reader) {
        return reader.read().then(({ done, value }) => {
          // 处理每个数据块
          if (!value) return;

          const lines = value.split("\n");
          lines.forEach(line => {
            if (line.startsWith("data: ")) {
              const jsonString = line.substring(6);

              try {
                if (jsonString !== "[DONE]") {
                  const data = JSON.parse(jsonString);
                  const content = data?.choices[0].delta.content;
                  str += content;
                }

                const newPointIntroList = wordTypeList.map(item => {
                  if (item.title === aKey.current) {
                    item.content = str;
                  }
                  return {
                    title: item.title,
                    content: item.content
                  };
                });

                onChange(JSON.stringify(newPointIntroList));
                hide();

                if (jsonString === "[DONE]") {
                  // 生成下一个 tab 的内容
                  const index = selectWordType.findIndex(e => e === aKey.current);
                  const nextIndex = index + 1;
                  if (nextIndex < selectWordType.length) {
                    setTimeout(() => {
                      onFinishAi(values, selectWordType[nextIndex]);
                    }, 1000);
                  } else {
                    aKey.current = "";
                    message.success("AI 智能生成完成");
                  }
                }
              } catch (error) {
                console.error("Error parsing JSON:", error);
              }
            }
          });

          return processText(reader);
        });
      })
      .catch(error => {
        console.error("Error:", error);
      });
  };

  return (
    <div>
      <ProCard
        tabs={{
          type: "line",
          activeKey: activeKey,
          onChange: key => {
            setActiveKey(key);
          },
          indicator: {
            align: "start"
          }
        }}
      >
        {wordTypeList?.map(item => (
          <ProCard.TabPane
            key={item.title}
            tab={
              <div>
                {aKey.current === item.title ? <LoadingOutlined /> : ""} {wordTypeEnum[item.title]}
              </div>
            }
          >
            {edit ? (
              <div>
                <TextArea
                  value={item.content}
                  onChange={e => {
                    const newWordTypeList = wordTypeList.map(i => {
                      if (i.title === item.title) {
                        return { ...i, content: e.target.value };
                      }
                      return i;
                    });
                    onChange(JSON.stringify(newWordTypeList));
                  }}
                  autoSize={{ minRows: 6 }}
                  placeholder={`请输入`}
                  count={{
                    show: true,
                    max: item.title == 2 ? 15000 : 1500
                  }}
                />
                <div
                  onClick={() => {
                    setAiOpen(true);
                  }}
                  style={{
                    color: "#1890ff",
                    marginTop: "5px",
                    cursor: "pointer"
                  }}
                >
                  AI 智能生成
                </div>
              </div>
            ) : (
              <div>{item.content}</div>
            )}
          </ProCard.TabPane>
        ))}
      </ProCard>
      <Modal
        title="AI 智能生成"
        width={560}
        open={aiOpen}
        onCancel={() => setAiOpen(false)}
        // onOk={}
        footer={null}
      >
        <Form
          name="basic"
          // labelCol={{ span: 8 }}
          // wrapperCol={{ span: 16 }}
          layout={"vertical"}
          form={form}
          // style={{ maxWidth: 600 }}
          initialValues={{ remember: true }}
          onFinish={values => {
            const key = selectWordType[0]; // 从第一个 tab 开始
            onFinishAi(values, key);
          }}
          // onFinishFailed={onFinishFailed}
          autoComplete="off"
        >
          <Form.Item rules={[{ required: true, message: "请选择文案类型" }]} label="文案类型" name="wordType">
            <Checkbox.Group
              options={aiWordTypeList}
              onChange={e => {
                setSelectWordType(e);
              }}
            />
          </Form.Item>

          <Form.Item label="生成字数" name="wordNum">
            <Select
              defaultValue={300}
              options={[
                { value: 300, label: "300字" },
                { value: 500, label: "500字" },
                { value: 800, label: "800字" },
                { value: 1000, label: "1000字" }
              ]}
            />
          </Form.Item>
          <Form.Item label="点位描述" rules={[{ required: true }]} name="attractionDes">
            <TextArea
              autoSize={{ minRows: 6 }}
              count={{
                show: true,
                max: 1000
              }}
              placeholder="请输入"
            />
          </Form.Item>
          {/* <Form.Item> */}
          <div style={{ display: "flex", justifyContent: "center" }}>
            <Button type="primary" htmlType="submit">
              提交
            </Button>
          </div>
          {/* </Form.Item> */}
        </Form>
      </Modal>
    </div>
  );
};
