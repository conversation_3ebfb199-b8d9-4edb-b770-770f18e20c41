import { tableConfig } from "@/common/utils/config";
import { modelWidth } from "@/common/utils/gConfig";
import { actionType, addOperationLogRequest, deepMapRoutes, findNamePathByID } from "@/common/utils/operationLog";
import { transformArrToString } from "@/common/utils/tool";
import { apiSensorLogList } from "@/services/api/settings";
import type { ProColumns } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import { Modal } from "antd";
import { isEmpty } from "lodash";
import dayjs from "dayjs";
import React, { useMemo, useState } from "react";
import { useModel } from "@umijs/max";
import Routes from "../../../../config/routes";

const TableList: React.FC = () => {
  const [visible, setVisible] = useState(false);
  const { initialState }: any = useModel("@@initialState");
  const companyId = localStorage.getItem("currentCompanyId");
  const { appId } = initialState?.scenicInfo;

  const [currentRow, setCurrentRow] = useState<Scenic.OperationLogItem>();
  const getSensorLogList = async (params: any) => {
    const _params = {
      ...params,
      module: transformArrToString(params?.module),
      function: transformArrToString(params?.function)
    };
    const { data } = await apiSensorLogList(_params);
    return {
      total: data.total || 0,
      data: data?.records || []
    };
  };

  //  生成树
  const operationMenuTree = useMemo(() => deepMapRoutes(Routes), []);
  const columns: ProColumns<Scenic.OperationLogItem>[] = [
    {
      title: "操作时间",
      dataIndex: "time",
      valueType: "dateTimeRange",
      hideInTable: true,
      search: {
        transform: (value: any) => ({ startTime: value[0], endTime: value[1] })
      }
    },
    {
      title: "操作时间",
      dataIndex: "createTime",
      search: false,
      renderText: text => dayjs(text).format("YYYY-MM-DD HH:mm:ss")
    },
    {
      title: "账号",
      dataIndex: "creator"
    },
    {
      title: "昵称",
      dataIndex: "nickname"
    },
    {
      title: "操作模块",
      dataIndex: "module",
      hideInTable: true,
      valueType: "treeSelect",
      fieldProps: {
        treeCheckable: true,
        options: operationMenuTree,
        placeholder: "请输入",
        allowClear: true,
        treeDefaultExpandedKeys: ["xU9qK5pJ"],
        fieldNames: {
          label: "title"
        },
        maxTagCount: 3
      }
    },
    {
      title: "操作模块",
      search: false,
      dataIndex: "module",
      renderText: text => findNamePathByID(operationMenuTree[0]?.children || [], text)
    },
    {
      title: "设备 IP",
      dataIndex: "deviceIp"
    },
    {
      title: "操作类型",
      dataIndex: "function",
      valueType: "select",
      fieldProps: {
        mode: "multiple"
      },
      valueEnum: actionType
    },
    {
      title: "操作内容",
      dataIndex: "content",
      // width: 400,
      ellipsis: true,
      search: false
      // renderText: (text) =>|| '-',
    },
    {
      title: "操作详情",
      valueType: "option",
      key: "operation",
      render: (_, record) => {
        if (!isEmpty(JSON.parse(record.contentDetails || "[]"))) {
          return (
            <a
              onClick={() => {
                setCurrentRow(record);
                setVisible(true);
                addOperationLogRequest({
                  action: "info",
                  content: `查看【${record.content}】详情`
                });
              }}
            >
              查看
            </a>
          );
        }
        return <>--</>;
      }
    }
  ];

  const logColumns: ProColumns<Scenic.OperationLogItem>[] = [
    {
      dataIndex: "key",
      hideInTable: true
    },
    {
      title: "字段",
      dataIndex: "name"
    },
    {
      title: "编辑前",
      dataIndex: "before"
    },
    {
      title: "编辑后",
      dataIndex: "after"
    }
  ];

  return (
    <>
      <ProTable
        {...tableConfig}
        rowKey="id"
        params={{
          project: companyId,
          app: appId
        }}
        request={getSensorLogList}
        columns={columns}
      />
      <Modal
        title="操作详情"
        width={modelWidth.md}
        open={visible}
        destroyOnClose
        onCancel={() => setVisible(false)}
        footer={false}
      >
        <ProTable
          rowKey="key"
          dataSource={JSON.parse(currentRow?.contentDetails || "[]")}
          columns={logColumns}
          pagination={false}
          search={false}
          options={false}
          bordered
        />
        {/* 待处理数据：{currentRow && currentRow.contentDetails} */}
      </Modal>
    </>
  );
};

export default TableList;
