// @ts-ignore
/* eslint-disable */
import { request } from "@umijs/max";
import { scenicHost } from ".";

/** 新增规则 PUT /api/rule */
export async function updateRule(options?: { [key: string]: any }) {
  return request<API.RuleListItem>(`${scenicHost}/rule`, {
    method: "PUT",
    ...(options || {})
  });
}

// 检票点
/** 检票点分页列表查询 GET */
export async function getCheckPageList(
  params: {
    // query
    /** 当前的页码 */
    pageNum?: number;
    /** 页面的容量 */
    pageSize?: number;
  },
  options?: { [key: string]: any }
) {
  params.pageNum = params.current;
  const { code, data } = await request(`${scenicHost}/checkInPoint/pageList`, {
    method: "GET",
    params: {
      ...params
    },
    ...(options || {})
  });
  // // 处理启用状态的样式
  // data.data.map((e: any) => {
  //   e.isEnable = {
  //     color: e.isEnable == '1' ? 'blue' : 'red',
  //     text: e.isEnable == '1' ? '启用' : '禁用',
  //   };
  // });
  return {
    data: data.data,
    // success 请返回 true，
    // 不然 table 会停止解析数据，即使有数据
    success: code === 20000,
    // 不传会使用 data 的长度，如果是分页一定要传
    total: data.total
  };
}

/** 检票点根据 id 删除 */
export async function delCheckaddr(id: string) {
  return request(`${scenicHost}/checkInPoint/del/${id}`, {
    method: "DELETE"
  });
}

/** 检票点新增编辑 POST */
export async function addCheckaddr(params: any, options?: { [key: string]: any }) {
  return await request<API.RuleList>(`${scenicHost}/checkInPoint/${params.id ? "update" : "save"}`, {
    method: "POST",
    data: params,
    ...(options || {})
  });
}

/** 根据 id 查询检票点详情 GET */
export function getIddetails(options: any) {
  return request(`${scenicHost}/checkInPoint/info/${options.id}`);
}

/** 编辑【检票点】状态 */
export function setCheckInPointStatus(params: any) {
  return request(`${scenicHost}/checkInPoint/status`, {
    method: "POST",
    data: params
  });
}

// 检票设备设备
/** 查询检票设备分页列表查询 GET */
export async function getCheckEquipmentPageList(
  params: {
    // query
    /** 当前的页码 */
    pageNum?: number;
    /** 页面的容量 */
    pageSize?: number;
  },
  options?: { [key: string]: any }
) {
  params.pageNum = params.current;
  const { code, data } = await request(`${scenicHost}/checkEquipment/pageList`, {
    method: "GET",
    params: {
      ...params
    },
    ...(options || {})
  });
  // // 处理启用状态的样式
  // data.data.map((e: any) => {
  //   e.isEnable = {
  //     color: e.isEnable == '1' ? 'blue' : 'red',
  //     text: e.isEnable == '1' ? '启用' : '禁用',
  //   };
  // });

  return {
    data: data.data,
    // success 请返回 true，
    // 不然 table 会停止解析数据，即使有数据
    success: code === 20000,
    // 不传会使用 data 的长度，如果是分页一定要传
    total: data.total
  };
}

/** 检票设备删除根据 id 删除 */
export async function delCheckEquipment(options?: { [key: string]: any }) {
  return request<API.RuleListItem>(`${scenicHost}/checkEquipment/del/${options}`, {
    method: "DELETE"
  });
}

/** 检票设备新增编辑 POST */
export async function AddCheckEquipment(params: any, options?: { [key: string]: any }) {
  return await request<API.RuleList>(`${scenicHost}/checkEquipment/${params.id ? "update" : "save"}`, {
    method: "POST",
    data: params,
    ...(options || {})
  });
}

/** 根据 id 查询检票设备详情 GET */
export async function getCheckdetails(options: any) {
  const { code, msg, data } = await request(`${scenicHost}/checkEquipment/info/${options}`, {
    method: "GET"
  });

  if (code !== 20000) {
    // message.error(msg);
    return {};
  }
  // data.scenicId = '1456171644658683906';
  // data.isEnable += '';
  // console.log(data);

  return data;
}

/** 根据景区 id 查询下拉列表检票点 GET */
export async function getCheckInPointDownList(params: any) {
  return request<ResponseData<{ id: string; checkName: string }[]>>(`${scenicHost}/checkInPoint/simpleList`, {
    method: "GET",
    params
  });
}

/** 根据景区 id 查询下拉列表检票点排除产品类型 GET */
export async function checkEquipmentSimpleList(params: any) {
  const { code, data }: any = await request<API.RuleListItem>(
    `${scenicHost}/checkInPoint/checkEquipmentSimpleList/${params.scenicId}`
  );
  if (code == 20000 && data.length > 0) {
    return data.map((item: any) => {
      return {
        value: item.id,
        label: item.checkName
      };
    });
  } else {
    return [];
  }
}

/** 编辑【检票设备】状态 */
export function setCheckEquipmentStatus(params: any) {
  return request(`${scenicHost}/checkEquipment/status`, {
    method: "POST",
    data: params
  });
}

// 售票窗口模块
/** 查询售票窗口分页列表查询 GET */
export async function getTicketPageList(
  params: {
    // query
    /** 当前的页码 */
    pageNum?: number;
    /** 页面的容量 */
    pageSize?: number;
  },
  options?: { [key: string]: any }
) {
  params.pageNum = params.current;
  const { code, data } = await request(`${scenicHost}/ticketOffice/pageList`, {
    method: "GET",
    params: {
      ...params
    },
    ...(options || {})
  });
  // // 处理启用状态的样式
  // data.data.map((e: any) => {
  //   e.isEnable = {
  //     color: e.isEnable == '1' ? 'blue' : 'red',
  //     text: e.isEnable == '1' ? '启用' : '禁用',
  //   };
  // });

  return {
    data: data.data,
    // success 请返回 true，
    // 不然 table 会停止解析数据，即使有数据
    success: code === 20000,
    // 不传会使用 data 的长度，如果是分页一定要传
    total: data.total
  };
}

/** 售票点删除根据 id 删除 */
export async function delTicket(id: string) {
  return request(`${scenicHost}/ticketOffice/del/${id}`, {
    method: "DELETE"
  });
}

/** 售票点新增 POST */
export function addTicketOffice(params: any, options?: { [key: string]: any }) {
  return request(`${scenicHost}/ticketOffice/save`, {
    method: "POST",
    data: params,
    ...(options || {})
  });
}
/** 售票点编辑 POST */
export function editTicketOffice(params: any, options?: { [key: string]: any }) {
  return request(`${scenicHost}/ticketOffice/update`, {
    method: "POST",
    data: params,
    ...(options || {})
  });
}

/** 根据 id 查询售票窗口详情 GET */
export function getTicketOffice(options: any) {
  return request(`${scenicHost}/ticketOffice/info/${options.id}`, {
    method: "GET"
  });
}

// /** 根据景区 id 查询 [检票点] 下拉列表 GET */
export async function getTicketOfficeList(options: { scenicId: any }) {
  return request(`${scenicHost}/ticketOffice/simpleList/${options.scenicId}`, {
    method: "GET"
  });
}

// 售票设备模块
/** 查询售票设备模块分页列表查询 GET */
export async function getTicketEquipmentList(params: any) {
  try {
    const { code, data } = await request(`${scenicHost}/ticketEquipment/pageList`, {
      method: "GET",
      params
    });
    return {
      // 表格数据
      data: data.data,
      // success 返回非 true table 会停止解析数据
      success: code === 20000,
      // 分页必传，不传默认 data 长度
      total: data.total
    };
  } catch (error) {
    return {};
  }
}

/** 售票设备删除根据 id 删除 */
export async function delTicketEquipment(id: any) {
  return request(`${scenicHost}/ticketEquipment/del/${id}`, {
    method: "DELETE"
  });
}

/** 售票设备新增编辑 POST */
export function AddTicketEquipment(params: any, options?: { [key: string]: any }) {
  return request<ResponseData<number>>(`${scenicHost}/ticketEquipment/${params.id ? "update" : "save"}`, {
    method: "POST",
    data: params,
    ...(options || {})
  });
}

/** 【售票设备】详情 */
export async function getTicketEquipments(options: any) {
  const { code, msg, data } = await request(`${scenicHost}/ticketEquipment/info/${options.id}`, {
    method: "GET"
  });

  if (code !== 20000) {
    // message.error(msg);
    return {};
  }
  // data.scenicId = '1456171644658683906';
  data.beginTime = [data.beginTime, data.endTime];
  // data.isEnable += '';

  return data;
}

/** 编辑【售票点】状态 */
export function setTicketOfficeStatus(params: any) {
  return request(`${scenicHost}/ticketOffice/status`, {
    method: "POST",
    data: params
  });
}

/** 编辑【售票设备】状态 */
export function setTicketEquipmentStatus(params: any) {
  return request(`${scenicHost}/ticketEquipment/status`, {
    method: "POST",
    data: params
  });
}

/** 查看【售票设备销售权限】列表 */
export function getPermissionRetrieve(params: any) {
  return request(`${scenicHost}/permission/retrieve`, {
    method: "GET",
    params
  });
}

//视频列表
export function apiVideoPageList(params: any) {
  return request(`${scenicHost}/video/pageList`, {
    method: "GET",
    params
  });
}

/** 视频新增和编辑*/
export function apiVideoAddUpdate(params: any) {
  return request(`${scenicHost}/video/${params.id ? "update" : "save"}`, {
    method: "POST",
    data: params
  });
}
//视频详情
export function apiVideoDetails(params: any) {
  return request(`${scenicHost}/video/info/${params}`, {
    method: "GET",
    params
  });
}
//视频删除
export async function apiVideoDelete(id: any) {
  return request(`${scenicHost}/video/info/${id}`, {
    method: "DELETE"
    // headers: {
    //   'Content-Type': 'application/x-www-form-urlencoded',
    // },
  });
}
/**视频是否禁用 */
export async function apiVideoFisEnable(params: any) {
  return request(`${scenicHost}/video/info/${params}`, {
    method: "PUT"
  });
}

//WiFI 列表
export function apiWiFiPageList(params: any) {
  return request(`${scenicHost}/wifi/pageList`, {
    method: "GET",
    params
  });
}

/** WIFI 新增和编辑*/
export function apiWiFiAddUpdate(params: any) {
  return request(`${scenicHost}/wifi/${params.id ? "update" : "save"}`, {
    method: "POST",
    data: params
  });
}
//WIFI 详情
export function apiWiFiDetails(params: any) {
  return request(`${scenicHost}/wifi/info/${params}`, {
    method: "GET",
    params
  });
}
//WiFI 删除
export async function apiWiFiDelete(id: any) {
  return request(`${scenicHost}/wifi/info/${id}`, {
    method: "DELETE"
    // headers: {
    //   'Content-Type': 'application/x-www-form-urlencoded',
    // },
  });
}
/** WIFI 是否禁用 */
export async function apiWiFisEnable(params: any) {
  return request(`${scenicHost}/wifi/info/${params}`, {
    method: "PUT"
  });
}

//广播列表
export function apiRadioPageList(params: any) {
  return request(`${scenicHost}/radio/pageList`, {
    method: "GET",
    params
  });
}
/** 广播新增和编辑*/
export function apiRadioAddUpdate(params: any) {
  return request(`${scenicHost}/radio/${params.id ? "update" : "save"}`, {
    method: "POST",
    data: params
  });
}
//广播详情
export function apiRadioDetails(params: any) {
  return request(`${scenicHost}/radio/info/${params}`, {
    method: "GET",
    params
  });
}
//广播删除
export async function apiRadioDelete(id: any) {
  return request(`${scenicHost}/radio/info/${id}`, {
    method: "DELETE"
  });
}
/** 广播是否禁用 */
export async function apisRadioEnable(params: any) {
  return request(`${scenicHost}/radio/info/${params}`, {
    method: "PUT"
  });
}

//sos 报警点列表
export function apiSosPageList(params: any) {
  return request(`${scenicHost}/sos/pageList`, {
    method: "GET",
    params
  });
}
/** sos 报警新增和编辑*/
export function apiSosAddUpdate(params: any) {
  return request(`${scenicHost}/sos/${params.id ? "update" : "save"}`, {
    method: "POST",
    data: params
  });
}
//sos 报警详情
export function apiSosDetails(params: any) {
  return request(`${scenicHost}/sos/info/${params}`, {
    method: "GET",
    params
  });
}
//sos 报警删除
export async function apiSosDelete(id: any) {
  return request(`${scenicHost}/sos/info/${id}`, {
    method: "DELETE"
  });
}
/** sos 报警是否禁用 */
export async function apiSosEnable(params: any) {
  return request(`${scenicHost}/sos/info/${params}`, {
    method: "PUT"
  });
}

//游客服项目列表
export function apiTouristsPageList(params: any) {
  return request(`${scenicHost}/tourists/pageList`, {
    method: "GET",
    params
  });
}
/** 游客服项目增和编辑*/
export function apiTouristsAddUpdate(params: any) {
  return request(`${scenicHost}/tourists/${params.id ? "update" : "save"}`, {
    method: "POST",
    data: params
  });
}
//游客服项目详情
export function apiTouristsDetails(params: any) {
  return request(`${scenicHost}/tourists/info/${params}`, {
    method: "GET",
    params
  });
}
//游客服项目删除
export async function apiTouristsDelete(id: any) {
  return request(`${scenicHost}/tourists/info/${id}`, {
    method: "DELETE"
  });
}
/** 游客服项目是否禁用 */
export async function apiTouristsEnable(params: any) {
  return request(`${scenicHost}/tourists/info/${params}`, {
    method: "PUT"
  });
}

//娱乐项目列表
export function apiRecreationPageList(params: any) {
  return request(`${scenicHost}/entertainment/pageList`, {
    method: "GET",
    params
  });
}
/** 游客服项目增和编辑*/
export function apiRecreationAddUpdate(params: any) {
  return request(`${scenicHost}/entertainment/${params.id ? "update" : "save"}`, {
    method: "POST",
    data: params
  });
}
//游客服项目详情
export function apiRecreationDetails(params: any) {
  return request(`${scenicHost}/entertainment/info/${params}`, {
    method: "GET",
    params
  });
}
//游客服项目删除
export async function apiRecreationDelete(id: any) {
  return request(`${scenicHost}/entertainment/info/${id}`, {
    method: "DELETE"
  });
}
/** 游客服项目是否禁用 */
export async function apiRecreationEnable(params: any) {
  return request(`${scenicHost}/entertainment/info/${params}`, {
    method: "PUT"
  });
}

//节假日列表
export function apiHolidayPageList(params: any) {
  return request(`${scenicHost}/holiday/pageList`, {
    method: "GET",
    params
  });
}
/** 节假日增和编辑*/
export function apiHolidayAddUpdate(params: any) {
  return request(`${scenicHost}/holiday/info`, {
    method: "POST",
    data: params
  });
}
//节假日项目详情
export function apiHolidayDetails(params: any) {
  return request(`${scenicHost}/holiday/info/${params}`, {
    method: "GET",
    params
  });
}
//节假日项目删除
export async function apiHolidayDelete(id: any) {
  return request(`${scenicHost}/holiday/info/${id}`, {
    method: "DELETE"
  });
}
/** 节假日是否禁用 */
export async function apiHolidayEnable(params: any) {
  return request(`${scenicHost}/holiday/info/${params}`, {
    method: "PUT"
  });
}
/** 新增售票设备轮播图 */
export function addEquipmentCarousel(params: {
  data: { imgUrl: string; skipUrl: string }[];
  ticketEquipmentId: number;
}) {
  return request(`${scenicHost}/ticketEquipment/addCarousel`, {
    method: "POST",
    data: params
  });
}

/** 新增售票设备宣传信息 */
export function addEquipmentPublicize(params: { data: { publicizeUrl: string }[]; ticketEquipmentId: number }) {
  return request(`${scenicHost}/ticketEquipment/addPublicize`, {
    method: "POST",
    data: params
  });
}

/** 售票设备轮播图详情 */
export function getEquipmentCarousel(params: { ticketEquipmentId: number }) {
  return request(`${scenicHost}/ticketEquipment/getCarousel`, {
    method: "GET",
    params
  });
}

/** 售票设备宣传信息详情 */
export function getEquipmentPublicize(params: { ticketEquipmentId: number }) {
  return request(`${scenicHost}/ticketEquipment/getPublicize`, {
    method: "GET",
    params
  });
}
