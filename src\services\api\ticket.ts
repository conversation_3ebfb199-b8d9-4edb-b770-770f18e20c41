// @ts-ignore
/* eslint-disable */
// import { request } from './api';
import { request } from "@umijs/max";
import { scenicHost } from ".";

/**
 * lin start
 */

/* 【票务模块】 */

// 产品列表
export async function apiSimpleTicket(params: any) {
  const { data } = await request(`${scenicHost}/simpleTicket/pageList`, {
    method: "GET",
    params
  });
  return data;
}

// // 所有商品列表
// export async function apiSimpleTicket(params: any) {
//   const { data } = await request(`${scenicHost}/simpleTicket/pageList`, {
//     method: 'GET',
//     params,
//   });
//   return data;
// }
// 服务商（企业）列表
export async function apiCoInfoOption(params: any) {
  const { data } = await request(`${scenicHost}/scenic/getCoInfo/${params.scenicId}`);
  return data.map((item: any) => ({
    value: item.coId,
    label: item.coName
  }));
}
// 服务商（企业）列表
export function getCoInfo(params: { scenicId: string }) {
  return request(`${scenicHost}/scenic/getCoInfo/${params.scenicId}`);
}

// 产品新增
export function apiSimpleTicketAdd(params: any) {
  return request(`${scenicHost}/simpleTicket/info`, {
    method: "POST",
    data: params
  });
}

// 产品编辑
export function apiSimpleTicketEdit(params: any) {
  return request(`${scenicHost}/simpleTicket/info`, {
    method: "PUT",
    data: params
  });
}

// 产品详情
export async function apiSimpleTicketInfo(id: any) {
  const { data } = await request(`${scenicHost}/simpleTicket/info/${id}`);
  return data;
}

// 产品删除
export function apiSimpleTicketDel(id: string) {
  return request(`${scenicHost}/simpleTicket/info/${id}`, {
    method: "DELETE"
  });
}

// 产品禁/启用
export function apiSimpleTicketStatus(params: any) {
  return request(`${scenicHost}/simpleTicket/status`, {
    method: "POST",
    data: params
  });
}

// 分时列表
export function apiSimpleByIdList(params: any) {
  return request(`${scenicHost}/simpleTicket/simpleByIdList`, {
    method: "GET",
    params
  });
}

// 同步商品
export function apiBatchAddGoods(params: any) {
  return request(`${scenicHost}/simpleGoods/batchAddGoods`, {
    method: "POST",
    data: params
  });
}

// 商品列表
export async function apiSimpleGoods(params: any) {
  const { data } = await request(`${scenicHost}/simpleGoods/pageList`, {
    method: "GET",
    params
  });
  return data;
}

// 商品新增
export function apiSimpleGoodsAdd(params: any) {
  return request(`${scenicHost}/simpleGoods/save`, {
    method: "POST",
    data: params
  });
}

// 商品编辑
export function apiSimpleGoodsUp(params: any) {
  return request(`${scenicHost}/simpleGoods/update`, {
    method: "POST",
    data: params
  });
}

// 商品详情
export async function apiSimpleGoodsDetail(id: any) {
  const { data } = await request(`${scenicHost}/simpleGoods/info/${id}`, {
    method: "GET"
  });
  return data;
}

// 商品删除
export function apiSimpleGoodsDel(id: string) {
  return request(`${scenicHost}/simpleGoods/del/${id}`, {
    method: "DELETE"
  });
}

// 通过景区获取所有票 权限名称
export async function apiJurisDictionList(id: any) {
  const { data } = await request(`${scenicHost}/simpleGoods/jurisdictionList/${id}`, {
    method: "GET"
  });
  return data;
}

// 商品禁/启用
export function apiSimpleGoodsStatus(params: any) {
  return request(`${scenicHost}/simpleGoods/status`, {
    method: "POST",
    data: params
  });
}

// 编辑实际售价的接口
export function apiGoodsUpdatePrice(params: any) {
  return request(`${scenicHost}/simpleGoods/updatePrice`, {
    method: "POST",
    data: params
  });
}

// ======== 票务模块 =========

/**
 * @description: 票商品详情
 * @see https://dev.shukeyun.com/scenic/api-v2/doc.html#/%E7%A5%A8%E5%8A%A1%E6%A8%A1%E5%9D%97/%E5%95%86%E5%93%81%E6%A8%A1%E5%9D%97/infoUsingGET_15
 * @param {string} id
 */
export function getSimpleGoodsInfo(id: string) {
  return request<ResponseData<API.SimpleGoodsInfo>>(`${scenicHost}/simpleGoods/info/${id}`);
}

/** 商品分页列表查询 */
export async function getSimpleGoodsPageList(params: {
  /** 当前的页码 */
  pageNum?: number;
  /** 页面的容量 */
  pageSize?: number;
  /* 景区 ID */
  scenicId?: string;
}) {
  const { code, data } = await request(`${scenicHost}/simpleGoods/pageList`, {
    method: "GET",
    params
  });
  // 处理启用状态的样式
  // data.data.map((e) => {
  //   e.isEnable = {
  //     color: e.isEnable == '1' ? 'blue' : 'red',
  //     text: e.isEnable == '1' ? '启用' : '禁用',
  //   };
  // });
  return {
    data: data.data,
    // success 请返回 true，
    // 不然 table 会停止解析数据，即使有数据
    success: code === 20000,
    // 不传会使用 data 的长度，如果是分页一定要传
    total: data.total
  };
}

/** 根据产品 id，统计对应商品数量 */
export async function AddSimpleGoodStaNum(params: any) {
  return await request(`${scenicHost}/simpleGoods/statisticalNum`, {
    method: "GET",
    params
  });
}

/** 编辑状态 */
export async function AddSimpleGoodsStatus(params: any) {
  return await request(`${scenicHost}/simpleGoods/status`, {
    method: "POST",
    data: params
  });
}

/** 商品编辑 */
export async function AddSimpleGoodsUpdate(params: any) {
  return await request(`${scenicHost}/simpleGoods/update`, {
    method: "POST",
    data: params
  });
}

// === 检票规则 ===

/** 编辑和保存 */
export async function addTicketCheck(params: any) {
  return await request(`${scenicHost}/ticketCheck/${params.id ? "update" : "save"}`, {
    method: "POST",
    data: params
  });
}

/** 详情 */
export async function TicketCheckXq(params: any) {
  return await request(`${scenicHost}/ticketCheck/info`, {
    method: "GET",
    params
  });
}

/** 根据 id 删除 */
export function delTicketCheck(id: string) {
  return request(`${scenicHost}/ticketCheck/info/${id}`, {
    method: "DELETE"
  });
}

/** 检票规则下拉 */
export async function getTicketCheckList(params: any) {
  const { data } = await request(`${scenicHost}/ticketCheck/list`, {
    method: "GET",
    params
  });
  return data.map((item: any) => ({
    value: item.id,
    label: item.name,
    ...item
  }));
}

/** 简单列表 参数：景区 id */
export async function ticketRetreatList(params: any) {
  const { data } = await request(`${scenicHost}/ticketRetreat/list`, {
    method: "GET",
    params
  });
  return data.map((item: any) => ({
    value: item.id,
    label: item.name
  }));
}

/** 简单列表 参数：景区 id */
export async function ticketLssueList(params: any) {
  const { data } = await request(`${scenicHost}/ticketIssue/list`, {
    method: "GET",
    params
  });
  return data.map((item: any) => ({
    ...item,
    value: item.id,
    label: item.name
  }));
}
// 出票规则，选择列表
export async function apiIssueRuleList(params: any) {
  return await request(`${scenicHost}/ticketIssue/ruleList`, {
    method: "GET",
    params
  });
}
// 检票票规则，选择列表
export async function apiCheckRuleList(params: any) {
  return await request(`${scenicHost}/ticketCheck/ruleList`, {
    method: "GET",
    params
  });
}

/** 检票规则列表 */
export function getTicketCheckRulePageList(params) {
  return request<ResponseListData<any[]>>(`${scenicHost}/ticketCheck/pageList`, {
    method: "GET",
    params
  });
}

/** 禁用/启用 */
export async function getTicketCheckStatus(params: any) {
  return await request(`${scenicHost}/ticketCheck/status`, {
    method: "POST",
    data: params
  });
}

/** 核销列表 */
export function apiWriteOffList(params: any) {
  return request<ResponseListData<any[]>>(`${scenicHost}/ticketCheck/writeOffList`, {
    method: "GET",
    params
  });
}

// 获取分销商 (下游) 列表  经销
export async function getDistributorList(params: any) {
  return request(`${scenicHost}/distribution/distribution/downDistributorList`, {
    method: "GET",
    params: { ...params }
    // skipErrorHandler: true, //不走错误处理
  });
}

export function getCheckedTicketRecordsPageList(params) {
  return request<ResponseListData<any[]>>(`${scenicHost}/orderTicketCheck/checkTicketRecordPage`, {
    method: "POST",
    data: params
  });
}

// === 设备权限 ===

/** 一键赋值保存结果 */
export async function Saveconfirm(params: any) {
  return await request(`${scenicHost}/relationGoodsEquipment/confirm`, {
    method: "POST",
    data: params
  });
}

/** 设备权限页面，表格数据 */
export async function AuthorityPageList(params: any) {
  return await request(`${scenicHost}/relationGoodsEquipment/equipmentIdAuthorityPageList`, {
    method: "GET",
    params
  });
}

/** 根据设备 id 获取销售权限 */
export function EquipmentList(params: any) {
  return request(`${scenicHost}/relationGoodsEquipment/equipmentList`, {
    method: "GET",
    params
  });
}

/** 设备新增编辑 */
export async function AddEquipment(params: any) {
  return await request(`${scenicHost}/relationGoodsEquipment/equipmentSaveAndUpdate`, {
    method: "POST",
    data: params
  });
}

/** 商品列表页跳转根据商品 id 查询对应的所有设备 */
export function getgRPList(params: any) {
  return request(`${scenicHost}/relationGoodsEquipment/gRPList`, {
    method: "GET",
    params
  });
}

/** 商品列表页分配销售设备 保存操作 */
export async function saveAndUpdate(params: any) {
  return await request(`${scenicHost}/relationGoodsEquipment/saveAndUpdate`, {
    method: "POST",
    data: params
  });
}

// === 退票服务 ===
/** 详情 */
export function ticketRetreatXq(params: any) {
  return request(`${scenicHost}/ticketRetreat/info`, {
    method: "GET",
    params
  });
}

/** 规则新增 */
export async function AddTicketRetreat(params: any) {
  return await request(`${scenicHost}/ticketRetreat/${params.id ? "update" : "save"}`, {
    method: "POST",
    data: params
  });
}

/** 退票规则列表 */
export function getTicketRetreatRulePageList(params) {
  return request<ResponseListData<any[]>>(`${scenicHost}/ticketRetreat/pageList`, {
    method: "GET",
    params
  });
}

/** 根据 id 删除 */
export function delTicketRetreat(id: string) {
  return request(`${scenicHost}/ticketRetreat/info/${id}`, {
    method: "DELETE"
  });
}

/** 禁用/启用 */
export async function getTicketRetreatStatus(params: any) {
  return await request(`${scenicHost}/ticketRetreat/status`, {
    method: "POST",
    data: params
  });
}

// 票务打印模块
/** 分页列表 */
export async function PrintPageList(
  params: {
    // query
    /** 当前的页码 */
    current?: number;
    /** 页面的容量 */
    pageSize?: number;
  },
  options?: { [key: string]: any }
) {
  params.current = params.current;
  const { code, data } = await request(`${scenicHost}/relationGoodsPrint/pageList`, {
    method: "GET",
    params: {
      ...params
    },
    ...(options || {})
  });
  return {
    data: data.data,
    // success 请返回 true，
    // 不然 table 会停止解析数据，即使有数据
    success: code === 20000,
    // 不传会使用 data 的长度，如果是分页一定要传
    total: data.total
  };
}

/** 新增编辑 */
export async function getRelationGoodsPrint(params: any) {
  return await request(`${scenicHost}/relationGoodsPrint/info`, {
    method: "POST",
    data: params
  });
}

// 单种票 (产品) 模块
/** 分页列表 */
export async function simpleTicketPageList(
  params: {
    // query
    /** 当前的页码 */
    current?: number;
    /** 页面的容量 */
    pageSize?: number;
  },
  options?: { [key: string]: any }
) {
  params.current = params.current;
  const { code, data } = await request(`${scenicHost}/simpleTicket/pageList`, {
    method: "GET",
    params: {
      ...params
    },
    ...(options || {})
  });
  return {
    data: data.data,
    // success 请返回 true，
    // 不然 table 会停止解析数据，即使有数据
    success: code === 20000,
    // 不传会使用 data 的长度，如果是分页一定要传
    total: data.total
  };
}

/** 根据 id 删除票务 */
export function delSimpleTicket(id: string) {
  return request(`${scenicHost}/simpleTicket/info/${id}`, {
    method: "DELETE"
  });
}

/** 下拉票务详情 */
export function getTicketDetail(id: string) {
  return request(`${scenicHost}/simpleTicket/info/${id}`, {
    method: "GET"
  });
}

/** 通过商品 id 获取所有分时预约下拉列表（库存专用） */
export function getSimpleLawsList(id: string, params?: any) {
  return request(`${scenicHost}/simpleTicket/stockSimpleLaws/${id}`, {
    method: "GET",
    params
  });
}

export function getGoodsListApi(params?: any) {
  return request(`${scenicHost}/simpleTicket/stockSimpleLaws`, {
    method: "POST",
    data: {
      ...params
    }
  });
}

// /** 通过商品 id 获取所有分时预约下拉列表 */
// export function getSimpleLawsList(id: string) {
//   return request(`${scenicHost}/simpleTicket/simpleLaws/${id}`, {
//     method: 'GET',
//   });
// }
// /** 通过景区 id 获取所有产品下拉列表 */
// export async function getSimpleList(options: any) {
//   const { code, data } = await request<API.RuleListItem>(
//     `${scenicHost}/simpleTicket/simpleList/${options.id}`,
//   );
//   if (code == 20000) {
//     return data;
//   } else {
//     return [];
//   }
// }
/** 通过景区 id+ 服务商 id 获取所有产品下拉列表 */
export async function getSimpleList(params: any) {
  try {
    const { data } = await request(`${scenicHost}/simpleTicket/SPSimpleList`, {
      method: "POST",
      data: params
    });
    return data;
  } catch (error) {
    return [];
  }
}

// export function getSimpleList(id: string) {
//   return request(`${scenicHost}/simpleTicket/simpleList/${id}`, {
//     method: 'GET',
//   });
//   if (code == 20000) {
//     return data.map((item) => {
//       return {
//         value: item.id,
//         label: item.checkName,
//       };
//     });
//   } else {
//     return [];
//   }
// }

/** 通过分时预约 id 获取对象数据 */
export function getSimpletimeInfo(id: string) {
  return request(`${scenicHost}/simpleTicket/timeShareInfo/${id}`, {
    method: "GET"
  });
}

/** 新增编辑票务 */
export async function getSimpleTicketInfo(params: any) {
  return await request(`${scenicHost}/simpleTicket/info`, {
    method: "POST",
    data: params
  });
}

// 出票规则模块
/** 出票规则新增 */
export async function AddTicketLssue(params: any) {
  return await request(`${scenicHost}/ticketIssue/${params.id ? "update" : "save"}`, {
    method: "POST",
    data: params
  });
}
/** 审批配置 */
export async function apiApproveConfigure(params: any) {
  return await request(`${scenicHost}/ticketIssue/approveConfigure`, {
    method: "POST",
    data: params
  });
}
/** 审批详情 */
export async function apiApproveConfInfo(params: any) {
  return await request(`${scenicHost}/ticketIssue/approveConfInfo/${params.approveId}`, {
    method: "get"
  });
}

/** 禁用/启用 */
export async function getTicketLssueStatus(params: any) {
  return await request(`${scenicHost}/ticketIssue/status`, {
    method: "POST",
    data: params
  });
}

/** 根据 id 删除 */
export function delTicketLssue(id: string) {
  return request(`${scenicHost}/ticketIssue/info/${id}`, {
    method: "DELETE"
  });
}

/** 简单列表 参数：景区 id */
export function ticketLssueXq(params: any) {
  return request(`${scenicHost}/ticketIssue/info`, {
    method: "GET",
    params
  });
}

/** 权益列表 */
export async function downListApi() {
  try {
    const { data } = await request(`${scenicHost}/rightsService/rights/downList`);
    return data.map((item: any) => ({ value: item.rightsId, label: item.rightsName }));
  } catch (error) {}
}

/** 编辑 */
export function UpManageStock(params: any) {
  return request(`${scenicHost}/manageStock/info`, {
    method: "PUT",
    data: params
  });
}

/** 详情 */
export function ManageStockXq(id: string) {
  return request(`${scenicHost}/manageStock/info/${id}`, {
    method: "GET"
  });
}

/** 删除 */
export function delManageStock(dto: string) {
  return request(`${scenicHost}/manageStock/info/${dto}`, {
    method: "DELETE"
  });
}

/** 禁用/启用 */
export async function getManageStockStatus(params: any) {
  return await request(`${scenicHost}/manageStock/status`, {
    method: "PUT",
    data: params
  });
}

// 查询服务商 id
export function apiUserCoInfo(params: any) {
  return request(`${scenicHost}/orgStructure/userCoInfo`, {
    method: "GET",
    params
  });
}

/** 根据批次 id 查询库存区块链 */
export async function manageStockBatchInfoList(params: any) {
  try {
    return await request(`${scenicHost}/manageStock/batchInfoList`, {
      method: "POST",
      data: params
    });
  } catch (error) {
    return {};
  }
}

/** 调用区块链查询剩余库存明细 */
export async function surplusStockNumber(params: any) {
  const { code, data } = await request(`${scenicHost}/manageStock/surplusStockNumber`, {
    method: "GET",
    params
  });
  return {
    data: data.data,
    success: code === 20000,
    total: data.total
  };
}

/** 门票销售管理列表 */
export function getTicketSalePageList(params: any) {
  return request<ResponseListData<Ticket.SaleTicketItem[]>>(`${scenicHost}/orderTicketCheck/pageList`, {
    method: "POST",
    data: params
  });
}

/** 门票销售管理详情 */
export function getOrderTicketCheckDetail(ticketId: string) {
  return request<ResponseData<Ticket.TicketInfo>>(`${scenicHost}/orderTicketCheck/info/${ticketId}`, {
    method: "GET"
  });
}

// 批量查询可退票票详情
export function getMultipleTicketInfo(params: string[]) {
  return request<ResponseData<Ticket.MultipleTicketInfoType[]>>(`${scenicHost}/orderTicketCheck/retreatInfoList`, {
    method: "POST",
    data: params,
    skipCommonParams: true
  });
}

// 批量查询可核销列表
export function getAvailableCheckTicket(params: string[]) {
  return request<ResponseData<Ticket.AvailableCheckItem[]>>(`${scenicHost}/orderTicketCheck/ticketCheckInfoBatch`, {
    method: "POST",
    data: params,
    skipCommonParams: true
  });
}

export interface CheckedMultipleTicketParams {
  data: {
    data: {
      checkCount: number;
      idCardNumber: string;
      useDate: string;
    }[];
    ticketNumber: string;
  }[];
  userId: string;
  username: string;
}

// 批量核销
export function checkedMultipleTicket(params: CheckedMultipleTicketParams) {
  return request<ResponseData<any>>(`${scenicHost}/ticketCheck/manualCheck2`, {
    method: "PUT",
    data: params
  });
}

interface RefundMultipleTicketParams {
  data: {
    idCardNumber: string[];
    ticketNumber: string;
  }[];
  remark?: string;
}
// 批量退票
export function refundMultipleTicket(params: RefundMultipleTicketParams) {
  return request<ResponseData<any>>(`${scenicHost}/order/ticketRefund`, {
    method: "POST",
    data: params
  });
}

interface updateServiceChargeParams {
  dataSourcesType: number;
  goodsId: string;
  goodsName: string;
  productName: string;
  scenicName: string;
  serviceChargeRate: number;
  serviceCharge: number;
  marketPrice: number;
  createUserName: string;
  updateContent: string;
}

/** 提交技术服务费审批 */
export function addChargeApproval(params: updateServiceChargeParams, options: Record<string, any> = {}) {
  return request(`${scenicHost}/chargeApproval/info`, {
    method: "POST",
    data: params,
    ...options
  });
}

interface ServiceChargeInfoData {
  approvalState: number;
  approvalType: number;
  serviceCharge: number;
  serviceChargeRate: number;
  updateContent: string;
}
/** 获取发行服务费 */
export function getServiceChargeInfo(
  params: {
    id: string;
  },
  options: Record<string, any> = {}
) {
  return request<ResponseData<ServiceChargeInfoData>>(`${scenicHost}/chargeApproval/info/${params.id}`, {
    method: "GET",
    params,
    skipErrorHandler: true,
    ...options
  });
}

// 更新状态
export async function getOrderTicketCheckStatus(params: any) {
  return await request(`${scenicHost}/orderTicketCheck/info`, {
    method: "PUT",
    data: params
  });
}
//后台退票
export async function getOrderRefundBack(params: any) {
  return await request(`${scenicHost}/order/refundBack`, {
    method: "PUT",
    data: params
  });
}

// 库存预警
export async function getStockWarningTotal(data: API.StockListParams) {
  return request(`${scenicHost}/manageStock/getStockWarningTotal`, {
    method: "POST",
    data
  });
}

// 库存列表
export async function getTicketStockList(params: API.StockListParams, options: Record<string, any> = {}) {
  return request<ResponseListData<API.StockListItem[]>>(`${scenicHost}/manageStock/manageStockList`, {
    method: "POST",
    data: params,
    ...options
  });
}

// 商品分时
export function getTimeShareByGoodsId(params: string) {
  return request(`${scenicHost}/simpleGoods/timeShareByGoodsId/${params}`);
}

/** 查询库存详情 */
export async function getTicketStockInfo(params: API.StockInfoParams, options: Record<string, any> = {}) {
  return request<ResponseData<API.StockListItem>>(`${scenicHost}/manageStock/info`, {
    method: "GET",
    params,
    ...options
  });
}

/** 出票规则列表 */
export function getIssueTicketRulePageList(params) {
  return request<ResponseListData<any[]>>(`${scenicHost}/ticketIssue/pageList`, {
    method: "GET",
    params
  });
}

// 区块链记录
export function getChainInfo(params: { ticketNumber: string }) {
  return request<
    ResponseData<{
      id: string;
      txId: string;
    }>
  >(`${scenicHost}/ticketIssue/selectTxIdByTicketNumber`, {
    params,
    method: "GET"
  });
}

// 溯源记录
export function getTraceRecord(params: { ticketId: string }) {
  return request(`${scenicHost}/blockChain/ticket/traceRecord`, {
    params,
    method: "GET"
  });
}

// 库存创建 - 铸造记录
export function getInventoryTraceability(params: { batchId: string; distributorId: string }) {
  return request(`${scenicHost}/manageStock/inventoryTraceability`, {
    params,
    method: "GET"
  });
}

// 库存销量
export function getHistorySales(params: any) {
  return request(`${scenicHost}/bourse/getHistorySales`, {
    params,
    method: "GET"
  });
}

// 库存销量
export function getDigitGoodsByCompany(params: any) {
  return request(`${scenicHost}/bourse/getDigitGoodsByCompany`, {
    params,
    method: "GET"
  });
}

// 查询库存
export async function apiGetInventory(data) {
  return request<ResponseData<any>>(`${scenicHost}/manageStock/sales/inventory`, {
    method: "POST",
    data
  });
}

// 查询可发行库存量
export async function apiGetPublishedStock(data) {
  return request<ResponseData<any>>(`${scenicHost}/manageStock/publishedStock`, {
    method: "POST",
    data
  });
}
