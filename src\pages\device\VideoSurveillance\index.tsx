import EditPop from "@/common/components/EditPop";
import { getUniqueId } from "@/common/utils/tool";
import {
  apiVideoAddUpdate,
  apiVideoDelete,
  apiVideoDetails,
  apiVideoFisEnable,
  apiVideoPageList
} from "@/services/api/device";
import { PlusOutlined } from "@ant-design/icons";
import ProForm, { ModalForm, ProFormSelect, ProFormText, ProFormUploadButton } from "@ant-design/pro-form";
import type { ActionType } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import { Button, Input, message, Modal, Tag } from "antd";
import { useRef, useState } from "react";
import { Access, useAccess, useModel } from "@umijs/max";
import Details from "./Details";
import { tableConfig } from "@/common/utils/config";
import { getEnv } from "@/common/utils/getEnv";

const { confirm } = Modal;

/**
 * @en-US Update node
 * @zh-CN 更新节点
 *
 * @param fields
 */
const VideoSurveillance = () => {
  const formRef = useRef();
  const access = useAccess();
  const [formImg] = ProForm.useForm();
  const formObj = useRef();

  // 【表单】数据绑定
  const [editVisible, setEditVisible] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<any>({ id: "", isEnable: 0 });
  const [modalVisit, setModalVisit] = useState(false);
  //获取当前ID
  const [Id, setId] = useState(undefined);
  /**
   * @en-US Pop-up window of new window
   * @zh-CN 新增窗口的弹窗
   *  */
  const [createModalVisible, handleModalVisible] = useState<boolean>(false);
  /**
   * @zh-CN 分布更新窗口的弹窗
   * */
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  /**
   * 查询详情的id值
   * */
  const [optionId, handleOptionId] = useState<string>("");

  ///区分新增和编辑的状态
  const [distinguishStatus, setDistinguishStatus] = useState(false);
  /**
   * 景区列表
   * */
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.RuleListItem>();

  // 获取景区ID
  const { initialState }: any = useModel("@@initialState");
  const scenicId = initialState?.scenicInfo?.scenicId;
  // console.log(scenicId, initialState);
  //获取景区名称
  const scenicName = initialState?.scenicInfo?.scenicName;
  //初始视化数据
  const [initialValues, setInitialValues] = useState({});

  // 详情
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [detailData, setDetailsData] = useState([]);
  const { UPLOAD_HOST, IMG_HOST } = getEnv();
  // //获取当前id
  // const [isId,setIsId]=useState()

  const showModal = async (val: any) => {
    const id = val.id;
    const result: any = await apiVideoDetails(id);
    const { data } = result;
    console.log("详情列表数据", data);
    //回选
    // formObj?.current?.setFieldsValue(data);
    setDetailsData(data);
    setInitialValues(data);
    // setDataSource(data);
    setId(val.id);
    // setIsEnableStatus(data.isEnable);
    // setModalVisit(true);
    setIsModalVisible(true);
  };

  const handleOk = () => {
    setIsModalVisible(false);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  //图片地址
  const [url, setUrl] = useState("");

  /* 图片预览所需信息
   * */
  const [imgInfo, handleImgInfo] = useState({
    previewImage: "",
    previewVisible: false,
    previewTitle: ""
  });

  // 图片回选

  // if (data.url !== '') {
  //   formObj.setFieldsValue({
  //     title: data['title'],
  //     content: data['content'] !== '' ? data['content'] : '无',
  //     file: [
  //       {
  //         uid: -data.id,
  //         name: data.name,
  //         status: 'done',
  //         url: UPLOAD_HOST + data['url'],
  //       },
  //     ],
  //   });
  // } else {
  //   formObj.resetFields();
  // }

  const [urlArray, setUrlArray] = useState([]);
  const [removeStatus, setRemoveStatus] = useState(false);
  // const [removeImg, setRemoveImg] = useState(false)
  const [removeImg, setRemoveImg] = useState([]);
  const [clearStatus, setClearStatus] = useState(false);
  const imgUrl: any = [];

  const [picture, setPicture] = useState("");

  //上传图片
  const addUpload = ({ file, fileList }: any) => {
    console.log("pppppppppppp", file, fileList);
    if (file.status == "done") {
      message.success("上传图片成功");
      const result = file.response;
      const path = result[0].path;
      // fileList.forEach((e) => {
      //   if (e.response) {
      //     imgUrl.push({ id: e.uid, name: e.name, path: e.response[0].path });
      //   }
      // });
      setUrlArray(fileList);
      setUrl(path);
      //  getItem(file.name);
    } else if (file.status == "removed") {
      message.success("删除图片成功");
      setRemoveImg(fileList);
      // removeImg = fileList
      setRemoveStatus(true);
      console.log("oooooooooooooooo", removeImg, fileList);
      // console.log(file);
    } else if (file.status == "error") {
      // message.error('图片大小不能超过1M');
      message.error("图片上失败");
    }
  };
  /*
          /*
           * 预览图片
           * */
  const handlePreview = async (file: any) => {
    console.log(file);
    if (file.status && file.status !== "done") {
      message.warn("无法预览");
      return;
    }
    let previewImage = "";
    if (file.url) {
      //已有
      previewImage = file.url;
      console.log(previewImage);
    } else {
      //新增
      previewImage = IMG_HOST + file?.response?.[0]?.path;
      console.log("图片地址", previewImage);
    }
    handleImgInfo({
      previewImage,
      previewVisible: true,
      previewTitle: "预览"
    });
  };

  const [urls, setUrls] = useState("");
  //编辑
  const updateMethod = async (val: any) => {
    const id = val.id;
    const { data } = await apiVideoDetails(id);
    console.log("uuuuuuuuuuuuuuuuuuuuuu", data);
    const imgs: any = [];
    const pathList = data.videoPicture.split(",");
    if (data.videoPicture !== "null" && data.videoPicture !== "") {
      pathList.map((item, index) => {
        imgs.push({
          uid: -index,
          url: UPLOAD_HOST + item,
          name: index,
          status: "done"
        });
      });
    }
    // console.log('yyyyyyyyyyyyyy', imgs, pathList);
    formImg.setFieldsValue({
      ...data,
      file: imgs
    });
    setId(val.id);
    setIsModalVisible(false);
    setModalVisit(true);
    setDistinguishStatus(true);
    setUrls(data.videoPicture);
  };

  //启用/禁用
  const onStatus = async (val: any) => {
    console.log(val.isEnable);
    confirm({
      title: `您确定${val.isEnable == 1 ? "禁用" : "启用"}吗?`,
      // content: 'Some descriptions',
      onOk: async () => {
        try {
          const result = await apiVideoFisEnable(val.id);
          message.success(val.isEnable == 0 ? "启用成功" : "禁用成功");
          console.log(val);
          // getNoticeList()
          // 刷新
          actionRef?.current?.reload();
          setIsModalVisible(false);
        } catch (e) {
          console.error(e);
        }
      },
      onCancel() {
        console.log("Cancel");
      }
    });
  };
  //删除
  // const onDelete = () => { }
  const onDelete = (id: any) => {
    confirm({
      title: `您确定删除吗?`,
      // content: 'Some descriptions',
      onOk: async () => {
        try {
          const result = await apiVideoDelete(id);
          // console.log(result);
          message.success("删除成功");
          // 刷新
          actionRef?.current?.reload();
          setIsModalVisible(false);
        } catch (e) {
          console.log(e);
        }
      },
      onCancel() {
        console.log("Cancel");
      }
    });
  };

  const [defaultImg, setDefaultImg] = useState(undefined);

  const getVideoPageList = async (params: any) => {
    const pars = { ...params, scenicId };
    try {
      const result = await apiVideoPageList(pars);
      const { data } = result.data;
      console.log("hujiajia", data);
      // setBrandData(data)
      // 刷新
      return {
        data,
        success: true,
        total: data.total
      };
    } catch (e) {
      console.error(e);
    }
  };
  // 保存数据
  const submitData = async (val: any) => {
    console.log("valvlvlalvalvlava", val);
    //新增
    if (!distinguishStatus) {
      let delfaultVideoPicture = "";
      if (val.file || val.file?.length > 1) {
        val.file.forEach((item, index) => {
          delfaultVideoPicture = delfaultVideoPicture + "," + item.response[0].path;
        });
        val.videoPicture = delfaultVideoPicture.replace(/^,/, "");
      } else {
        val.videoPicture = "null";
      }
      // new RegExp(/^,/)
      console.log("mmmmmmmmmmmmmmmmmmmmmmmmmmmmm", val.videoPicture);
    }
    // 编辑
    else {
      let delfaultVideoPicture = "";
      let delfaultUrl = "";
      val.file.forEach((item, index) => {
        if (item.response) {
          delfaultVideoPicture = delfaultVideoPicture + "," + item.response[0].path;
        } else {
          delfaultUrl = delfaultUrl + "," + item.url.slice(7);
        }
      });
      val.videoPicture = (delfaultUrl + delfaultVideoPicture).replace(/^,/, "");
      console.log("编辑部分", val);
      // setPicture(val.videoPicture)
      if (val.file.length < 1) {
        val.videoPicture = "null";
      }
    }
    // else {
    //   if (removeStatus) {
    //     if (removeImg.length < 1) {
    //       val.videoPicture = '123'
    //       console.log('已经被删除了？？？？？？')
    //     } else {
    //       console.log('删除后图片', removeImg, url);
    //       if (urlArray.length == 1) {
    //         let url1 = url;
    //         removeImg.forEach((item, index) => {
    //           url1 = url1 + ',' + item.url.slice(7);
    //         });
    //         val.videoPicture = url1;
    //       } else if (urlArray.length > 1) {
    //         let url1 = ''
    //         removeImg.forEach((item2, index) => {
    //           url1 = item2.url.slice(7) + url1
    //         });
    //         urlArray.forEach((item) => {
    //           url1 = item.path + ',' + url1
    //         });
    //         val.videoPicture = url1
    //       }

    //       // else if (urlArray.length < 1) {
    //       //   let url1 = '?'
    //       //   removeImg.forEach((item2, index) => {
    //       //     url1 = url1 + ',' + item2.url.slice(7)
    //       //   });
    //       //   val.videoPicture = url1.substring(2)
    //       //   console.log('小小白白拜拜啊表白阿比', val.videoPicture)
    //       // }
    //     }
    //   } else {
    //     console.log('上传后的图片', urlArray, '==========', urls);
    //     let url1 = urls
    //     urlArray.forEach((item, index) => {
    //       url1 = url1 + ',' + item.path;
    //     });
    //     val.videoPicture = url1
    //   }
    // }
    console.log("hujiajiajiajiajiajiajaia", val);
    const pars = { ...val, scenicId };
    try {
      const result = await apiVideoAddUpdate(pars);
      // val.videoPicture = ''
      // message.success('保存成功');
      message.success(val.hasOwnProperty("id") ? "编辑成功" : "新增成功");
      actionRef?.current?.reload();
      setEditVisible(false);
      setModalVisit(false);
    } catch (e) {
      console.error(e);
    }

    // if (videoPicture) {
    //   message.success('保存成功');
    //   setEditVisible(false);
    // } else {
    //   message.error('保存失败');
    //   setEditVisible(true);
    // }
  };

  const columns: any = [
    {
      title: "所属景区",
      dataIndex: "scenicName",
      hideInSearch: true
    },
    {
      title: "监控点名称",
      dataIndex: "videoName",
      // ellipsis: true,
      render: (dom, record) => {
        return <a onClick={() => showModal(record)}>{dom}</a>;
      },
      renderFormItem: () => {
        return <Input placeholder="请输入监控点名称" />;
      }
      // transform: (date: any) => {
      //   console.log(date);
      //   return {
      //     serviceProviderId: date,
      //   };
      // },
      // renderFormItem: () => {
      //   return <MonitoringName />;
      // },
      // render: (dom: any, entity: any) => {
      //   return dom;
      //   // return (
      //   //   <a
      //   //     onClick={async () => {
      //   //       // handleModalVisible(true);
      //   //       // handleOptionId(entity.id);
      //   //       setDataSource(await getIddetails(entity.id));
      //   //       setDetailsVisible(true);
      //   //     }}
      //   //   >
      //   //     {dom}
      //   //   </a>
      //   // );
      // },
    },
    {
      title: "品牌",
      dataIndex: "brand",
      valueType: "select",
      // hideInSearch: true,
      valueEnum: {
        "0": "雄迈",
        "1": "星望",
        "2": "宇视",
        "3": "天地伟业",
        "4": "景阳",
        "5": "联通",
        "6": "捷高",
        "7": "华迈",
        "8": "海康云平台",
        "9": "海康",
        "10": "电信全球眼副本",
        "11": "flash格式",
        "12": "烽火",
        "13": "电信监控",
        "14": "大华云平台",
        "15": "大华",
        "16": "奥利克斯"
      }
    },
    {
      title: "链接地址",
      dataIndex: "linkAddress",
      hideInSearch: true
    },
    {
      title: "序列号",
      dataIndex: "serial",
      hideInSearch: true
    },
    {
      title: "状态",
      dataIndex: "isEnable",
      valueType: "select",
      valueEnum: {
        "0": {
          text: "禁用"
        },
        "1": {
          text: "启用"
        }
      },
      render: (dom: any, record: any) => {
        return <Tag color={record.isEnable == 1 ? "blue" : "red"}>{record.isEnable == 1 ? "已启用" : "已禁用"}</Tag>;
      }
      // render: (_, record) => <Tag color={record.isEnable.color}>{record.isEnable.text}</Tag>,
    }
  ];
  const editColumns = [
    {
      columns: [
        {
          title: "所属景区",
          dataIndex: "scenicName",
          name: "scenicName",
          initialValue: `${scenicName}`,
          key: "scenicName",
          // valueEnum: {
          //   '0': '公告',
          // },
          formItemProps: {
            // rules: [{ required: true }],
            // disable: false
          },
          fieldProps: {
            disabled: true
          }
        },
        {
          dataIndex: "videoName",
          title: "监控点名称",
          name: "videoName",
          key: "videoName",
          fieldProps: {},
          formItemProps: {
            rules: [{ required: true }, { max: 100, message: "最多可输入100个字符" }]
          }
        },
        {
          dataIndex: "loginName",
          title: "账号",
          name: "loginName",
          key: "loginName",
          fieldProps: {},
          formItemProps: {
            rules: [{ required: true }, { max: 100, message: "最多可输入100个字符" }]
          }
        },
        {
          dataIndex: "loginPassword",
          title: "密码",
          name: "loginPassword",
          key: "loginPassword",
          fieldProps: {},
          formItemProps: {
            rules: [{ required: true }, { max: 100, message: "最多可输入100个字符" }]
          }
        },
        {
          dataIndex: "longitude",
          title: "经度",
          tooltip: '"-" 为西经,"+" 为东经',
          name: "longitude",
          key: "longitude",
          fieldProps: {},
          formItemProps: {
            rules: [
              { required: true },
              // { max: 100, message: '最多可输入100个字' },
              {
                pattern:
                  /^(\-|\+)?(((\d|[1-9]\d|1[0-7]\d|0{1,3})\.\d{0,6})|(\d|[1-9]\d|1[0-7]\d|0{1,3})|180\.0{0,6}|180)$/,
                message: '(范围："-180°~ +180°",保留6位小数)'
              }
              // { type: 'number', message: '请输入数字' }
            ]
          }
        },
        {
          dataIndex: "latitude",
          title: "纬度",
          tooltip: '"+"为北纬，"-"为南纬',
          name: "latitude",
          key: "latitude",
          fieldProps: {},
          formItemProps: {
            rules: [
              { required: true },
              {
                pattern: /^(\-|\+)?([0-8]?\d{1}\.\d{0,6}|90\.0{0,6}|[0-8]?\d{1}|90)$/,
                message: '( 范围："-90°~+90°",保留6位小数)'
              }
            ]
          }
        },

        {
          title: "品牌",
          dataIndex: "brand",
          name: "brand",
          valueEnum: {
            "0": "雄迈",
            "1": "星望",
            "2": "宇视",
            "3": "天地伟业",
            "4": "景阳",
            "5": "联通",
            "6": "捷高",
            "7": "华迈",
            "8": "海康云平台",
            "9": "海康",
            "10": "电信全球眼副本",
            "11": "flash格式",
            "12": "烽火",
            "13": "电信监控",
            "14": "大华云平台",
            "15": "大华",
            "16": "奥利克斯"
          },
          key: "brand",
          formItemProps: {
            rules: [{ required: true }]
          },
          valueType: "select",
          fieldProps: {
            // mode: 'multiple',
            // options: positionValue2 == 1 ? scenicData2 : companyData2,
            // onChange: (value, option) => {
            //   console.log(value);
            //   setAcceptorData(value);
            // },
            // showArrow: true,
            // disabled: flag ? false : show ? false : true,
          }
        },
        {
          dataIndex: "linkAddress",
          title: "链接地址",
          name: "linkAddress",
          key: "linkAddress",
          fieldProps: {},
          formItemProps: {
            rules: [{ required: true }, { max: 100, message: "最多可输入100个字符" }]
          }
        },
        {
          dataIndex: "largeM3u8Address",
          title: "大码率M3U8播放地址",
          name: "largeM3u8Address",
          key: "largeM3u8Address",
          fieldProps: {},
          formItemProps: {
            rules: [{ required: true, message: "最多可输入100个字符" }]
          }
        },

        {
          dataIndex: "littleM3u8Address",
          title: "小码率M3U8播放地址",
          name: "littleM3u8Address",
          key: "littleM3u8Address",
          fieldProps: {},
          formItemProps: {
            rules: [{ required: true, message: "最多可输入100个字符" }]
          }
        },
        {
          dataIndex: "videoPoint",
          title: "管理端口",
          name: "videoPoint",
          key: "videoPoint",
          fieldProps: { max: 100, message: "最多可输入100个字符" }
        },
        {
          dataIndex: "channel",
          title: "通道号",
          name: "channel",
          key: "channel",
          fieldProps: { max: 100, message: "最多可输入100个字符" }
          // formItemProps: {
          //   rules: [{ required: true }],
          // },
        },
        {
          dataIndex: "videoAddress",
          title: "视频服务地址",
          name: "videoAddress",
          key: "videoAddress",
          fieldProps: {},
          formItemProps: {
            rules: [{ required: true, message: "最多可输入100个字符" }]
          }
        },
        {
          dataIndex: "serial",
          title: "序列号",
          name: "serial",
          key: "serial",
          fieldProps: {},
          formItemProps: {
            rules: [{ required: true, message: "最多可输入100个字符" }]
          }
        },
        {
          dataIndex: "videoPicture",
          // title:'监控图片列表',
          // key: 'img',
          renderFormItem: () => {
            return (
              <ProForm.Item
                label="监控图片列表"
                tooltip={
                  <>
                    <span>最多可上传 9 张图片,每张最</span>
                    <div>大为100M,支持jpg、png格式</div>
                  </>
                }
              >
                <ProFormUploadButton
                  name="file"
                  // label="图片"
                  max={99}
                  // fileList={defaultFile}
                  // rules={[{ required: true, message: '请上传图片' }]}
                  fieldProps={{
                    multiple: false,
                    listType: "picture-card",
                    accept: ".jpg,.png,.jpeg",
                    onPreview: handlePreview,
                    onChange: addUpload
                  }}
                  action={UPLOAD_HOST}
                />
              </ProForm.Item>
            );
          }
        }
      ]
    }
  ];
  return (
    <>
      {/* 详情 */}
      <Modal
        title="视频监控详情"
        visible={isModalVisible}
        width={800}
        onOk={handleOk}
        onCancel={handleCancel}
        footer={[
          <Access key={getUniqueId()} accessible={access.canHardwareVideo_openClose}>
            <Button
              type="primary"
              ghost
              danger={detailData.isEnable == 1 ? true : false}
              key="isEnable"
              onClick={() => onStatus(detailData)}
            >
              {detailData.isEnable == 1 ? "禁用" : "启用"}
            </Button>
          </Access>,
          <Access key={getUniqueId()} accessible={access.canHardwareVideo_delete}>
            <Button key="del" type="primary" danger onClick={() => onDelete(Id)}>
              删除
            </Button>
          </Access>,
          <Access key={getUniqueId()} accessible={access.canHardwareVideo_edit}>
            <Button key="update" type="primary" onClick={() => updateMethod(detailData)}>
              编辑
            </Button>
          </Access>,
          <Button key="col" onClick={handleCancel}>
            取消
          </Button>
        ]}
      >
        <Details detailData={detailData} />
      </Modal>
      {/* 编辑 */}

      {JSON.stringify(initialValues) !== "{}" ? (
        <ModalForm
          title="视频监控"
          visible={modalVisit}
          formRef={formRef}
          form={formImg}
          initialValues={initialValues}
          onFinish={async val => {
            val.id = Id;
            // val.isEnable = isEnableStatus
            submitData(val);
            return true;
          }}
          onVisibleChange={visit => {
            setModalVisit(visit);
            if (!visit) {
              setInitialValues({});
            }
          }}
        >
          <ProForm.Group>
            <ProFormText width="md" name="scenicName" label="所属景区" initialValue={scenicName} disabled={true} />
            <ProFormText
              width="md"
              name="videoName"
              label="监控点名称"
              placeholder="请输入监控点名称"
              rules={[
                { required: true, message: "请输入监控名称" },
                { max: 100, message: "最多可输入100个字符" }
              ]}
            />
          </ProForm.Group>

          <ProForm.Group>
            <ProFormText
              width="md"
              name="loginName"
              label="账号"
              placeholder="请输入账号"
              rules={[
                { required: true, message: "请输入账号" },
                { max: 100, message: "最多可输入100个字符" }
              ]}
            />
            <ProFormText
              width="md"
              name="loginPassword"
              label="密码"
              placeholder="请输入密码"
              rules={[
                { required: true, message: "请输入密码" },
                { max: 100, message: "最多可输入100个字符" }
              ]}
            />
          </ProForm.Group>

          <ProForm.Group>
            <ProFormText
              width="md"
              name="longitude"
              label="经度"
              tooltip='"-" 为西经,"+" 为东经'
              placeholder="请输入经度"
              rules={[
                { required: true, message: "请输入经度" },
                {
                  pattern:
                    /^(\-|\+)?(((\d|[1-9]\d|1[0-7]\d|0{1,3})\.\d{0,6})|(\d|[1-9]\d|1[0-7]\d|0{1,3})|180\.0{0,6}|180)$/,
                  message: '(范围："-180°~ +180°",保留6位小数)'
                }
              ]}
            />
            <ProFormText
              width="md"
              name="latitude"
              label="纬度"
              tooltip='"+"为北纬，"-"为南纬'
              placeholder="请输入纬度"
              rules={[
                { required: true, message: "请输入纬度" },
                {
                  pattern: /^(\-|\+)?([0-8]?\d{1}\.\d{0,6}|90\.0{0,6}|[0-8]?\d{1}|90)$/,
                  message: '( 范围："-90°~+90°",保留6位小数)'
                }
              ]}
            />
          </ProForm.Group>

          <ProForm.Group>
            <ProFormSelect
              width="md"
              name="brand"
              label="品牌"
              valueEnum={{
                "0": "雄迈",
                "1": "星望",
                "2": "宇视",
                "3": "天地伟业",
                "4": "景阳",
                "5": "联通",
                "6": "捷高",
                "7": "华迈",
                "8": "海康云平台",
                "9": "海康",
                "10": "电信全球眼副本",
                "11": "flash格式",
                "12": "烽火",
                "13": "电信监控",
                "14": "大华云平台",
                "15": "大华",
                "16": "奥利克斯"
              }}
              placeholder="请输入品牌"
              rules={[{ required: true, message: "请输入品牌" }]}
            />
            <ProFormText
              width="md"
              name="linkAddress"
              label="链接地址"
              placeholder="请输入链接地址"
              rules={[
                { required: true, message: "请输入链接地址" },
                { max: 100, message: "最多可输入100个字符" }
              ]}
            />
          </ProForm.Group>

          <ProForm.Group>
            <ProFormText
              width="md"
              name="largeM3u8Address"
              label="大码率M3U8播放地址"
              placeholder="请输入大码率M3U8播放地址"
              rules={[
                { required: true, message: "请输入大码率M3U8播放地址" },
                { max: 100, message: "最多可输入100个字符" }
              ]}
            />
            <ProFormText
              width="md"
              name="littleM3u8Address"
              label="小码率M3U8播放地址"
              placeholder="请输入小码率M3U8播放地址"
              rules={[
                { required: true, message: "请输入小码率M3U8播放地址" },
                { max: 100, message: "最多可输入100个字符" }
              ]}
            />
          </ProForm.Group>
          <ProForm.Group>
            <ProFormText
              width="md"
              name="videoPoint"
              label="管理端口"
              placeholder="请输入管理端口"
              rules={[{ max: 100, message: "最多可输入100个字符" }]}
            />
            <ProFormText
              width="md"
              name="channel"
              label="通道号"
              placeholder="请输入通道号"
              rules={[{ max: 100, message: "最多可输入100个字符" }]}
            />
          </ProForm.Group>
          <ProForm.Group>
            <ProFormText
              width="md"
              name="videoAddress"
              label="视频服务地址"
              placeholder="请输入视频服务地址"
              rules={[{ max: 100, message: "最多可输入100个字符" }]}
            />
            <ProFormText
              width="md"
              name="serial"
              label="序列号"
              placeholder="请输入序列号"
              rules={[{ max: 100, message: "最多可输入100个字符" }]}
            />
          </ProForm.Group>
          <ProForm.Item label="监控图片列表（注：最多上传99张图片）">
            <ProFormUploadButton
              name="file"
              // label="图片"
              max={99}
              // fileList={defaultFile}
              // rules={[{ required: true, message: '请上传图片' }]}
              fieldProps={{
                multiple: false,
                listType: "picture-card",
                accept: ".jpg,.png,.jpeg",
                onPreview: handlePreview,
                onChange: addUpload
              }}
              action={UPLOAD_HOST}
            />
          </ProForm.Item>
        </ModalForm>
      ) : (
        ""
      )}

      <EditPop
        title="视频监控点"
        visible={editVisible}
        setVisible={setEditVisible}
        columns={editColumns}
        dataSource={dataSource}
        // 新增/编辑
        onFinish={(val: any) => {
          // val.videoPicture = url;
          // console.log(val);
          submitData(val);
        }}
      />
      <Modal
        visible={imgInfo.previewVisible}
        title={imgInfo.previewTitle}
        footer={null}
        onCancel={() =>
          handleImgInfo({
            previewImage: "",
            previewVisible: false,
            previewTitle: "预览"
          })
        }
      >
        <img alt="example" style={{ width: "100%" }} src={imgInfo.previewImage} />
      </Modal>
      <>
        <ProTable
          {...tableConfig}
          actionRef={actionRef}
          rowKey="id"
          toolBarRender={() => [
            <Access key={getUniqueId()} accessible={access.canHardwareVideo_insert}>
              <Button
                type="primary"
                key="primary"
                onClick={() => {
                  handleOptionId("");
                  handleModalVisible(true);
                  setEditVisible(true);
                  setDistinguishStatus(false);
                }}
              >
                <PlusOutlined /> 新增
              </Button>
            </Access>
          ]}
          // dataSource={data}
          // dataSource={data}
          // params={{ scenicId }}
          request={getVideoPageList}
          columns={columns}
        />
        {/* 新增编辑 */}
        {/*预览图片*/}
      </>
    </>
  );
};

export default VideoSurveillance;
