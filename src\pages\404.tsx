import { Button, Result } from 'antd';
import React from 'react';
import { jumpPage } from '@/common/utils/tool';

const NoFoundPage: React.FC = () => (
  <Result
    status="404"
    title="404"
    subTitle="Sorry, the page you visited does not exist."
    extra={
      <Button type="primary" onClick={() => jumpPage.push('/')}>
        Back Home
      </Button>
    }
  />
);

export default NoFoundPage;
