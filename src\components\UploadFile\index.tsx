import { getUniqueId } from "@/common/utils/tool";
import { uploadFileNew } from "@/services/api/file";
import { InboxOutlined, PlusOutlined, UploadOutlined } from "@ant-design/icons";
import type { UploadProps } from "antd";
import { Button, Upload, message } from "antd";
import type { RcFile, UploadChangeParam, UploadFile as UploadFileType } from "antd/es/upload/interface";
import { useEffect, useMemo, useState, type FC } from "react";
import { getEnv } from "@/common/utils/getEnv";

interface ResponseFileData {
  size: number;
  path: string;
  name: string;
  type: string;
  mtime: string;
}

interface UploadFileProps extends Omit<UploadProps, "onChange" | "fileList" | "beforeUpload" | "action"> {
  /** 单文件大小上限  */
  size?: number;
  /** 初始值 */
  defaultValue?: string;
  /** 上传列表变化的回调 */
  onChange?: (value: string) => void;
  /** 只读 */
  readonly?: boolean;
  /** 文件类型 如 .png .mp4 */
  accept?: string;
  /** 是否启用拖拽上传模式 */
  dragger?: boolean;
  /** 拖拽区域自定义内容 */
  dragContent?: React.ReactNode;
}
/**
 * @description 封装 antd 文件上传组件 针对所有文件 用新的上传地址
 * @see API https://4x.ant.design/components/upload-cn/#API
 * @see  https://test.shukeyun.com/scenic/api-v2/doc.html#/aws%E6%96%87%E4%BB%B6%E4%B8%8A%E4%BC%A0/aws%E6%96%87%E4%BB%B6%E4%B8%8A%E4%BC%A0/uploadFileUsingPOST
 *  */
const { Dragger } = Upload;

const UploadFile: FC<UploadFileProps> = ({
  size = 100,
  defaultValue = "",
  readonly = false,
  accept,
  dragger = false,
  dragContent,
  onChange,
  ...otherProps
}) => {
  // 受控值
  const [fileList, setFileList] = useState<UploadFileType[]>([]);
  // 类型校验
  const checkFileType = (acceptType: string, fileName: string) => {
    const arr = fileName.split(".");
    const format = arr[arr.length - 1];

    if (`.${format}` === acceptType) {
      return true;
    }

    // .jpg 和 .jpeg 是同一个格式
    if (format === "jpeg" && acceptType === ".jpg") {
      return true;
    }
    return false;
  };

  // 上传前图片格式和大小校验 不合法不会上传服务器
  const beforeUpload = (file: RcFile, FileList: RcFile[]) => {
    console.log(file, accept);
    // 类型校验
    if (accept) {
      const isValidType = accept.split(",").some(type => checkFileType(type, file.name));
      if (!isValidType) {
        message.error("文件格式不正确！");
        return Upload.LIST_IGNORE;
      }
    }

    // 文件大小验证
    const isOverSize = file.size / 1024 / 1024 > size;
    if (isOverSize) {
      message.error(`文件必须小于${size}M!`);
      return Upload.LIST_IGNORE;
    }
    return true;
  };

  // 自定义上传请求
  const uploadRequest: UploadProps["customRequest"] = async options => {
    const { file, onProgress, onError, onSuccess } = options;

    // 上传进度条 fetch 不支持上传事件监听 所以弄个假的
    onProgress?.({ percent: 10 });

    try {
      const res = await uploadFileNew(file as File);
      onProgress?.({ percent: 100 });
      onSuccess?.(res);
    } catch (e) {
      onError?.(e);
    }
  };

  // onchange 回调
  const onFileListChange = (info: UploadChangeParam<UploadFileType<ResponseData<ResponseFileData>>>) => {
    const { file, fileList: list } = info;
    const { status } = file;

    // 上传成功
    if (status === "done") {
      // 处理数据
      const newList = list.map(item => {
        const { data } = item.response || {};
        if (item.status === "done") {
          return {
            ...item,
            // 预览地址
            thumbUrl: data ? getEnv().FILE_HOST + (data?.path || "") : item.thumbUrl,
            // preview 字段暂时用来存后端图片返回地址
            preview: data ? data?.path : item.preview
          };
        }
        return item;
      });
      console.log(newList);
      setFileList(newList);
      return;
    } else if (status === "error") {
      // 上传失败
      // 列表过滤掉上传失败的数据
      message.error("文件上传失败！");
      setFileList(fileList.filter(item => item.status !== "error" && item.status !== "uploading"));
      console.log(fileList);
      return;
    } else {
      setFileList(list);
    }
  };

  // 文件回显
  useEffect(() => {
    if (defaultValue) {
      const defaultFileList = defaultValue.split(",").map(item => ({
        uid: getUniqueId(),
        name: item,
        percent: 100,
        status: "done",
        thumbUrl: getEnv().FILE_HOST + item,
        preview: item
      }));
      setFileList(defaultFileList as UploadFileType[]);
    }
  }, [defaultValue]);

  useEffect(() => {
    if (onChange) {
      // 需过滤上传失败的图片
      const changedValue = fileList
        .filter(item => item.preview)
        .map(item => item.preview)
        .join(",");
      onChange(changedValue);
    }
  }, [fileList]);

  const uploadButton = useMemo(() => {
    // 图片 卡片模式
    if (otherProps.listType === "picture-card") {
      if (fileList.length < (otherProps.maxCount ?? 0) && !readonly) {
        return <PlusOutlined />;
      }
    } else {
      if (!readonly) {
        return <Button icon={<UploadOutlined />}>点击上传</Button>;
      }
    }

    return null;
  }, [fileList.length, otherProps.listType, otherProps.maxCount, readonly]);

  return dragger ? (
    <Dragger
      {...otherProps}
      accept={accept}
      showUploadList={{
        showRemoveIcon: !readonly
      }}
      customRequest={uploadRequest}
      beforeUpload={beforeUpload}
      onChange={onFileListChange}
      fileList={fileList}
      disabled={readonly}
    >
      {dragContent || (
        <>
          <p className="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p className="ant-upload-hint">支持单文件或批量上传，请勿上传敏感数据</p>
        </>
      )}
    </Dragger>
  ) : (
    <Upload
      {...otherProps}
      accept={accept}
      showUploadList={{
        showRemoveIcon: !readonly
      }}
      customRequest={uploadRequest}
      beforeUpload={beforeUpload}
      onChange={onFileListChange}
      fileList={fileList}
    >
      {uploadButton}
    </Upload>
  );
};

export default UploadFile;
