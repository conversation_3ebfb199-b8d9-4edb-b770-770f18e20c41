/**
 * 基础设施管理
 */
import { request } from "@umijs/max";
import { scenicHost } from ".";
const url = (v: string) => scenicHost + v;

/** 新增检票点 */
export function addCheckPoint(data: any) {
  return request(url("/checkInPoint/save"), {
    method: "POST",
    data
  });
}
/** 编辑检票点 */
export function editCheckPoint(data: any) {
  return request(url("/checkInPoint/update"), {
    method: "POST",
    data
  });
}
/** 查看检票点 */
export function infoCheckPoint(params: any) {
  return request(url("/checkInPoint/info/" + params.id));
}

/** 新增检票设备 */
export function addCheckDevice(data: any) {
  return request(url("/checkEquipment/save"), {
    method: "POST",
    data
  });
}
/** 编辑检票设备 */
export function editCheckDevice(data: any) {
  return request(url("/checkEquipment/update"), {
    method: "POST",
    data
  });
}
/** 查看检票设备 */
export function infoCheckDevice(params: any) {
  return request(url("/checkEquipment/info/" + params.id));
}
