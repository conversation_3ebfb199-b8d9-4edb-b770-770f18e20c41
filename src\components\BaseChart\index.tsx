/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-05-08 15:00:19
 * @LastEditTime: 2023-09-21 17:10:36
 * @LastEditors: zhangfengfei
 */
import { getTicket } from "@/services/api/chart";
import { Spin } from "antd";
import qs from "qs";
import React, { useEffect, useState } from "react";
import { useModel, useRequest } from "@umijs/max";
import Chart from "../Common/Chart";
import type { ChartDatePickerProps } from "./ChartDatePicker";
import ChartDatePicker from "./ChartDatePicker";
import styles from "./index.less";
import { getEnv } from "@/common/utils/getEnv";
const isTest = getEnv().ENV == "test";
// https://git.shukeyun.com/scenic/workflow/-/issues/2410
const urlMap = {
  1: {
    date: "issue_refund_ticket_stats_d/scenic_ads_dw/",
    month: "issue_refund_ticket_stats_m/scenic_ads_dw/"
  },
  2: {
    date: "actual_out_in_park_stat/scenic_ads_dw/"
  },
  3: {
    date: "book_in_park_stat_d/scenic_ads_dw/",
    month: "book_in_park_stat_m/scenic_ads_dw/"
  },
  4: {
    date: "goods_unsubscribe_stat_d/scenic_ads_dw/",
    month: "goods_unsubscribe_stat_m/scenic_ads_dw/"
  }
};

function getBarOptions(
  {
    dimensions,
    source
  }: {
    dimensions: any[];
    source: any[];
  },
  unit: string = ""
) {
  return {
    grid: {
      left: "36px",
      bottom: "60px",
      top: "48px",
      right: "24px"
    },
    legend: {
      bottom: 0,
      icon: "circle",
      itemGap: 30
    },
    tooltip: {
      valueFormatter: (value: number) => `${value}${unit}`
    },
    color: ["#1890FF", "#9787F2"],
    dataset: {
      source,
      dimensions
    },
    xAxis: { type: "category" },
    yAxis: {
      splitLine: {
        lineStyle: {
          type: [5, 5]
        }
      },
      minInterval: 1
    },
    series: dimensions.slice(1, dimensions.length).map(i => ({ type: "bar", barMaxWidth: 40 }))
  };
}

function getLineOptions(
  {
    dimensions,
    source
  }: {
    dimensions: any[];
    source: any[];
  },
  unit: string = ""
) {
  return {
    grid: {
      left: "36px",
      bottom: "60px",
      top: "48px",
      right: "24px"
    },
    legend: {
      bottom: 0,
      icon: "circle",
      itemGap: 30
    },
    xAxis: {
      type: "category"
    },
    yAxis: {
      type: "value",
      splitLine: {
        lineStyle: {
          type: [5, 5]
        }
      },
      minInterval: 1
    },
    tooltip: {
      trigger: "axis",
      valueFormatter: (value: number) => `${value}${unit}`,
      borderWidth: 1,
      borderColor: "#1890FF"
    },
    color: ["#1890FF", "#A699F4"],
    dataset: {
      source,
      dimensions
    },
    series: dimensions.slice(1, dimensions.length).map((i, k) => ({
      type: "line",
      symbol: "none",
      areaStyle: {
        color: {
          type: "linear",
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: k ? "#A699F488" : "#1890FF88"
            },
            {
              offset: 1,
              color: k ? "#A699F400" : "#1890FF00"
            }
          ],
          global: false // 缺省为 false
        }
      }
    }))
  };
}

interface ParamsType {
  sort: ChartDatePickerProps["sort"];
  value: ChartDatePickerProps["defaultValue"];
}

export interface IBarChartProps {
  id: string;
  datePickerConfig: Omit<ChartDatePickerProps, "onValuesChange">;
  type: "bar" | "line";
  title: string;
  filterDataFunc: any;
  unit: string;
}

const BarChart: React.FC<IBarChartProps> = props => {
  const { datePickerConfig, id } = props;
  const { initialState } = useModel("@@initialState") || {};
  const { scenicId = "" } = initialState?.scenicInfo || {};
  const { coId } = initialState?.currentCompanyInfo || {};

  const [option, setOption] = useState({});
  const getChartDataReq = useRequest(
    ({ sort, value }: ParamsType) => {
      // 处理后端地址 参数
      if (value) {
        const current = urlMap[id];
        const requestUrl = current[sort];
        const commonParams = {
          operatorId: coId,
          scenic_id: scenicId,
          update_frequency: "0.0"
        };
        let specialParams: Record<string, any>;
        // rangePicker
        if (Array.isArray(value)) {
          // 按日
          if (sort === "date") {
            specialParams = {
              begin_year_month_day: value[0].format("YYYY-MM-DD"),
              end_year_month_day: value[1].format("YYYY-MM-DD")
            };
          } else {
            // 按月
            specialParams = {
              begin_year_month: value[0].format("YYYY-MM"),
              end_year_month: value[1].format("YYYY-MM")
            };
          }
        } else {
          specialParams = {
            year_month_day: value.format("YYYY-MM-DD")
          };
        }
        const params = { ...commonParams, ...specialParams };
        return getTicket(
          requestUrl +
            qs.stringify(params, {
              addQueryPrefix: true
            })
        );
      }
      return;
    },
    {
      manual: true,
      formatResult({ code, data }) {
        if (code < 20000 || code >= 30000) return;

        const res = props.filterDataFunc(data.result || []);
        // console.log(res);

        if (props.type === "bar") {
          setOption(getBarOptions(res, props.unit));
        } else if (props.type === "line") {
          setOption(getLineOptions(res || [], props.unit));
        }
      }
    }
  );

  useEffect(() => {
    if (datePickerConfig.defaultValue) {
      getChartDataReq.run({
        sort: datePickerConfig.sort || "date",
        value: datePickerConfig.defaultValue || null
      });
    }
  }, []);

  return (
    <section className={styles.chart} style={{ width: "100%", height: "100%", position: "relative" }}>
      {getChartDataReq.loading && (
        <div
          style={{
            width: "100%",
            height: "100%",
            position: "absolute",
            textAlign: "center",
            backgroundColor: "#fff",
            opacity: 0.5,
            paddingTop: 40,
            zIndex: 200
          }}
        >
          <Spin />
        </div>
      )}
      <div
        style={{
          display: "flex",
          flexWrap: "wrap",
          alignItems: "center",
          width: "100%",
          overflow: "visible"
        }}
      >
        <span
          style={{
            minWidth: 140,
            fontSize: 20,
            fontWeight: 500,
            color: "#000000"
          }}
        >
          {props.title}
        </span>
        <div className={styles.control}>
          <ChartDatePicker
            {...datePickerConfig}
            onValuesChange={(sort, value) => {
              getChartDataReq.run({
                sort,
                value
              });
            }}
          />
        </div>
      </div>
      <div
        style={{
          width: "100%",
          height: 320
        }}
      >
        <Chart options={option} loading={getChartDataReq.loading} />
      </div>
    </section>
  );
};

export default BarChart;
