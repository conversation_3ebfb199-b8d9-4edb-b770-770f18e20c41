import { ticketStatusEnum } from "@/common/utils/enum";
import { modelWidth } from "@/common/utils/gConfig";
import { formatTime } from "@/common/utils/tool";
import { getTraceRecord } from "@/services/api/ticket";
import { QuestionCircleOutlined } from "@ant-design/icons";
import { Descriptions, Modal, Popover, Space, Spin, Timeline, Typography } from "antd";
import type { FC } from "react";
import { useEffect, useState } from "react";
import { useRequest } from "@umijs/max";
import styles from "./index.less";
import { getEnv } from "@/common/utils/getEnv";

const { Text, Paragraph } = Typography;
interface ChainModalProps {
  params: any;
  getTraceRequest: any;
}

const ChainRecordModal: FC<ChainModalProps> = ({ params, getTraceRequest }) => {
  const [visible, setVisible] = useState(false);
  const chainInfoReq = useRequest(getTraceRequest, {
    manual: true
  });
  const chainRecordType = {
    1: "库存创建",
    2: "价格策略设置",
    3: "门票采购",
    4: "门票购买",
    5: "门票核销",
    6: "门票退订",
    7: "门票采购退单",
    8: "门票过期",
    9: "库存修改"
  };

  useEffect(() => {
    if (visible && params) {
      chainInfoReq.run(params);
    }
  }, [visible, params]);

  return (
    <>
      <a onClick={() => setVisible(true)}>区块链溯源记录</a>
      <Modal
        width={modelWidth.md}
        title={
          <Space>
            <Text>区块链溯源记录</Text>
            <Popover placement="right" content="可通过交易哈希在区块链浏览器中查询链上信息">
              <QuestionCircleOutlined />
            </Popover>
          </Space>
        }
        footer={null}
        onCancel={() => setVisible(false)}
        open={visible}
        destroyOnClose
      >
        <Spin spinning={chainInfoReq.loading}>
          <Timeline mode="left" reverse className={styles.timeline}>
            {chainInfoReq.data?.map((item: any) => (
              <Timeline.Item label={<div>{formatTime(item.triggerAt * 1000)}</div>}>
                <Descriptions title={chainRecordType[item.optType]} column={1}>
                  {[1, 9].includes(item.optType) && getTraceRequest == getTraceRecord && (
                    <Descriptions.Item label="铸造方">{item.minerName}</Descriptions.Item>
                  )}
                  {[1, 9].includes(item.optType) && getTraceRequest == getTraceRecord && (
                    <Descriptions.Item label="库存批次号">{item.batchId}</Descriptions.Item>
                  )}
                  {[3, 4, 6, 7].includes(item.optType) && (
                    <Descriptions.Item label="发送方">{item.senderName}</Descriptions.Item>
                  )}
                  {[3, 4, 6, 7].includes(item.optType) && (
                    <Descriptions.Item label="接受方">{item.receiverName}</Descriptions.Item>
                  )}
                  {[3, 4].includes(item.optType) && (
                    <Descriptions.Item label="订单号">
                      <Paragraph copyable style={{ marginBottom: 0 }}>
                        {item.orderNumber}
                      </Paragraph>
                    </Descriptions.Item>
                  )}
                  {[6, 7].includes(item.optType) && (
                    <Descriptions.Item label="退单号">
                      <Paragraph copyable style={{ marginBottom: 0 }}>
                        {item.refundNumber}
                      </Paragraph>
                    </Descriptions.Item>
                  )}
                  {[1, 3, 4, 5, 6, 7, 8, 9].includes(item.optType) && getTraceRequest == getTraceRecord && (
                    <Descriptions.Item label="门票状态">{ticketStatusEnum[item.ticketsStatus]}</Descriptions.Item>
                  )}
                  <Descriptions.Item label="交易哈希">
                    <Paragraph
                      copyable={{
                        text: item.txHash
                      }}
                      style={{ marginBottom: 0 }}
                    >
                      <a target="_blank" href={`${getEnv().CHAIN_URL}#/tx_list?tx=${item.txHash}`} rel="noreferrer">
                        {item.txHash.length > 20
                          ? item.txHash.slice(0, 10) + "..." + item.txHash.slice(-10)
                          : item.txHash}
                      </a>
                    </Paragraph>
                  </Descriptions.Item>
                </Descriptions>
              </Timeline.Item>
            ))}
          </Timeline>
        </Spin>
      </Modal>
    </>
  );
};

export default ChainRecordModal;
