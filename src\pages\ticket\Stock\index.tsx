import PrefixTitle from "@/common/components/PrefixTitle";
import ProModal from "@/common/components/ProModal";
import useModal from "@/common/components/ProModal/useModal";
import TimeStore from "@/common/components/TimeStore";
import { tableConfig } from "@/common/utils/config";
import { GuideStepStatus, productTypeEnum, stockType, ticketTypeEnum } from "@/common/utils/enum";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import { getAllPath, getHashParams, getUniqueId, sleep } from "@/common/utils/tool";
import EchartsCom from "@/components/EchartsCom";
import { useGuide } from "@/hooks/useGuide";
import {
  UpManageStock,
  apiGetInventory,
  apiGetPublishedStock,
  getDigitGoodsByCompany,
  getGoodsListApi,
  getHistorySales,
  getInventoryTraceability,
  getSimpleList,
  getTicketStockInfo,
  getTicketStockList,
  getTimeShareByGoodsId
} from "@/services/api/ticket";
import { addStock } from "@/services/api/travelCardStock";
import { PlusOutlined, SwapRightOutlined } from "@ant-design/icons";
import { ProCard, ProFormFieldSet, ProFormItem, Statistic } from "@ant-design/pro-components";
import type { ProFormColumnsType } from "@ant-design/pro-form";
import type { ActionType, ProColumns } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import type { TableProps } from "antd";
import {
  Alert,
  Button,
  Col,
  DatePicker,
  Flex,
  Form,
  Input,
  InputNumber,
  Row,
  Space,
  Spin,
  Table,
  Tabs,
  Tag,
  message
} from "antd";
import { useForm } from "antd/lib/form/Form";
import dayjs from "dayjs";
import { trim } from "lodash";
import qs from "qs";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { Access, useAccess, useLocation, useModel, useRequest } from "@umijs/max";
import ChainRecordModal from "../Sale/components/ChainRecordModal";
import styles from "./index.module.less";
import { InventoryEnum, options } from "./options";
import { getEnv } from "@/common/utils/getEnv";

const { RangePicker } = DatePicker;

let cpList: any = [];
let goodsList: any = [];
const value = 0;

const TableList: React.FC = () => {
  // 首页带过来的查询参数
  const { search } = useLocation();
  const goodsName = new URLSearchParams(search).get("goodsName");
  const searchParams = qs.parse(search, {
    ignoreQueryPrefix: true
  });
  const [modalForm] = useForm();
  const [enterBeginTime, enterEndTime] = [dayjs().format("YYYY-MM-DD"), dayjs().endOf("year").format("YYYY-MM-DD")];
  // 入园时间年份
  const [enterYear, setEnterYear] = useState(dayjs());
  // 可发行库存量
  const [publishedStock, setPublishedStock] = useState(null);
  const [tabKey, setTabKey] = useState<string>(searchParams?.tabKey || "0");
  const [record, setRecord] = useState<any>({});
  const modalState = useModal();
  const { data: globalConfigData } = useModel("useGlobalModel", model => model.paramsConfig);
  const { currentCompanyInfo, scenicInfo } = useModel("@@initialState").initialState || {};
  const [cpOptions, setCpOptions] = useState([]);
  const [productLoading, setProductLoading] = useState(false);
  // 【景区】信息
  const { scenicId, scenicName, creditCode, isBlockChain }: any = scenicInfo || {};
  const { coId }: any = currentCompanyInfo || {};
  const access = useAccess();
  // 【表格】数据绑定
  const NumSection: any = ({ value, onChange }: any) => (
    <Space.Compact block>
      <Form.Item name="minResidualInventory" initialValue={searchParams.stockAmount ? 0 : ""}>
        <Input
          value={value?.minResidualInventory}
          onChange={v => {
            onChange({ ...value, minResidualInventory: v.target.value.replace(/\D+/g, "") });
          }}
          style={{ width: "100%", textAlign: "center" }}
          placeholder="最小库存"
        />
      </Form.Item>
      <Input
        style={{
          width: 30,
          borderLeft: 0,
          borderRight: 0,
          pointerEvents: "none",
          flexShrink: 0
        }}
        placeholder="~"
        disabled
      />
      <Form.Item name="maxResidualInventory" initialValue={searchParams.stockAmount}>
        <Input
          value={value?.maxResidualInventory}
          onChange={v => {
            onChange({ ...value, maxResidualInventory: v.target.value.replace(/\D+/g, "") });
          }}
          style={{ width: "100%", textAlign: "center" }}
          placeholder="最大库存"
        />
      </Form.Item>
    </Space.Compact>
  );
  const actionRef = useRef<ActionType>();
  const formRef = React.useRef<any>();

  useEffect(() => {
    if (goodsName && formRef.current) {
      formRef.current?.setFieldsValue({ goodsName });
    }
  }, [goodsName]);
  const columns: ProColumns[] = [
    {
      title: "商品名称",
      dataIndex: "goodsName"
    },
    {
      title: "票种",
      dataIndex: "ticketGoodsType",
      valueEnum: ticketTypeEnum
    },
    {
      title: "库存批次号",
      dataIndex: "id",
      hideInTable: true,
      search: {
        transform: val => trim(val)
      }
    },
    {
      title: "库存批次号",
      dataIndex: "id",
      hideInSearch: true,
      render: (dom: any, record: any) => {
        let text = "-";
        if (record.timeRestrict === 0) {
          text = record.batchId[0];
        }
        return (
          <span>
            {text}
            {record.isExchange === 1 && (
              <Tag style={{ marginLeft: "10px" }} color="blue">
                交易所
              </Tag>
            )}
          </span>
        );
      }
    },
    {
      title: "产品名称",
      dataIndex: "ticketId",
      valueType: "select",
      fieldProps: {
        allowClear: true,
        showSearch: true,
        virtual: false,
        options: cpOptions
      },
      search: {
        transform: val => trim(val)
      },
      render: (_, entity) => entity.ticketName
    },
    {
      title: "产品类型",
      dataIndex: "type",
      hideInSearch: true,
      render: (dom: any) => productTypeEnum[dom]
    },
    {
      title: "购买有效时间",
      hideInSearch: true,
      render: (_, { purchaseBeginTime, purchaseEndTime }) => [purchaseBeginTime, purchaseEndTime].join(" 至 ")
    },
    {
      title: "购买有效时间",
      dataIndex: "purchaseEndTime",
      valueType: "dateRange",
      hideInTable: true,
      search: {
        transform: value => ({
          purchaseBeginTime: value[0],
          purchaseEndTime: value[1]
        })
      }
    },
    {
      title: "入园有效时间",
      dataIndex: "enterEndTime",
      valueType: "dateRange",
      hideInTable: true,
      initialValue: [searchParams?.enterEndTime, searchParams?.enterEndTime],
      search: {
        transform: value => {
          return {
            enterBeginTime: value[0],
            enterEndTime: value[1]
          };
        }
      }
    },
    {
      title: "入园有效时间",
      dataIndex: "enterTime",
      search: false,
      render: (_, { dayBegin, dayEnd }) => [dayBegin, dayEnd].join(" 至 ")
    },
    {
      title: "总库存量",
      dataIndex: "totalStock",
      hideInSearch: true,
      valueType: "digit",
      align: "right"
    },
    {
      title: "剩余库存",
      hideInTable: true,
      dataIndex: "residualInventory",
      search: { transform: v => v },
      renderFormItem: () => <NumSection />
    },
    {
      title: "剩余库存",
      dataIndex: "residualInventory",
      hideInSearch: true,
      valueType: "digit",
      align: "right"
    },
    {
      title: "出票库存",
      dataIndex: "issueStock",
      hideInSearch: true,
      valueType: "digit",
      align: "right"
    },
    {
      title: "过期库存",
      dataIndex: "expireStock",
      hideInSearch: true,
      valueType: "digit",
      align: "right"
    },
    {
      title: "交易所",
      dataIndex: "isExchange",
      hideInSearch: tabKey == "0",
      hideInTable: true,
      valueType: "select",
      valueEnum: {
        0: "否",
        1: "是"
      }
    },
    // {
    //   title: '交易所审核',
    //   dataIndex: 'assetStatus',
    //   fixed: 'right',
    //   search: false,
    //   hideInTable: tabKey == '0',
    //   render: (dom: any) => <Tag2 type="assetStatus" value={dom} />,
    // },
    {
      title: "操作",
      valueType: "option",
      fixed: "right",
      render: (_: any, record: any) => (
        <Space size="large">
          <a
            onClick={() => {
              setRecord(record);
              modalState.setType("info");
              addOperationLogRequest({
                action: "info",
                content: `查看【${record.ticketName}】库存详情`
              });
            }}
          >
            查看
          </a>
          <Access
            accessible={
              access.canStorageManage_edit &&
              record.assetStatus != "0" &&
              record.assetStatus != "3" &&
              record?.isExchange !== 1
            }
          >
            <a
              onClick={() => {
                setRecord(record);
                modalState.setType("edit");
              }}
            >
              编辑
            </a>
          </Access>
          <Access accessible={tabKey == "1" && record.inventoryHash.length != 0}>
            <ChainRecordModal
              getTraceRequest={getInventoryTraceability}
              params={{ batchId: record.batchId[0], distributorId: coId }}
            />
          </Access>
        </Space>
      )
    }
  ];
  // 【表单/详情】数据绑定
  const [lawsList, setLawsList] = useState([]);
  const [ticketGoodsListVOList, setTicketGoodsListVOList] = useState({});
  const [restrictWeekList, setRestrictWeekList] = useState([]);
  const [dataSource, setDataSource] = useState<any>({ id: "", isEnable: 0 });
  const [typeValue, setTypeValue] = useState();
  const [ticketId, setTicketId] = useState();
  const [operatorId2, setOperatorId2] = useState();
  const isBlockChangeTime = useRef(false);
  const salesReq = useRequest(getHistorySales, { manual: true });
  const [historySales, setHistorySales] = useState({});
  const historySalesRef = useRef(historySales);
  const Statistics = () => {
    return [
      {
        title: "同期票务销售量",
        tips: "此数据为景区去年在慧旅云系统同期票务销售总量，可为企业确定今年同期票务发行量提供参考依据！",
        value: historySalesRef.current?.historySales
      },
      {
        title: "本期交易所票务待售量",
        tips: "此数据为景区今年在慧旅云系统本期的交易所票务待销售量，可为企业确定今年本期票务发行量提供参考依据！",
        value: historySalesRef.current?.futureExchangeSales
      },
      {
        title: "本期非交易所票务待售量",
        tips: "此数据为景区今年在慧旅云系统本期的非交易所票务待销售量，可为企业确定今年本期票务发行量提供参考依据！",
        value: historySalesRef.current?.futureSales
      }
    ].map((item, index) => (
      <ProCard bordered key={index}>
        <Statistic title={item.title} tip={item.tips} value={item.value} />
      </ProCard>
    ));
  };
  // 交易所列表
  const [exchangeVOList, setExchangeVOList] = useState({});
  // 经销商分组列表
  const [distributorInfoVOList, setDistributorInfoVOList] = useState<any>([]);
  const [exchangeVOLoading, setExchangeVOLoading] = useState({});
  // 库存设置
  const [cInventoryReqLoading, setCInventoryReqLoading] = useState(false);
  const [pInventoryReqLoading, setPInventoryReqLoading] = useState(false);
  const [currentInventory, setCurrentInventory] = useState({
    list: [],
    totalInventory: 0
  });
  const [previousInventory, setPreviousInventory] = useState({
    list: [],
    totalInventory: 0
  });
  const [sourceInventory, setSourceInventory] = useState({
    totalInventory: 0
  });
  const [innerWidth, setInnerWidth] = useState(window.innerWidth);
  // 引导
  const { updateGuideInfo } = useGuide();

  const alert = assetReason => {
    return (
      <Alert
        message={
          {
            0: "交易所库存发布审核中！",
            1: "交易所库存发布审核通过！",
            2: `交易所库存发布审核不通过，具体原因：${assetReason}，请修改后重新提交！`,
            3: `交易所库存信息修改审核中！`,
            4: `交易所库存信息修改审核通过！`,
            5: `交易所库存信息修改审核不通过，具体原因：${assetReason}，请修改后重新提交！`
          }[record.assetStatus as string]
        }
        type={
          { 0: "success", 1: "info", 2: "error", 3: "success", 4: "info", 5: "error" }[record.assetStatus as string]
        }
        style={{
          width: "100%",
          marginBottom: 12
        }}
        showIcon
      />
    );
  };

  /*********************************************** 库存设置 ************************************************/
  /**
   * 填写库存数量，计算百分比=库存数量/库存总数（库存总数已累加标的库存数量）
   * @param value 库存数量
   * @param sourceInventory 本期库存数据源
   */
  const onInstrumentQuantityChange = (value: number, inventorySource = sourceInventory) => {
    const totalInventory = inventorySource.totalInventory + value;
    const instrumentPercent = ((value / totalInventory) * 100 || 0).toFixed(3);
    const _newSourceInventory = {
      ...inventorySource,
      totalInventory
    };
    modalForm?.setFieldsValue({
      instrumentPercent
    });
    setCurrentInventory({
      list: dealInventoryDatas(_newSourceInventory, value),
      totalInventory: totalInventory || 0
    });
  };
  /**
   * 填写百分比，计算库存数量=库存总数（未累加标的库存数量）*百分比/（1-百分比） 结果四舍五入取整
   * @param value 库存数量百分比
   */
  const onInstrumentPercentChange = (value: number) => {
    const instrumentPercent = Math.floor(value);
    const percent = instrumentPercent / 100;
    const totalStock = Math.round(sourceInventory.totalInventory * (percent / (1 - percent)) || 0);
    const totalInventory = sourceInventory.totalInventory + totalStock;
    const _newSourceInventory = {
      ...sourceInventory,
      totalInventory
    };
    setCurrentInventory({
      list: dealInventoryDatas(_newSourceInventory, totalStock),
      totalInventory: totalInventory || 0
    });
    modalForm?.setFieldsValue({
      totalStock,
      instrumentPercent
    });
  };
  // 处理库存数据
  const dealInventoryDatas = (data: any, quantity = 0) => {
    const type = tabKey === "0" ? "commonTicketsForSale" : "blockChainTicketsForSale";
    const inventoryList: any = [];
    Object.keys(InventoryEnum).forEach((key: any) => {
      let value = Number(data[key]?.quantity || 0);
      if (quantity && key === type) {
        value += Number(quantity);
      }
      // 计算百分比
      const percent = Math.round((value ? value / data?.totalInventory : value) * 100) || 0;
      const _item = {
        value,
        name: `${InventoryEnum[key] || ""},${percent}%, ${value}`
      };
      inventoryList.push(_item);
    });
    return inventoryList;
  };
  // 获取库存信息
  // 本期
  const getCurrentInventoryDatas = async () => {
    try {
      setCInventoryReqLoading(true);
      const currentInventoryDate = modalForm?.getFieldValue("time") || [];
      const startTime = currentInventoryDate?.[0] || "";
      const endTime = currentInventoryDate?.[1] || "";
      // 本期
      const { data: sourceInventory } = await apiGetInventory({
        scenicId,
        startTime,
        endTime
      });

      modalForm.setFieldsValue({
        currentInventoryDate: [dayjs(startTime) || null, dayjs(endTime) || null]
      });
      setCInventoryReqLoading(false);
      setSourceInventory(sourceInventory);
      onInstrumentQuantityChange(modalForm?.getFieldValue("totalStock") || 0, sourceInventory);
    } catch (error) {
      setCInventoryReqLoading(false);
    }
  };
  // 往期
  const getPreviousInventoryDatas = async () => {
    try {
      setPInventoryReqLoading(true);
      const previousInventoryDate = modalForm?.getFieldValue("time") || [];
      const startTime = dayjs(previousInventoryDate?.[0])?.subtract(1, "year")?.format("YYYY-MM-DD") || "";
      const endTime = dayjs(previousInventoryDate?.[1])?.subtract(1, "year")?.format("YYYY-MM-DD") || "";
      // 往期
      const { data: previousInventory } = await apiGetInventory({
        scenicId,
        startTime,
        endTime
      });

      modalForm.setFieldsValue({
        previousInventoryDate: [dayjs(startTime) || null, dayjs(endTime) || null]
      });
      setPInventoryReqLoading(false);
      setPreviousInventory({
        list: dealInventoryDatas(previousInventory),
        totalInventory: previousInventory?.totalInventory || 0
      });
    } catch (error) {
      setPInventoryReqLoading(false);
    }
  };

  /**
   * 组装入园有效时间
   */
  const combineDates = (yearSource, monthDaySource) => {
    let month = null;
    let date = null;
    if (typeof monthDaySource == "string") {
      const arr = monthDaySource.split("-").map(Number);
      month = arr[arr.length - 2] - 1;
      date = arr[arr.length - 1];
    } else {
      month = monthDaySource.month();
      date = monthDaySource.date();
    }
    return dayjs().year(yearSource.year()).month(month).date(date);
  };

  const modalColumns: ProFormColumnsType<unknown, "text">[] = [
    // {
    //   title: '',
    //   noDivider: true,
    //   hideInForm: tabKey == '0' || record.isExchange == 0 || modalState.type == 'add',
    //   hideInDescriptions: tabKey == '0' || record.isExchange == 0,
    //   columns: [
    //     {
    //       dataIndex: 'assetReason',
    //       colProps: { span: 24 },
    //       formItemProps: {
    //         noStyle: true,
    //       },
    //       width: 'xxl',
    //       renderFormItem: (_, _values: any) => alert(_values?.value),
    //       render: (value) => alert(value),
    //     },
    //   ],
    // },
    // {
    //   title: '经营状况',
    //   hideInDescriptions: isBlockChain !== 1, // 非区块链景区不展示经营状况
    //   hideInForm: isBlockChain !== 1,
    //   columns: [
    //     {
    //       title: '',
    //       renderFormItem: () => Statistics()[0],
    //       renderText: () => Statistics()[0],
    //     },
    //     {
    //       title: '',
    //       renderFormItem: () => Statistics()[1],
    //       renderText: () => Statistics()[1],
    //     },
    //     {
    //       title: '',
    //       renderFormItem: () => Statistics()[2],
    //       renderText: () => Statistics()[2],
    //     },
    //   ],
    // },
    {
      title: "基础信息",
      columns: [
        {
          title: "所属景区",
          dataIndex: "scenicId",
          valueType: "select",
          fieldProps: {
            disabled: true
          },
          valueEnum: { [scenicId]: scenicName },
          initialValue: scenicId
        },
        {
          title: (
            <div className="flex justify-content-between w-100">
              产品名称
              {modalState.type == "info" || (
                <a
                  style={{ marginLeft: "8px" }}
                  onClick={() => {
                    localStorage.setItem("pageOperateType", "add");
                    window.open(`${getAllPath()}/ticket/ticket-type/edit?type=add&tag=product`, "_blank");
                  }}
                >
                  新增
                </a>
              )}
            </div>
          ),
          dataIndex: "ticketName",
          valueType: "select",
          fieldProps: {
            options: cpList.map((item: any, index: any) => ({
              value: index,
              label: item.name
            })),
            allowClear: false,
            showSearch: true,
            virtual: false,
            loading: productLoading,
            onDropdownVisibleChange: e => {
              if (e) {
                getProductFn();
              }
            },
            onChange: async (e: any) => {
              // 控制星期
              setRestrictWeekList(cpList[e]?.timeRestrict ? cpList[e].restrictWeekList : []);
              setTypeValue(cpList[e]?.type);
              setTicketId(cpList[e]?.id);
              setOperatorId2(cpList[e]?.operatorId);
              modalForm.setFieldValue("lawsList", []);
            },
            disabled: modalState.type == "edit" && record.assetStatus != 2
          },
          formItemProps: { rules: [{ required: true, message: "请输入产品名称" }] }
        },
        {
          title: "产品类型",
          dataIndex: "type",
          valueType: "select",
          valueEnum: productTypeEnum,
          fieldProps: {
            disabled: true,
            value: typeValue
          }
        },
        {
          title: "交易所发行",
          dataIndex: "bourseYn",
          hideInForm: tabKey == "0",
          hideInDescriptions: tabKey == "0",
          valueType: "switch",
          convertValue: value => !!value,
          transform: value => (value ? 1 : 0),
          formItemProps: {
            tooltip: "如需在交易所交易，请开启交易所发行"
          },
          fieldProps: {
            disabled: modalState.type !== "add",
            checkedChildren: "是",
            unCheckedChildren: "否",
            onChange: () => {
              getPublishedStockFn();
            }
          },
          initialValue: 0
        },
        {
          title: "商品名称",
          dataIndex: "goodsId",
          // hideInForm: tabKey == '0',
          // hideInDescriptions: tabKey == '0',
          formItemProps: { rules: [{ required: true }] },
          valueEnum: ticketGoodsListVOList,
          fieldProps: {
            disabled: modalState.type == "edit" && record.assetStatus != 2,
            onChange: (e: string) => {
              const r = goodsList.find(({ goodsId }: any) => goodsId == e);
              const type = r ? String(r.type) : null;
              modalForm.setFieldValue("totalStock", null);
              modalForm.setFieldValue("ticketGoodsType", type);
              const obj = goodsList.find((item: any) => item.goodsId == e);
              if (obj?.timeRestrict == "1") {
                getTimeShareByGoodsId(e).then(({ data }) => {
                  modalForm.setFieldValue("lawsList", data);
                });
              } else {
                modalForm.setFieldValue("lawsList", []);
              }
            }
          },
          render: (dom, entity) => entity?.goodsName || "-"
        },
        {
          title: "库存批次号",
          dataIndex: "id",
          hideInForm: true,
          render: (dom: any, record: any) => {
            return (
              <span>
                {dom || "-"}
                {record.isExchange === 1 && (
                  <Tag style={{ marginLeft: "10px" }} color="blue">
                    交易所
                  </Tag>
                )}
              </span>
            );
          }
        },
        {
          valueType: "dependency",
          name: ["bourseYn", "goodsId", "lawsList"],
          columns: ({ bourseYn, goodsId, lawsList }) => {
            return [
              {
                dataIndex: "totalStocks",
                title: lawsList?.length ? "总库存" : "总库存量", // 由【入园时间】与【控制星期】自动计算
                colProps: { xs: 24, sm: 12, xl: 8, xxl: 6 },
                formItemProps: {
                  style: {
                    margin: 0
                  },
                  rules: [
                    { required: true }
                    // {
                    //   validator: (_, value) => {
                    //     if (
                    //       isBlockChain == 1 &&
                    //       salesReq.data.futureExchangeSales &&
                    //       bourseYn == 1 &&
                    //       (salesReq.data.futureExchangeSales + value) / salesReq.data.futureSales <
                    //         4
                    //     ) {
                    //       return Promise.reject('交易所总库存量不得低于本期可用库存总量的80% !');
                    //     } else if (
                    //       isBlockChain == 1 &&
                    //       salesReq.data.futureExchangeSales &&
                    //       !bourseYn &&
                    //       salesReq.data.futureExchangeSales / (salesReq.data.futureSales + value) <
                    //         4
                    //     ) {
                    //       return Promise.reject('非交易所总库存量不得高于本期可用库存总量的20% !');
                    //     } else {
                    //       return Promise.resolve();
                    //     }
                    //   },
                    // },
                  ]
                },
                renderFormItem: () => {
                  return (
                    <ProFormFieldSet>
                      <Flex align="center" gap={8} style={{ padding: "0 4px" }}>
                        <ProFormItem name="totalStock" required style={{ width: "65%", padding: 0, margin: 0 }}>
                          <InputNumber
                            name="totalStock"
                            style={{ width: "100%", padding: 0, margin: 0 }}
                            min={0}
                            step={1}
                            max={99999999}
                            onChange={e => onInstrumentQuantityChange(e)}
                            disabled={
                              (modalState.type == "edit" && record.assetStatus != 2) || lawsList?.length || !goodsId
                            }
                          />
                        </ProFormItem>
                        <ProFormItem name="instrumentPercent" required style={{ width: "35%", padding: 0, margin: 0 }}>
                          <InputNumber
                            name="instrumentPercent"
                            addonAfter="%"
                            style={{ width: "100%", padding: 0, margin: 0 }}
                            min={0}
                            max={sourceInventory.totalInventory == 0 ? 100 : 99.999}
                            step={1}
                            onChange={e => onInstrumentPercentChange(e)}
                            disabled={
                              (modalState.type == "edit" && record.assetStatus != 2) || lawsList?.length || !goodsId
                            }
                          />
                        </ProFormItem>
                      </Flex>
                    </ProFormFieldSet>
                  );
                },
                render: (dom, entity) => {
                  return typeof entity?.totalStock === "number" ? entity?.totalStock : "-";
                }
              }
            ];
          }
        },
        {
          title: "剩余库存",
          dataIndex: "residualInventory",
          hideInForm: true
        },
        {
          title: "出票库存",
          dataIndex: "issueStock",
          hideInForm: true
        },
        {
          title: "过期库存",
          dataIndex: "expireStock",
          hideInForm: true
        },
        {
          title: "购买有效时间",
          dataIndex: "times",
          valueType: "dateRange",
          formItemProps: { rules: [{ required: true }] },
          render: (dom: any) => dom?.join(" 至 "),
          renderFormItem: () => {
            return (
              <div className={styles.rangePickerWrap}>
                <DatePicker
                  disabledDate={current => current < dayjs().startOf("day")}
                  disabled={modalState.type !== "add"}
                  value={modalForm.getFieldValue("times")?.[0] ? dayjs(modalForm.getFieldValue("times")?.[0]) : ""}
                  onChange={(_: any, date: any) => {
                    console.log(_, date);
                    const times = [date, modalForm.getFieldValue("times")?.[1]];
                    modalForm.setFieldsValue({
                      times
                    });
                    if (times?.[0] && times?.[1]) {
                      if (record.isExchange === 1) isBlockChangeTime.current = true; // 交易所编辑页，改动时间展示实时
                      salesReq.run({
                        companyId: currentCompanyInfo?.coId,
                        scenicId,
                        startTime: times[0],
                        endTime: times[1]
                      });
                    }
                  }}
                />
                <SwapRightOutlined />
                <DatePicker
                  disabledDate={current => current < dayjs().startOf("day")}
                  disabled={
                    modalState.type == "edit" &&
                    (record.assetStatus === 0 ||
                      record.assetStatus === 3 ||
                      modalForm.getFieldValue("lawsList")?.length)
                  }
                  value={modalForm.getFieldValue("times")?.[1] ? dayjs(modalForm.getFieldValue("times")?.[1]) : ""}
                  onChange={(_: any, date: any) => {
                    console.log(_, date);

                    const times = [modalForm.getFieldValue("times")?.[0], date];
                    modalForm.setFieldsValue({
                      times
                    });
                    if (times?.[0] && times?.[1]) {
                      if (record.isExchange === 1) isBlockChangeTime.current = true; // 交易所编辑页，改动时间展示实时
                      salesReq.run({
                        companyId: currentCompanyInfo?.coId,
                        scenicId,
                        startTime: times[0],
                        endTime: times[1]
                      });
                    }
                  }}
                />
              </div>
            );
          }
        },
        {
          title: "入园有效时间",
          dataIndex: "time",
          valueType: "dateRange",
          formItemProps: {
            rules: [{ required: true }],
            extra: publishedStock == null ? "" : `可发行库存量：${publishedStock}`,
            className: styles.enterTime
          },
          render: (dom: any) => dom?.join(" 至 "),
          renderFormItem: () => {
            return (
              <div className={styles.rangePickerWrap}>
                <DatePicker
                  picker="year"
                  style={{ width: 100, marginRight: 8 }}
                  value={enterYear}
                  disabled={modalState.type !== "add"}
                  disabledDate={current => current < dayjs().startOf("year")}
                  onChange={date => {
                    setEnterYear(date);
                    // 组装年份
                    const [t1, t2] = modalForm.getFieldValue("time") || [];
                    const _newDate1 = combineDates(date, t1).format("YYYY-MM-DD");
                    const _newDate2 = combineDates(date, t2).format("YYYY-MM-DD");
                    const time = [_newDate1, _newDate2];
                    modalForm.setFieldsValue({ time });
                    // 获取可发行库存量
                    getPublishedStockFn(date);
                  }}
                />
                <DatePicker
                  disabledDate={current =>
                    current < dayjs().startOf("day") ||
                    current > dayjs().year(enterYear.year()).endOf("year").startOf("day")
                  }
                  disabled={modalState.type !== "add"}
                  format="MM-DD"
                  value={modalForm.getFieldValue("time")?.[0] ? dayjs(modalForm.getFieldValue("time")?.[0]) : ""}
                  onChange={(_: any, date: any) => {
                    // 组装年份
                    const _newDate = combineDates(enterYear, date).format("YYYY-MM-DD");
                    const time = [_newDate, modalForm.getFieldValue("time")?.[1]];
                    modalForm.setFieldsValue({ time });
                    if (time?.[0] && time?.[1]) {
                      if (record.isExchange === 1) isBlockChangeTime.current = true; // 交易所编辑页，改动时间展示实时
                      salesReq.run({
                        companyId: currentCompanyInfo?.coId,
                        scenicId,
                        startTime: time[0],
                        endTime: time[1]
                      });
                      if (modalState.type === "add") {
                        // 获取库存信息
                        getCurrentInventoryDatas();
                        getPreviousInventoryDatas();
                      }
                    }
                  }}
                />
                <SwapRightOutlined />
                <DatePicker
                  disabledDate={current =>
                    current < dayjs(modalForm.getFieldValue("time")?.[0]) ||
                    current < dayjs().startOf("day") ||
                    current > dayjs().year(enterYear.year()).endOf("year").startOf("day")
                  }
                  disabled={
                    modalState.type == "edit" &&
                    (record.assetStatus === 0 ||
                      record.assetStatus === 3 ||
                      modalForm.getFieldValue("lawsList")?.length)
                  }
                  format="MM-DD"
                  value={modalForm.getFieldValue("time")?.[1] ? dayjs(modalForm.getFieldValue("time")?.[1]) : ""}
                  onChange={(_: any, date: any) => {
                    // 组装年份
                    const _newDate = combineDates(enterYear, date).format("YYYY-MM-DD");
                    const time = [modalForm.getFieldValue("time")?.[0], _newDate];
                    modalForm.setFieldsValue({ time });
                    if (time?.[0] && time?.[1]) {
                      if (record.isExchange === 1) isBlockChangeTime.current = true; // 交易所编辑页，改动时间展示实时
                      salesReq.run({
                        companyId: currentCompanyInfo?.coId,
                        scenicId,
                        startTime: time[0],
                        endTime: time[1]
                      });
                      if (modalState.type === "add") {
                        // 获取库存信息
                        getCurrentInventoryDatas();
                        getPreviousInventoryDatas();
                      }
                    }
                  }}
                />
              </div>
            );
          }
        },
        {
          title: "票种",
          dataIndex: "ticketGoodsType",
          valueType: "select",
          valueEnum: ticketTypeEnum,
          fieldProps: { disabled: true }
        },

        // {
        //   valueType: 'dependency',
        //   name: ['bourseYn'],
        //   columns: ({ bourseYn }) =>
        //     bourseYn
        //       ? [
        //           {
        //             title: (
        //               <div className="flex justify-content-between w-100">
        //                 申请交易所
        //                 {modalState.type == 'info' || (
        //                   <a
        //                     style={{ marginLeft: '8px' }}
        //                     onClick={() => {
        //                       window.open(getEnv().Attestation);
        //                     }}
        //                   >
        //                     去认证
        //                   </a>
        //                 )}
        //               </div>
        //             ),
        //             dataIndex: 'exchangeId',
        //             valueEnum: exchangeVOList,
        //             formItemProps: { rules: [{ required: true, message: '请选择申请交易所' }] },
        //             colProps: { xs: 24, sm: 12, xl: 8, xxl: 6 },
        //             fieldProps: {
        //               disabled: modalState.type == 'edit' && record.assetStatus != 2,
        //               loading: exchangeVOLoading,
        //               onDropdownVisibleChange: (e) => {
        //                 if (e) {
        //                   getDigitGoodsByCompanyFn();
        //                 }
        //               },
        //             },
        //           },
        //         ]
        //       : [],
        // },
        {
          colProps: { span: 24 },
          formItemProps: {
            noStyle: true
          },
          hideInDescriptions: true,
          renderFormItem: () => (
            <Alert
              message="请在门票设置中仔细核对该产品下各商品的使用有效期"
              type="warning"
              style={{
                marginBottom: 12
              }}
              showIcon
            />
          )
        }
      ]
    },
    {
      title: "库存设置",
      valueType: "group",
      hideInForm: modalState.type !== "add",
      hideInDescriptions: modalState.type !== "add",
      columns: [
        {
          title: "",
          dataIndex: "",
          colProps: { xs: 24, sm: 24, xl: 24, xxl: 24 },
          renderFormItem: () => {
            return (
              <Col span={24}>
                <Row gutter={[16, 16]}>
                  <Col xs={24} sm={24} md={24} lg={24} xl={12}>
                    <Spin spinning={cInventoryReqLoading} style={{ flex: 1 }}>
                      <div className={styles.stockSubTitle}>
                        <span>本期库存</span>
                        <ProFormItem name="currentInventoryDate">
                          <RangePicker disabled />
                        </ProFormItem>
                      </div>
                      <EchartsCom
                        options={options({
                          title: "本期库存",
                          data: currentInventory?.list,
                          totalInventory: currentInventory?.totalInventory,
                          innerWidth
                        })}
                        style={{
                          height: "280px"
                        }}
                      />
                    </Spin>
                  </Col>
                  <Col xs={24} sm={24} md={24} lg={24} xl={12}>
                    <Spin spinning={pInventoryReqLoading} style={{ flex: 1 }}>
                      <div className={styles.stockSubTitle}>
                        <span>往期库存</span>
                        <ProFormItem name="previousInventoryDate">
                          <RangePicker disabled />
                        </ProFormItem>
                      </div>
                      <EchartsCom
                        options={options({
                          title: "往期库存",
                          data: previousInventory?.list,
                          totalInventory: previousInventory?.totalInventory,
                          innerWidth
                        })}
                        style={{
                          height: "280px"
                        }}
                      />
                    </Spin>
                  </Col>
                </Row>
              </Col>
            );
          }
        }
      ]
    },
    // {
    //   hideInDescriptions: true,
    //   valueType: 'dependency',
    //   name: ['bourseYn', 'exchangeAssetDistributorList'],
    //   columns: ({ bourseYn }) => [
    //     {
    //       valueType: 'group',
    //       title: <PrefixTitle>经销价格</PrefixTitle>,
    //       hideInForm: tabKey == '0' || !bourseYn,
    //       columns: [
    //         {
    //           title: '',
    //           valueType: 'formList',
    //           dataIndex: 'exchangeAssetDistributorList',
    //           initialValue: [{ distributorGroupId: null, amount: null }],
    //           fieldProps: {
    //             className: 'on-margin',
    //             creatorButtonProps: {
    //               creatorButtonText: '添加价格',
    //             },
    //             copyIconProps: false,
    //             min: 1,
    //             max:
    //               modalState.type == 'edit' && record.assetStatus === 2
    //                 ? distributorInfoVOList.length
    //                 : dataSource.exchangeAssetDistributorList?.length, // 创建库存驳回时才可编辑
    //             itemRender: (doms, listMeta) => {
    //               const b = listMeta.index < dataSource.exchangeAssetDistributorList?.length;
    //               return (
    //                 <ConfigProvider
    //                   componentDisabled={
    //                     !(modalState.type == 'edit' && record.assetStatus == 2) && b
    //                   }
    //                 >
    //                   <div style={{ display: 'flex', cursor: 'not-allowed' }}>
    //                     {doms.listDom}
    //                     <div style={{ position: 'absolute', right: '-22px' }}>
    //                       {b || doms.action}
    //                     </div>
    //                   </div>
    //                 </ConfigProvider>
    //               );
    //             },
    //           },
    //           colProps: { xs: 24, sm: 24, xl: 16, xxl: 12 },
    //           columns: [
    //             {
    //               valueType: 'group',
    //               rowProps: {
    //                 gutter: 24,
    //               },
    //               columns: [
    //                 {
    //                   title: '',
    //                   dataIndex: 'distributorGroupId',
    //                   valueType: 'select',
    //                   formItemProps: {
    //                     rules: [{ required: true }],
    //                   },
    //                   fieldProps: {
    //                     fieldNames: { label: 'name', value: 'id' },
    //                     options: distributorInfoVOList,
    //                     labelInValue: true,
    //                     placeholder: '请选择经销商分组',
    //                   },
    //                   colProps: { span: 12 },
    //                 },
    //                 {
    //                   title: '',
    //                   dataIndex: 'amount',
    //                   valueType: 'digit',
    //                   formItemProps: {
    //                     rules: [{ required: true }],
    //                   },
    //                   fieldProps: {
    //                     placeholder: '请输入经销价格',
    //                   },
    //                   colProps: { span: 12 },
    //                   width: '100%',
    //                 },
    //               ],
    //             },
    //           ],
    //         },
    //       ],
    //     },
    //   ],
    // },
    // {
    //   hideInForm: true,
    //   valueType: 'dependency',
    //   name: ['bourseYn', 'exchangeAssetDistributorList'],
    //   hideInDescriptions: tabKey == '0',
    //   columns: ({ bourseYn, exchangeAssetDistributorList }) => [
    //     {
    //       title: '经销价格',
    //       hideInDescriptions: !bourseYn || !exchangeAssetDistributorList,
    //       columns: [
    //         {
    //           title: '',
    //           dataIndex: 'exchangeAssetDistributorList',
    //           render: (dom: any) => (
    //             <div style={{ display: 'flex', flexDirection: 'column' }}>
    //               {dom?.map((item: any, index: any) => (
    //                 <div key={getUniqueId()} style={{ marginTop: index ? '16px' : '0' }}>
    //                   <span>{item.name}：</span>
    //                   <Tag color="blue">
    //                     <ProField value={item.amount} valueType="money" mode="read" />
    //                   </Tag>
    //                 </div>
    //               ))}
    //             </div>
    //           ),
    //         },
    //       ],
    //     },
    //   ],
    // },
    {
      valueType: "dependency",
      name: ["lawsList", "time", "goodsId", "ticketGoodsType"],
      columns: ({ lawsList, time, goodsId, ticketGoodsType }) => [
        {
          title: <PrefixTitle>分时预约</PrefixTitle>,
          valueType: "group",
          hideInDescriptions: true,
          hideInForm: !lawsList?.length,
          columns: [
            {
              title: "",
              dataIndex: "timeLaws",
              colProps: { span: 24 },
              renderFormItem: () => {
                return (
                  <TimeStore
                    type={modalState.type}
                    dateRange={time}
                    lawsColumns={[
                      {
                        title: ({ beginTime, endTime }: any) => `分时时段：${[beginTime, endTime].join("-")}`,
                        render: () => {}
                      },
                      {
                        title: () => "日库存量",
                        render: ({ stockAmount = 0 }) => stockAmount
                      }
                    ]}
                    lawsList={lawsList}
                    set={(id: any, date: any) => {
                      setModalSetData({
                        dateType: "data",
                        timeShareId: [id],
                        dateList: [date]
                      });
                      setTimeTableData([
                        {
                          id,
                          goodsName: ticketGoodsListVOList[goodsId],
                          ticketGoodsType: ticketGoodsType,
                          stockAmount: valueObj?.[date]?.[id]?.stockAmount
                        }
                      ]);
                      modalSetState.setType("edit");
                    }}
                  />
                );
              }
            }
          ]
        },
        {
          title: "分时预约",
          hideInDescriptions: !lawsList?.length,
          hideInForm: true,
          columns: [
            {
              title: "",
              dataIndex: "timeLaws",
              render: dom => (
                <TimeStore
                  value={dom}
                  dateRange={time}
                  lawsColumns={[
                    {
                      title: ({ beginTime, endTime }: any) => `分时时段：${[beginTime, endTime].join("-")}`,
                      render: () => {}
                    },
                    {
                      title: () => "出票库存/总库存量",
                      render: ({ issueStock = 0, stockAmount = 0 }) => `${issueStock}/${stockAmount}`
                    }
                  ]}
                  lawsList={lawsList}
                />
              )
            }
          ]
        }
      ]
    },
    {
      title: "说明信息",
      columns: [
        {
          title: "备注",
          dataIndex: "remark",
          valueType: "textarea",
          colProps: { span: 24 },
          fieldProps: {
            showCount: true,
            maxLength: 1000
          }
        }
      ]
    }
  ];

  // 分时库存设置
  const modalSetState = useModal();
  const [modalSetData, setModalSetData] = useState({});
  const timeEnum = () => {
    const obj: Record<string, string> = {};
    modalForm.getFieldValue("lawsList")?.forEach((item: any) => {
      obj[item.id] = [item.beginTime, item.endTime].join("-");
    });
    return obj;
  };
  const valueObj = useMemo(() => {
    const obj1: any = {};
    dataSource.timeLaws?.forEach((item1: any) => {
      const obj2: any = {};
      item1.timeShareDateList.forEach((item2: any) => {
        obj2[item2.timeShareId] = item2;
      });
      obj1[item1.timeShareData] = obj2;
    });
    return obj1;
  }, [dataSource]);
  const [timeTableData, setTimeTableData] = useState<any>([]);
  const timeColumns: TableProps["columns"] = [
    {
      title: "分时时段",
      dataIndex: "id",
      fixed: "left",
      render: value => timeEnum()[value]
    },
    {
      title: "商品名称",
      dataIndex: "goodsName"
    },
    {
      title: "票种",
      dataIndex: "ticketGoodsType",
      render: value => ticketTypeEnum[value]
    },
    {
      title: "库存数量",
      dataIndex: "stockAmount",
      fixed: "right",
      render: (value, record, index) => (
        <InputNumber
          min={0}
          value={value}
          onChange={(v: any) => {
            const list = structuredClone(timeTableData);
            list[index].stockAmount = v;
            setTimeTableData(list);
          }}
        />
      )
    }
  ];
  const modalSetColumns: ProFormColumnsType[] = [
    {
      title: "",
      columns: [
        {
          title: "时间选择",
          dataIndex: "dateType",
          valueType: "radio",
          valueEnum: {
            data: "按日期",
            week: "按星期"
          },
          initialValue: "data",
          formItemProps: { rules: [{ required: true }] }
        },
        {
          valueType: "dependency",
          name: ["dateType"],
          columns: ({ dateType }) => [
            {
              title: "选择日期",
              dataIndex: "dateList",
              valueType: "date",
              hideInForm: dateType !== "data",
              colProps: { xs: 24, sm: 12 },
              formItemProps: { rules: [{ required: true }] },
              fieldProps: {
                multiple: true,
                disabledDate: (current: any) => {
                  return (
                    current < dayjs(modalForm.getFieldValue("time")[0]) ||
                    current > dayjs(modalForm.getFieldValue("time")[1])
                  );
                }
              }
            },
            {
              title: "选择区间",
              dataIndex: "dateRange",
              valueType: "dateRange",
              hideInForm: dateType !== "week",
              colProps: { xs: 24, sm: 12 },
              formItemProps: { rules: [{ required: true }] },
              initialValue: [modalForm.getFieldValue("time")[0], modalForm.getFieldValue("time")[1]],
              fieldProps: {
                disabledDate: (current: any) => {
                  return (
                    current < dayjs(modalForm.getFieldValue("time")[0]) ||
                    current > dayjs(modalForm.getFieldValue("time")[1])
                  );
                }
              }
            },
            {
              title: "设置星期",
              dataIndex: "dateWeek",
              valueType: "checkbox",
              valueEnum: {
                0: "每周日",
                1: "每周一",
                2: "每周二",
                3: "每周三",
                4: "每周四",
                5: "每周五",
                6: "每周六"
              },
              initialValue: ["0", "1", "2", "3", "4", "5", "6"],
              hideInForm: dateType !== "week",
              formItemProps: { rules: [{ required: true }] }
            }
          ]
        }
      ]
    },
    {
      title: "",
      columns: [
        {
          title: "分时选择",
          dataIndex: "timeShareId",
          valueEnum: timeEnum(),
          fieldProps: {
            mode: "multiple",
            onChange: (list: any) => {
              setTimeTableData(
                list?.map((id: any) => ({
                  id,
                  goodsName: ticketGoodsListVOList[modalForm.getFieldValue("goodsId")],
                  ticketGoodsType: modalForm.getFieldValue("ticketGoodsType")
                }))
              );
            }
          },
          colProps: { span: 24 },
          formItemProps: { rules: [{ required: true }] }
        },
        {
          title: "",
          colProps: { span: 24 },
          renderFormItem: () => (
            <Table {...tableConfig} rowKey="id" columns={timeColumns} dataSource={timeTableData} pagination={false} />
          )
        }
      ]
    }
  ];

  // 获取交易所数据
  const getDigitGoodsByCompanyFn = () => {
    setExchangeVOLoading(true);
    getDigitGoodsByCompany({ coId, scenicId })
      .then(({ data: { exchangeVOList, distributorInfoVOList } }) => {
        const exchangeObj: any = {};
        for (const item of exchangeVOList || []) {
          exchangeObj[item.id] = item.legalName;
        }
        setExchangeVOList(exchangeObj);
        setDistributorInfoVOList(distributorInfoVOList);
        setExchangeVOLoading(false);
      })
      .catch(() => {
        setExchangeVOLoading(false);
      });
  };

  // 获取产品
  const getProductFn = () => {
    setProductLoading(true);
    getSimpleList({ scenicId, operatorId: [currentCompanyInfo?.coId] })
      .then(res => {
        cpList = res;
        setProductLoading(false);
        setCpOptions(cpList.map((item: any) => ({ value: item.id, label: item.name })));
      })
      .catch(() => {
        setProductLoading(false);
      });
  };

  // 获取可发行库存量
  const getPublishedStockFn = (date = enterYear) => {
    const _params = {
      isExchange: modalForm.getFieldValue("bourseYn") == 1 ? 1 : 0,
      scenicId: scenicInfo?.scenicId,
      year: date?.year()
    };
    apiGetPublishedStock(_params).then(res => {
      setPublishedStock(res.data);
    });
  };

  // 点击新增
  const onClickAdd = () => {
    // 初始化表单
    setDataSource({
      id: "",
      isEnable: 0,
      enterBeginTime,
      enterEndTime,
      time: [enterBeginTime, enterEndTime]
    });
    setTicketGoodsListVOList({});
    setRecord({});
    modalForm.setFieldsValue({
      lawsList: [],
      time: [enterBeginTime, enterEndTime],
      times: [enterBeginTime, enterEndTime]
    });
    modalState.setType("add");
    salesReq.run({
      companyId: currentCompanyInfo?.coId,
      scenicId,
      startTime: enterBeginTime,
      endTime: enterEndTime
    });
    // 获取库存信息
    getCurrentInventoryDatas();
    getPreviousInventoryDatas();
    // 设置入园时间年份
    setEnterYear(dayjs());
  };

  // 重新获取商品，重置对应的数据
  const getGoodsList = async (form: any, e: any) => {
    const { data } = await getGoodsListApi({
      id: cpList[e.ticketName].id,
      isChain: Number(tabKey),
      isExchange: e.bourseYn == 1 ? 1 : 0
    });
    const obj: any = {};
    (data.ticketGoodsListVOList || []).map((item: any) => {
      obj[item.goodsId] = item.goodsName;
    });
    goodsList = data.ticketGoodsListVOList || [];
    setTicketGoodsListVOList(obj);
    form.setFieldsValue({
      totalStock: null,
      goodsId: null
    });
  };

  useEffect(() => {
    if (modalState.type === "add" || isBlockChangeTime.current) {
      // 展示实时经营状况
      setHistorySales(salesReq.data);
      historySalesRef.current = salesReq.data;
    } else {
      // 展示落库经营状况
      if (dataSource.assetAuthInfo) {
        const assetAuthInfo = JSON.parse(dataSource.assetAuthInfo);
        setHistorySales(assetAuthInfo);
        historySalesRef.current = assetAuthInfo;
      } else {
        setHistorySales(salesReq.data);
        historySalesRef.current = salesReq.data;
      }
    }
  }, [salesReq.data, dataSource, modalState.type]);

  useEffect(() => {
    const resize = () => {
      setInnerWidth(window.innerWidth);
    };
    if (modalState.type) {
      getPublishedStockFn();
      window.addEventListener("resize", resize);
    }
    return () => {
      window.removeEventListener("resize", resize);
    };
  }, [modalState.type]);

  useEffect(() => {
    getProductFn();
    if (searchParams?.type === "add") {
      modalState.setType("add");
      onClickAdd();
    }
  }, []);

  return (
    <>
      {isBlockChain == 1 && (
        <Tabs
          style={modalState.tableStytle}
          tabBarStyle={{ padding: "0 24px", margin: "0", background: "#fff" }}
          onChange={setTabKey}
          activeKey={tabKey}
          items={[
            {
              label: "普通库存",
              key: "0"
            },
            {
              label: "区块链库存",
              key: "1"
            }
          ]}
        />
      )}
      <ProTable<API.StockListItem, API.StockListParams>
        style={modalState.tableStytle}
        {...tableConfig}
        actionRef={actionRef}
        formRef={formRef}
        toolBarRender={() => [
          <Access key={getUniqueId()} accessible={access.canStorageManage_insert}>
            <Button type="primary" key="primary" onClick={onClickAdd}>
              <PlusOutlined /> 新增
            </Button>
          </Access>
        ]}
        params={{
          flag: tabKey,
          scenicId,
          operatorId: [currentCompanyInfo.coId],
          selectType: 0,
          goodsName
        }}
        request={async params => {
          try {
            const formValues: any = formRef.current?.getFieldsValue();
            const { data } = await getTicketStockList({ ...params, goodsName: formValues?.goodsName });
            return data;
          } catch (error) {
            return {
              data: [],
              total: 0
            };
          }
        }}
        columns={columns}
      />

      <ProModal
        page
        {...modalState}
        title={stockType[tabKey]}
        actionRef={actionRef}
        columns={modalColumns}
        formInstance={modalForm}
        onValuesChange={async (form: any, e: any, ...props: any) => {
          try {
            if (e.hasOwnProperty("exchangeAssetDistributorList")) {
              if (JSON.stringify(e.exchangeAssetDistributorList).includes("distributorGroupId")) {
                setDistributorInfoVOList(
                  structuredClone(distributorInfoVOList).map((item: any) => {
                    return {
                      ...item,
                      disabled: !!form
                        .getFieldValue("exchangeAssetDistributorList")
                        ?.find(
                          (i: any) => i?.distributorGroupId?.value == item?.id || i?.distributorGroupId == item?.id
                        )
                    };
                  })
                );
              }
            }
            if (e.hasOwnProperty("bourseYn")) {
              if (e.bourseYn == 1) {
                //开关交易所，还原禁用状态
                setDistributorInfoVOList(
                  structuredClone(distributorInfoVOList).map((item: any) => {
                    return {
                      ...item,
                      disabled: false
                    };
                  })
                );
              }

              if (cpList[props?.[0].ticketName]?.id) {
                getGoodsList(form, props?.[0]);
              }
            }
            if (e.hasOwnProperty("ticketName")) {
              if (cpList[e.ticketName]?.id) {
                getGoodsList(form, props?.[0]);
              }
            }
            if (e.hasOwnProperty("timeLaws")) {
              const sum = form.getFieldsValue().timeLaws;
              let totalStock = 0;
              sum.forEach(({ timeShareDateList }: any) => {
                timeShareDateList.forEach(({ stockAmount }: any) => {
                  if (stockAmount) totalStock += stockAmount;
                });
              });
              form.setFieldsValue({ totalStock, totalStocks: [totalStock] });
            }
          } catch (error) {}
        }}
        onFinish={async (val: any) => {
          if (modalState.type == "edit") {
            let b = true;
            for (const key of Object.keys(val)) {
              if (JSON.stringify(val[key]) != JSON.stringify(dataSource[key])) {
                b = false;
                break;
              }
            }
            if (b) return message.warning("请编辑内容后提交！");
          }
          // 库存校验
          if (!val.id && val.totalStock > publishedStock && publishedStock != null) {
            message.warning(`超过库存最大发行数量，请减少库存数量或调整景区最大发行量`);
            return;
          }
          if (!val.totalStock) {
            message.info("总库存量不能为 0");
            return;
          }
          if (dataSource.id) {
            val.id = dataSource.id;
          } else {
            val.restrictWeekList = restrictWeekList;
          }
          val.flag = tabKey;
          if (tabKey == "1") val.goodsName = ticketGoodsListVOList[val.goodsId];
          val.enterBeginTime = val.time[0];
          val.enterEndTime = val.time[1];
          val.purchaseBeginTime = val.times[0];
          val.purchaseEndTime = val.times[1];
          val.type = typeValue;
          // val.nums = lawsList.length > 0 ? value : val.totalStock;
          val.creditCode = creditCode;
          val.ticketId = ticketId;
          val.operatorId = operatorId2;
          if (modalState.type == "edit") {
            // val.ticketId = dataSource.ticketId;
            val.operatorId = dataSource.operatorId;
            val.batchId = record.batchId[0];
          }
          for (const item of cpList) {
            if (val.ticketId == item.id) {
              val.ticketName = item.name;
              break;
            }
          }
          // 格式化经销价格
          if (val.exchangeAssetDistributorList) {
            val.exchangeAssetDistributorList.map((item: any) => {
              if (item.distributorGroupId.value) {
                item.name = item.distributorGroupId.label;
                item.distributorGroupId = item.distributorGroupId.value;
              }
            });
          }
          delete val.time;
          delete val.times;
          // 修改状态
          if ([0, 2].includes(dataSource.assetStatus)) {
            // 创建库存审核中
            val.assetStatus = 0;
          } else if ([1, 3, 4, 5].includes(dataSource.assetStatus)) {
            // 修改库存审核中
            val.assetStatus = 3;
          }
          // 传入经营状态
          const assetAuthInfo: any = {};
          if (val.id) {
            // 编辑，非交易所和空值要加
            if (record.isExchange !== 1 && !dataSource.assetAuthInfo) {
              assetAuthInfo.futureExchangeSales = salesReq?.data?.futureExchangeSales;
              assetAuthInfo.futureSales = salesReq?.data?.futureSales;
              assetAuthInfo.historySales = salesReq?.data?.historySales;
            }
          } else {
            // 新增，必加
            assetAuthInfo.futureExchangeSales = salesReq?.data?.futureExchangeSales;
            assetAuthInfo.futureSales = salesReq?.data?.futureSales;
            assetAuthInfo.historySales = salesReq?.data?.historySales;
          }
          const _ticketDatas = {
            ...val,
            ticketType: val.type * 1,
            productType: 0,
            ...assetAuthInfo
          };
          const msgType = val.id ? "编辑" : "新增";
          if (val.id) {
            const hide = message.loading("正在" + msgType);
            try {
              await UpManageStock(_ticketDatas);
              addOperationLogRequest({
                action: "edit",
                content: `编辑【${val.ticketName}】库存`
              });
              message.success(msgType + "成功");
              // 关闭弹窗并刷新列表
              modalState.setType(null);
              actionRef?.current?.reload();
            } catch (error) {}
            hide();
          } else {
            const hide = message.loading("正在" + msgType);
            try {
              await addStock({
                sourceType: 1,
                ticket: _ticketDatas
              });
              addOperationLogRequest({
                action: "add",
                content: `新增【${val.ticketName}】库存`
              });
              // 更新引导
              updateGuideInfo({ tabIndex: 0, status: GuideStepStatus.step0_3 });
              message.success(msgType + "成功");
              // 关闭弹窗并刷新列表
              modalState.setType(null);
              await sleep(1000); //加延时防刷新太快没有数据返回
              actionRef?.current?.reload();
            } catch (error) {}
            hide();
          }
        }}
        infoRequest={async () => {
          try {
            const { data }: any = await getTicketStockInfo({
              id: record.id,
              productType: 0
            });
            const { totalStock, residualInventory, issueStock, expireStock, ticketGoodsType } = record;
            data.time = [data.enterBeginTime, data.enterEndTime];
            data.times = [data.purchaseBeginTime, data.purchaseEndTime];
            data.type += "";
            data.lawsList = data.timeShareVoList;
            setDataSource(data);
            setTicketId(data.ticketId);
            setTicketGoodsListVOList({ [data.goodsId]: data.goodsName });
            salesReq.run({
              companyId: currentCompanyInfo?.coId,
              scenicId,
              startTime: data.enterBeginTime,
              endTime: data.enterEndTime
            });
            onInstrumentQuantityChange(totalStock);
            return {
              data: {
                ...data,
                totalStocks: [totalStock],
                totalStock,
                residualInventory,
                issueStock,
                expireStock,
                ticketGoodsType: String(ticketGoodsType)
              }
            };
          } catch (error) {
            return {};
          }
        }}
      />
      {/* 设置库存 */}
      <ProModal
        {...modalSetState}
        fullTitle="编辑采购数量"
        columns={modalSetColumns}
        layout="horizontal"
        dataSource={modalSetData}
        onFinish={async v => {
          const obj = structuredClone(valueObj);
          let dateList = [];
          if (v.dateType == "data") {
            dateList = v.dateList;
          } else {
            let nowDate = dayjs(v.dateRange[0]);
            do {
              if (v.dateWeek.includes(String(nowDate.day()))) {
                dateList.push(nowDate.format("YYYY-MM-DD"));
              }
              nowDate = nowDate.add(1, "day");
            } while (!nowDate.isAfter(dayjs(v.dateRange[1])));
          }
          dateList.forEach((d: string) => {
            v.timeShareId.forEach((t: string, i: number) => {
              if (!obj[d]) obj[d] = {};
              const obj2: any = modalForm.getFieldValue("lawsList").find((item: any) => item.id == t);
              obj[d][t] = {
                timeShareId: obj2.id,
                timeShareBeginTime: obj2.beginTime,
                timeShareEndTime: obj2.endTime,
                stockAmount: timeTableData[i].stockAmount
              };
            });
          });

          const timeLaws = Object.keys(obj).map(item1 => ({
            timeShareData: item1,
            timeShareDateList: Object.values(obj[item1])
          }));
          // 更新分时数据
          modalForm.setFieldValue("timeLaws", timeLaws);
          // 更新总库存数据
          let totalStock = 0;
          timeLaws.forEach(({ timeShareDateList }: any) => {
            timeShareDateList.forEach(({ stockAmount }: any) => {
              if (stockAmount) totalStock += stockAmount;
            });
          });
          modalForm.setFieldsValue({ totalStock, totalStocks: [totalStock] });
          // 更新表单数据
          const data = structuredClone(dataSource);
          data.timeLaws = timeLaws;
          setDataSource(data);
          return true;
        }}
      />
    </>
  );
};

export default TableList;
