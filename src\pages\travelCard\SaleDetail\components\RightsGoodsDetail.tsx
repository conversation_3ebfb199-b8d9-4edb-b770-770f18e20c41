/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-04-10 16:51:46
 * @LastEditTime: 2023-04-18 09:54:48
 * @LastEditors: zhangfeng<PERSON>i
 */
import DetailsPop from "@/common/components/DetailsPop";
import { useTypeEnum } from "@/common/utils/enum";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import type { ProDescriptionsGroup } from "@/components/ModalDescriptions";
import type { ModalState } from "@/hooks/useModal";
import useModal from "@/hooks/useModal";
import IssueRuleDetails from "@/pages/ruleSet/SellTickets/components/IssueRuleDetails";
import { getTravelCardGoodsInfo } from "@/services/api/travelCard";
import { Divider, Space } from "antd";
import { isNil, round } from "lodash";
import type { FC } from "react";
import { useEffect, useState } from "react";
import { useRequest } from "@umijs/max";
import RightsTicketDetail from "./RightsTicketDetail";

type RightsGoodsDetailProps = ModalState & {
  productSkuId?: string;
  dataItem?: API.OrderRightsGoodsListItem;
  isTravelCard?: boolean;
};

/**
 * @description: 权益卡商品详情
 */
const RightsGoodsDetail: FC<RightsGoodsDetailProps> = ({
  visible,
  setVisible,
  dataItem,
  productSkuId = "",
  isTravelCard
}) => {
  const [ticketItem, setTicketItem] = useState<API.TravelGoodsTicketItem>();
  const rightsTicketModal = useModal();

  // 详情信息
  const getGoodsInfoReq = useRequest(getTravelCardGoodsInfo, {
    manual: true,
    onSuccess(data, params) {
      addOperationLogRequest({
        action: "info",
        content: isTravelCard ? `查看【${data.goodsName}】商品详情` : `查看【${dataItem?.orderId}】权益票所属权益卡详情`
      });
    }
  });

  const goodsInfoColumns: ProDescriptionsGroup<API.TravelCardGoodsListItem>[] = [
    {
      title: "基础信息",
      columns: [
        {
          title: "所属权益卡名称",
          dataIndex: "travelCardName"
        },
        {
          title: "商品名称",
          dataIndex: "goodsName"
        },
        {
          title: "使用方式",
          dataIndex: "useType",
          valueEnum: useTypeEnum
        },
        {
          dataIndex: "effectiveTime",
          render: dom => (
            <span>
              生效日期起 {dom ?? "-"}
              天内有效（若填 1 天内有效则表示生效当天有效）
            </span>
          )
        },

        {
          dataIndex: "renewEndTime",
          render: dom => (
            <span>
              失效 {dom ?? "-"}
              天内可续
            </span>
          )
        }
      ]
    },

    {
      title: "价格信息",
      columns: [
        {
          title: `商品折扣率（%）`,
          render: (_, { marketPrice, overallDiscount }) => {
            return (
              <div>
                <div>{overallDiscount} %</div>
                <div>
                  {marketPrice} * {overallDiscount}% = {round(marketPrice * (overallDiscount / 100), 2)}
                </div>
              </div>
            );
          }
        },
        {
          title: "分销折扣区间（%）",
          render: (_, { beginDiscount, marketPrice, endDiscount, overallDiscount }) => {
            if (isNil(beginDiscount ?? endDiscount)) {
              return "-";
            }
            return (
              <div>
                <div>
                  {beginDiscount} % ~ {endDiscount} %
                </div>
                <div>
                  {round(((beginDiscount * 1) / 100) * (marketPrice * (overallDiscount / 100)), 2)} ~{" "}
                  {round(((endDiscount * 1) / 100) * (marketPrice * (overallDiscount / 100)), 2)}
                </div>
              </div>
            );
          }
        }
      ]
    },
    {
      title: "权益信息",
      columns: [
        {
          title: "是否首次创建权益",
          dataIndex: "isFirst",
          valueType: "select",
          valueEnum: {
            0: { text: "否" },
            1: { text: "是" }
          }
        },
        {
          title: "权益名称",
          dataIndex: "rightsName"
        }
      ]
    },
    {
      title: "规则信息",
      columns: [
        {
          title: "出票规则",
          dataIndex: "issueName",
          renderText: (dom: any, entity: any) => (
            <a
              onClick={() => {
                IssueRuleDetails.show(entity.issueId);
              }}
            >
              {dom}
            </a>
          )
        }
      ]
    },
    {
      title: "说明信息",
      columns: [
        {
          title: "预定须知",
          dataIndex: "notices"
        }
      ]
    },
    {
      title: "关联的权益票",
      columns: [
        {
          span: 2,
          dataIndex: "travelGoodsTicket",
          render: (_, { travelGoodsTicket = [] }) => (
            <Space split={<Divider type="vertical" />} wrap>
              {travelGoodsTicket.map(({ id, goodsName }) => (
                <a
                  key={id}
                  onClick={() => {
                    setTicketItem({
                      id,
                      goodsName
                    });
                    rightsTicketModal.setVisible(true);
                  }}
                >
                  {goodsName}
                </a>
              ))}
            </Space>
          )
        }
      ]
    }
  ];

  useEffect(() => {
    if (visible && productSkuId) {
      getGoodsInfoReq.run({
        id: productSkuId
      });
    }
  }, [productSkuId, visible]);

  return (
    <>
      <DetailsPop
        title="权益卡详情"
        visible={visible}
        setVisible={setVisible}
        columnsInitial={goodsInfoColumns}
        isLoading={getGoodsInfoReq.loading}
        dataSource={getGoodsInfoReq.data}
      />

      <RightsTicketDetail {...rightsTicketModal} id={ticketItem?.id} />
    </>
  );
};

export default RightsGoodsDetail;
