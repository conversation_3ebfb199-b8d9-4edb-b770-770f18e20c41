/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-04-11 14:51:48
 * @LastEditTime: 2023-04-18 11:15:41
 * @LastEditors: zhangfeng<PERSON>i
 */

import { ticketStatusEnum } from "@/common/utils/enum";
import { modelWidth } from "@/common/utils/gConfig";
import { addOperationLogRequest } from "@/common/utils/operationLog";
import type { ModalState } from "@/hooks/useModal";
import { getOrderRightsGoodsList } from "@/services/api/travelCard";
import type { ProColumns } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import { Modal, Tooltip } from "antd";
import dayjs from "dayjs";
import type { FC } from "react";
import { useModel } from "@umijs/max";

type UsageRecordProps = ModalState & {
  cardId?: string;
  orderId?: string;
};

const UsageRecord: FC<UsageRecordProps> = ({ visible, setVisible, cardId = "", orderId = "" }) => {
  const { initialState } = useModel("@@initialState");
  const { scenicId = "" } = initialState?.scenicInfo || {};
  const { coId = "" } = initialState?.currentCompanyInfo || {};

  const ticketColumns: ProColumns<API.OrderRightsGoodsListItem>[] = [
    {
      title: "订单号",
      dataIndex: "orderId",
      renderText: (text: string) =>
        text ? (
          <Tooltip title={text}>
            {text.substring(0, 6) + "..." + text.substring(text.length - 6, text.length - 1)}
          </Tooltip>
        ) : (
          "-"
        )
    },
    {
      title: "票号",
      dataIndex: "ticketNumber",
      renderText: (text: string) =>
        text ? (
          <Tooltip title={text}>
            {text.substring(0, 5) + "..." + text.substring(text.length - 6, text.length - 1)}
          </Tooltip>
        ) : (
          "-"
        )
    },
    {
      title: "权益票",
      dataIndex: "rightsTicketName",
      valueType: "select"
    },

    {
      title: "入园日期",
      dataIndex: "enterTime",
      valueType: "date"
    },

    {
      title: "出票时间",
      dataIndex: "createTime",
      renderText: text => dayjs(text).format("YYYY-MM-DD HH:mm:ss")
    },
    {
      title: "支付金额（元）",
      dataIndex: "payAmount",
      align: "right",
      render: (dom, record) => (record.payAmount * 1).toFixed(2)
    },
    {
      title: "实收金额（元）",
      dataIndex: "payAmount",
      align: "right",
      render: (dom, record) => (record.payAmount * 1).toFixed(2)
    },
    {
      title: "状态",
      dataIndex: "status",
      valueEnum: ticketStatusEnum
    }
  ];

  const modalProps = {
    visible,
    title: "使用记录",
    onCancel: () => {
      setVisible(false);
    },
    width: modelWidth.lg,
    okButtonProps: {
      style: {
        display: "none"
      }
    },
    footer: false
  };

  return (
    <Modal {...modalProps} destroyOnClose>
      <ProTable
        rowKey="orderId"
        columns={ticketColumns}
        options={false}
        search={false}
        pagination={{ defaultPageSize: 10 }}
        params={{ serviceProviderId: coId, scenicId, cardId }}
        request={async (params: any) => {
          const { data } = await getOrderRightsGoodsList({ type: "2", ...params });
          addOperationLogRequest({
            action: "info",
            content: `查看【${orderId}】权益卡使用记录`
          });
          return data;
        }}
      />
    </Modal>
  );
};

export default UsageRecord;
