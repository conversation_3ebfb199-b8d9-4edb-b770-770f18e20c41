/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-05-05 11:48:20
 * @LastEditTime: 2022-08-26 14:46:20
 * @LastEditors: zhangfeng<PERSON>i
 */

import { request } from "@umijs/max";
import { scenicHost } from ".";

/** 查询权益卡审核列表 */
export async function getAuditList(params: API.AuditListParams, options: Record<string, any> = {}) {
  return request<ResponseListData<API.AuditListItem[]>>(`${scenicHost}/examineTravelGoods/pageList`, {
    method: "GET",
    params,
    ...options
  });
}

/** 权益卡通过/驳回审核 */
export async function switchAudit(
  params: Pick<API.AuditListItem, "id" | "status"> & { reason?: string },
  options: Record<string, any> = {}
) {
  return request<ResponseData<null>>(`${scenicHost}/examineTravelGoods/approve`, {
    method: "PUT",
    data: params,
    ...options
  });
}

/** 权益卡审核查看详情 */
export async function getAuditInfo(params: Pick<API.AuditListItem, "id">, options: Record<string, any> = {}) {
  return request<ResponseData<API.AuditListItem>>(`${scenicHost}/examineTravelGoods/info/${params.id}`, {
    method: "GET",
    ...options
  });
}

/** 权益票审核列表 */
export async function getTicketRightsList(params: API.TicketRightsListParams, options: Record<string, any> = {}) {
  return request<ResponseListData<API.TicketRightsListItem[]>>(`${scenicHost}/ticketRights/pageList`, {
    method: "GET",
    params,
    ...options
  });
}

/** 权益票详情 */
export async function getTicketRightsInfo(
  params: {
    id: string;
  },
  options: Record<string, any> = {}
) {
  return request<ResponseData<API.TicketRightsInfo>>(`${scenicHost}/ticketRights/info/${params.id}`, {
    method: "GET",
    params,
    ...options
  });
}
/** 权益卡通过/驳回审核 */
export async function updateRightsStatus(params: API.UpdateRightsStatusParams, options: Record<string, any> = {}) {
  return request<ResponseData<null>>(`${scenicHost}/ticketRights/approve`, {
    method: "PUT",
    data: params,
    ...options
  });
}
