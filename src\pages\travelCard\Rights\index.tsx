/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-04-24 09:42:40
 * @LastEditTime: 2023-08-21 17:14:15
 * @LastEditors: zhangfengfei
 */

import { tableConfig } from "@/common/utils/config";
import { ScenicServiceType } from "@/common/utils/enum";
import useModal from "@/hooks/useModal";
import { getRightsList } from "@/services/api/rightsManage";
import type { ActionType, ProColumns } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import { Space } from "antd";
import type { FC } from "react";
import { useRef, useState } from "react";
import { Access, useAccess, useModel } from "@umijs/max";
import AllPrivileges from "./components/AllPrivileges";
import EquityDetail from "./components/EquityDetail";

type RightsManageProps = Record<string, never>;

/**
 * @description: 权益卡权益
 */
const RightsManage: FC<RightsManageProps> = () => {
  const { initialState } = useModel("@@initialState");

  const { scenicId } = initialState!.scenicInfo!;

  const { currentCompanyInfo } = useModel("@@initialState").initialState || {};

  const access = useAccess();

  // 权益 Item
  const [rightsItem, setRightsItem] = useState<API.RightsListItem>();

  const actionRef = useRef<ActionType>();

  // modal 状态控制
  const rightsDetailModal = useModal();
  const allPrivilegesModal = useModal();

  const columns: ProColumns<API.RightsListItem>[] = [
    {
      title: "权益 ID",
      dataIndex: "id"
    },

    {
      title: "权益名称",
      dataIndex: "rightsName"
    },
    {
      title: "关联权益卡",
      dataIndex: "travelCardNames",
      search: false,
      renderText: (_, record) => (record.travelCardNames || []).join("，")
    },
    {
      title: "旅游场景",
      dataIndex: "scenicType",
      valueType: "select",
      valueEnum: ScenicServiceType,
      search: false
    },
    {
      title: "操作",
      key: "option",
      valueType: "option",
      fixed: "right",
      renderText: (_, record) => (
        <Space size="middle">
          <Access accessible={access.canRightsManage_equityDetail}>
            <a
              onClick={() => {
                setRightsItem(record);
                rightsDetailModal.setVisible(true);
              }}
            >
              权益明细
            </a>
          </Access>
          <Access accessible={access.canRightsManage_selectPrivilege}>
            <a
              onClick={() => {
                setRightsItem(record);
                allPrivilegesModal.setVisible(true);
              }}
            >
              查看所有特权
            </a>
          </Access>
        </Space>
      )
    }
  ];

  return (
    <>
      <ProTable<API.RightsListItem, API.RightsListParams>
        {...tableConfig}
        actionRef={actionRef}
        rowKey="id"
        params={{ scenicId, operatorId: currentCompanyInfo.coId }}
        pagination={{ defaultPageSize: 10 }}
        request={async params => {
          try {
            const { data } = await getRightsList(params);

            return data;
          } catch (error) {
            return {
              data: [],
              total: 0
            };
          }
        }}
        columns={columns}
      />
      <EquityDetail rightsItem={rightsItem} {...rightsDetailModal} />
      <AllPrivileges rightsItem={rightsItem} {...allPrivilegesModal} />
    </>
  );
};

export default RightsManage;
