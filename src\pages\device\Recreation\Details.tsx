import ProDescriptions from "@ant-design/pro-descriptions";
import { useModel } from "@umijs/max";
import { getEnv } from "@/common/utils/getEnv";

export default function Details(props) {
  const { detailData } = props;
  // 获取景区 ID
  const { initialState }: any = useModel("@@initialState");
  const scenicId = initialState?.scenicInfo?.scenicId;
  console.log(scenicId, initialState);
  //获取景区名称
  const scenicName = initialState?.scenicInfo?.scenicName;
  console.log("景区名称", scenicName);
  const columns = [
    {
      title: "所属景区",
      dataIndex: "scenicName",
      name: "scenicName",
      initialValue: scenicName,
      key: "scenicName",
      render: () => {
        return scenicName;
      },
      // valueEnum: {
      //   '0': '公告',
      // },
      formItemProps: {
        // rules: [{ required: true }],
        // disable: false
      },
      fieldProps: {
        disabled: true
      }
    },
    {
      dataIndex: "managementUnit",
      title: "管理单位",
      name: "managementUnit",
      key: "managementUnit",
      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      dataIndex: "enterName",
      title: "景区娱乐项目名称",
      name: "enterName",
      key: "enterName",
      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      dataIndex: "type",
      title: "类型",
      name: "type",
      valueType: "select",
      valueEnum: {
        "0": "休闲场所",
        "1": "KTV",
        "2": "游乐场",
        "3": "茶楼"
      },
      key: "type",
      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      dataIndex: "contacts",
      title: "联系人",
      name: "contacts",
      key: "contacts",
      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      dataIndex: "contactMobile",
      title: "联系人电话",
      name: "contactMobile",
      key: "contactMobile",
      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      title: "省市区",
      dataIndex: "province",
      name: "province",
      key: "province",
      formItemProps: {
        rules: [{ required: true }]
      },
      render: () => {
        return detailData.provinceName + detailData.cityName + detailData.areaName;
      },
      valueType: "select"
      // fieldProps: {
      //     // mode: 'multiple',
      //     options:

      //     // onChange: (value, option) => {
      //     //   console.log(value);
      //     //   setAcceptorData(value);
      //     // },
      //     // showArrow: true,
      //     // disabled: flag ? false : show ? false : true,
      // },
    },
    {
      dataIndex: "address",
      title: "详细地址",
      name: "address",
      key: "address",
      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },

    {
      dataIndex: "longitude",
      title: "经度",
      name: "longitude",
      key: "longitude",
      fieldProps: {},
      render: (dom, record) => {
        const str = record.longitude.charAt(0);
        if (str == "+" || str == "-") {
          if (str == "+") {
            return <span>{record.longitude.slice(1)}° E </span>;
          } else if (str == "-") {
            return <span>{record.longitude.slice(1)}° W</span>;
          }
        } else {
          return <span>{record.longitude}° E</span>;
        }
      }
      // formItemProps: {
      //     rules: [{ required: true }],
      // },
    },
    {
      dataIndex: "latitude",
      title: "纬度",
      name: "latitude",
      key: "latitude",
      fieldProps: {},
      render: (dom, record) => {
        const str = record.latitude.charAt(0);
        if (str == "+" || str == "-") {
          if (str == "+") {
            return <span>{record.latitude.slice(1)}° N</span>;
          } else if (str == "-") {
            return <span>{record.latitude.slice(1)}° S</span>;
          }
        } else {
          return <span>{record.latitude}° N</span>;
        }
      }
      // formItemProps: {
      //     rules: [{ required: true }],
      // },
    }

    // {
    //     dataIndex: 'img',
    //     // title:'监控图片列表',
    //     key: 'img',
    //     renderFormItem: () => {
    //         return (
    //             <ProForm.Item
    //                 // style={{ width: '300px' }}
    //                 label="监控图片列表"

    //             // rules={[{ required: true, message: '请输入选择公告内容' }]}
    //             >

    //                 <ProFormUploadButton
    //                     name="file"
    //                     // label="图片"
    //                     max={1}
    //                     // fileList={defaultFile}
    //                     // rules={[{ required: true, message: '请上传图片' }]}
    //                     fieldProps={{
    //                         multiple: false,
    //                         listType: 'picture-card',
    //                         accept: '.jpg,.png,.jpeg',
    //                         onPreview: handlePreview,
    //                         onChange: addUpload,
    //                     }}
    //                     action={getEnv().UPLOAD_HOST}
    //                 />
    //             </ProForm.Item>
    //         );
    //     },
    // },
  ];
  return (
    <ProDescriptions
      title="基础信息"
      //   request={async () => {}}
      dataSource={detailData}
      columns={columns}
      column={2}
    >
      {/* <ProDescriptions.Item dataIndex="percent" label="百分比" valueType="percent">
                100
            </ProDescriptions.Item> */}
    </ProDescriptions>
  );
}
