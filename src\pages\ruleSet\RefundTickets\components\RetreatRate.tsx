import { isEmpty } from 'lodash';

export default function RetreatRate({ text, title }) {
  return (
    <div>
      <div>
        按{title}时间退票费率：{isEmpty(text) && '无'}
      </div>
      {text.map(
        (
          { dayBeginHour, dayBeginTime, dayEndHour, dayEndTime, flag, id, rate, retreatId },
          index,
        ) => {
          const flagArr = ['前', '后'];
          return (
            <div key={index}>
              {title}
              {flagArr[flag]}：{dayBeginTime} 天 {dayBeginHour} 小时 - {dayEndTime} 天 {dayEndHour}{' '}
              小时 退票费率：{rate}
            </div>
          );
        },
      )}
    </div>
  );
}
