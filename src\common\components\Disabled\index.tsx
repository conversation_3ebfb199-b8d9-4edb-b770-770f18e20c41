import type { ActionType } from '@ant-design/pro-table';
import { Switch, message } from 'antd';
import type { MutableRefObject } from 'react';

export default ({
  access,
  status,
  params,
  request,
  actionRef,
}: {
  access: boolean;
  status: boolean;
  params: any;
  request: Function;
  actionRef: MutableRefObject<ActionType | undefined>;
}) => (
  <Switch
    disabled={!access}
    checkedChildren="启用"
    unCheckedChildren="禁用"
    checked={status}
    onChange={async () => {
      try {
        await request(params);
        message.success(status ? '已禁用' : '已启用');
        // 刷新列表
        actionRef?.current?.reload();
      } catch (error) {}
    }}
  />
);
