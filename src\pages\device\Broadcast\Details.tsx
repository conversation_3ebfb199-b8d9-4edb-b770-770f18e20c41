import ProDescriptions from "@ant-design/pro-descriptions";
import { useModel } from "@umijs/max";
import { getEnv } from "@/common/utils/getEnv";

export default function Details(props) {
  const { detailData } = props;
  // 获取景区 ID
  const { initialState }: any = useModel("@@initialState");
  const scenicId = initialState?.scenicInfo?.scenicId;
  console.log(scenicId, initialState);
  //获取景区名称
  const scenicName = initialState?.scenicInfo?.scenicName;
  console.log("景区名称", scenicName);
  const columns = [
    {
      title: "所属景区",
      dataIndex: "scenicName",
      name: "scenicName",
      key: "scenicName",
      render: () => {
        return scenicName;
      },
      // valueEnum: {
      //   '0': '公告',
      // },
      formItemProps: {
        // rules: [{ required: true }],
        // disable: false
      },
      fieldProps: {
        disabled: true
      }
    },
    {
      dataIndex: "radioName",
      title: "点位名称",
      name: "radioName",
      key: "radioName",
      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      dataIndex: "loginName",
      title: "账号",
      name: "loginName",
      key: "loginName",
      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      dataIndex: "loginPassword",
      title: "密码",
      name: "loginPassword",
      key: "loginPassword",
      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      dataIndex: "longitude",
      title: "经度",
      name: "longitude",
      key: "longitude",
      render: (dom, record) => {
        const str = record.longitude.charAt(0);
        if (str == "+" || str == "-") {
          if (str == "+") {
            return <span>{record.longitude.slice(1)}° E </span>;
          } else if (str == "-") {
            return <span>{record.longitude.slice(1)}° W</span>;
          }
        } else {
          return <span>{record.longitude}° E</span>;
        }
      },
      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      dataIndex: "latitude",
      title: "纬度",
      name: "latitude",
      key: "latitude",
      render: (dom, record) => {
        const str = record.latitude.charAt(0);
        if (str == "+" || str == "-") {
          if (str == "+") {
            return <span>{record.latitude.slice(1)}° N</span>;
          } else if (str == "-") {
            return <span>{record.latitude.slice(1)}° S</span>;
          }
        } else {
          return <span>{record.latitude}° N</span>;
        }
      },
      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      title: "品牌",
      dataIndex: "brand",
      name: "brand",
      // valueEnum: {
      //     '0': '1',
      //     "1": '2'
      // },
      key: "brand",
      formItemProps: {
        rules: [{ required: true }]
      },
      // valueType: 'select',
      fieldProps: {
        // mode: 'multiple',
        // options: positionValue2 == 1 ? scenicData2 : companyData2,
        // onChange: (value, option) => {
        //   console.log(value);
        //   setAcceptorData(value);
        // },
        // showArrow: true,
        // disabled: flag ? false : show ? false : true,
      }
    },
    {
      dataIndex: "linkAddress",
      title: "链接地址",
      name: "linkAddress",
      key: "linkAddress",
      fieldProps: {},
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      dataIndex: "point",
      title: "管理端口",
      name: "point",
      key: "point",
      fieldProps: {}
    },
    {
      dataIndex: "channel",
      title: "通道号",
      name: "channel",
      key: "channel",
      fieldProps: {}
      // formItemProps: {
      //   rules: [{ required: true }],
      // },
    }

    // {
    //     dataIndex: 'img',
    //     // title:'监控图片列表',
    //     key: 'img',
    //     renderFormItem: () => {
    //         return (
    //             <ProForm.Item
    //                 // style={{ width: '300px' }}
    //                 label="监控图片列表"

    //             // rules={[{ required: true, message: '请输入选择公告内容' }]}
    //             >

    //                 <ProFormUploadButton
    //                     name="file"
    //                     // label="图片"
    //                     max={1}
    //                     // fileList={defaultFile}
    //                     // rules={[{ required: true, message: '请上传图片' }]}
    //                     fieldProps={{
    //                         multiple: false,
    //                         listType: 'picture-card',
    //                         accept: '.jpg,.png,.jpeg',
    //                         onPreview: handlePreview,
    //                         onChange: addUpload,
    //                     }}
    //                     action={getEnv().UPLOAD_HOST}
    //                 />
    //             </ProForm.Item>
    //         );
    //     },
    // },
  ];
  return (
    <ProDescriptions
      title="基础信息"
      //   request={async () => {}}
      dataSource={detailData}
      columns={columns}
      column={2}
    >
      {/* <ProDescriptions.Item dataIndex="percent" label="百分比" valueType="percent">
                100
            </ProDescriptions.Item> */}
    </ProDescriptions>
  );
}
