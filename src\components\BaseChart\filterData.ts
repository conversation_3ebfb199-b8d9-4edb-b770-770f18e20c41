/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-05-08 15:00:19
 * @LastEditTime: 2023-08-29 20:26:57
 * @LastEditors: zhang<PERSON><PERSON>i
 */

// 出退票统计
export const filterBarData1 = (result: any) => {
  const dimensions = ['type', '出票', '退票'];

  const source: any[] = [];
  for (const item of result) {
    const current = source.find((i) => i.type === item.name);
    if (current) {
      Object.assign(current, {
        [item.legend]: item.value,
      });
      // console.log(current);
    } else {
      source.push({
        type: item.name,
        [item.legend]: item.value,
      });
    }
  }
  return {
    dimensions,
    source,
  };
};

// 预定入园统计
export const filterBarData2 = (result: any) => {
  const dimensions = ['type', '入园'];
  const source: any[] = [];
  for (const item of result) {
    source.push({
      type: item.name,
      ['入园']: item.value,
    });
  }
  return {
    dimensions,
    source,
  };
};

// 商品退订统计
export const goodsChartData = (result: any[]) => {
  const dimensions = ['type', '预定', '退单'];

  const source: any[] = [];
  for (const item of result) {
    const current = source.find((i) => i.type === item.name);
    if (current) {
      Object.assign(current, {
        [item.legend]: item.value,
      });
      // console.log(current);
    } else {
      source.push({
        type: item.name,
        [item.legend]: item.value,
      });
    }
  }

  return {
    dimensions,
    source,
  };
};

// 当日实际出入园人数
export const filterLineDataOut = (result: any[]) => {
  const dimensions = ['type', '入园', '出园'];

  const source: any[] = [];
  for (const item of result) {
    const current = source.find((i) => i.type === item.name + ':00');
    if (current) {
      Object.assign(current, {
        [item.legend]: item.value,
      });
      // console.log(current);
    } else {
      source.push({
        type: item.name + ':00',
        [item.legend]: item.value,
      });
    }
  }

  return {
    dimensions,
    source,
  };
};
